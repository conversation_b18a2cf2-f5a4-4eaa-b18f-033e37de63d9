#!/usr/bin/env python3
"""
Weather.py
测试天气模块的所有功能，包括工具函数、Weather类和缓存机制
"""

import sys
import os
import unittest
import time
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from application.utils.weather import Weather, fahrenheit_to_celsius, formate_date
from application.utils.weather_cache import WeatherCacheManager, get_weather_data_cached


class TestWeatherClass(unittest.TestCase):
    """测试Weather类"""

    def setUp(self):
        """测试前准备"""
        self.test_date = "20240115"
        self.test_lat = 52.1934
        self.test_lng = 5.4305

        # 清理类实例缓存
        Weather._instances.clear()

    def test_weather_initialization(self):
        """测试Weather类初始化"""
        weather = Weather(self.test_date, self.test_lat, self.test_lng)

        self.assertEqual(weather.date, "2024-01-15")
        self.assertEqual(weather.latitude, self.test_lat)
        self.assertEqual(weather.longitude, self.test_lng)
        self.assertEqual(weather.next_day_str, "2024-01-16")
        self.assertIsNotNone(weather.key)

    def test_weather_initialization_with_formatted_date(self):
        """测试使用已格式化日期初始化"""
        formatted_date = "2024-01-15"
        weather = Weather(formatted_date, self.test_lat, self.test_lng)

        self.assertEqual(weather.date, "2024-01-15")
        self.assertEqual(weather.next_day_str, "2024-01-16")

    def test_weather_singleton_pattern(self):
        """测试Weather类的单例模式"""
        weather1 = Weather(self.test_date, self.test_lat, self.test_lng)
        weather2 = Weather(self.test_date, self.test_lat, self.test_lng)

        # 应该是同一个实例
        self.assertIs(weather1, weather2)

        # 不同参数应该是不同实例
        weather3 = Weather("20240116", self.test_lat, self.test_lng)
        self.assertIsNot(weather1, weather3)

    @patch('requests.get')
    def test_weather_api_call_success(self, mock_get):
        """测试天气API调用成功"""
        # 模拟API响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.content = json.dumps({
            "days": [
                {
                    "datetime": "2024-01-15",
                    "temp": 15.5,
                    "humidity": 65,
                    "hours": [
                        {"datetime": "00:00:00", "temp": 12.0},
                        {"datetime": "01:00:00", "temp": 11.5}
                    ]
                }
            ]
        }).encode()
        mock_get.return_value = mock_response

        weather = Weather(self.test_date, self.test_lat, self.test_lng)
        result = weather()

        self.assertIsNotNone(result)
        self.assertIn("days", result)
        mock_get.assert_called_once()

    @patch('requests.get')
    def test_weather_api_call_failure(self, mock_get):
        """测试天气API调用失败"""
        # 模拟API失败响应
        mock_response = MagicMock()
        mock_response.status_code = 404
        mock_get.return_value = mock_response

        weather = Weather(self.test_date, self.test_lat, self.test_lng)
        result = weather()

        self.assertIsNone(result)

    @patch('requests.get')
    def test_weather_api_exception(self, mock_get):
        """测试天气API异常处理"""
        # 模拟网络异常
        mock_get.side_effect = Exception("Network error")

        weather = Weather(self.test_date, self.test_lat, self.test_lng)
        result = weather()

        self.assertIsNone(result)

    def test_weather_url_generation(self):
        """测试天气API URL生成"""
        weather = Weather(self.test_date, self.test_lat, self.test_lng)

        # 通过检查内部逻辑验证URL格式
        expected_base = "https://weather.visualcrossing.com/VisualCrossingWebServices/rest/services/timeline"
        expected_coords = f"{self.test_lat},{self.test_lng}"
        expected_dates = "2024-01-15/2024-01-16"

        # 这里我们可以通过mock来验证URL
        with patch('requests.get') as mock_get:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.content = b'{"test": "data"}'
            mock_get.return_value = mock_response

            weather()

            # 验证调用的URL包含预期的组件
            called_url = mock_get.call_args[1]['url'] if 'url' in mock_get.call_args[1] else mock_get.call_args[0][0]
            self.assertIn(expected_base, called_url)
            self.assertIn(expected_coords, called_url)
            self.assertIn(expected_dates, called_url)


class TestWeatherCacheManager(unittest.TestCase):
    """测试天气缓存管理器"""

    def setUp(self):
        """测试前准备"""
        self.cache_manager = WeatherCacheManager(ttl_hours=1, maxsize=100)
        self.test_date = "2024-01-15"
        self.test_lat = 52.1934
        self.test_lng = 5.4305

    def tearDown(self):
        """测试后清理"""
        self.cache_manager.clear_cache()

    def test_cache_key_generation(self):
        """测试缓存键生成"""
        key1 = self.cache_manager._generate_cache_key(self.test_date, self.test_lat, self.test_lng)
        key2 = self.cache_manager._generate_cache_key(self.test_date, self.test_lat, self.test_lng)

        # 相同参数应该生成相同的键
        self.assertEqual(key1, key2)

        # 不同参数应该生成不同的键
        key3 = self.cache_manager._generate_cache_key("2024-01-16", self.test_lat, self.test_lng)
        self.assertNotEqual(key1, key3)

    @patch('application.utils.weather_cache.Weather')
    def test_cache_miss_and_hit(self, mock_weather_class):
        """测试缓存未命中和命中"""
        # 模拟Weather类
        mock_weather = MagicMock()
        mock_weather.return_value = {"temp": 20, "humidity": 60}
        mock_weather_class.return_value = mock_weather

        # 第一次调用 - 缓存未命中
        result1 = self.cache_manager.get_cached_weather_data(self.test_date, self.test_lat, self.test_lng)

        # 第二次调用 - 缓存命中
        result2 = self.cache_manager.get_cached_weather_data(self.test_date, self.test_lat, self.test_lng)

        # 验证结果一致
        self.assertEqual(result1, result2)

        # 验证Weather只被调用一次
        mock_weather_class.assert_called_once()

    def test_cache_info(self):
        """测试缓存信息获取"""
        info = self.cache_manager.get_cache_info()

        expected_keys = ['cache_size', 'maxsize', 'ttl', 'currsize']
        for key in expected_keys:
            self.assertIn(key, info)

        self.assertEqual(info['maxsize'], 100)
        self.assertEqual(info['ttl'], 3600)  # 1小时

    def test_cache_clear(self):
        """测试缓存清理"""
        # 添加一些缓存数据
        self.cache_manager._set_weather_data(self.test_date, self.test_lat, self.test_lng, {"test": "data"})

        # 验证缓存不为空
        self.assertGreater(self.cache_manager.get_cache_info()['cache_size'], 0)

        # 清理缓存
        self.cache_manager.clear_cache()

        # 验证缓存为空
        self.assertEqual(self.cache_manager.get_cache_info()['cache_size'], 0)


def run_manual_tests():
    # 测试: Weather类基本功能
    print("\n2. Weather类基本功能测试")
    print("-" * 30)

    try:
        # 使用荷兰阿姆斯特丹的坐标
        weather = Weather("20240115", 52.3676, 4.9041)
        print(f"初始化成功:")
        print(f"  日期: {weather.date}")
        print(f"  次日: {weather.next_day_str}")
        print(f"  坐标: ({weather.latitude}, {weather.longitude})")

        # 测试单例模式
        weather2 = Weather("20240115", 52.3676, 4.9041)
        print(f"  单例模式: {'是' if weather is weather2 else '否'}")

    except Exception as e:
        print(f"  Weather类测试失败: {str(e)}")

    # 测试3: 缓存管理器测试
    print("\n3. 缓存管理器测试")
    print("-" * 30)

    try:
        cache_manager = WeatherCacheManager(ttl_hours=1, maxsize=10)

        # 显示初始缓存信息
        info = cache_manager.get_cache_info()
        print(f"初始缓存信息:")
        for key, value in info.items():
            print(f"  {key}: {value}")

        # 测试缓存键生成
        key1 = cache_manager._generate_cache_key("2024-01-15", 52.3676, 4.9041)
        key2 = cache_manager._generate_cache_key("2024-01-15", 52.3676, 4.9041)
        print(f"\n缓存键一致性: {'是' if key1 == key2 else '否'}")
        print(f"缓存键示例: {key1[:16]}...")

    except Exception as e:
        print(f"  缓存管理器测试失败: {str(e)}")

    print("\n" + "=" * 60)
    print("手动测试完成")
    print("=" * 60)


if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 运行单元测试")
    print("2. 运行手动测试")
    print("3. 运行所有测试")

    choice = input("请输入选择 (1/2/3): ").strip()

    if choice == "1":
        unittest.main(argv=[''], exit=False, verbosity=2)
    elif choice == "2":
        run_manual_tests()
    elif choice == "3":
        print("运行单元测试...")
        unittest.main(argv=[''], exit=False, verbosity=2)
        print("\n运行手动测试...")
        run_manual_tests()
    else:
        print("无效选择，运行手动测试...")
        run_manual_tests()
