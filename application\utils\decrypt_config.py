import os

import yaml
from pathlib import Path

from application.utils.crypto import ConfigE<PERSON>ryptor


def decrypt_config(config_file: Path, key: str) -> str:
    # 检查是否是加密文件
    is_encrypted = config_file.suffix == '.enc'
    if not is_encrypted:
        raise ValueError("Config file is not encrypted")

    encryptor = ConfigEncryptor(key)
    config_data = encryptor.get_decrypted_config(config_file)
    return config_data


if __name__ == "__main__":
    # 获取项目根路径
    project_root = Path(__file__).parent.parent.parent
    with open(project_root / "application" / "settings" / "encryption_keys.yaml", "r") as f:
        enc_keys = f.read()
    enc_keys = yaml.safe_load(enc_keys)["encryption_keys"]

    for file in os.listdir(project_root / "application" / "settings" / "encrypted"):
        if not file.endswith(".enc"):
            continue
        env = file.split(".")[0]
        enc_key = enc_keys[env]
        config_data = decrypt_config(project_root / "application" / "settings" / "encrypted" / file, enc_key)
        print(config_data)
