from scipy.integrate import trapz
import json
import pandas as pd
from plotSoc import getPowerSocCurve
import numpy as np
from concurrent.futures import ProcessPoolExecutor
import multiprocessing
from functools import partial
import os
from datetime import datetime
from calculate_power_curve import calcPowerCurve, calcPowerCurve_2
import argparse


def curve_diff(curve1, curve2):
    dict1 = {p[0]: p[1] for p in curve1}
    dict2 = {p[0]: p[1] for p in curve2}

    # print(f"dict1 keys is {dict1.keys()}")
    # print(f"dict2 keys is {dict2.keys()}")
    common_x = set(dict1.keys()).intersection(dict2.keys())
    common_x = sorted(common_x)  # very important
    if not common_x:
        return 0.0

    common_x_array = np.array(list(common_x))  # convert to np array
    y1 = np.array([dict1[x] for x in common_x])
    y2 = np.array([dict2[x] for x in common_x])
    # print(f"common_x is {common_x},y1 is {y1},y2 is {y2}")
    diff = np.abs(y1 - y2)
    # pdb.set_trace()
    integral_diff = trapz(diff, common_x_array)
    # print(f"integral_diff is {integral_diff}")
    return integral_diff


def handle_one_curve_task(curve, params, pattern):
    dict_power, coeff_a, coeff_b1, coeff_b2, coeff_c = params
    error = 0
    sSOC = curve["sSOC"]
    startup_power = curve["startup_power"]
    descending_flag = curve["descending_flag"]
    plimit = curve["plimit"]
    power_list = curve["power_list"]
    soc_list = curve["soc_list"]
    # dict_power = self.dict_power

    # calcuate the predicted power and the diff between it and actual power curve
    jsonname = curve["jsonname"]
    if pattern == "pattern1":
        control_points, fit_curve = calcPowerCurve(sSOC, startup_power, descending_flag, plimit, coeff_a, coeff_b1,
                                               coeff_b2, coeff_c, dict_power)
    else:
        control_points, fit_curve = calcPowerCurve_2(sSOC, startup_power, descending_flag, plimit, coeff_a, coeff_b1,
                                                   coeff_b2, coeff_c, dict_power)
    # curve is a list,whose element is a tuple,(soc,power)
    actual_curve = list(zip(soc_list, power_list))

    error = curve_diff(fit_curve, actual_curve)
    # print(f"jsonname is {jsonname},and error is {error},control point is {control_points}")
    # if "ce4_205" in jsonname:
    #     print(f"jsonname is {jsonname},and error is {error},control point is {control_points}")
    # if error > 4000:
    #     print(f"jsonname is {jsonname},and error is {error},control point is {control_points}")
    return error


# get the voltage and power at the 60th second
def get_start_up_voltage_power(json_data):
    fmt = " %Y-%m-%d %H:%M:%S.%f"
    voltage_at_startup = 0
    startup_power = 0
    max_target_cur = 0
    descending_flag = False  # indicate it will be descending from the point at 60th second
    for i, item in enumerate(json_data):
        if isinstance(item, dict):
            for k, v in item.items():
                # print(f"-{k}:{v}")
                if k == "MaximumCurrent":
                    maxim_current = v
                if k == "MaximumVoltage":
                    maxim_voltage = v
        elif isinstance(item, list):
            cnt = 0
            for list_item in item:
                # pdb.set_trace()
                parts = list_item.split(',')
                time_str = parts[7]
                if '.' not in time_str:
                    time_str += ".000"
                cnt = cnt + 1
                if cnt == 1:
                    continue  # it is the title
                elif cnt == 2:
                    initial_time = datetime.strptime(time_str, fmt)
                    continue
                target_cur = float(parts[0])
                actual_cur = float(parts[1])
                out_vol = float(parts[3])
                actual_power = actual_cur * out_vol
                time = datetime.strptime(time_str, fmt)
                deta = (time - initial_time).total_seconds()
                if target_cur > max_target_cur:
                    max_target_cur = target_cur
                if deta >= 89 and deta <= 91:
                    startup_power = actual_power
                    voltage_at_startup = out_vol
                    if target_cur < max_target_cur:
                        descending_flag = True

    if voltage_at_startup == 0:
        print(f"voltage_at_startup is invalid")
    return voltage_at_startup, startup_power, descending_flag


# para:
# row:it is one line of dataframe which is loaded from a file like tesla_575_sel1.csv
# a row has several data fields like 'jsonname','sSOC','gunlimit1','ratedpower',.etc
#
def load_one_curve_task(row, vehicle):
    jsonname = row['jsonname']
    sSOC = row['sSOC']
    gunlimit = row['gunlimit1']
    ratedpower = row['ratedpower']
    print(f"jsonname is {jsonname}")
    # now to load json as power-soc curve
    try:
        with open(os.path.join(f"{vehicle}", jsonname)) as file:
            jsondata = json.load(file)
    except FileNotFoundError:
        print(f"{jsonname} not exist")
        return dict()  # return a empty dict

    # to calculate the plimit,use the voltage at 60th second to calculate the maxpower
    startup_voltage, startup_power, descending_flag = get_start_up_voltage_power(jsondata)
    if startup_voltage == 0:
        print(f"{jsonname} startup_voltage invalid")
        return dict()
        # maxVoltage = jsondata[0]["MaximumVoltage"] #jsondata[0] is a dictionary
    pmax_by_curr = startup_voltage * gunlimit / 1000  # unit is kw
    startup_power = startup_power / 1000  # now unit is kw
    plimit = min(pmax_by_curr, ratedpower)
    # pdb.set_trace()
    soc_list, power_list = getPowerSocCurve(jsondata)

    json_info = {
        "sSOC": sSOC,
        "startup_power": startup_power,
        "descending_flag": descending_flag,
        "soc_list": soc_list,
        "power_list": power_list,
        "plimit": plimit,
        "jsonname": jsonname
    }
    return json_info


class FitCalc:
    def __init__(self, file_csv, power_table, vehicle, pattern):
        self.vehicle = vehicle
        self.pattern = pattern
        # load the power table
        with open(power_table, "r") as file:
            f = json.load(file)
        power_series = f['powertable']
        # conver the list to a dictionary
        self.dict_power = {index: value for index, value in enumerate(power_series)}

        file_to_load = []
        # load the tested json
        df = pd.read_csv(file_csv)
        for i in range(len(df)):
            # print(f"index is {i}")
            row = df.iloc[i]
            file_to_load.append(row)

        # now to load the files parallelly
        executor = ProcessPoolExecutor(8)  # 16
        print(f"it has {len(file_to_load)} files to load totally")
        results = executor.map(load_one_curve_task, file_to_load, [self.vehicle] * len(file_to_load))
        executor.shutdown()
        # pdb.set_trace()
        print("results:", results)
        self.json_data_list = list(results)
        # now to filter the empty dictionary
        self.json_data_list = [d for d in self.json_data_list if d]
        print(f"results has {len(self.json_data_list)} files")

    def fit_error_all(self, coeff_a, coeff_b1, coeff_b2, coeff_c):
        # executor = ProcessPoolExecutor(10)  # 20
        # results = executor.map(handle_one_curve_task, self.json_data_list,
        #                        [(self.dict_power, coeff_a, coeff_b1, coeff_b2, coeff_c)] * len(self.json_data_list))
        # executor.shutdown()
        results = []
        for json_data in self.json_data_list:
            results.append(handle_one_curve_task(json_data, (self.dict_power, coeff_a, coeff_b1, coeff_b2, coeff_c), self.pattern))
        error_sum = np.nansum(results)
        # print(f"the total error is {error_sum}")
        return error_sum

    def fitness_function(self, params, index):
        """示例适应度函数，实际使用时替换为你的目标函数"""
        # x, y, z,c1,c2 = params
        coeff_a, coeff_b1, coeff_b2, coeff_c = params
        # return -(x**2 + y**2 + z**2)  # 示例：求最小值问题
        fit = -self.fit_error_all(coeff_a, coeff_b1, coeff_b2, coeff_c)
        # pdb.set_trace()
        print(f"fitness for {params} is {fit}")
        print("in", index, "population")
        return fit


class GeneticAlgorithm:
    def __init__(self, param_ranges, csv_path, power_table, vehicle, pattern, pop_size=50, elite_size=20,
                 mutation_rate=0.1, generations=1):
        """
        param_ranges: 每个参数的取值范围 [(min1,max1), (min2,max2), (min3,max3)]
        """
        self.param_ranges = param_ranges
        self.pop_size = pop_size
        self.elite_size = elite_size
        self.mutation_rate = mutation_rate
        self.generations = generations
        #pdb.set_trace()
        self.fitCalc = FitCalc(csv_path, power_table, vehicle, pattern)

    def init_population(self):   # 初始化种群
        return np.array([
            [np.random.uniform(low, high) for low, high in self.param_ranges]
            for _ in range(self.pop_size)
        ])

    def selection(self, ranked_pop):
        """轮盘赌选择"""
        #pdb.set_trace()
        fitness = np.array([ind[1] for ind in ranked_pop])
        fitness = fitness - fitness.min() + 1e-6
        probs = fitness / fitness.sum()
        #ranked_pop_array = np.array(ranked_pop)
        index_list = np.random.choice(np.arange(self.pop_size), size=self.pop_size, p=probs, replace=True).tolist()
        #pdb.set_trace()
        return [ranked_pop[i] for i in index_list]

    def crossover(self, parent1, parent2):
        """单点交叉"""
        idx = np.random.randint(1, len(parent1)-1)
        return np.concatenate([parent1[:idx], parent2[idx:]])

    def mutate(self, individual):
        """随机突变"""
        for i in range(len(individual)):
            if np.random.random() < self.mutation_rate:
                low, high = self.param_ranges[i]
                individual[i] = np.random.uniform(low, high)
        return individual

    def evolve(self):
        pop = self.init_population()
        best_fitness = []
        best_params=[]
        for gen in range(self.generations):
            # 评估适应度
            #pdb.set_trace()
            fitness = np.array([self.fitCalc.fitness_function(ind, index) for index, ind in enumerate(pop)])
            ranked_pop = sorted(zip(pop, fitness), key=lambda x: x[1], reverse=True) #in descending order
            #pdb.set_trace()
            # 保留精英
            elites = [ind[0] for ind in ranked_pop[:self.elite_size]]

            # 选择、交叉、变异
            selected = self.selection(ranked_pop)
            children = []
            for i in range(0, len(selected)-1, 2):
                #pdb.set_trace()
                child1 = self.crossover(selected[i][0], selected[i+1][0]) #selected[i] is (params,fitness)
                child2 = self.crossover(selected[i+1][0], selected[i][0])
                children.extend([self.mutate(child1), self.mutate(child2)])

            # 新一代种群
            pop = np.vstack([elites, children[:self.pop_size - self.elite_size]])
            best_fitness.append(ranked_pop[0][1])
            best_params.append(ranked_pop[0][0])
            print("----------------")
            print(f"Gen {gen}: Best params {ranked_pop[0][0]}, fitness {ranked_pop[0][1]}")
            print("----------------")

        #now to select the best fitness and params in all generations
        max_value = max(best_fitness)
        #pdb.set_trace()
        index = best_fitness.index(max_value)
        params = best_params[index]
        print(f"finally best fitness is {max_value},generation is {i},params is {params}")
        return pop, best_fitness, params


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Train the power predict model')
    parser.add_argument('--vehicle', required=True, help='vehicle for training, same with the folder name')
    parser.add_argument('--power_table', required=True, help='power table of the training vehicle')
    parser.add_argument('--file_list', required=True, help='csv file with training set')
    parser.add_argument('--pattern', required=True, help='csv file with training set')
    args = parser.parse_args()


    param_ranges = [(0, 1), (0, 0.5), (0, 100), (0, 1)]
    ga = GeneticAlgorithm(param_ranges, args.file_list, args.power_table, args.vehicle, args.pattern, pop_size=200, generations=50)
    final_pop, fitness_history, params = ga.evolve()

    data = {
        "coeff_a": params[0],
        "coeff_b1": params[1],
        "coeff_b2": params[2],
        "coeff_c": params[3],
        "fitness": max(fitness_history)
    }
    filename = f"{args.vehicle}_all_genetic_para_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(filename, 'w') as f:
        json.dump(data, f, indent=4)
