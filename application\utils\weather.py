import json
import traceback

import requests
from datetime import datetime,timedelta


def fahrenheit_to_celsius(fahrenheit):
    celsius = (fahrenheit - 32) * 5 / 9
    return round(celsius, 2)


def formate_date(date_string):
    return datetime.strptime(date_string, "%Y%m%d").strftime("%Y-%m-%d")


class Weather(object):
    _instances = dict()

    def __new__(cls, date, latitude, longitude):
        key = (date, latitude, longitude)

        if key in cls._instances:
            instance = cls._instances[key]
        else:
            instance = super().__new__(cls)
            cls._instances[key] = instance

        return instance

    def __init__(self, date, latitude, longitude):
        self.date = formate_date(date) if '-' not in date else date
        self.latitude = latitude
        self.longitude = longitude
        date_obj = datetime.strptime(self.date, "%Y-%m-%d")
        next_day = date_obj + timedelta(days=1)
        self.next_day_str = next_day.strftime("%Y-%m-%d")
        self.key = 'M4Q72YY4AVJRTZEDW7GKQY5HD'

    def __call__(self, *args, **kwargs):
        url  = f'https://weather.visualcrossing.com/VisualCrossingWebServices/rest/services/timeline/{self.latitude},{self.longitude}/{self.date}/{self.next_day_str}/?unitGroup=metric&include=hours&key={self.key}&contentType=json'
        try:
            response = requests.get(url=url)
            if response.status_code == 200:
                data = json.loads(response.content)
                return data
        except:
            print(traceback.print_exc())


if __name__ == '__main__':
    w = Weather(date='2022-06-04', latitude=52.19340694027317, longitude=5.430548227543284)
    temp = w()
    print(json.dumps(temp))
