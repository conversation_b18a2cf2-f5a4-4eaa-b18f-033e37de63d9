# 数据库操作文件
import json
import os
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Union, Set, Any

import pytz
import yaml
from sqlalchemy import create_engine, desc, and_, select, func, delete
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.orm.exc import NoResultFound, MultipleResultsFound

from application.algorithm_schedule.status_enumeration import EVPredictionStatus, ChargePileStatus
from application.db_operate.models import ChargerSessionDB, EVModelDB, ChargingRecordDB, ChargerDB, ModuleDB, PileDB, \
    SiteDB, ChargerRealtimeDataDB, SiteDemandDataDB, SiteDynamicPriceDataDB, ESRealtimeDataDB, PVAndMeterRealtimeDataDB, \
    SiteSellPriceDataDB, SiteFixedAndTimeOfUsePriceDataDB, PVCurvePredictionDB, HybridLoadPredictionDB, \
    EnergyStorageTaskDB, SiteDemandPredictionDB, ChargerNonSuppressPowerPredictionDB, PowerPredictCurveForEmsDB
from application.utils.logger import setup_logger
from application.utils.timezone_mapping import get_timezone_with_fallback, convert_utc_to_local_time

logger = setup_logger("db_operate", direction="db")


def singleton(cls):
    """
    单例模式装饰器
    """
    _instances = {}
    
    def get_instance(*args, **kwargs):
        if cls not in _instances:
            _instances[cls] = cls(*args, **kwargs)
        return _instances[cls]
    
    return get_instance


@singleton
class DBOperate:
    """数据库操作类"""

    def __init__(self):
        """初始化数据库连接"""
        try:
            # 读取配置文件 - 使用跨平台路径
            # 获取当前文件所在目录的父目录（application目录）
            current_dir = os.path.dirname(os.path.abspath(__file__))
            application_dir = os.path.dirname(current_dir)
            config_path = os.path.join(application_dir, 'settings', 'config.yaml')

            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 获取数据库配置
            db_config = config['artifact_instances']['mysql']
            
            # 构建数据库URL
            db_url = f"mysql+pymysql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}"
            
            # 创建数据库引擎，使用连接池
            self.engine = create_engine(
                db_url,
                pool_size=5,  # 连接池大小
                max_overflow=10,  # 最大溢出连接数
                pool_timeout=30,  # 连接池超时时间
                pool_recycle=3600,  # 连接回收时间（1小时）
                pool_pre_ping=True,  # 自动检测连接是否有效
                connect_args={
                    'connect_timeout': 10,  # 连接超时时间
                    'read_timeout': 30,  # 读取超时时间
                    'write_timeout': 30,  # 写入超时时间
                }
            )
            
            # 创建会话工厂
            self.SessionLocal = sessionmaker(bind=self.engine)
            
            logger.info("数据库连接初始化成功")
        except Exception as e:
            logger.error(f"数据库连接初始化失败: {str(e)}")
            raise

    def get_db(self) -> Session:
        """
        获取数据库会话，使用上下文管理器确保连接正确关闭
        
        Returns:
            Session: 数据库会话对象
            
        Raises:
            Exception: 数据库连接错误
        """
        db = self.SessionLocal()
        try:
            return db
        except Exception as e:
            logger.error(f"数据库连接错误: {str(e)}")
            db.close()
            raise

    def create_charging_record(self, record_data: Dict) -> bool:
        """
        创建充电记录
        
        Args:
            record_data: 包含充电记录信息的字典，需要包含以下字段：
                charger_sn: 充电枪序列号
                curr_output: 输出电流
                vol_output: 输出电压
                curr_demand: 需求电流
                soc: 当前SOC
                max_power: 最大功率
                consumed_energy: 已消耗能量
                report_at: 上报时间
                collect_at: 采集时间
                local_id: 充电桩本地ID
                
        Returns:
            bool: 创建是否成功
        """
        try:
            with self.get_db() as db:
                # 创建新的充电记录
                charging_record = ChargingRecordDB(
                    charger_sn=record_data['charger_sn'],
                    curr_output=record_data['curr_output'],
                    vol_output=record_data['vol_output'],
                    curr_demand=record_data['curr_demand'],
                    soc=record_data['soc'],
                    max_power=record_data['max_power'],
                    consumed_energy=record_data['consumed_energy'],
                    report_at=record_data['report_at'],
                    created_at=record_data['created_at'],
                    local_id=record_data['local_id']
                )

                db.add(charging_record)
                db.commit()

                logger.info(f"Created charging record for charger: {record_data['charger_sn']}, "
                            f"local_id: {record_data['local_id']}")
                return True

        except Exception as e:
            logger.error(f"Error creating charging record: {e}")
            return False

    def create_or_update_ev_model(self, model_data: Dict) -> bool:
        """
        创建或更新车型记录。如果记录已存在则更新recognized_vehicle字段，不存在则创建新记录。

        Args:
            model_data: 包含车型信息的字典

        Returns:
            bool: 操作是否成功
        """
        local_id_str = 'local_id'
        recognized_vehicle_str = 'recognized_vehicle'
        required_fields = [
            local_id_str, 'charger_sn', 'mac_addr', 'capacity',
            'max_voltage', 'max_current', 'max_power', recognized_vehicle_str
        ]
        # 验证必填字段
        for field in required_fields:
            if field not in model_data:
                logger.info(f"Missing required field: {field}")
                return False

        try:
            with self.get_db() as db:
                # 查询是否存在记录
                existing_model = db.query(EVModelDB) \
                    .filter(and_(EVModelDB.local_id == model_data[local_id_str])) \
                    .first()

                if existing_model:
                    # 更新已存在记录的recognized_vehicle字段
                    existing_model.recognized_vehicle = model_data[recognized_vehicle_str]
                    existing_model.predict_status = EVPredictionStatus.TWICE_PREDICTED
                    logger.info(f"Updated recognized_vehicle for local_id: {model_data[local_id_str]}")
                else:
                    # 创建新记录
                    new_model = EVModelDB(
                        charger_sn=model_data['charger_sn'],
                        mac_addr=model_data['mac_addr'],
                        capacity=model_data['capacity'],
                        max_voltage=model_data['max_voltage'],
                        max_current=model_data['max_current'],
                        max_power=model_data['max_power'],
                        recognized_vehicle=model_data[recognized_vehicle_str],
                        local_id=model_data['local_id'],
                        predict_status=EVPredictionStatus.WAITING_TWICE_PREDICT
                    )
                    db.add(new_model)
                    logger.info(f"Created new EV model for local_id: {model_data[local_id_str]}")

                db.commit()
                return True

        except Exception as e:
            logger.error(f"Error in create_or_update_ev_model: {e}")
            return False

    def create_charger_session_if_not_exist(self, local_id: str, mac_addr: str, charger_sn: str,
                                            status: int, start_time: datetime) -> bool:
        """
        获取或创建充电会话记录。如果记录已存在则返回True，如果不存在则创建新记录。

        Args:
            local_id: 会话ID
            mac_addr: MAC地址
            charger_sn: 充电枪序列号
            status: 会话状态
            start_time: 开始时间

        Returns:
            bool: 操作是否成功
        """
        try:
            with self.get_db() as db:
                # 首先查询是否存在记录
                existing_session = db.query(ChargerSessionDB) \
                    .filter(ChargerSessionDB.local_id == local_id) \
                    .first()

                if existing_session:
                    logger.info(f"Charger session already exists for local_id: {local_id}")
                    return True

                # 不存在则创建新记录
                new_session = ChargerSessionDB(
                    local_id=local_id,
                    mac_addr=mac_addr,
                    charger_sn=charger_sn,
                    status=status,
                    start_time=start_time
                )

                db.add(new_session)
                db.commit()
                logger.info(f"Created new charger session for local_id: {local_id}")
            return True

        except Exception as e:
            logger.error(f"Error in get_or_create_charger_session: {e}")
            return False

    def query_charger_session(self, charger_sn: str, status: Union[int, List[int], Set[int]]) -> Optional[
        ChargerSessionDB]:
        """
        查询充电枪的充电会话

        Args:
            charger_sn: str, 充电枪编号
            status: 可以是单个状态值(int)，状态列表(List[int])或状态集合(Set[int])

        Returns:
            Optional[ChargerSessionDB]: 返回会话信息，如果没找到返回 None
        """
        try:
            with self.get_db() as db:
                query = db.query(ChargerSessionDB).filter(
                    ChargerSessionDB.charger_sn == charger_sn
                )

                # 根据 status 类型构建不同的查询条件
                if isinstance(status, (list, set)):
                    query = query.filter(ChargerSessionDB.status.in_(status))
                else:
                    query = query.filter(ChargerSessionDB.status == status)

                return query.order_by(desc(ChargerSessionDB.start_time)).first()

        except Exception as e:
            logger.error(f"查询充电会话失败: {str(e)}")
            return None

    def get_ev_model_by_local_id(self, local_id: str) -> Optional[EVModelDB]:
        """根据local_id获取车型信息"""
        try:
            with self.get_db() as db:
                return db.query(EVModelDB).filter(
                    and_(EVModelDB.local_id == local_id)
                ).first()
        except Exception as e:
            logger.error(f"Error getting EV model: {e}")
            return None

    def get_recent_charging_records(self, local_id: str, seconds: int = 120, limit: int = 12) -> List[ChargingRecordDB]:
        """获取最近的充电记录，确保第一条和最后一条记录的时间间隔至少为指定秒数，中间记录均匀分布
        
        Args:
            local_id: 会话ID
            seconds: 第一条和最后一条记录的最小时间间隔（秒）
            limit: 需要的记录数量
        """
        try:
            with self.get_db() as db:
                # 1. 获取最新的记录
                latest_stmt = select(ChargingRecordDB).where(
                    ChargingRecordDB.local_id == local_id
                ).order_by(ChargingRecordDB.report_at.desc()).limit(1)
                latest_record = db.scalar(latest_stmt)

                if not latest_record:
                    return []

                # 2. 获取至少seconds秒之前的记录
                earliest_time = latest_record.report_at - timedelta(seconds=seconds)
                earliest_stmt = select(ChargingRecordDB).where(
                    and_(
                        ChargingRecordDB.local_id == local_id,
                        ChargingRecordDB.report_at <= earliest_time
                    )
                ).order_by(ChargingRecordDB.report_at.desc()).limit(1)
                earliest_record = db.scalar(earliest_stmt)

                if not earliest_record:
                    # 如果没有足够早的记录，返回最新的记录
                    return [latest_record]

                # 3. 在这个时间范围内获取记录总数
                total_count = db.scalar(
                    select(func.count(ChargingRecordDB.id)).where(
                        and_(
                            ChargingRecordDB.local_id == local_id,
                            ChargingRecordDB.report_at.between(
                                earliest_record.report_at,
                                latest_record.report_at
                            )
                        )
                    )
                )

                # 4. 计算间隔并获取均匀分布的记录
                if total_count <= limit:
                    # 如果记录数小于等于需要的数量，返回所有记录
                    stmt = select(ChargingRecordDB).where(
                        and_(
                            ChargingRecordDB.local_id == local_id,
                            ChargingRecordDB.report_at.between(
                                earliest_record.report_at,
                                latest_record.report_at
                            )
                        )
                    ).order_by(ChargingRecordDB.report_at.desc())
                    return list(db.scalars(stmt).all())

                # 5. 均匀获取记录
                interval = total_count // (limit - 1)  # 减1是因为我们已经确定要包含最新和最早的记录
                records = [latest_record]  # 先添加最新的记录

                for i in range(1, limit - 1):  # limit-1是因为我们已经有了最新的记录，还要留一个位置给最早的记录
                    offset = i * interval
                    stmt = select(ChargingRecordDB).where(
                        and_(
                            ChargingRecordDB.local_id == local_id,
                            ChargingRecordDB.report_at.between(
                                earliest_record.report_at,
                                latest_record.report_at
                            )
                        )
                    ).order_by(ChargingRecordDB.report_at.desc()).offset(offset).limit(1)

                    record = db.scalar(stmt)
                    if record:
                        records.append(record)

                records.append(earliest_record)  # 最后添加最早的记录
                return records

        except Exception as e:
            logger.error(f"Error getting charging records: {e}")
            return []

    def get_start_soc_by_local_id(self, local_id: str) -> Optional[float]:
        """
        根据local_id获取充电记录的起始SOC
        
        Args:
            local_id: 充电会话ID
            
        Returns:
            float: 起始SOC值，如果没有找到记录则返回None
        """
        try:
            with self.get_db() as db:
                # 查询充电记录的起始SOC
                stmt = select(ChargingRecordDB.soc) \
                    .filter(and_(ChargingRecordDB.local_id == local_id)) \
                    .order_by(ChargingRecordDB.report_at.asc()) \
                    .limit(1)

                start_soc = db.scalar(stmt)

                if start_soc is not None:
                    logger.info(f"Found start SOC {start_soc} for local_id: {local_id}")
                    return float(start_soc)
                else:
                    logger.warning(f"No charging records found for local_id: {local_id}")
                    return None

        except Exception as e:
            logger.error(f"Error getting start SOC for local_id {local_id}: {e}")
            return None

    def get_ev_model_prediction_status_by_local_id(self, local_id: str) -> Optional[EVPredictionStatus]:
        """获取预测状态"""
        try:
            with self.get_db() as db:
                ev_model = db.query(EVModelDB) \
                    .filter(and_(EVModelDB.local_id == local_id)) \
                    .first()
                return ev_model.predict_status if ev_model else None
        except Exception as e:
            logger.error(f"Error getting prediction status for local_id {local_id}: {e}")
            return None

    def update_ev_model_prediction_status_by_local_id(self, local_id: str, status: EVPredictionStatus) -> bool:
        """更新预测状态"""
        try:
            with self.get_db() as db:
                ev_model = db.query(EVModelDB) \
                    .filter(and_(EVModelDB.local_id == local_id)) \
                    .first()
                if ev_model:
                    ev_model.predict_status = status
                    db.commit()
                    return True
                return False
        except Exception as e:
            logger.error(f"Error updating prediction status for local_id {local_id}: {e}")
            return False

    def get_recognized_vehicle_by_local_id(self, local_id):
        try:
            with self.get_db() as db:
                result = db.query(EVModelDB.recognized_vehicle).filter(EVModelDB.local_id == local_id).one()
            return result.recognized_vehicle
        except NoResultFound:
            logger.error(f"No result found for local_id: {local_id}")
            return None
        except MultipleResultsFound:
            logger.error(
                f"Multiple results found for local_id: {local_id}, this should not happen with a unique constraint.")
            return None
        except Exception as e:
            logger.error(f"An error occurred: {e}")
            return None

    def get_charging_start_time_by_local_id(self, local_id: str) -> Optional[datetime]:
        """
        获取充电会话的启动时间
        
        Args:
            local_id: 充电会话ID
            
        Returns:
            datetime: 充电启动时间，如果未找到则返回 None
        """
        try:
            with self.get_db() as db:
                stmt = select(ChargerSessionDB.start_time) \
                    .filter(and_(ChargerSessionDB.local_id == local_id))

                start_time = db.scalar(stmt)

                if start_time:
                    logger.info(f"Found charging start time for local_id: {local_id}")
                    return start_time
                else:
                    logger.warning(f"No charging session found for local_id: {local_id}")
                    return None

        except Exception as e:
            logger.error(f"Error getting charging start time for local_id {local_id}: {e}")
            return None

    def query_charge_records_count_by_local_id(self, local_id: str) -> int:
        """
        根据local_id查询充电记录数量

        Args:
            local_id: 充电会话ID

        Returns:
            int: 充电记录数量
        """
        try:
            with self.get_db() as db:
                count = db.scalar(
                    select(func.count(ChargingRecordDB.id)).where(
                        ChargingRecordDB.local_id == local_id
                    )
                )
                return count or 0

        except Exception as e:
            logger.error(f"Error querying charging records count for local_id {local_id}: {e}")
            return 0

    def query_charger_info(self, charger_sn: str) -> Optional[ChargerDB]:
        """获取充电器信息"""
        try:
            with self.get_db() as db:
                stmt = select(ChargerDB).where(ChargerDB.charger_sn == charger_sn)
                return db.scalar(stmt)
        except Exception as e:
            logger.error(f"Error getting charger info: {e}")
            return None

    def update_charger_info(self, charger_info: ChargerDB) -> bool:
        """更新充电器信息"""
        try:
            with self.get_db() as db:
                stmt = select(ChargerDB).where(
                    and_(ChargerDB.charger_sn == charger_info.charger_sn)
                )
                charger = db.scalar(stmt)
                if charger:
                    charger.max_power = charger_info.max_power
                    db.commit()
                    return True
                return False
        except Exception as e:
            logger.error(f"Error updating charger info: {e}")
            return False

    def query_charger_list_by_pile_sn(self, pile_sn: str) -> List[ChargerDB]:
        """获取充电桩下的充电枪列表"""
        try:
            with self.get_db() as db:
                stmt = select(ChargerDB).where(ChargerDB.pile_sn == pile_sn)
                return list(db.scalars(stmt).all())
        except Exception as e:
            logger.error(f"Error getting charger list by pile_sn: {e}")
            return []

    def query_module_list_by_pile_sn(self, pile_sn: str) -> List[ModuleDB]:
        """获取充电桩下的模块列表"""
        try:
            with self.get_db() as db:
                stmt = select(ModuleDB).where(ModuleDB.pile_sn == pile_sn)
                return list(db.scalars(stmt).all())
        except Exception as e:
            logger.error(f"Error getting module list by pile_sn: {e}")
            return []

    def update_charger_session_by_local_id(self, local_id: str, status: int, end_time: datetime) -> bool:
        """
        更新充电会话状态

        Args:
            local_id: str, 会话ID
            status: int, 新的会话状态
            end_time: datetime, 结束时间

        Returns:
            bool: 更新是否成功
        """
        try:
            with self.get_db() as db:
                stmt = select(ChargerSessionDB).where(
                    ChargerSessionDB.local_id == local_id
                )
                session = db.scalar(stmt)
                if session:
                    session.status = status
                    session.end_time = end_time
                    db.commit()
                    return True
                return False
        except Exception as e:
            logger.error(f"更新充电会话失败: {str(e)}")
            return False

    def create_or_update_pile(self, data):
        try:
            with self.get_db() as db:
                pile_db = db.query(PileDB).filter((PileDB.pile_sn == data['pile_sn'])).first()
                if pile_db:
                    pile_db.rated_power = data['rated_power']
                    pile_db.type = data['type']
                else:
                    pile_db = PileDB(
                        pile_sn=data['pile_sn'],
                        type=data['type'],
                        rated_power=data['rated_power'],
                        site_no=data['site_no']
                    )
                    db.add(pile_db)
                db.commit()
            return True
        except Exception as e:
            logger.error(f"Error in create_or_update_pile: {e}")
            return False

    def get_pile_rated_power(self, pile_sn: str) -> Optional[float]:
        """
        根据充电桩编号查询额定功率
        
        Args:
            pile_sn: str, 充电桩编号
            
        Returns:
            Optional[float]: 额定功率，如果未找到返回 None
        """
        try:
            with self.get_db() as db:
                stmt = select(PileDB.rated_power).where(
                    PileDB.pile_sn == pile_sn
                )
                return db.scalar(stmt)
        except Exception as e:
            logger.error(f"Error getting pile rated power for pile_sn {pile_sn}: {e}")
            return None

    def query_latest_charge_record_by_local_id(self, local_id: str) -> Optional[ChargingRecordDB]:
        """
        根据local_id获取最新的一条充电记录
        
        Args:
            local_id: str, 充电会话ID
            
        Returns:
            Optional[ChargingRecordDB]: 最新的充电记录，如果未找到返回 None
        """
        try:
            with self.get_db() as db:
                stmt = select(ChargingRecordDB).where(
                    ChargingRecordDB.local_id == local_id
                ).order_by(
                    desc(ChargingRecordDB.report_at)
                ).limit(1)

                return db.scalar(stmt)

        except Exception as e:
            logger.error(f"Error getting latest charging record for local_id {local_id}: {e}")
            return None

    def filter_charging_chargers(self, charge_list: List[ChargerDB]) -> List[ChargerDB]:
        """
        从充电枪列表中筛选出正在充电的枪
        
        Args:
            charge_list: List[Dict], 充电枪列表
            
        Returns:
            List[Dict]: 正在充电的充电枪列表
        """
        try:
            charging_list = []
            with self.get_db() as db:
                for charger in charge_list:
                    # 查询该充电枪是否有正在进行的充电会话
                    stmt = select(ChargerSessionDB).where(
                        and_(
                            ChargerSessionDB.charger_sn == charger.charger_sn,
                            ChargerSessionDB.status.in_([
                                ChargePileStatus.VEHICLE_CONNECTED,
                                ChargePileStatus.FORMAL_CHARGING
                            ])
                        )
                    ).order_by(desc(ChargerSessionDB.start_time)).limit(1)  # 只会有一条数据，一把枪处于充电状态的会话只有一个

                    if db.scalar(stmt):
                        charging_list.append(charger)

            return charging_list

        except Exception as e:
            logger.error(f"Error filtering charging chargers: {e}")
            return []

    def get_latest_3min_charging_records(self, local_id: int) -> List[ChargingRecordDB]:
        """
        获取指定充电枪最近3分钟的充电记录

        Args:
            local_id: 充电枪ID

        Returns:
            list: 最近3分钟的充电记录列表，按时间升序排序
        """
        with self.get_db() as db:
            # 获取最新记录的时间
            latest_record = db.query(ChargingRecordDB) \
                .filter(ChargingRecordDB.local_id == local_id) \
                .order_by(ChargingRecordDB.report_at.desc()) \
                .first()

            if not latest_record:
                return []

            end_time = latest_record.report_at
            start_time = end_time - timedelta(minutes=3)

            # 获取3分钟内的记录
            records = db.query(ChargingRecordDB).filter(
                ChargingRecordDB.local_id == local_id,
                ChargingRecordDB.report_at.between(start_time, end_time)
            ).order_by(ChargingRecordDB.report_at.asc()).all()

            return records

    def update_site_db(self, site_no: str, update_dict: Dict) -> tuple:
        """
        更新SiteDB（场站基础信息）。如果场站记录不存在，则不进行任何操作。
        
        Returns:
            tuple: (操作是否成功, 场站是否存在, AI是否开启)
                - 操作是否成功: bool, True表示数据库操作成功，False表示失败
                - 场站是否存在: bool, True表示场站记录存在，False表示不存在
                - AI是否开启: bool, True表示AI已开启，False表示未开启
        """
        try:
            with self.get_db() as db:
                site = db.query(SiteDB).filter(SiteDB.site_no == site_no).first()
                
                if site:
                    # 场站记录存在，获取当前AI状态
                    current_ai_active = bool(site.is_ai_active)
                    
                    # 如果AI未开启，不需要更新场站信息，直接返回
                    if not current_ai_active:
                        logger.info(f"场站 {site_no} AI未开启，跳过场站信息更新")
                        return True, True, False
                    
                    # AI已开启，更新场站信息
                    for k, v in update_dict.items():
                        if hasattr(site, k):
                            setattr(site, k, v)
                    site.updated_at = datetime.now(pytz.UTC)
                    
                    db.commit()
                    logger.info(f"SiteDB updated for site_no={site_no}, fields={list(update_dict.keys())}")
                    return True, True, True
                else:
                    # 场站记录不存在，不创建新记录，直接返回
                    logger.info(f"场站 {site_no} 记录不存在，跳过处理")
                    return True, False, False
                
        except Exception as e:
            logger.error(f"Error in update_site_db: {e}")
            return False, False, False

    def insert_pv_and_meter_realtime_data_db(self, site_no: str, data: Dict) -> bool:
        """
        插入光伏和电表实时数据
        
        Args:
            site_no: str, 场站编号
            data: Dict, 包含以下字段：
                ts: int, 采样点时间毫秒时间戳
                pv_power: int, 当前光伏发电功率，单位 kW
                pv_status: str, 光伏状态：discharge/non-discharge
                meter_value: int, 市电电表输入功率
                
        Returns:
            bool: 插入是否成功
        """
        try:
            with self.get_db() as db:
                record = PVAndMeterRealtimeDataDB(
                    site_no=site_no,
                    ts=data['ts'],
                    pv_power=data['pv_power'],
                    pv_status=data['pv_status'],
                    meter_value=data['meter_value']
                )
                db.add(record)
                db.commit()
                logger.info(f"Inserted PVAndMeterRealtimeData for site_no={site_no}")
                return True
        except Exception as e:
            logger.error(f"Error in insert_pv_and_meter_realtime_data_db: {e}")
            return False

    def insert_es_realtime_data_db(self, site_no: str, data: Dict) -> bool:
        """
        插入储能实时数据
        
        Args:
            site_no: str, 场站编号
            data: Dict, 包含以下字段：
                ts: int, 采样点时间毫秒时间戳
                es_sn: str, 储能设备序列号
                es_soc: int, 储能电池状态（SOC）
                es_power: int, 储能电池功率，单位 kW
                rated_cap: float, 额定容量 单位kwh
                real_cap: float, 实时容量 单位kwh
                es_max_soc: int, 最大SOC
                es_min_soc: int, 最小SOC
                status: str, 状态：discharge/charging/standby
                
        Returns:
            bool: 插入是否成功
        """
        try:
            with self.get_db() as db:
                record = ESRealtimeDataDB(
                    site_no=site_no,
                    ts=data['ts'],
                    es_sn=data['es_sn'],
                    es_soc=data['es_soc'],
                    es_power=data['es_power'],
                    rated_cap=data['rated_cap'],
                    real_cap=data['real_cap'],
                    es_max_soc=data['es_max_soc'],
                    es_min_soc=data['es_min_soc'],
                    status=data['status']
                )
                db.add(record)
                db.commit()
                logger.info(f"Inserted ESRealtimeData for site_no={site_no}, es_sn={data['es_sn']}")
                return True
        except Exception as e:
            logger.error(f"Error in insert_es_realtime_data_db: {e}")
            return False

    def batch_insert_charger_realtime_data_db(self, site_no: str, charger_data_list: List[Dict]) -> bool:
        """
        批量插入充电桩实时数据
        
        Args:
            site_no: str, 场站编号
            charger_data_list: List[Dict], 充电桩数据列表，每个字典包含以下字段：
                pile_sn: str, 充电桩序列号
                ts: int, 采样点时间毫秒时间戳
                connector: int, 充电枪接口编号
                status: str, 充电枪状态
                power: int, 充电枪功率
                ocpp_limit: int, 最大功率
                bus_power_limit: int, 桩总线功率限制
                pile_group_name: str, 桩组名称
                pile_group_power_limit: int, 桩组功率限制
                
        Returns:
            bool: 插入是否成功
        """
        if not charger_data_list:
            logger.info("No charger data to insert")
            return True
            
        try:
            with self.get_db() as db:
                # 准备批量插入的数据
                records = []
                for data in charger_data_list:
                    record = ChargerRealtimeDataDB(
                        site_no=site_no,
                        ts=data['ts'],
                        pile_sn=data['pile_sn'],
                        connector=data['connector'],
                        status=data['status'],
                        power=data['power'],
                        ocpp_limit=data['ocpp_limit'],
                        bus_power_limit=data['bus_power_limit'],
                        pile_group_name=data['pile_group_name'],
                        pile_group_power_limit=data['pile_group_power_limit']
                    )
                    records.append(record)
                
                # 使用bulk_save_objects进行批量插入
                db.bulk_save_objects(records)
                db.commit()
                logger.info(f"Batch inserted {len(records)} ChargerRealtimeData records for site_no={site_no}")
                return True
        except Exception as e:
            logger.error(f"Error in batch_insert_charger_realtime_data_db: {e}")
            return False

    def batch_insert_site_demand_data_db(self, site_no: str, demand_data_list: List[Dict]) -> bool:
        """
        批量插入场站需求电价数据
        
        Args:
            site_no: str, 场站编号
            demand_data_list: List[Dict], 需求电价数据列表，每个字典包含以下字段：
                version: str, 电价版本号，格式：YYYYMMDD_HHMMSS
                start_time: datetime, 生效开始时间
                end_time: datetime, 生效结束时间
                price: float, 电价
                unit: str, 电价单位
                total_demand_target: int, 总需求目标
                target_demand_warning_ratio: float, 目标需求警告比例
                
        Returns:
            bool: 插入是否成功
        """
        if not demand_data_list:
            logger.info("No demand data to insert")
            return True
            
        try:
            with self.get_db() as db:
                # 准备批量插入的数据
                records = []
                for data in demand_data_list:
                    record = SiteDemandDataDB(
                        site_no=site_no,
                        version=data['version'],
                        start_time=data['start_time'],
                        end_time=data['end_time'],
                        price=data['price'],
                        unit=data['unit'],
                        total_demand_target=data['total_demand_target'],
                        target_demand_warning_ratio=data['target_demand_warning_ratio']
                    )
                    records.append(record)
                
                # 使用bulk_save_objects进行批量插入
                db.bulk_save_objects(records)
                db.commit()
                logger.info(f"Batch inserted {len(records)} SiteDemandData records for site_no={site_no}")
                return True
        except Exception as e:
            logger.error(f"Error in batch_insert_site_demand_data_db: {e}")
            return False

    def batch_insert_site_sell_price_data_db(self, site_no: str, sell_price_data_list: List[Dict]) -> bool:
        """
        批量插入场站卖电电价数据
        
        Args:
            site_no: str, 场站编号
            sell_price_data_list: List[Dict], 卖电电价数据列表，每个字典包含以下字段：
                version: str, 电价版本号，格式：YYYYMMDD_HHMMSS
                start_time: datetime, 电价生效开始时间
                end_time: datetime, 电价生效结束时间
                price: float, 电价
                unit: str, 电价单位
                
        Returns:
            bool: 插入是否成功
        """
        if not sell_price_data_list:
            logger.info("No sell price data to insert")
            return True
            
        try:
            with self.get_db() as db:
                # 准备批量插入的数据
                records = []
                for data in sell_price_data_list:
                    record = SiteSellPriceDataDB(
                        site_no=site_no,
                        version=data['version'],
                        start_time=data['start_time'],
                        end_time=data['end_time'],
                        price=data['price'],
                        unit=data['unit']
                    )
                    records.append(record)
                
                # 使用bulk_save_objects进行批量插入
                db.bulk_save_objects(records)
                db.commit()
                logger.info(f"Batch inserted {len(records)} SiteSellPriceData records for site_no={site_no}")
                return True
        except Exception as e:
            logger.error(f"Error in batch_insert_site_sell_price_data_db: {e}")
            return False

    def get_site_info(self, site_no):
        """
        获取场站信息
        
        Args:
            site_no (str): 场站编号
            
        Returns:
            dict: 场站信息
        """
        try:
            with self.get_connection() as conn:
                with conn.cursor(dictionary=True) as cursor:
                    cursor.execute(
                        """
                        SELECT * FROM Site WHERE site_no = %s
                        """,
                        (site_no,)
                    )
                    return cursor.fetchone()
        except Exception as e:
            logger.error(f"Error getting site info for {site_no}: {str(e)}")
            return None

    def get_piles_by_site(self, site_no: str) -> List[str]:
        """
        获取场站下所有充电桩编号
        
        参数:
            site_no (str): 场站编号
            
        返回:
            List[str]: 充电桩编号列表
        """
        try:
            with self.get_db() as db:
                stmt = select(PileDB.pile_sn).where(PileDB.site_no == site_no)
                results = db.scalars(stmt).all()
                return [row for row in results] if results else []
        except Exception as e:
            logger.error(f"获取场站充电桩列表失败: {str(e)}")
            return []

    def save_pile_data(self, device_data: Dict, site_no: str) -> bool:
        """
        将桩数据保存到数据库
        参数:
            device_data (Dict): 桩数据，支持两种格式：
                格式1 (AC): 包含sn, chargeType, gunNum, ratedPower等字段
                格式2 (DC): 包含pile_sn, pile_type, gun_num, rated_power等字段
            site_no (str): 场站编号
        返回:
            bool: 保存是否成功
        """
        try:
            # 兼容新旧两种数据格式
            if 'pile_sn' in device_data:
                # 新格式 (来自交流桩工具类)
                pile_sn = device_data['pile_sn']
                pile_type = device_data['pile_type']
                gun_num = device_data['gun_num']
                rated_power = device_data['rated_power']
            else:
                # 旧格式 (来自运维接口)
                pile_sn = device_data['sn']
                pile_type = device_data['chargeType']
                gun_num = device_data['gunNum']
                rated_power = device_data['ratedPower']
            
            with self.get_db() as db:
                # 查询是否存在记录
                stmt = select(PileDB).where(PileDB.pile_sn == pile_sn)
                pile = db.scalar(stmt)
                
                if pile:
                    # 更新已存在记录
                    pile.pile_type = pile_type
                    pile.gun_num = gun_num
                    pile.rated_power = rated_power
                    logger.info(f"桩数据更新成功: {pile_sn}, 类型: {pile_type}, 额定功率: {rated_power}kW")
                else:
                    # 创建新记录
                    pile = PileDB(
                        pile_sn=pile_sn,
                        pile_type=pile_type,
                        gun_num=gun_num,
                        rated_power=rated_power,
                        site_no=site_no
                    )
                    db.add(pile)
                    logger.info(f"桩数据创建成功: {pile_sn}, 类型: {pile_type}, 额定功率: {rated_power}kW")
                
                db.commit()
                return True
        except Exception as e:
            logger.error(f"保存桩数据失败: {str(e)}")
            return False

    def update_or_create_charger(self, charger_sn: str, bom_index: int, max_curr: int = None) -> bool:
        """
        更新或创建充电枪记录。如果记录存在则更新，不存在则创建新记录。
        
        参数:
            charger_sn (str): 充电枪序列号，格式为 "pile_sn_gun_no"
            bom_index (int): 总线索引
            max_curr (int, optional): 最大电流值
            
        返回:
            bool: 操作是否成功
        """
        try:
            # 从charger_sn中解析出pile_sn和gun_no
            parts = charger_sn.split('_')
            if len(parts) != 2:
                logger.error(f"无效的充电枪序列号格式: {charger_sn}")
                return False
                
            pile_sn = parts[0]
            gun_no = int(parts[1])
            
            with self.get_db() as db:
                # 查询是否存在记录
                stmt = select(ChargerDB).where(ChargerDB.charger_sn == charger_sn)
                charger = db.scalar(stmt)
                
                if charger:
                    # 更新已存在记录
                    charger.bom_index = bom_index
                    if max_curr is not None:
                        charger.max_curr = max_curr
                    charger.pile_sn = pile_sn
                    charger.gun_no = gun_no
                else:
                    # 创建新记录
                    charger = ChargerDB(
                        charger_sn=charger_sn,
                        pile_sn=pile_sn,
                        gun_no=gun_no,
                        bom_index=bom_index,
                        max_curr=max_curr if max_curr is not None else 0
                    )
                    db.add(charger)
                
                db.commit()
                logger.info(f"充电枪数据更新成功: {charger_sn}, pile_sn={pile_sn}, gun_no={gun_no}")
                return True
                
        except Exception as e:
            logger.error(f"更新充电枪数据失败: {str(e)}")
            return False

    def delete_pile_module_info(self, pile_sn: str) -> bool:
        """
        删除指定充电桩的所有模块信息
        
        参数:
            pile_sn (str): 充电桩序列号
            
        返回:
            bool: 删除是否成功
        """
        try:
            with self.get_db() as db:
                # 使用delete语句批量删除
                stmt = delete(ModuleDB).where(ModuleDB.pile_sn == pile_sn)
                result = db.execute(stmt)
                db.commit()
                logger.info(f"删除充电桩 {pile_sn} 的模块信息成功，删除记录数: {result.rowcount}")
                return True
        except Exception as e:
            logger.error(f"删除充电桩 {pile_sn} 的模块信息失败: {str(e)}")
            return False

    def insert_pile_module_info(self, module_list: List[Dict]) -> bool:
        """
        批量插入模块信息
        
        参数:
            module_list (List[Dict]): 模块信息列表，每个字典包含以下字段：
                pile_sn: str, 充电桩序列号
                type: str, 模块类型
                module_no: int, 模块编号
                unit_power: int, 单元功率
                unit_num: int, 单元数量
                
        返回:
            bool: 插入是否成功
        """
        try:
            with self.get_db() as db:
                # 准备批量插入的数据
                modules = [
                    ModuleDB(
                        pile_sn=module['pile_sn'],
                        type=module.get('type', 'unknown'),
                        module_no=module['module_no'],
                        unit_power=module['unit_power'],
                        unit_num=module['unit_num']
                    )
                    for module in module_list
                ]
                
                # 使用bulk_save_objects进行批量插入
                db.bulk_save_objects(modules)
                db.commit()
                logger.info(f"批量插入模块信息成功，插入记录数: {len(modules)}")
                return True
                
        except Exception as e:
            logger.error(f"批量插入模块信息失败: {str(e)}")
            return False

    def check_idempotency(self, biz_seq: str, site_no: str, status: str) -> bool:
        """
        检查请求是否重复（幂等性检查）
        
        Args:
            biz_seq: str, 业务序列号
            site_no: str, 场站编号
            status: str, 操作状态 ("start"/"stop")
            
        Returns:
            bool: True表示已处理过，False表示未处理过
        """
        try:
            with self.get_db() as db:
                # 查询场站记录
                stmt = select(SiteDB).where(SiteDB.site_no == site_no)
                site = db.scalar(stmt)
                
                if not site:
                    # 场站不存在，表示未处理过
                    logger.info(f"场站 {site_no} 不存在，需要处理")
                    return False
                
                # 检查当前状态是否与请求状态一致
                current_status = "start" if site.is_ai_active == 1 else "stop"
                if current_status == status:
                    logger.info(f"场站 {site_no} 当前状态与请求状态一致: {status}")
                    return True
                
                # 状态不一致，需要处理
                logger.info(f"场站 {site_no} 当前状态与请求状态不一致，需要处理")
                return False
                
        except Exception as e:
            logger.error(f"幂等性检查失败: {str(e)}")
            return False

    def update_site_status(self, site_no: str, status: str) -> bool:
        """
        更新场站AI状态
        
        Args:
            site_no: str, 场站编号
            status: str, 操作状态 ("start"/"stop")
            
        Returns:
            bool: True表示更新成功，False表示更新失败
        """
        try:
            with self.get_db() as db:
                # 查询场站记录
                stmt = select(SiteDB).where(SiteDB.site_no == site_no)
                site = db.scalar(stmt)
                
                if not site:
                    # 场站不存在，创建新记录
                    site = SiteDB(
                        site_no=site_no,
                        is_ai_active=1 if status == "start" else 0
                    )
                    db.add(site)
                    logger.info(f"创建新场站记录: {site_no}, AI状态: {status}")
                else:
                    # 更新现有记录
                    site.is_ai_active = 1 if status == "start" else 0
                    logger.info(f"更新场站 {site_no} AI状态为: {status}")
                
                db.commit()
                return True
                
        except Exception as e:
            logger.error(f"更新场站状态失败: {str(e)}")
            return False

    def get_active_ai_sites(self) -> list:
        """
        获取所有AI已开启的场站编号（site_no）
        Returns:
            list: 所有is_ai_active=1的site_no
        """
        try:
            with self.get_db() as db:
                stmt = select(SiteDB.site_no).where(SiteDB.is_ai_active == 1)
                result = db.scalars(stmt).all()
                return list(result) if result else []
        except Exception as e:
            logger.error(f"获取AI已开启场站失败: {str(e)}")
            return []

    def get_piles_by_sites(self, site_no_list: list) -> list:
        """
        获取指定场站下所有桩号
        Args:
            site_no_list (list): 场站编号列表
        Returns:
            list: 所有桩号pile_sn
        """
        if not site_no_list:
            return []
        try:
            with self.get_db() as db:
                stmt = select(PileDB.pile_sn).where(PileDB.site_no.in_(site_no_list))
                result = db.scalars(stmt).all()
                return list(result) if result else []
        except Exception as e:
            logger.error(f"获取场站桩列表失败: {str(e)}")
            return []

    def save_site_electricity_price(self, region, date, datas):
        logger.info(f'save {region} {date} electricity price')
        try:
            with self.get_db() as db:
                s = select(SiteDynamicPriceDataDB).where(
                    and_(
                        SiteDynamicPriceDataDB.region == region,
                        SiteDynamicPriceDataDB.date == date
                    )
                )
                r = db.scalar(s)
                if r:
                    logger.info(f'electricity price data has been written into the db, region:{region}, date:{date}')
                    return
                for data in datas:
                    logger.info(data)
                    site_buy_price = SiteDynamicPriceDataDB(
                        region=region,
                        date=date,
                        start_time=data['start_time'],
                        end_time=data['end_time'],
                        price=data['price'],
                        unit=data['unit']
                    )
                    db.add(site_buy_price)

                db.commit()
            logger.info(f'save {region} {date} end.')
        except Exception as e:
            logger.error(f"save electricity price failed.{str(e)}")

    def get_region_by_site_no(self, site_no):
        try:
            with self.get_db() as db:
                s = db.query(SiteDB).filter(SiteDB.site_no==site_no).first()
                return s.region if s else None
        except Exception as e:
            logger.error(f"get region failed.{str(e)}")
            return

    def insert_site_fixed_and_time_of_use_price_data_db(self, site_no: str, data_list: List[Dict]) -> bool:
        """
        插入场站固定电价和分时电价数据

        Args:
            site_no: str, 场站编号
            data_list: List[Dict], 电价数据列表，每个字典包含以下字段：
                belong_year: int, 所属年份
                belong_month: int, 所属月份
                start_time: str, 时段划分开始时间，HH:mm格式
                end_time: str, 时段划分结束时间，HH:mm格式
                time_type: int, 时段类型 1:尖, 2:峰, 3:平, 4:谷
                price: float, 买电单价
                unit: str, 电价单位

        Returns:
            bool: 插入是否成功
        """
        try:
            with self.get_db() as db:
                # 按年份和月份分组处理数据
                year_month_groups = {}
                for data in data_list:
                    key = (data['belong_year'], data['belong_month'])
                    if key not in year_month_groups:
                        year_month_groups[key] = []
                    year_month_groups[key].append(data)

                # 对每个年份月份组合进行处理
                for (year, month), group_data in year_month_groups.items():
                    # 先删除已存在的同年同月记录
                    stmt = delete(SiteFixedAndTimeOfUsePriceDataDB).where(
                        and_(
                            SiteFixedAndTimeOfUsePriceDataDB.site_no == site_no,
                            SiteFixedAndTimeOfUsePriceDataDB.belong_year == year,
                            SiteFixedAndTimeOfUsePriceDataDB.belong_month == month
                        )
                    )
                    result = db.execute(stmt)
                    logger.info(f"删除场站 {site_no} {year}年{month}月的旧电价数据，删除记录数: {result.rowcount}")

                    # 插入新的记录
                    for data in group_data:
                        record = SiteFixedAndTimeOfUsePriceDataDB(
                            site_no=site_no,
                            belong_year=data['belong_year'],
                            belong_month=data['belong_month'],
                            start_time=data['start_time'],
                            end_time=data['end_time'],
                            time_type=data['time_type'],
                            price=data['price'],
                            unit=data['unit']
                        )
                        db.add(record)

                    logger.info(f"插入场站 {site_no} {year}年{month}月的新电价数据，插入记录数: {len(group_data)}")

                db.commit()
                logger.info(f"场站 {site_no} 固定电价和分时电价数据处理完成")
                return True

        except Exception as e:
            logger.error(f"插入场站固定电价和分时电价数据失败: {str(e)}")
            return False

    def update_charger_session_status(self, charger_sn: str, status: int) -> bool:
        """
        更新充电会话状态
        
        Args:
            charger_sn: str, 充电枪序列号
            status: int, 新的会话状态
            
        Returns:
            bool: 更新是否成功
        """
        try:
            with self.get_db() as db:
                # 查询该充电枪最新的未完成充电会话记录
                stmt = select(ChargerSessionDB).where(
                    and_(
                        ChargerSessionDB.charger_sn == charger_sn,
                        ChargerSessionDB.status != ChargePileStatus.CHARGING_COMPLETED
                    )
                ).order_by(
                    desc(ChargerSessionDB.start_time)
                ).limit(1)
                
                session = db.scalar(stmt)
                
                if not session:
                    logger.info(f"充电枪 {charger_sn} 没有找到未完成的充电会话记录，无需更新")
                    return False
                
                # 更新状态
                session.status = status
                if status == ChargePileStatus.CHARGING_COMPLETED:
                    # 如果状态为充电完成且没有提供end_time，自动设置为当前UTC时间
                    session.end_time = datetime.now(pytz.UTC)
                
                db.commit()
                logger.info(f"更新充电会话状态成功: charger_sn={charger_sn}, local_id={session.local_id}, status={status}")
                return True
                
        except Exception as e:
            logger.error(f"更新充电会话状态失败: {str(e)}")
            return False

    def get_site_no_by_charger_sn(self, charger_sn: str) -> Optional[str]:
        """
        根据充电枪序列号获取场站编号
        
        Args:
            charger_sn: str, 充电枪序列号，格式为 "pile_sn_bom_index"
            例如: "DE7480B2GRCC00008B_1"
            
        Returns:
            Optional[str]: 场站编号，如果未找到返回None
        """
        try:
            # 从charger_sn中解析出pile_sn
            # charger_sn格式: "pile_sn_bom_index"，例如: "DE7480B2GRCC00008B_1"
            parts = charger_sn.split('_')
            if len(parts) != 2:
                logger.warning(f"充电枪序列号格式错误: {charger_sn}")
                return None
            
            pile_sn = parts[0]
            bom_index = parts[1]
            
            if not pile_sn or not bom_index:
                logger.warning(f"充电枪序列号格式错误: {charger_sn}")
                return None
            
            with self.get_db() as db:
                # 直接通过充电桩表查询场站编号
                stmt = select(PileDB.site_no).where(
                    PileDB.pile_sn == pile_sn
                )
                site_no = db.scalar(stmt)
                
                if site_no:
                    logger.info(f"获取场站编号成功: charger_sn={charger_sn}, pile_sn={pile_sn}, site_no={site_no}")
                    return site_no
                else:
                    logger.warning(f"充电桩不存在或未关联场站: {pile_sn}")
                    return None
                
        except Exception as e:
            logger.error(f"获取场站编号失败: {str(e)}")
            return None

    def save_non_suppress_power_prediction(self, local_id: str, prediction_data: Dict) -> bool:
        """
        保存非压制功率预测结果到数据库
        
        Args:
            local_id: str, 充电会话ID
            prediction_data: Dict, 预测数据，包含以下字段：
                curve_start_time: datetime, 预测开始时间
                curve_end_time: datetime, 预计充电结束时间
                time_list: List[int], 相对时间（秒）
                power_list: List[float], 功率值（kW）
                
        Returns:
            bool: 保存是否成功
        """
        try:
            with self.get_db() as db:
                # 检查是否已存在记录
                existing_record = db.scalar(
                    select(ChargerNonSuppressPowerPredictionDB).where(
                        ChargerNonSuppressPowerPredictionDB.local_id == local_id
                    )
                )

                time_list_json = json.dumps(prediction_data["time_list"])
                power_list_json = json.dumps(prediction_data["power_list"])

                if existing_record:
                    # 更新已存在的记录
                    existing_record.curve_start_time = prediction_data["curve_start_time"]
                    existing_record.curve_end_time = prediction_data["curve_end_time"]
                    existing_record.time_list = time_list_json
                    existing_record.power_list = power_list_json
                    existing_record.updated_at = datetime.now(timezone.utc)

                    logger.info(f"Updated non-suppress power prediction for local_id: {local_id}")
                else:
                    # 创建新记录
                    new_record = ChargerNonSuppressPowerPredictionDB(
                        local_id=local_id,
                        curve_start_time=prediction_data["curve_start_time"],
                        curve_end_time=prediction_data["curve_end_time"],
                        time_list=time_list_json,
                        power_list=power_list_json
                    )
                    db.add(new_record)

                    logger.info(f"Created new non-suppress power prediction for local_id: {local_id}")

                db.commit()
                return True

        except Exception as e:
            logger.error(f"Error saving non-suppress power prediction to database for local_id {local_id}: {e}")
            return False

    def get_charging_sessions_by_site(self, site_no: str) -> List[ChargerSessionDB]:
        """
        获取场站内所有正在充电的会话
        
        Args:
            site_no: str, 场站编号
            
        Returns:
            List[ChargerSessionDB]: 正在充电的会话列表
        """
        try:
            with self.get_db() as db:
                # 查询状态不为CHARGING_COMPLETED的充电会话
                stmt = select(ChargerSessionDB).where(
                    and_(
                        ChargerSessionDB.site_no == site_no,
                        ChargerSessionDB.status != ChargePileStatus.CHARGING_COMPLETED
                    )
                )
                charging_sessions = list(db.scalars(stmt).all())

                logger.info(f"Found {len(charging_sessions)} charging sessions in site {site_no}")
                return charging_sessions

        except Exception as e:
            logger.error(f"Error getting charging sessions for site {site_no}: {e}")
            return []

    def save_site_demand_prediction(self, site_no: str, aggregated_data: Dict) -> bool:
        """
        将场站总需求功率预测曲线保存到SiteDemandPrediction表
        
        Args:
            site_no: str, 场站编号
            aggregated_data: Dict, 叠加后的场站需求功率曲线数据，包含以下字段：
                curve_start_time: datetime, 曲线开始时间
                curve_end_time: datetime, 曲线结束时间
                time_list: List[int], 统一时间轴（相对秒数）
                power_list: List[float], 叠加后的总功率值（kW）
                
        Returns:
            bool: 保存是否成功
        """
        try:
            with self.get_db() as db:
                # 检查是否已存在记录（按site_no）
                existing_record = db.scalar(
                    select(SiteDemandPredictionDB).where(
                        SiteDemandPredictionDB.site_no == site_no
                    )
                )

                time_list_json = json.dumps(aggregated_data["time_list"])
                power_list_json = json.dumps(aggregated_data["power_list"])

                if existing_record:
                    # 更新已存在的记录
                    existing_record.curve_start_time = aggregated_data["curve_start_time"]
                    existing_record.curve_end_time = aggregated_data["curve_end_time"]
                    existing_record.time_list = time_list_json
                    existing_record.power_list = power_list_json
                    existing_record.updated_at = datetime.now(timezone.utc)

                    logger.info(f"Updated site demand prediction for site_no: {site_no}")
                else:
                    # 创建新记录
                    new_record = SiteDemandPredictionDB(
                        site_no=site_no,
                        curve_start_time=aggregated_data["curve_start_time"],
                        curve_end_time=aggregated_data["curve_end_time"],
                        time_list=time_list_json,
                        power_list=power_list_json
                    )
                    db.add(new_record)

                    logger.info(f"Created new site demand prediction for site_no: {site_no}")

                db.commit()
                return True

        except Exception as e:
            logger.error(f"Error saving site demand prediction for site_no {site_no}: {e}")
            return False

    def get_site_demand_data(self, site_no):
        """
        获取场站当前时间段的demand数据（最新版本中时间匹配的记录）
        
        Args:
            site_no: str, 场站编号
            
        Returns:
            list: 包含一条当前时间段demand数据的列表，如果没有数据则返回空列表
        """
        try:
            current_time = datetime.now(timezone.utc)
            
            with self.get_db() as db:
                # 获取场站信息用于时区转换
                site_stmt = select(SiteDB).where(SiteDB.site_no == site_no)
                site_info = db.scalar(site_stmt)
                
                if site_info and site_info.region:
                    # 使用工具方法转换时区
                    local_time = convert_utc_to_local_time(current_time, site_info.region, site_no)
                    logger.info(f'local_time:{local_time}')
                    if local_time is None:
                        logger.error(f"场站 {site_no} 时区转换失败，无法查询demand数据")
                        return []
                    
                    query_time = local_time
                else:
                    # 如果没有找到region信息，使用UTC时间
                    query_time = current_time
                    logger.warning(f"场站 {site_no} 没有region信息，使用UTC时间查询")
                
                # 查询最新版本中当前时间范围内的demand数据
                sdd = db.scalar(
                    select(SiteDemandDataDB).where(
                        SiteDemandDataDB.site_no == site_no,
                        SiteDemandDataDB.start_time <= query_time,
                        SiteDemandDataDB.end_time >= query_time
                    ).order_by(SiteDemandDataDB.version.desc()).limit(1)
                )
                
                if sdd:
                    logger.debug(f"场站 {site_no} 在时间 {query_time} 获取到demand数据（版本 {sdd.version}）")
                    data = [
                        {
                            "start_time": sdd.start_time.strftime("%Y-%m-%d %H:%M:%S"),
                            "end_time": sdd.end_time.strftime("%Y-%m-%d %H:%M:%S"),
                            "price": float(sdd.price),
                            "unit": sdd.unit,
                            "total_demand_target": sdd.total_demand_target,
                            "target_demand_warning_ratio": float(sdd.target_demand_warning_ratio)
                        }
                    ]
                    return data
                else:
                    logger.warning(f"场站 {site_no} 在时间 {query_time} 没有找到demand数据")
                    return []
                    
        except Exception as e:
            logger.error(f"Error getting site demand data for site_no {site_no}: {e}")
            return []
    
    def get_current_demand_limit(self, site_no, current_time=None, site_info=None):
        """
        根据当前时间获取对应的demand_limit，支持根据场站region自动获取时区
        
        Args:
            site_no: str, 场站编号
            current_time: datetime, 当前时间，如果为None则使用当前UTC时间
            site_info: SiteDB, 场站信息对象，如果提供则不会重新查询数据库
            
        Returns:
            int or None: 当前时间对应的total_demand_target，如果没有找到则返回None（不进行功率分配）
        """
        try:
            if current_time is None:
                current_time = datetime.now(timezone.utc)
            
            with self.get_db() as db:
                # 如果没有传递site_info，则查询数据库获取
                if site_info is None:
                    site_stmt = select(SiteDB).where(SiteDB.site_no == site_no)
                    site_info = db.scalar(site_stmt)
                
                if site_info and site_info.region:
                    # 使用工具方法转换时区
                    local_time = convert_utc_to_local_time(current_time, site_info.region, site_no)
                    if local_time is None:
                        logger.error(f"场站 {site_no} 时区转换失败，无法查询需求限制")
                        return None
                    
                    # 使用当地时间查询demand数据
                    query_time = local_time
                else:
                    # 如果没有找到region信息，使用UTC时间
                    query_time = current_time
                    logger.warning(f"场站 {site_no} 没有region信息，使用UTC时间查询")
                
                # 查询当前时间范围内的demand数据，只取最新版本
                stmt = select(SiteDemandDataDB).where(
                    SiteDemandDataDB.site_no == site_no,
                    SiteDemandDataDB.start_time <= query_time,
                    SiteDemandDataDB.end_time >= query_time
                ).order_by(SiteDemandDataDB.version.desc()).limit(1)
                
                sdd = db.scalar(stmt)
                
                if sdd:
                    logger.debug(f"场站 {site_no} 在时间 {query_time} 找到demand数据（最新版本 {sdd.version}），total_demand_target={sdd.total_demand_target}")
                    return sdd.total_demand_target
                else:
                    # 如果没有找到对应时间的数据，返回None，不进行功率分配
                    logger.warning(f"场站 {site_no} 在时间 {query_time} 未找到demand数据，将跳过功率分配")
                    return None
                    
        except Exception as e:
            logger.error(f"Error getting current demand limit for site_no {site_no}: {e}")
            return None  # 返回None，不进行功率分配

    def get_es_realtime_data(self, site_no):
        """
        获取场站储能的最新实时数据
        
        Args:
            site_no: str, 场站编号
            
        Returns:
            ESRealtimeDataDB or None: 最新的储能实时数据记录
        """
        try:
            with self.get_db() as db:
                es = db.scalar(
                    select(ESRealtimeDataDB).where(
                        ESRealtimeDataDB.site_no == site_no
                    ).order_by(ESRealtimeDataDB.ts.desc()).limit(1)
                )
                if es:
                    logger.debug(f"场站 {site_no} 获取到最新储能数据，时间戳={es.ts}, SOC={es.es_soc}%")
                    return es
                else:
                    logger.warning(f"场站 {site_no} 没有找到储能实时数据")
                    return None
        except Exception as e:
            logger.error(f"Error getting es realtime data for site_no {site_no}: {e}")
            return None

    def get_price_by_site_no_and_date(self, site_no, date):
        try:
            with self.get_db() as db:
                stmt = (
                    select(SiteDynamicPriceDataDB.price)
                    .join(SiteDB, SiteDB.region == SiteDynamicPriceDataDB.region)
                    .where(
                        and_(
                            SiteDB.site_no == site_no,
                            SiteDynamicPriceDataDB.date == date
                        )
                    )
                    .order_by(SiteDynamicPriceDataDB.id.asc())
                )
                return [float(price) for price in db.scalars(stmt).all()]
        except Exception as e:
            logger.error(f"get price list failed, site_no {site_no}: {e}")
            return []

    def get_pv_predicted_list(self, site_no):
        try:
            with self.get_db() as db:
                pvp = db.query(PVCurvePredictionDB).filter(
                    and_(PVCurvePredictionDB.site_no == site_no)
                ).first()
                if pvp:
                    return json.loads(pvp.power_list)
                else:
                    return []
        except Exception as e:
            logger.error('get pv predicted list failed.')
            return []

    def get_hybrid_load_predicted(self, site_no):
        with self.get_db() as db:
            hlp = db.query(HybridLoadPredictionDB).filter(
                and_(HybridLoadPredictionDB.site_no == site_no)
            ).first()
            if hlp:
                return hlp

    def get_site(self, site_no):
        try:
            with self.get_db() as db:
                site = db.query(SiteDB).filter(SiteDB.site_no == site_no).first()
                return site if site else None
        except Exception as e:
            logger.error(f"get site failed.{str(e)}")
            return

    def get_pile(self, site_no):
        try:
            with self.get_db() as db:
                pile = db.query(PileDB).filter(PileDB.site_no == site_no).first()
                return pile if pile else None
        except Exception as e:
            logger.error(f"get pile failed.{str(e)}")
            return

    def save_energy_storage_task(self, site_no, result):
        try:
            with self.get_db() as db:
                record = EnergyStorageTaskDB(
                    site_no=site_no,
                    scheduling_time=result['scheduling_time'],
                    es_scheduling_strategy=result['es_scheduling_strategy'],
                    scheduling_time_interval=15
                )
                db.add(record)
                db.commit()
        except Exception as e:
            logger.error(f"save energy storage task failed.{str(e)}")
            return
        
    def get_last_n_seconds_charging_records(self, local_id: str, seconds: int = 60) -> List[ChargingRecordDB]:
        """获取最近n秒的充电记录
        
        Args:
            local_id: 会话ID
            seconds: 从最后一条充电记录向前取充电记录的时间间隔（秒）
        """
        try:
            with self.get_db() as db:
                # 1. 获取最新的记录
                latest_stmt = select(ChargingRecordDB).where(
                    ChargingRecordDB.local_id == local_id
                ).order_by(ChargingRecordDB.report_at.desc()).limit(1)
                latest_record = db.scalar(latest_stmt)

                if not latest_record:
                    return []

                # 2. 计算时间范围的起始点
                earliest_time = latest_record.report_at - timedelta(seconds=seconds)

                # 3. 检查是否存在足够早的记录（至少seconds秒之前）
                earliest_check_stmt = select(ChargingRecordDB).where(
                    and_(
                        ChargingRecordDB.local_id == local_id,
                        ChargingRecordDB.report_at <= earliest_time
                    )
                ).order_by(ChargingRecordDB.report_at.desc()).limit(1)
                earliest_record = db.scalar(earliest_check_stmt)

                if not earliest_record:
                    # 如果没有足够早的记录，返回空列表
                    logger.info(f"The current charging records time span is less than {seconds} seconds, local_id: {local_id}")
                    return []

                # 4. 获取时间范围内的所有记录，按时间正序排序
                stmt = select(ChargingRecordDB).where(
                    and_(
                        ChargingRecordDB.local_id == local_id,
                        ChargingRecordDB.report_at.between(earliest_record.report_at, latest_record.report_at)
                    )
                ).order_by(ChargingRecordDB.report_at.asc())  # 按时间正序排序
                
                return list(db.scalars(stmt).all())

        except Exception as e:
            logger.error(f"获取充电记录时发生错误: {str(e)}, local_id: {local_id}")
            return []
        
    def query_pile_sn_by_charger_sn(self, charger_sn: str) -> Optional[str]:
        try:
            with self.get_db() as db:
                charger_data = db.query(ChargerDB).filter(ChargerDB.charger_sn == charger_sn).first()
                return charger_data.pile_sn if charger_data else None
        except Exception as e:
            logger.error(f"获取充电桩序列号时发生错误: {str(e)}, charger_sn: {charger_sn}")
            return None
        
    def query_charger_sn_by_local_id(self, local_id: str) -> Optional[str]:
        try:
            with self.get_db() as db:
                charger_session = db.query(ChargerSessionDB).filter(ChargerSessionDB.local_id == local_id).first()
                return charger_session.charger_sn if charger_session else None
        except Exception as e:
            logger.error(f"获取充电桩序列号时发生错误: {str(e)}, local_id: {local_id}")
            return None
        
    def get_non_suppress_rated_power_of_pile(self, local_id: str) -> Optional[float]:
        """获取充电桩的非压制功率
        
        Args:
            local_id: 会话ID
        """
        try:
            # 1. 获取charger_sn
            charger_sn = self.query_charger_sn_by_local_id(local_id)
            if not charger_sn:
                return None
        
            # 2. 获取对应的pile_sn
            pile_sn = self.query_pile_sn_by_charger_sn(charger_sn)
            if not pile_sn:
                return None
            
            # 3. 获取桩对应的所有模块
            modules = self.query_module_list_by_pile_sn(pile_sn)
            if not modules:
                return None
            
            # 4. 计算非压制下的总额定功率
            total_rated_power = 0
            for module in modules:
                total_rated_power += module.unit_power * module.unit_num
            return float(total_rated_power)
            
        except Exception as e:
            logger.error(f"获取充电桩的非压制功率时发生错误: {str(e)}, local_id: {local_id}")
            return None
        
    def get_max_curr_of_charger(self, local_id: str) -> Optional[float]:
        """获取充电桩的最大电流
        
        Args:
            local_id: 会话ID
        """
        try:
            # 1. 获取charger_sn
            charger_sn = self.query_charger_sn_by_local_id(local_id)
            if not charger_sn:
                return None
            
            # 2. 获取枪线最大电流
            with self.get_db() as db:
                charger_data = db.query(ChargerDB).filter(ChargerDB.charger_sn == charger_sn).first()
                return charger_data.max_curr if charger_data else None    # 单位：A
        
        except Exception as e:
            logger.error(f"获取充电桩的最大电流时发生错误: {str(e)}, local_id: {local_id}")
            return None

    def get_future_24h_sell_price_from_db(self, site_no: str) -> List[Dict]:
        try:
            with self.get_db() as db:
                # 获取当前时间和日期
                current_time = datetime.now()
                today = current_time.date()
                yesterday = today - timedelta(days=1)
                tomorrow = today + timedelta(days=1)

                # 查询三天的电价数据
                price_data = {"yesterday": [], "today": [], "tomorrow": []}

                for day_key, target_date in [("yesterday", yesterday), ("today", today), ("tomorrow", tomorrow)]:
                    try:
                        # 查询指定日期的最新版本号
                        latest_version_stmt = select(func.max(SiteSellPriceDataDB.version)).where(
                            and_(
                                SiteSellPriceDataDB.site_no == site_no,
                                func.date(SiteSellPriceDataDB.start_time) == target_date
                            )
                        )
                        latest_version = db.scalar(latest_version_stmt)

                        if latest_version:
                            # 获取该版本的所有电价数据
                            price_data_stmt = select(SiteSellPriceDataDB).where(
                                and_(
                                    SiteSellPriceDataDB.site_no == site_no,
                                    SiteSellPriceDataDB.version == latest_version,
                                    func.date(SiteSellPriceDataDB.start_time) == target_date
                                )
                            ).order_by(SiteSellPriceDataDB.start_time.asc())

                            price_records = db.scalars(price_data_stmt).all()

                            price_data[day_key] = [
                                {
                                    "start_time": record.start_time.strftime("%Y-%m-%d %H:%M:%S"),
                                    "end_time": record.end_time.strftime("%Y-%m-%d %H:%M:%S"),
                                    "price": float(record.price),
                                    "unit": record.unit,
                                    "version": record.version
                                }
                                for record in price_records
                            ]

                            logger.info(
                                f"场站 {site_no} {day_key} 获取到 {len(price_data[day_key])} 条电价数据，版本: {latest_version}")
                        else:
                            logger.warning(f"场站 {site_no} {day_key} ({target_date}) 没有找到电价数据")

                    except Exception as e:
                        logger.error(f"查询场站 {site_no} {day_key} 电价数据失败: {e}")
                        price_data[day_key] = []

                # 生成未来24小时的电价数据
                result = []

                for hour_offset in range(24):
                    hour_start = current_time + timedelta(hours=hour_offset)
                    hour_end = hour_start + timedelta(hours=1)

                    # 格式化为字符串
                    hour_start_str = hour_start.strftime("%Y-%m-%d %H:%M:%S")
                    hour_end_str = hour_end.strftime("%Y-%m-%d %H:%M:%S")

                    # 确定使用哪一天的数据
                    target_date = hour_start.date()

                    if target_date == today:
                        primary_list = price_data["today"]
                        fallback_list = price_data["yesterday"]
                    elif target_date == tomorrow:
                        primary_list = price_data["tomorrow"]
                        fallback_list = price_data["today"]
                    else:
                        # 其他情况（理论上不会发生）
                        primary_list = price_data["today"]
                        fallback_list = price_data["yesterday"]

                    # 查找匹配的电价
                    found_price = None

                    # 首先在主要数据源中查找
                    for item in primary_list:
                        item_start = datetime.strptime(item['start_time'], "%Y-%m-%d %H:%M:%S")
                        item_end = datetime.strptime(item['end_time'], "%Y-%m-%d %H:%M:%S")
                        if item_start <= hour_start < item_end:
                            found_price = item
                            break

                    # 如果没找到，在备用数据源中查找（使用相同的时间点）
                    if not found_price and fallback_list:
                        target_time_str = hour_start.strftime("%H:%M:%S")
                        for item in fallback_list:
                            item_start_time = datetime.strptime(item['start_time'], "%Y-%m-%d %H:%M:%S").strftime(
                                "%H:%M:%S")
                            item_end_time = datetime.strptime(item['end_time'], "%Y-%m-%d %H:%M:%S").strftime(
                                "%H:%M:%S")

                            # 处理跨天的情况
                            if item_end_time == "23:59:59":
                                item_end_time = "24:00:00"

                            if item_start_time <= target_time_str < item_end_time:
                                found_price = item
                                break

                    if found_price:
                        result.append({
                            "start_time": hour_start_str,
                            "end_time": hour_end_str,
                            "price": found_price['price'],
                            "unit": found_price['unit']
                        })
                    else:
                        logger.warning(f"场站 {site_no} 在时间 {hour_start_str} 没有找到电价数据")
                        raise TypeError(f"场站 {site_no} 在时间 {hour_start_str} 没有找到电价数据")

                logger.info(f"场站 {site_no} 获取到未来24小时 {len(result)} 条电价数据")
                logger.info(f"price data:{result}")
                price_list = [r['price'] for r in result]
                return price_list

        except Exception as e:
            logger.error(f"Error getting future 24h sell price from db for site_no {site_no}: {e}")
            return []

    def save_power_predict_curve_for_ems(self, site_no: str,
                                       es_power_list: List[float] = None,
                                       pv_power_list: List[float] = None,
                                       load_power_list: List[float] = None,
                                       local_date: str = None) -> bool:
        """
        保存/更新功率预测曲线数据到数据库（支持部分字段更新）

        Args:
            site_no: str, 场站编号
            es_power_list: List[float], 储能功率列表（可选）
            pv_power_list: List[float], 光伏功率列表（可选）
            load_power_list: List[float], 融合负载功率列表（可选）
            local_date: str, 本地日期，格式：YYYY-MM-DD（必需，用于定位记录）

        Returns:
            bool: 保存是否成功

        注意：
            - local_date是必需参数，用于与site_no组合唯一定位记录
            - 可以只更新部分功率字段，未提供的字段保持原值不变
            - 创建新记录时支持部分字段，缺失字段将保存为空数组[]
            - 一个场站可以保存多天的记录
            - 适应预测算法时间差异，允许分批次提供不同类型的功率数据
        """
        try:
            # local_date是必需参数
            if local_date is None:
                logger.error(f"local_date is required for site_no: {site_no}")
                return False

            # 检查至少提供了一个功率字段要更新
            if not any([es_power_list is not None, pv_power_list is not None,
                       load_power_list is not None]):
                logger.warning(f"No power fields provided to update for site_no: {site_no}, local_date: {local_date}")
                return False

            with self.get_db() as db:
                # 检查是否已存在记录（按site_no和local_date查询）
                existing_record = db.scalar(
                    select(PowerPredictCurveForEmsDB).where(
                        and_(
                            PowerPredictCurveForEmsDB.site_no == site_no,
                            PowerPredictCurveForEmsDB.local_date == local_date
                        )
                    )
                )

                if existing_record:
                    # 更新已存在的记录（只更新提供的字段）
                    updated_fields = []

                    if es_power_list is not None:
                        existing_record.es_power_list = json.dumps(es_power_list)
                        updated_fields.append("es_power_list")

                    if pv_power_list is not None:
                        existing_record.pv_power_list = json.dumps(pv_power_list)
                        updated_fields.append("pv_power_list")

                    if load_power_list is not None:
                        existing_record.load_power_list = json.dumps(load_power_list)
                        updated_fields.append("load_power_list")

                    existing_record.updated_at = datetime.now(datetime.timezone.utc)

                    logger.info(f"Updated power predict curve for site_no: {site_no}, fields: {updated_fields}")
                else:
                    # 创建新记录（支持部分字段创建，缺失字段使用空数组）
                    # 为缺失的字段提供默认空数组
                    es_data = es_power_list if es_power_list is not None else []
                    pv_data = pv_power_list if pv_power_list is not None else []
                    load_data = load_power_list if load_power_list is not None else []

                    new_record = PowerPredictCurveForEmsDB(
                        site_no=site_no,
                        es_power_list=json.dumps(es_data),
                        pv_power_list=json.dumps(pv_data),
                        load_power_list=json.dumps(load_data),
                        local_date=local_date
                    )
                    db.add(new_record)

                    # 记录实际提供的字段
                    provided_fields = []
                    if es_power_list is not None:
                        provided_fields.append("es_power_list")
                    if pv_power_list is not None:
                        provided_fields.append("pv_power_list")
                    if load_power_list is not None:
                        provided_fields.append("load_power_list")

                    logger.info(f"Created new power predict curve for site_no: {site_no}, local_date: {local_date}, "
                               f"provided fields: {provided_fields}")

                db.commit()
                return True

        except Exception as e:
            logger.error(f"Error saving power predict curve for site_no {site_no}: {e}")
            return False

    def get_power_predict_curve_for_ems(self, site_no: str, local_date: str) -> Optional[Dict]:
        """
        获取指定场站和日期的功率预测曲线数据

        Args:
            site_no: str, 场站编号
            local_date: str, 本地日期，格式：YYYY-MM-DD

        Returns:
            Dict: 功率预测曲线数据，包含以下字段：
                site_no: str, 场站编号
                es_power_list: List[float], 储能功率列表
                pv_power_list: List[float], 光伏功率列表
                load_power_list: List[float], 融合负载功率列表
                local_date: str, 本地日期，格式：YYYY-MM-DD
                created_at: datetime, 创建时间
                updated_at: datetime, 更新时间
            如果没有找到记录则返回None
        """
        try:
            with self.get_db() as db:
                record = db.scalar(
                    select(PowerPredictCurveForEmsDB).where(
                        and_(
                            PowerPredictCurveForEmsDB.site_no == site_no,
                            PowerPredictCurveForEmsDB.local_date == local_date
                        )
                    )
                )

                if record:
                    return {
                        "site_no": record.site_no,
                        "es_power_list": json.loads(record.es_power_list),
                        "pv_power_list": json.loads(record.pv_power_list),
                        "load_power_list": json.loads(record.load_power_list),
                        "local_date": record.local_date,
                        "created_at": record.created_at,
                        "updated_at": record.updated_at
                    }
                else:
                    logger.warning(f"No power predict curve found for site_no: {site_no}, local_date: {local_date}")
                    return None

        except Exception as e:
            logger.error(f"Error getting power predict curve for site_no {site_no}, local_date {local_date}: {e}")
            return None

    def check_site_status(self, site_no: str) -> tuple:
        """
        检查场站是否存在以及AI状态，但不进行任何更新操作

        Args:
            site_no: str, 场站编号

        Returns:
            tuple: (操作是否成功, 场站是否存在, AI是否开启)
                - 操作是否成功: bool, True表示数据库操作成功，False表示失败
                - 场站是否存在: bool, True表示场站记录存在，False表示不存在
                - AI是否开启: bool, True表示AI已开启，False表示未开启
        """
        try:
            with self.get_db() as db:
                site = db.query(SiteDB).filter(SiteDB.site_no == site_no).first()

                if site:
                    # 场站记录存在，获取当前AI状态
                    current_ai_active = bool(site.is_ai_active)
                    return True, True, current_ai_active
                else:
                    # 场站记录不存在
                    return True, False, False

        except Exception as e:
            logger.error(f"Error in check_site_status: {e}")
            return False, False, False

    def update_site_basic_info(self, site_no: str, update_dict: Dict) -> bool:
        """
        仅更新场站基本信息，不检查AI状态

        Args:
            site_no: str, 场站编号
            update_dict: Dict, 需要更新的字段字典

        Returns:
            bool: 更新是否成功
        """
        try:
            with self.get_db() as db:
                site = db.query(SiteDB).filter(SiteDB.site_no == site_no).first()

                if site:
                    # 更新场站信息
                    for k, v in update_dict.items():
                        if hasattr(site, k):
                            setattr(site, k, v)
                    site.updated_at = datetime.now(pytz.UTC)

                    db.commit()
                    logger.info(f"场站 {site_no} 基本信息更新成功，字段: {list(update_dict.keys())}")
                    return True
                else:
                    logger.warning(f"场站 {site_no} 记录不存在，无法更新基本信息")
                    return False

        except Exception as e:
            logger.error(f"Error in update_site_basic_info: {e}")
            return False
