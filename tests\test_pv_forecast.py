import unittest
import json
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
from typing import cast, Dict, Any
import sys
import os
import random
import math

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from application.algorithm_schedule.pv_forecast_ import (
    get_solar_radiation_data,
    generate_default_test_weather,
    get_input_data,
    pv_forecast,
    get_elevation_data,
    estimate_elevation_by_region,
    get_system_metadata,
    get_weather_forecast,
    get_pv_history_data,
    save_pv_prediction_to_db
)

from application.db_operate.db_operate import DBOperate
from application.db_operate.models import (
    PVCurvePredictionDB, 
    PVAndMeterRealtimeDataDB, 
    SiteDB
)


class TestDataSetup:
    """测试数据设置工具类"""
    
    @staticmethod
    def generate_realistic_pv_data(site_no: str, hours: int = 24) -> list:
        """
        生成真实的光伏发电数据
        模拟一天的光伏发电曲线，考虑日照变化
        """
        power_data = []
        
        # 根据场站编号设定不同的容量
        site_capacities = {
            'TEST_SITE_001': 100.0,
            'SITE_SZ_001': 150.0,    # 深圳场站
            'SITE_PARIS_001': 120.0, # 巴黎场站
            'SITE_NL_001': 80.0,     # 荷兰场站
        }
        
        max_capacity = site_capacities.get(site_no, 100.0)
        
        for i in range(96):  # 24小时 * 4个15分钟间隔
            hour = (i // 4) % 24
            minute = (i % 4) * 15
            
            # 模拟真实的光伏发电曲线
            if 6 <= hour <= 18:  # 白天有发电
                # 使用正弦函数模拟日照变化
                # 上午6点到下午6点，12点为峰值
                time_factor = math.sin(math.pi * (hour - 6) / 12)
                
                # 基础功率 (考虑云层等随机因素)
                base_power = max_capacity * time_factor * 0.8
                
                # 添加随机波动 (±20%)
                random_factor = 1 + (random.random() - 0.5) * 0.4
                power = base_power * random_factor
                
                # 确保功率在合理范围内
                power = max(0, min(power, max_capacity))
            else:
                power = 0.0  # 夜间无发电
            
            power_data.append(round(power, 2))
        
        return power_data
    
    @staticmethod
    def create_test_sites():
        """创建测试场站数据"""
        return [
            {
                'site_no': 'TEST_SITE_001',
                'lat_and_lng': '52.19340694027317,5.430548227543284',  # 荷兰阿尔梅勒
                'max_capacity': 100.0,
                'is_ai_active': 1,  # 必填字段：开启AI能力
            },
            {
                'site_no': 'SITE_SZ_001', 
                'lat_and_lng': '22.5430,114.0579',  # 深圳
                'max_capacity': 150.0,
                'is_ai_active': 1,  # 必填字段：开启AI能力
            }
        ]
    
    @staticmethod
    def setup_test_data():
        """设置测试数据"""
        try:
            print("🔧 设置测试数据...")
            db_operate = DBOperate()
            test_sites = TestDataSetup.create_test_sites()
            
            with db_operate.get_db() as session:
                # 设置测试场站
                for site_data in test_sites:
                    existing_site = session.query(SiteDB).filter(
                        SiteDB.site_no == site_data['site_no']
                    ).first()
                    
                    if not existing_site:
                        new_site = SiteDB(**site_data)
                        session.add(new_site)
                
                # 设置历史数据
                for site_data in test_sites:
                    site_no = site_data['site_no']
                    
                    # 检查是否已有历史数据
                    existing_data = session.query(PVAndMeterRealtimeDataDB).filter(
                        PVAndMeterRealtimeDataDB.site_no == site_no
                    ).first()
                    
                    if not existing_data:
                        # 生成过去1天的历史数据
                        date = datetime.now() - timedelta(days=1)
                        power_data = TestDataSetup.generate_realistic_pv_data(site_no)
                        
                        for i, power in enumerate(power_data):
                            record_time = date.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(minutes=15*i)
                            
                            pv_record = PVAndMeterRealtimeDataDB(
                                site_no=site_no,
                                ts=int(record_time.timestamp() * 1000),  # 毫秒时间戳
                                pv_power=int(power),
                                pv_status='discharge',  # 光伏状态
                                meter_value=int(power * 0.95),  # 假设损耗5%
                                created_at=record_time
                            )
                            session.add(pv_record)
                
                session.commit()
                print("✅ 测试数据设置完成")
                return True
                
        except Exception as e:
            print(f"⚠️  设置测试数据失败: {e}")
            return False
    
    @staticmethod
    def check_test_data():
        """检查测试数据是否存在"""
        try:
            db_operate = DBOperate()
            with db_operate.get_db() as session:
                test_site = session.query(SiteDB).filter(
                    SiteDB.site_no == 'TEST_SITE_001'
                ).first()
                return test_site is not None
        except:
            return False


class TestPVForecast(unittest.TestCase):
    """光伏预测算法单元测试"""

    def setUp(self):
        """测试前置设置"""
        # 检查并设置测试数据
        if not TestDataSetup.check_test_data():
            TestDataSetup.setup_test_data()
        
        self.test_site_no = "TEST_SITE_001"
        self.test_latitude = 52.19340694027317
        self.test_longitude = 5.430548227543284
        self.test_start_date = "2024-01-15"
        self.test_end_date = "2024-01-16"
        
        # 模拟系统元数据
        self.mock_system_metadata = {
            'max_capacity': 100.0,
            'azimuth': 180.0,
            'tilt': 30.0,
            'latitude': self.test_latitude,
            'longitude': self.test_longitude,
            'elevation': 50.0
        }
        
        # 模拟天气数据点
        self.mock_weather_point = {
            'ghi': 0.5,
            'dni': 0.35,
            'dhi': 0.15,
            'air_temperature': 25.0,
            'cloud_cover': 20.0,
            'wind_speed': 10.0,
            'relative_humidity': 50.0,
            'hour': 12,
            'minute': 0,
            'dayofyear': 15,
            'latitude': self.test_latitude,
            'longitude': self.test_longitude,
            'elevation': 50.0
        }


    # @patch('application.algorithm_schedule.pv_forecast.DBOperate')
    # def test_get_site_coordinates(self, mock_db_operate):
    #     """测试场站坐标获取"""
    #     # 模拟数据库返回
    #     mock_site = Mock()
    #     mock_site.lat_and_lng = "52.19340694027317,5.430548227543284"
    
    #     mock_session = Mock()
    #     mock_session.query().filter().first.return_value = mock_site
    
    #     # 创建上下文管理器Mock
    #     mock_context_manager = MagicMock()
    #     mock_context_manager.__enter__.return_value = mock_session
    #     mock_context_manager.__exit__.return_value = None
    
    #     mock_db_instance = Mock()
    #     mock_db_instance.get_db.return_value = mock_context_manager
    #     mock_db_operate.return_value = mock_db_instance
    
    #     latitude, longitude = get_site_coordinates(self.test_site_no)
    
    #     self.assertIsInstance(latitude, float)
    #     self.assertIsInstance(longitude, float)
    #     self.assertAlmostEqual(latitude, 52.19340694027317, places=5)
    #     self.assertAlmostEqual(longitude, 5.430548227543284, places=5)
    
    # @patch('application.algorithm_schedule.pv_forecast.DBOperate')
    # def test_get_system_metadata(self, mock_db_operate):
    #     """测试系统元数据获取"""
    #     # 模拟数据库返回
    #     mock_site = Mock()
    #     mock_site.lat_and_lng = "52.19340694027317,5.430548227543284"
    #     mock_site.max_capacity = 100.0
    
    #     mock_session = Mock()
    #     mock_session.query().filter().first.return_value = mock_site
    
    #     # 创建上下文管理器Mock
    #     mock_context_manager = MagicMock()
    #     mock_context_manager.__enter__.return_value = mock_session
    #     mock_context_manager.__exit__.return_value = None
    
    #     mock_db_instance = Mock()
    #     mock_db_instance.get_db.return_value = mock_context_manager
    #     mock_db_operate.return_value = mock_db_instance
    
    #     metadata = get_system_metadata(self.test_site_no)
    
    #     self.assertIsNotNone(metadata)
    #     self.assertIsInstance(metadata, dict)
    #     # 类型转换确保metadata不为None
    #     metadata_dict = cast(Dict[str, Any], metadata)
    #     required_keys = ['max_capacity', 'azimuth', 'tilt', 'latitude', 'longitude', 'elevation']
    #     for key in required_keys:
    #         self.assertIn(key, metadata_dict)
    
    # @patch('application.algorithm_schedule.pv_forecast.weather_cache_manager')
    # def test_get_weather_forecast(self, mock_cache_manager):
    #     """测试天气预报获取"""
    #     # 模拟缓存返回天气数据
    #     mock_weather_data = {
    #         'days': [
    #             {
    #                 'hours': [
    #                     {
    #                         'solarradiation': 0.5,
    #                         'cloudcover': 30.0,
    #                         'temp': 20.0,
    #                         'humidity': 60.0,
    #                         'uvindex': 5,
    #                         'visibility': 10,
    #                         'windspeed': 15.0,
    #                         'pressure': 1013,
    #                         'precip': 0,
    #                         'snow': 0,
    #                         'conditions': 'clear',
    #                         'icon': 'clear-day'
    #                     }
    #                 ] * 24  # 24小时数据
    #             }
    #         ]
    #     }
    #     mock_cache_manager.get_cached_weather_data.return_value = mock_weather_data
    
    #     weather_data = get_weather_forecast(
    #         self.test_latitude,
    #         self.test_longitude,
    #         self.test_start_date,
    #         self.test_end_date
    #     )
    
    #     self.assertIsInstance(weather_data, list)
    #     self.assertEqual(len(weather_data), 96)  # 应该返回96个15分钟间隔的数据点
    
    #     # 验证缓存被调用
    #     mock_cache_manager.get_cached_weather_data.assert_called_once_with(
    #         self.test_start_date, self.test_latitude, self.test_longitude
    #     )
    
    # @patch('application.algorithm_schedule.pv_forecast.DBOperate')
    # def test_get_pv_history_data(self, mock_db_operate):
    #     """测试历史光伏数据获取"""
    #     # 创建模拟的PV记录
    #     mock_records = []
    #     for i in range(96):
    #         mock_record = Mock()
    #         mock_record.pv_power = 10.0 + i * 0.5  # 模拟不同的功率值
    #         mock_record.created_at = datetime.now() - timedelta(hours=24) + timedelta(minutes=15*i)
    #         mock_records.append(mock_record)
    
    #     mock_session = Mock()
    #     mock_session.query.return_value.filter.return_value.order_by.return_value.all.return_value = mock_records
    
    #     # 创建上下文管理器Mock
    #     mock_context_manager = MagicMock()
    #     mock_context_manager.__enter__.return_value = mock_session
    #     mock_context_manager.__exit__.return_value = None
    
    #     mock_db_instance = Mock()
    #     mock_db_instance.get_db.return_value = mock_context_manager
    #     mock_db_operate.return_value = mock_db_instance
    
    #     history_data = get_pv_history_data(self.test_site_no)
    
    #     self.assertIsInstance(history_data, list)
    #     self.assertEqual(len(history_data), 96)
    
    #     # 验证所有功率值都是有效的
    #     for power in history_data:
    #         self.assertIsInstance(power, float)
    #         self.assertGreaterEqual(power, 0)
    
    # @patch('application.algorithm_schedule.pv_forecast.DBOperate')
    # def test_save_pv_prediction_to_db(self, mock_db_operate):
    #     """测试预测结果保存到数据库"""
    #     predicted_power = [10.0, 20.0, 30.0] * 32  # 96个数据点
    
    #     mock_session = Mock()
    
    #     # 创建上下文管理器Mock
    #     mock_context_manager = MagicMock()
    #     mock_context_manager.__enter__.return_value = mock_session
    #     mock_context_manager.__exit__.return_value = None
    
    #     mock_db_instance = Mock()
    #     mock_db_instance.get_db.return_value = mock_context_manager
    #     mock_db_operate.return_value = mock_db_instance
    
    #     # 应该不抛出异常
    #     save_pv_prediction_to_db(self.test_site_no, predicted_power)
    
    #     # 验证数据库操作被调用
    #     mock_session.commit.assert_called()


    def test_pv_forecast_main_function(self):
        """测试主预测函数"""
        # 执行预测
        result = pv_forecast(self.test_site_no, {})

        # 验证结果
        self.assertIsNotNone(result)
        self.assertIsInstance(result, dict)
        result_dict = cast(Dict[str, Any], result)
        self.assertIn('predicted_time', result_dict)
        self.assertIn('pv_predicted_list', result_dict)
        self.assertIsInstance(result_dict['pv_predicted_list'], list)
        self.assertEqual(len(result_dict['pv_predicted_list']), 96)
        
        # 验证预测值的合理性
        pv_predicted_list = result_dict['pv_predicted_list']
        self.assertTrue(all(isinstance(p, (int, float)) for p in pv_predicted_list))
        
        print("✅ 光伏预测功能测试通过")
        print(f"📋 预测结果包含 {len(pv_predicted_list)} 个数据点")
        print(f"📋 预测峰值功率: {max(pv_predicted_list):.2f}kW")




if __name__ == '__main__':
    print("🚀 开始运行光伏预测单元测试")
    print("="*60)
    
    # 检查并设置测试数据
    print("🔍 检查测试数据...")
    if not TestDataSetup.check_test_data():
        print("⚠️  未找到测试数据，正在设置...")
        if TestDataSetup.setup_test_data():
            print("✅ 测试数据设置成功")
        else:
            print("❌ 测试数据设置失败，某些测试可能会失败")
    else:
        print("✅ 测试数据已存在")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加基础功能测试
    test_suite.addTest(unittest.makeSuite(TestPVForecast))
    
    # 运行测试
    print("\n🧪 开始运行测试...")
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果统计
    print(f"\n{'='*60}")
    print(f"📊 测试结果统计:")
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print(f"\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}")
    
    if result.errors:
        print(f"\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\n✅ 测试成功率: {success_rate:.1f}%")
    print(f"{'='*60}")
    
    if result.wasSuccessful():
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败，请检查上述错误信息") 