from datetime import datetime
from typing import List, Dict, Any

import requests

from application.utils.constants import APIEndpoints
from application.utils.idempotency import generate_idempotency_key
from application.utils.logger import setup_logger

logger = setup_logger("energy_cloud_api", direction="api")


class EnergyCloudAPI:
    """能源云API接口类，用于发送预测曲线和电价曲线数据"""

    base_url = APIEndpoints.ENERGY_CLOUD_BASE_URL
    power_predict_curve_url = f"{base_url}/power_predict_curve"
    electricity_price_curve_url = f"{base_url}/electricit_price_curve"

    @staticmethod
    def send_power_predict_curves(
            site_no: str,
            es_power_curve: List[float] = None,
            es_start_time: int = None,
            pv_power_curve: List[float] = None,
            pv_start_time: int = None,
            load_power_curve: List[float] = None,
            load_start_time: int = None
    ) -> bool:
        """
        发送储能/光伏/负载(充电桩)预测曲线数据到能源云
        
        Args:
            site_no: 场站编号
            es_power_curve: 储能功率预测曲线，96个点，每15分钟一个点（可选）
            es_start_time: 储能功率预测曲线开始时间（可选，提供es_power_curve时必需）
            pv_power_curve: 光伏功率预测曲线，96个点，每15分钟一个点（可选）
            pv_start_time: 光伏功率预测曲线开始时间（可选，提供pv_power_curve时必需）
            load_power_curve: 充电桩功率预测曲线，96个点，每15分钟一个点（可选）
            load_start_time: 充电桩功率预测曲线开始时间（可选，提供load_power_curve时必需）
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 检查至少有一个曲线数据
            curves = [es_power_curve, pv_power_curve, load_power_curve]
            valid_curves = [curve for curve in curves if curve is not None]
            
            if not valid_curves:
                logger.error("至少需要提供一个功率预测曲线")
                return False

            # 验证曲线和对应的开始时间
            if es_power_curve is not None and es_start_time is None:
                logger.error("提供储能功率曲线时必须提供对应的开始时间")
                return False
                
            if pv_power_curve is not None and pv_start_time is None:
                logger.error("提供光伏功率曲线时必须提供对应的开始时间")
                return False
                
            if load_power_curve is not None and load_start_time is None:
                logger.error("提供负载功率曲线时必须提供对应的开始时间")
                return False

            # 构建请求数据
            data = {
                "site_no": site_no
            }
            
            # 只添加非空的曲线数据，每个曲线使用自己的开始时间
            if es_power_curve is not None:
                data["es_data"] = {
                    "start_time": es_start_time * 1000,  # 转换为毫秒时间戳
                    "power_curve": es_power_curve
                }
                
            if pv_power_curve is not None:
                data["pv_data"] = {
                    "start_time": pv_start_time * 1000,
                    "power_curve": pv_power_curve
                }
                
            if load_power_curve is not None:
                data["charger_data"] = {
                    "start_time": load_start_time * 1000,
                    "power_curve": load_power_curve
                }

            # 生成幂等串
            biz_seq = generate_idempotency_key(data)
            data["bizSeq"] = biz_seq

            # 发送请求
            response = requests.post(
                EnergyCloudAPI.power_predict_curve_url,
                json=data,
                headers={"Content-Type": "application/json"}
            )

            # 检查响应
            if response.status_code == 200:
                # 构建详细的发送信息
                sent_curves = []
                if es_power_curve is not None:
                    sent_curves.append(
                        f"储能预测曲线(起始时间: {datetime.fromtimestamp(es_start_time).strftime('%H:%M:%S')}, "
                        f"数据: {es_power_curve})")
                if pv_power_curve is not None:
                    sent_curves.append(
                        f"光伏预测曲线(起始时间: {datetime.fromtimestamp(pv_start_time).strftime('%H:%M:%S')}, "
                        f"数据: {pv_power_curve})")
                if load_power_curve is not None:
                    sent_curves.append(
                        f"负载预测曲线(起始时间: {datetime.fromtimestamp(load_start_time).strftime('%H:%M:%S')}, "
                        f"数据: {load_power_curve})")
                
                curve_count = len(sent_curves)
                curve_types = "、".join(sent_curves)
                logger.info(f"成功发送{curve_count}种预测曲线到能源云 - 场站: {site_no} - {curve_types} - 幂等串: {biz_seq}")
                return True
            else:
                logger.error(f"发送预测曲线数据失败，状态码: {response.status_code}, 响应: {response.text}")
                return False

        except Exception as e:
            logger.error(f"发送预测曲线数据时发生错误: {str(e)}")
            return False

    @staticmethod
    def send_electricity_price_curve(price_segments: Dict[str, Any]) -> bool:
        """
        发送电价曲线数据到能源云
        
        Args:
            price_segments: 电价数据，包含biz_seq和electricity_price字段
                格式：{
                    "biz_seq": "幂等串",
                    "electricity_price": [电价分段数据列表]
                }
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 验证数据
            if not price_segments:
                logger.error("电价分段数据不能为空")
                return False
                
            # 验证必要字段
            if "biz_seq" not in price_segments:
                logger.error("电价数据缺少biz_seq字段")
                return False
                
            if "electricity_price" not in price_segments:
                logger.error("电价数据缺少electricity_price字段")
                return False

            # # 验证时间连续性
            # total_duration = timedelta()
            # for i in range(len(price_segments) - 1):
            #     current_end = datetime.fromtimestamp(price_segments[i]["end_time"] / 1000)
            #     next_start = datetime.fromtimestamp(price_segments[i + 1]["start_time"] / 1000)
            #     if current_end != next_start:
            #         logger.error("电价分段时间不连续")
            #         return False
            #     total_duration += current_end - datetime.fromtimestamp(price_segments[i]["start_time"] / 1000)

            # # 添加最后一个时间段
            # last_segment = price_segments[-1]
            # total_duration += datetime.fromtimestamp(last_segment["end_time"] / 1000) - \
            #                   datetime.fromtimestamp(last_segment["start_time"] / 1000)

            # # 验证总时长是否为24小时
            # if total_duration != timedelta(hours=24):
            #     logger.error("电价分段总时长必须为24小时")
            #     return False

            # 发送请求
            logger.info(f'url:{EnergyCloudAPI.electricity_price_curve_url}')
            response = requests.post(
                EnergyCloudAPI.electricity_price_curve_url,
                json=price_segments,
                headers={"Content-Type": "application/json"}
            )

            # 检查响应
            if response.status_code == 200:
                if response.json()['data']:
                    logger.info(response.json())
                    logger.info("成功发送电价曲线数据到能源云")
                    return True
                else:
                    logger.error(f"发送电价曲线数据到能源云异常:{response.json()}")
                    return False
            else:
                logger.error(f"发送电价曲线数据失败，状态码: {response.status_code}, 响应: {response.text}")
                return False

        except Exception as e:
            logger.error(f"发送电价曲线数据时发生错误: {str(e)}")
            return False
