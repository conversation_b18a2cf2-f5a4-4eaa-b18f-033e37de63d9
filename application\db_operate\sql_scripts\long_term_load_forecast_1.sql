-- 测试数据插入脚本
-- 为PVAndMeterRealtimeDataDB和ESRealtimeDataDB表生成7天的测试数据
-- 每天96个采样点（每15分钟一个），从某日0时刻开始

-- 设置字符集和校对规则
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 设置测试参数
SET @start_date = '2025-06-24 00:00:00';  -- 起始日期，可根据需要修改
SET @site_no = 'SITE001';           -- 测试场站编号
SET @es_sn = 'ES_002';                    -- 储能设备序列号

-- 清空现有测试数据（可选）
DELETE FROM PVAndMeterRealtimeData WHERE site_no = @site_no;
DELETE FROM ESRealtimeData WHERE site_no = @site_no;

-- 插入PVAndMeterRealtimeDataDB测试数据
-- 使用存储过程生成7天的数据
DELIMITER $$

CREATE PROCEDURE InsertTestData()
BEGIN
    DECLARE current_date_var DATE;
    DECLARE current_time_var TIME;
    DECLARE ts BIGINT;
    DECLARE pv_power INT;
    DECLARE meter_value INT;
    DECLARE pv_status VARCHAR(20);
    DECLARE day_count INT DEFAULT 0;
    DECLARE time_slot INT DEFAULT 0;
    
    SET current_date_var = @start_date;
    
    -- 循环7天
    WHILE day_count < 7 DO
        SET time_slot = 0;
        
        -- 循环96个时间点（每15分钟一个）
        WHILE time_slot < 96 DO
            -- 计算当前时间
            SET current_time_var = SEC_TO_TIME(time_slot * 15 * 60);
            SET ts = UNIX_TIMESTAMP(CONCAT(current_date_var, ' ', current_time_var)) * 1000;
            
            -- 生成波动的光伏功率 (0-100kW，白天有功率，晚上为0)
            IF HOUR(current_time_var) BETWEEN 6 AND 18 THEN
                -- 白天：基于时间的正弦波 + 随机波动
                SET pv_power = GREATEST(0, 
                    ROUND(50 + 30 * SIN((time_slot - 24) * PI() / 48) + 
                    (RAND() - 0.5) * 20));
            ELSE
                -- 晚上：0功率
                SET pv_power = 0;
            END IF;
            
            -- 设置光伏状态
            IF pv_power > 0 THEN
                SET pv_status = 'discharge';
            ELSE
                SET pv_status = 'non-discharge';
            END IF;
            
            -- 生成波动的市电功率 (50-200kW，有随机波动)
            SET meter_value = ROUND(100 + 50 * SIN(time_slot * PI() / 48) + 
                (RAND() - 0.5) * 30);
            
            -- 插入PVAndMeterRealtimeDataDB数据
            INSERT INTO PVAndMeterRealtimeData 
            (site_no, ts, pv_power, pv_status, meter_value, created_at)
            VALUES 
            (@site_no, ts, pv_power, pv_status, meter_value, NOW());
            
            SET time_slot = time_slot + 1;
        END WHILE;
        
        SET day_count = day_count + 1;
        SET current_date_var = DATE_ADD(current_date_var, INTERVAL 1 DAY);
    END WHILE;
END$$

DELIMITER ;

-- 执行存储过程生成PVAndMeterRealtimeDataDB数据
CALL InsertTestData();

-- 删除存储过程
DROP PROCEDURE InsertTestData;

-- 插入ESRealtimeDataDB测试数据
DELIMITER $$

CREATE PROCEDURE InsertESTestData()
BEGIN
    DECLARE current_date_var DATE;
    DECLARE current_time_var TIME;
    DECLARE ts BIGINT;
    DECLARE es_soc INT;
    DECLARE es_power INT;
    DECLARE status VARCHAR(20);
    DECLARE day_count INT DEFAULT 0;
    DECLARE time_slot INT DEFAULT 0;
    DECLARE base_soc INT DEFAULT 60;  -- 基础SOC值
    
    SET current_date_var = @start_date;
    
    -- 循环7天
    WHILE day_count < 7 DO
        SET time_slot = 0;
        
        -- 循环96个时间点（每15分钟一个）
        WHILE time_slot < 96 DO
            -- 计算当前时间
            SET current_time_var = SEC_TO_TIME(time_slot * 15 * 60);
            SET ts = UNIX_TIMESTAMP(CONCAT(current_date_var, ' ', current_time_var)) * 1000;
            
            -- 生成波动的储能功率（-50到50kW，正值放电，负值充电）
            -- 基于时间模式：白天放电，晚上充电
            IF HOUR(current_time_var) BETWEEN 8 AND 18 THEN
                -- 白天：主要放电
                SET es_power = ROUND(20 + 20 * SIN(time_slot * PI() / 48) + 
                    (RAND() - 0.5) * 15);
                SET es_power = GREATEST(-10, es_power);  -- 限制最小值
            ELSE
                -- 晚上：主要充电
                SET es_power = ROUND(-30 - 15 * SIN(time_slot * PI() / 48) + 
                    (RAND() - 0.5) * 10);
                SET es_power = LEAST(10, es_power);  -- 限制最大值
            END IF;
            
            -- 根据功率设置状态
            IF es_power > 5 THEN
                SET status = 'discharge';
            ELSEIF es_power < -5 THEN
                SET status = 'charging';
            ELSE
                SET status = 'standby';
            END IF;
            
            -- 计算SOC（基于功率变化，简化计算）
            SET base_soc = base_soc + (es_power * 0.01);  -- 功率对SOC的影响
            SET es_soc = GREATEST(20, LEAST(90, ROUND(base_soc + (RAND() - 0.5) * 5)));
            
            -- 插入ESRealtimeDataDB数据
            INSERT INTO ESRealtimeData 
            (site_no, ts, es_sn, es_soc, es_power, rated_cap, real_cap, 
             es_max_soc, es_min_soc, status, created_at)
            VALUES 
            (@site_no, ts, @es_sn, es_soc, es_power, 100.00, 
             ROUND(100.00 * es_soc / 100, 2), 90, 20, status, NOW());
            
            SET time_slot = time_slot + 1;
        END WHILE;
        
        SET day_count = day_count + 1;
        SET current_date_var = DATE_ADD(current_date_var, INTERVAL 1 DAY);
    END WHILE;
END$$

DELIMITER ;

-- 执行存储过程生成ESRealtimeDataDB数据
CALL InsertESTestData();

-- 删除存储过程
DROP PROCEDURE InsertESTestData;

-- 验证插入的数据
SELECT 
    'PVAndMeterRealtimeData' as table_name,
    COUNT(*) as record_count,
    MIN(created_at) as min_date,
    MAX(created_at) as max_date
FROM PVAndMeterRealtimeData 
WHERE site_no COLLATE utf8mb4_unicode_ci = @site_no COLLATE utf8mb4_unicode_ci

UNION ALL

SELECT 
    'ESRealtimeData' as table_name,
    COUNT(*) as record_count,
    MIN(created_at) as min_date,
    MAX(created_at) as max_date
FROM ESRealtimeData 
WHERE site_no COLLATE utf8mb4_unicode_ci = @site_no COLLATE utf8mb4_unicode_ci;

-- 查看部分数据示例
SELECT 
    'PVAndMeterRealtimeData Sample' as info,
    site_no,
    FROM_UNIXTIME(ts/1000) as sample_time,
    pv_power,
    pv_status,
    meter_value
FROM PVAndMeterRealtimeData 
WHERE site_no COLLATE utf8mb4_unicode_ci = @site_no COLLATE utf8mb4_unicode_ci
ORDER BY ts
LIMIT 10;

SELECT 
    'ESRealtimeData Sample' as info,
    site_no,
    FROM_UNIXTIME(ts/1000) as sample_time,
    es_sn,
    es_soc,
    es_power,
    status
FROM ESRealtimeData 
WHERE site_no COLLATE utf8mb4_unicode_ci = @site_no COLLATE utf8mb4_unicode_ci
ORDER BY ts
LIMIT 10; 