apiVersion: apps/v1
kind: Deployment
metadata:
  name: ems
  namespace: energy
  labels:
    app: ems
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ems
  template:
    metadata:
      labels:
        app: ems
    spec:
      containers:
      - name: ems
        image: 541232816868.dkr.ecr.cn-northwest-1.amazonaws.com.cn/ems-cpu:{{COMMIT_ID_SHORTER}}
        ports:
        - containerPort: 8000
        env:
        - name: PYTHONPATH
          value: "/app"
        - name: ENVIRONMENT
          value: "cn-northwest-1-test"
        - name: CONFIG_ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: ems-secrets
              key: config-encryption-key
        volumeMounts:
        - name: logs-volume
          mountPath: /app/logs
        resources:
          requests:
            memory: "3Gi"
            cpu: "1.5"
          limits:
            memory: "3Gi"
            cpu: "1.5"
      volumes:
      - name: logs-volume
        hostPath:
          path: /var/logs/ems
          type: DirectoryOrCreate
---
apiVersion: v1
kind: Service
metadata:
  name: ems-service
  namespace: energy
spec:
  selector:
    app: ems
  ports:
  - port: 8000
    targetPort: 8000
  type: ClusterIP
---
apiVersion: v1
kind: Secret
metadata:
  name: ems-secrets
  namespace: energy
type: Opaque
data:
  config-encryption-key: "NDA2Ulc4YUtQeWc4SU5Ia0h4dTVHdDJxSk5kME1aQmRjVXZtd3JtMWZEST0="
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ems-ingress
  namespace: energy
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: ems.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ems-service
            port:
              number: 8000
