import threading
import time
import json

import yaml
from confluent_kafka import Producer

from application.kafka_operate.send_message_queue_manager import send_message_queue_manager
from application.utils.idempotency import generate_idempotency_key
from application.utils.logger import setup_logger

logger = setup_logger("kafka_producer", direction="kafka")


class KafkaProducer:

    def __init__(self):
        """Initialize Kafka Producer"""
        try:
            # Read configuration file
            with open('application/settings/config.yaml', 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # Get kafka configuration
            kafka_config = config['artifact_instances']['ems_kafka_producer']
            
            # Configure producer
            producer_config = {
                'bootstrap.servers': kafka_config['bootstrap_servers'],
                'acks': kafka_config['acks']
            }
            
            # Initialize producer
            self.kafka_producer = Producer(producer_config)
            self._running = False
            self._thread = None
            
            logger.info("Kafka Producer initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Kafka Producer: {str(e)}")
            raise



    def start_producer(self):
        """Start Kafka producer thread"""
        if self._running:
            logger.warning("Producer is already running")
            return

        self._running = True
        self._thread = threading.Thread(target=self._producer_loop, daemon=True)
        self._thread.start()
        logger.info("Kafka producer thread started")

    def stop_producer(self):
        """Stop producer thread"""
        if not self._running:
            logger.warning("Producer is already stopped")
            return

        try:
            # Set stop flag
            self._running = False

            # Wait for thread to end
            if self._thread and self._thread.is_alive():
                self._thread.join(timeout=15)  # Wait up to 15 seconds

            # Close producer
            if hasattr(self, 'kafka_producer'):
                self.kafka_producer.flush(10)  # Wait for all messages to be delivered
                self.kafka_producer.close()

            logger.info("Kafka producer stopped")
        except Exception as e:
            logger.error(f"Error stopping Kafka producer: {str(e)}")

    def _producer_loop(self):
        """Producer main loop"""
        while self._running:
            try:
                # Get message from queue with timeout
                message_data = send_message_queue_manager.get(timeout=1.0)
                
                if message_data is not None:
                    topic = message_data.get('topic')
                    value = message_data.get('value')
                    callback = message_data.get('callback')

                    # Send message
                    self.kafka_producer.produce(
                        topic=topic,
                        value=value,
                        callback=callback
                    )

                    # Trigger callback handling
                    self.kafka_producer.poll(0)
                    logger.info(f"produce message :{value} success")

            except Exception as e:
                logger.error(f"Error processing message in producer thread: {str(e)}")
                continue

        # Before thread ends, ensure all messages are sent
        try:
            self.kafka_producer.flush(10)
            logger.info("Kafka producer successfully flushed all messages")
        except Exception as e:
            logger.error(f"Failed to flush messages when closing producer thread: {str(e)}")
