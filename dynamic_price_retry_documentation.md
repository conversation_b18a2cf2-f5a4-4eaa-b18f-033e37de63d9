# 动态电价获取重试机制文档

## 概述

为了确保第一时间获取到每个国家的动态电价数据，我们为 `DynamicPriceScheduler` 实现了完整的重试机制。系统支持对6个欧洲国家（AT、BE、DE、FR、NL、PL）的电价数据进行独立重试，确保即使部分国家的数据暂时不可用，也能在数据发布后及时获取。

## 核心特性

### 🕐 智能调度时间
- **主要获取时间**: 每天 CET 12:10 开始获取次日电价
- **基于实际发布时间**: 动态电价通常在 12:10 左右发布
- **及时响应**: 确保第一时间获取到最新电价数据

### 🔄 国家级重试机制
- **独立重试**: 每个国家单独进行重试，互不影响
- **智能间隔**: 使用递增重试间隔 [2, 5, 10, 15, 30] 分钟
- **最大重试**: 每个国家最多重试 5 次
- **后台重试**: 失败的国家启动后台重试任务

### ✅ 数据验证机制
- **完整性检查**: 验证是否包含24小时完整数据
- **字段验证**: 检查必要字段（price、deliveryStart、deliveryEnd）
- **格式验证**: 确保数据格式符合预期

### 📊 状态监控
- **实时状态**: 跟踪每个国家的重试次数和成功状态
- **详细日志**: 记录每次重试的详细信息
- **状态查询**: 提供API查询当前重试状态

## 配置参数

### 重试配置
```python
class DynamicPriceRetryConfig:
    # 支持的国家列表
    SUPPORTED_COUNTRIES = ['AT', 'BE', 'DE', 'FR', 'NL', 'PL']
    
    # 重试配置
    MAX_RETRIES_PER_COUNTRY = 5  # 每个国家最大重试次数
    RETRY_INTERVALS = [2, 5, 10, 15, 30]  # 重试间隔（分钟）
    MAX_TOTAL_RETRY_TIME = 120  # 最大总重试时间（分钟）
    
    # 时间配置
    PRICE_PUBLISH_TIME = 12  # 电价发布时间
    EARLY_FETCH_START_TIME = 12  # 开始获取时间
    LATE_FETCH_END_TIME = 14  # 最晚结束时间
```

### 可调整参数
- **重试次数**: 根据网络稳定性调整每个国家的最大重试次数
- **重试间隔**: 根据API响应时间调整重试间隔
- **总重试时间**: 设置后台重试的最大持续时间
- **日志详细程度**: 控制日志的详细程度

## 工作流程

### 主要流程
```mermaid
flowchart TD
    A[12:10 CET 启动] --> B[重置每日状态]
    B --> C[并行获取6国电价]
    C --> D{所有国家成功?}
    D -->|是| E[构建并发送数据]
    D -->|否| F[记录成功国家]
    F --> G[启动后台重试]
    E --> H[触发储能调度]
    G --> I[后台持续重试]
    I --> J{重试成功?}
    J -->|是| K[更新并发送数据]
    J -->|否| L[记录最终失败]
```

### 单国家重试流程
```mermaid
flowchart TD
    A[开始获取国家X] --> B[调用API]
    B --> C{数据有效?}
    C -->|是| D[保存到数据库]
    C -->|否| E{还有重试次数?}
    E -->|是| F[等待重试间隔]
    F --> G[重试次数+1]
    G --> B
    E -->|否| H[标记失败]
    D --> I[标记成功]
```

## 使用方法

### 基本使用
```python
from application.algorithm_schedule.dynamic_price_scheduler import DynamicPriceScheduler

# 创建调度器
scheduler = DynamicPriceScheduler()

# 启动调度器（会在12:10开始获取电价）
scheduler.start_scheduler()
```

### 查询重试状态
```python
# 获取当前重试状态
status = scheduler.get_retry_status()

print(f"支持的国家: {status['supported_countries']}")
print(f"重试次数统计: {status['countries_retry_count']}")
print(f"成功状态: {status['countries_success_status']}")
print(f"最后获取日期: {status['last_fetch_date']}")
```

### 手动触发获取
```python
from datetime import datetime, timedelta

# 手动获取明天的电价
tomorrow = datetime.now().date() + timedelta(days=1)
success_countries, failed_countries = scheduler._fetch_all_countries_with_retry(tomorrow)

print(f"成功: {success_countries}")
print(f"失败: {failed_countries}")
```

## 日志示例

### 成功获取日志
```
2024-01-18 12:10:01 - dynamic_price_scheduler - INFO - 开始获取 2024-01-19 的电价数据
2024-01-18 12:10:02 - dynamic_price_scheduler - INFO - 开始获取国家 DE 的电价数据 (日期: 2024-01-19)
2024-01-18 12:10:03 - dynamic_price_scheduler - INFO - ✅ 国家 DE 电价获取成功
2024-01-18 12:10:04 - dynamic_price_scheduler - INFO - 成功获取到 6 个国家的电价: ['AT', 'BE', 'DE', 'FR', 'NL', 'PL']
```

### 重试过程日志
```
2024-01-18 12:10:05 - dynamic_price_scheduler - INFO - 国家 FR 第 1 次尝试获取电价 (日期: 2024-01-19)
2024-01-18 12:10:06 - dynamic_price_scheduler - WARNING - 国家 FR 电价数据无效或为空 (第 1 次尝试)
2024-01-18 12:10:07 - dynamic_price_scheduler - INFO - 国家 FR 等待 2 分钟后进行第 2 次重试...
2024-01-18 12:12:08 - dynamic_price_scheduler - INFO - 国家 FR 第 2 次尝试获取电价 (日期: 2024-01-19)
2024-01-18 12:12:09 - dynamic_price_scheduler - INFO - ✅ 国家 FR 电价获取成功 (第 2 次尝试)
```

### 后台重试日志
```
2024-01-18 12:15:00 - dynamic_price_scheduler - INFO - 启动后台重试任务，目标国家: ['PL']
2024-01-18 12:25:01 - dynamic_price_scheduler - INFO - 后台重试获取国家 PL 的电价数据
2024-01-18 12:25:02 - dynamic_price_scheduler - INFO - ✅ 后台重试成功: 国家 PL
```

## 监控和告警

### 关键指标
- **成功率**: 每个国家的电价获取成功率
- **重试次数**: 平均重试次数和最大重试次数
- **获取时间**: 从开始到成功获取的总时间
- **失败率**: 最终失败的国家比例

### 建议告警规则
```yaml
alerts:
  - name: "电价获取失败率过高"
    condition: "failed_countries_count > 2"
    severity: "warning"
    
  - name: "单国家重试次数过多"
    condition: "country_retry_count > 3"
    severity: "info"
    
  - name: "电价获取总时间过长"
    condition: "total_fetch_time > 30min"
    severity: "warning"
    
  - name: "后台重试最终失败"
    condition: "background_retry_failed == true"
    severity: "critical"
```

### 状态查询API
```python
# 获取详细状态
status = scheduler.get_retry_status()

# 检查是否有失败的国家
failed_countries = [
    country for country, success 
    in status['countries_success_status'].items() 
    if not success
]

# 检查重试次数是否过多
high_retry_countries = [
    country for country, count 
    in status['countries_retry_count'].items() 
    if count > 3
]
```

## 性能优化

### 并发优化
- 各国家数据获取相互独立，可以并行处理
- 后台重试任务不阻塞主流程
- 数据库操作异步化

### 网络优化
- 智能重试间隔避免频繁请求
- 失败快速检测，避免长时间等待
- 连接池复用减少连接开销

### 内存优化
- 及时释放临时数据
- 状态信息定期清理
- 避免大量数据缓存

## 故障处理

### 常见问题
1. **网络超时**: 自动重试，逐步增加间隔
2. **API限流**: 使用合理的重试间隔
3. **数据格式错误**: 详细验证和日志记录
4. **数据库连接失败**: 重试机制包含数据库操作

### 恢复策略
1. **部分失败**: 后台重试继续获取
2. **全部失败**: 记录详细日志，人工介入
3. **系统重启**: 自动恢复到正常调度状态

## 测试验证

### 运行测试
```bash
python test_dynamic_price_retry_mechanism.py
```

### 测试覆盖
- ✅ 重试机制逻辑测试
- ✅ 数据验证测试
- ✅ 配置参数测试
- ✅ 状态监控测试
- ✅ 异常处理测试

## 总结

动态电价重试机制确保了系统能够：
- 🎯 **及时获取**: 在12:10第一时间开始获取电价
- 🔄 **可靠重试**: 每个国家独立重试，最大化成功率
- 📊 **全面监控**: 详细的状态跟踪和日志记录
- 🛡️ **故障恢复**: 后台重试和异常处理机制
- ⚡ **高效处理**: 并行获取和智能调度

这套机制显著提高了动态电价获取的可靠性和及时性，为储能调度提供了更准确的电价数据基础。
