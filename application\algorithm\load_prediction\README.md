# Load Forecasting 

The Load forecasting goal is to predict the energy consumption at a station for the next 24hrs in a 15min intervals.

The Algorithm requires historical input data and uses it to ouput the prediction

## 1. Input Data

The input data should be in the form requires input data of the json format 

```python 

input_data = {
    "station_name": "Station Name",       # 站点名称
    "station_id": "Station ID",           # 站点ID
    "location": [40.1234, -74.5678],      # 位置 [lat, lon]
    "time_zone": "America/New_York",      # 时区
    "current_time": "2019-11-02 23:30:00",
    "fields": [
        "Timestamp","grid_power","grid_supply","power","charging_consumption",
        "number_of_chargers","active_chargers","occupied_chargers",
        "available_chargers","chargers_under_maintenance","electricity_pricing",
        "outdoor_dry_bulb_temperature","outdoor_relative_humidity",
        "diffuse_solar_irradiance","direct_solar_irradiance","holiday"
    ],
    "time_series": [
        ["2025-07-02 00:00:00",12.3,45.6,8.7,3.2,10,7,5,3,0,0.15,22.5,55.3,130.2,420.7,False],
        ["2025-07-02 00:15:00",11.8,44.1,9.1,3.5,10,8,6,2,0,0.15,22.3,54.9,128.4,415.8,False],
        # … additional 15-min slices …
        ["2025-07-02 23:45:00",13.0,47.2,9.5,3.8,10,9,7,1,0,0.15,19.8,58.1,110.0,300.5,False]
    ]
}

```
__NOTE:__ current_time value must be in the time_series

For the time_series, 7 days of data with the time difference between each datapoint being 15mins. this means the series needs to have a total of `4*24*7` or a total of `672` data points.

The fields and their meanings are as follows 

| 列名                        | English                       | 中文                        | Type               |
|-----------------------------|-------------------------------|-----------------------------|--------------------|
| `station_name`              | Charging station name         | 站点名称                     | `str`              |
| `station_id`                | Charging station ID           | 站点ID                       | `str`              |
| `current_time`              | time of prediction            |  预测时间                    | `timestamp`       |
| `location`                  | Latitude & longitude          | 地理位置（经纬度）           | `List[float]`      |
| `time_zone`                 | Time zone                     | 时区                         | `str`              |
| `timestamp`                 | ISO-8601 date/time            | 时间戳                       | `str` (datetime)   |
| `grid_power`                | Grid power (kW)               | 电网功率（千瓦）             | `float`            |
| `grid_supply`               | Grid supply (kW)              | 电网供给（千瓦）             | `float`            |
| `power`                     | Charger power (kW)            | 充电功率（千瓦）             | `float`            |
| `charging_consumption`      | Energy used (kWh)             | 能耗（千瓦时）               | `float`            |
| `number_of_chargers`        | Total chargers                | 充电桩总数                   | `int`              |
| `active_chargers`           | Currently active              | 正在使用                     | `int`              |
| `occupied_chargers`         | Occupied chargers             | 已占用                       | `int`              |
| `available_chargers`        | Available chargers            | 可用数量                     | `int`              |
| `chargers_under_maintenance`| Under-maintenance chargers    | 维护中数量                   | `int`              |
| `electricity_pricing`       | Price ($/kWh)                 | 电价（美元/千瓦时）          | `float`            |
| `outdoor_dry_bulb_temperature` | Outdoor temp (°C)          | 室外干球温度（摄氏度）       | `float`            |
| `outdoor_relative_humidity` | Relative humidity (%)         | 相对湿度（%）                | `float`            |
| `diffuse_solar_irradiance`  | Diffuse solar (W/m²)          | 散射辐照度（瓦/平方米）      | `float`            |
| `direct_solar_irradiance`   | Direct solar (W/m²)           | 直射辐照度（瓦/平方米）      | `float`            |
| `holiday`                   | Holiday flag (True/False)     | 是否假日（布尔）             | `bool`             |

## Output 

The output of the load prediction algorithm is a time-series of the next 24hrs energy prediction in 15min intervals

the output will look as follows:

```python
output = [energy_1, energy_2, ... , energy_96]
```
