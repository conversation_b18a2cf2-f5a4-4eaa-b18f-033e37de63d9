import threading

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from application.kafka_operate.noc_kafka_consumer import NOCKafkaConsumer
from application.kafka_operate.ems_kafka_consumer import EMSKafkaConsumer
from application.kafka_operate.kafka_producer import KafkaProducer
from application.api_manage.control_api import router as control_router
from application.algorithm_schedule.dynamic_price_scheduler import DynamicPriceScheduler
from application.utils.logger import setup_logger


logger = setup_logger("AI_EMS")
app = FastAPI(
    title="EMS AI Control API",
    description="EMS AI Control System API",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头
)

# 创建Kafka管理器实例
noc_kafka_consumer = NOCKafkaConsumer()
# 在单独的线程中启动 Kafka 消费者
consumer_thread = threading.Thread(
    target=noc_kafka_consumer.start_consumer,
    daemon=True
)
consumer_thread.start()

# 创建Kafka管理器实例
ems_kafka_consumer = EMSKafkaConsumer()
consumer_thread = threading.Thread(
    target=ems_kafka_consumer.start_consumer,
    daemon=True
)
consumer_thread.start()

kafka_producer = KafkaProducer()

# 在单独的线程中启动 Kafka 生产者
kafka_producer.start_producer()

dynamic_price_scheduler = DynamicPriceScheduler()
price_scheduler_thread = threading.Thread(
    target=dynamic_price_scheduler.start_scheduler,
    daemon=True,
    name="DynamicPriceScheduler"
)
price_scheduler_thread.start()

# 注册路由
app.include_router(control_router, prefix="/api/v1", tags=["AI Control"])

if __name__ == '__main__':
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8888)
