import hashlib
from typing import Dict, Any, Optional

from cachetools import TTLCache, cached

from application.utils.weather import Weather
from application.utils.logger import setup_logger

logger = setup_logger("weather_cache", direction="utils")


class WeatherCacheManager:
    """
    天气数据缓存管理器
    使用 TTL 缓存策略，避免过度调用天气 API
    """
    
    def __init__(self, ttl_hours: int = 1, maxsize: int = 1000):
        self.cache = TTLCache(maxsize=maxsize, ttl=ttl_hours * 3600)
        self.logger = logger
    
    def _generate_cache_key(self, date: str, lat: float, lng: float) -> str:
        # 使用日期和坐标生成唯一的缓存键
        key_data = f"{date}_{lat:.6f}_{lng:.6f}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _get_weather_data(self, date: str, lat: float, lng: float) -> Optional[Dict[str, Any]]:
        cache_key = self._generate_cache_key(date, lat, lng)
        
        if cache_key in self.cache:
            self.logger.info(f"从缓存获取天气数据: {date}, ({lat}, {lng})")
            return self.cache[cache_key]
        
        self.logger.info(f"缓存未命中，需要调用天气API: {date}, ({lat}, {lng})")
        return None
    
    def _set_weather_data(self, date: str, lat: float, lng: float, weather_data: Dict[str, Any]) -> None:
        cache_key = self._generate_cache_key(date, lat, lng)
        self.cache[cache_key] = weather_data
        self.logger.info(f"天气数据已缓存: {date}, ({lat}, {lng})")
    
    def get_cached_weather_data(self, date: str, lat: float, lng: float) -> Dict[str, Any]:
        # 首先尝试从缓存获取
        cached_data = self._get_weather_data(date, lat, lng)
        if cached_data is not None:
            return cached_data
        
        # 缓存中没有，调用天气API
        try:
            weather = Weather(date, lat, lng)
            weather_data = weather()
            
            if weather_data:
                # 缓存获取到的数据
                self._set_weather_data(date, lat, lng, weather_data)
                return weather_data
            else:
                self.logger.error(f"天气API返回空数据: {date}, ({lat}, {lng})")
                return {}
                
        except Exception as e:
            self.logger.error(f"获取天气数据失败: {date}, ({lat}, {lng}), 错误: {str(e)}")
            return {}
    
    def clear_cache(self) -> None:
        """清空缓存"""
        self.cache.clear()
        self.logger.info("天气缓存已清空")
    
    def get_cache_info(self) -> Dict[str, Any]:
        return {
            "cache_size": len(self.cache),
            "maxsize": self.cache.maxsize,
            "ttl": self.cache.ttl,
            "currsize": self.cache.currsize
        }


# 全局缓存管理器实例
weather_cache_manager = WeatherCacheManager()


# 装饰器版本的缓存函数（可选使用）
@cached(cache=TTLCache(maxsize=1000, ttl=1 * 3600))
def get_weather_data_cached(date: str, lat: float, lng: float) -> Dict[str, Any]:
    """使用装饰器缓存的天气数据获取函数"""
    try:
        weather = Weather(date, lat, lng)
        return weather() or {}
    except Exception as e:
        logger.error(f"获取天气数据失败: {date}, ({lat}, {lng}), 错误: {str(e)}")
        return {}
