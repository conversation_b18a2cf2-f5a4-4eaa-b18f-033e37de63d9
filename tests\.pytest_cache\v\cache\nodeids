["manual_test_dynamic_price.py::test_dynamic_price_fetch_function", "manual_test_dynamic_price.py::test_price_data_format", "manual_test_dynamic_price.py::test_scheduler_initialization", "manual_test_dynamic_price.py::test_scheduler_short_run", "manual_test_dynamic_price.py::test_scheduler_timing", "quick_test_dynamic_price.py::test_data_structure", "quick_test_dynamic_price.py::test_multiple_regions", "quick_test_dynamic_price.py::test_today_price", "quick_test_dynamic_price.py::test_yesterday_price", "test_dynamic_price.py::test_data_structure", "test_dynamic_price.py::test_multiple_regions", "test_dynamic_price.py::test_today_price", "test_dynamic_price.py::test_yesterday_price", "test_dynamic_price_fetch_detailed.py::TestDynamicPriceFetch::test_dynamic_price_fetch_no_data", "test_dynamic_price_fetch_detailed.py::TestDynamicPriceFetch::test_format_electricity_price_data", "test_dynamic_price_fetch_retry.py::TestDynamicPriceFetchRetry::test_send_electricity_price_with_retry_all_attempts_fail", "test_dynamic_price_fetch_retry.py::TestDynamicPriceFetchRetry::test_send_electricity_price_with_retry_exception_handling", "test_dynamic_price_fetch_retry.py::TestDynamicPriceFetchRetry::test_send_electricity_price_with_retry_success_after_retries", "test_dynamic_price_fetch_retry.py::TestDynamicPriceFetchRetry::test_send_electricity_price_with_retry_success_first_attempt", "test_energy_cloud_api.py::TestEnergyCloudAPI::test_class_attributes", "test_energy_cloud_api.py::TestEnergyCloudAPI::test_generate_idempotency_key_consistency", "test_energy_cloud_api.py::TestEnergyCloudAPI::test_send_electricity_price_curve_api_error", "test_energy_cloud_api.py::TestEnergyCloudAPI::test_send_electricity_price_curve_discontinuous", "test_energy_cloud_api.py::TestEnergyCloudAPI::test_send_electricity_price_curve_invalid_duration", "test_energy_cloud_api.py::TestEnergyCloudAPI::test_send_electricity_price_curve_missing_biz_seq", "test_energy_cloud_api.py::TestEnergyCloudAPI::test_send_electricity_price_curve_missing_electricity_price", "test_energy_cloud_api.py::TestEnergyCloudAPI::test_send_electricity_price_curve_success", "test_energy_cloud_api.py::TestEnergyCloudAPI::test_send_power_predict_curves_api_error", "test_energy_cloud_api.py::TestEnergyCloudAPI::test_send_power_predict_curves_different_start_times_scenario", "test_energy_cloud_api.py::TestEnergyCloudAPI::test_send_power_predict_curves_invalid_points", "test_energy_cloud_api.py::TestEnergyCloudAPI::test_send_power_predict_curves_missing_start_time", "test_energy_cloud_api.py::TestEnergyCloudAPI::test_send_power_predict_curves_no_curves", "test_energy_cloud_api.py::TestEnergyCloudAPI::test_send_power_predict_curves_optional_parameters", "test_energy_cloud_api.py::TestEnergyCloudAPI::test_send_power_predict_curves_partial_curves", "test_energy_cloud_api.py::TestEnergyCloudAPI::test_send_power_predict_curves_success", "test_storage_dispatch.py::TestStorageDispatch::test_algorithm_exception", "test_storage_dispatch.py::TestStorageDispatch::test_database_connection_failure", "test_storage_dispatch.py::TestStorageDispatch::test_different_trigger_values", "test_storage_dispatch.py::TestStorageDispatch::test_edge_case_soc_values", "test_storage_dispatch.py::TestStorageDispatch::test_empty_price_list", "test_storage_dispatch.py::TestStorageDispatch::test_invalid_json_in_hybrid_load", "test_storage_dispatch.py::TestStorageDispatch::test_invalid_pile_rated_power", "test_storage_dispatch.py::TestStorageDispatch::test_invalid_price_list_length", "test_storage_dispatch.py::TestStorageDispatch::test_missing_optional_data", "test_storage_dispatch.py::TestStorageDispatch::test_no_es_realtime_data", "test_storage_dispatch.py::TestStorageDispatch::test_no_hybrid_load_data", "test_storage_dispatch.py::TestStorageDispatch::test_no_pile_data", "test_storage_dispatch.py::TestStorageDispatch::test_no_site_data", "test_storage_dispatch.py::TestStorageDispatch::test_save_task_exception", "test_storage_dispatch.py::TestStorageDispatch::test_storage_dispatch_db_operate_exception", "test_storage_dispatch.py::TestStorageDispatch::test_storage_dispatch_error_logging", "test_storage_dispatch.py::TestStorageDispatch::test_storage_dispatch_invalid_price_list_length", "test_storage_dispatch.py::TestStorageDispatch::test_storage_dispatch_logging", "test_storage_dispatch.py::TestStorageDispatch::test_storage_dispatch_no_es_realtime_data", "test_storage_dispatch.py::TestStorageDispatch::test_storage_dispatch_no_hybrid_load_data", "test_storage_dispatch.py::TestStorageDispatch::test_storage_dispatch_no_pile_data", "test_storage_dispatch.py::TestStorageDispatch::test_storage_dispatch_no_site_data", "test_storage_dispatch.py::TestStorageDispatch::test_storage_dispatch_optimization_exception", "test_storage_dispatch.py::TestStorageDispatch::test_storage_dispatch_parameter_validation", "test_storage_dispatch.py::TestStorageDispatch::test_storage_dispatch_return_value_structure", "test_storage_dispatch.py::TestStorageDispatch::test_storage_dispatch_save_task_exception", "test_storage_dispatch.py::TestStorageDispatch::test_storage_dispatch_success", "test_storage_dispatch.py::TestStorageDispatch::test_storage_dispatch_with_empty_demand_data", "test_storage_dispatch.py::TestStorageDispatch::test_storage_dispatch_with_empty_pv_predicted_list", "test_storage_dispatch.py::TestStorageDispatch::test_storage_dispatch_with_extreme_soc_values", "test_storage_dispatch.py::TestStorageDispatch::test_storage_dispatch_with_invalid_json_in_hybrid_load", "test_storage_dispatch.py::TestStorageDispatch::test_storage_dispatch_with_large_numbers", "test_storage_dispatch.py::TestStorageDispatch::test_storage_dispatch_with_negative_price_list", "test_storage_dispatch.py::TestStorageDispatch::test_storage_dispatch_with_none_trigger", "test_storage_dispatch.py::TestStorageDispatch::test_storage_dispatch_with_zero_rated_power", "test_storage_dispatch.py::TestStorageDispatch::test_successful_dispatch", "test_weather_comprehensive.py::TestWeatherCacheManager::test_cache_clear", "test_weather_comprehensive.py::TestWeatherCacheManager::test_cache_info", "test_weather_comprehensive.py::TestWeatherCacheManager::test_cache_key_generation", "test_weather_comprehensive.py::TestWeatherCacheManager::test_cache_miss_and_hit", "test_weather_comprehensive.py::TestWeatherClass::test_weather_api_call_failure", "test_weather_comprehensive.py::TestWeatherClass::test_weather_api_call_success", "test_weather_comprehensive.py::TestWeatherClass::test_weather_api_exception", "test_weather_comprehensive.py::TestWeatherClass::test_weather_initialization", "test_weather_comprehensive.py::TestWeatherClass::test_weather_initialization_with_formatted_date", "test_weather_comprehensive.py::TestWeatherClass::test_weather_singleton_pattern", "test_weather_comprehensive.py::TestWeatherClass::test_weather_url_generation", "test_weather_comprehensive.py::TestWeatherUtilityFunctions::test_fahrenheit_to_celsius", "test_weather_comprehensive.py::TestWeatherUtilityFunctions::test_formate_date", "test_weather_comprehensive.py::TestWeatherUtilityFunctions::test_formate_date_invalid_input"]