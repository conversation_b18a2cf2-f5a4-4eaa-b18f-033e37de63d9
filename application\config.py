# 预测相关配置
CONFIG = {
    'power_prediction_interval': 10,  # 功率预测刷新间隔，单位秒
    'soc_prediction_interval': 10,  # SOC预测刷新间隔，单位秒
    'power_allocation_interval': 60,  # 功率分配时间间隔，单位秒
    'real_and_predict_power_diff': 20,  # 功率分配差值，当枪的上报功率和预测功率超过该值时触发功率分配算法，单位kw
    'current_and_latest_allocation_power_diff': 5,  # 前后两次功率分配差值超过该值触发分配下发，单位kw
    'process_predict_start_time': 120,  # 过程功率预测开始时间点离充电开始的时间距离
    'power_prediction_history_points': 30,  # 功率预测的历史数据点数量
}


class ConfigInterval:
    SHORT_TERM_LOAD_FORECAST_INTERVAL = 3 * 60  # 短期负载预测 3分钟
    PV_FORECAST_INTERVAL = 15 * 60  # 光伏预测 15分钟
    PV_CONTROL_INTERVAL = 15 * 60  # 光伏控制 15分钟
    LONG_TERM_LOAD_FORECAST_INTERVAL = 24 * 60 * 60  # 长期负载预测 24小时
    DYNAMIC_PRICE_FETCH_INTERVAL = 24 * 60 * 60  # 动态电价获取 24小时
    CHARGE_POWER_DISPATCH_INTERVAL = 60  # 充电桩功率分配 60秒

class TimeBasedTaskConfig:
    """时间点定时任务配置"""
    # 配置需要执行定时任务的时间点（24小时制，格式：HH:MM）
    SCHEDULED_TIME_POINTS = [
        "00:00",  # 午夜：触发各功率预测 + 发送功率预测曲线到能源云
        "02:00",  # 凌晨2点：发送功率预测曲线到能源云
        "04:00",  # 凌晨4点：发送功率预测曲线到能源云
        "06:00",  # 早上6点：发送功率预测曲线到能源云
        "08:00",  # 早上8点：发送功率预测曲线到能源云
        "10:00",  # 上午10点：发送功率预测曲线到能源云
        "12:00",  # 中午12点：发送功率预测曲线到能源云
        "14:00",  # 下午2点：发送功率预测曲线到能源云
        "16:00",  # 下午4点：发送功率预测曲线到能源云
        "18:00",  # 下午6点：发送功率预测曲线到能源云
        "20:00",  # 晚上8点：发送功率预测曲线到能源云
        "22:00",  # 晚上10点：发送功率预测曲线到能源云
    ]
    
    # 特殊任务时间点
    MIDNIGHT_TASKS = ["00:00"]  # 午夜特殊任务
    EVEN_HOUR_TASKS = ["02:00", "04:00", "06:00", "08:00", "10:00", "12:00", 
                       "14:00", "16:00", "18:00", "20:00", "22:00"]  # 偶数小时任务
