# 预测相关配置
CONFIG = {
    'power_prediction_interval': 10,  # 功率预测刷新间隔，单位秒
    'soc_prediction_interval': 10,  # SOC预测刷新间隔，单位秒
    'power_allocation_interval': 60,  # 功率分配时间间隔，单位秒
    'real_and_predict_power_diff': 20,  # 功率分配差值，当枪的上报功率和预测功率超过该值时触发功率分配算法，单位kw
    'current_and_latest_allocation_power_diff': 5,  # 前后两次功率分配差值超过该值触发分配下发，单位kw
    'process_predict_start_time': 120,  # 过程功率预测开始时间点离充电开始的时间距离
    'power_prediction_history_points': 30,  # 功率预测的历史数据点数量
}


class ConfigInterval:
    SHORT_TERM_LOAD_FORECAST_INTERVAL = 3 * 60  # 短期负载预测 3分钟
    PV_FORECAST_INTERVAL = 15 * 60  # 光伏预测 15分钟
    PV_CONTROL_INTERVAL = 15 * 60  # 光伏控制 15分钟
    LONG_TERM_LOAD_FORECAST_INTERVAL = 24 * 60 * 60  # 长期负载预测 24小时
    DYNAMIC_PRICE_FETCH_INTERVAL = 24 * 60 * 60  # 动态电价获取 24小时
    CHARGE_POWER_DISPATCH_INTERVAL = 60  # 充电桩功率分配 60秒
