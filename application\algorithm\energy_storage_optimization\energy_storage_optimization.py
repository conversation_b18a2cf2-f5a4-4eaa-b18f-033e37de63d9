import numpy as np
import time
from scipy.optimize import linprog
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')


class EnergyStorageOptimizer:
    """储能系统充放电功率规划优化器（含需量费优化）"""
    
    def __init__(self):
        self.time_intervals = 96  # 15分钟间隔，24小时共96个点
        self.hour_intervals = 24  # 1小时间隔，24个点
        self.minutes_per_interval = 15  # 每个时间间隔的分钟数
        self.efficiency = 0.95  # 储能系统效率
        self.max_cycles_per_day = 2  # 每日最大充放电循环次数
        self.power_change_penalty = 0.03  # 轻量级功率变化惩罚系数 (元/kW变化)
        
    def validate_inputs(self, inputs: Dict) -> bool:
        """验证输入参数的有效性"""
        required_keys = [
            'start_time', 'pv_predicted_list', 'electricity_purchase_price_list', 
            'es_soc', 'es_total_energy', 'es_rated_power', 'site_grid_limit', 
            'demand_data', 'min_soc', 'max_soc'
        ]
        
        for key in required_keys:
            if key not in inputs:
                raise ValueError(f"缺少必需的输入参数: {key}")
        
        # 验证数据长度
        if len(inputs['pv_predicted_list']) != self.time_intervals:
            raise ValueError(f"光伏预测数据长度应为{self.time_intervals}个点")
        
        if len(inputs['electricity_purchase_price_list']) != self.hour_intervals:
            raise ValueError(f"购电价格数据长度应为{self.hour_intervals}个点")
        
        # 验证售电价格（负载规划需要）
        if inputs.get('electricity_sale_price_list') and len(inputs['electricity_sale_price_list']) != self.hour_intervals:
            raise ValueError(f"售电价格数据长度应为{self.hour_intervals}个点")
        
        if inputs.get('hybrid_load_predicted_list') and len(inputs['hybrid_load_predicted_list']) != self.time_intervals:
            raise ValueError(f"负载预测数据长度应为{self.time_intervals}个点")
        
        # 验证SOC范围
        if not (0 <= inputs['min_soc'] <= inputs['max_soc'] <= 100):
            raise ValueError("SOC范围无效")
            
        if not (inputs['min_soc'] <= inputs['es_soc'] <= inputs['max_soc']):
            raise ValueError("当前SOC超出允许范围")
        
        # 验证储能功率
        if inputs['es_rated_power'] <= 0:
            raise ValueError("储能额定功率必须大于0")
        
        # 验证需量数据
        if not isinstance(inputs['demand_data'], list) or len(inputs['demand_data']) == 0:
            raise ValueError("demand_data应为非空列表")
        
        return True
    
    def parse_time_string(self, time_str: str) -> datetime:
        """解析时间字符串"""
        return datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
    
    def find_demand_periods(self, inputs: Dict) -> List[Dict]:
        """找出需量费计费时间段对应的数据点索引"""
        start_time = self.parse_time_string(inputs['start_time'])
        demand_periods = []
        
        for demand_item in inputs['demand_data']:
            demand_start = self.parse_time_string(demand_item['start_time'])
            demand_end = self.parse_time_string(demand_item['end_time'])
            
            # 计算时间段在96个点中的索引范围
            start_offset = (demand_start - start_time).total_seconds() / 60 / 15  # 15分钟间隔
            end_offset = (demand_end - start_time).total_seconds() / 60 / 15
            
            # 限制在0-95范围内
            start_idx = max(0, int(start_offset))
            end_idx = min(95, int(end_offset))
            
            if start_idx <= end_idx:
                demand_periods.append({
                    'start_idx': start_idx,
                    'end_idx': end_idx,
                    'price': demand_item['price'],
                    'total_demand_target': demand_item['total_demand_target'],
                    'warning_ratio': demand_item.get('target_demand_warning_ratio', 95.0)
                })
        
        return demand_periods
    
    def expand_hourly_to_15min(self, hourly_data: List[float]) -> List[float]:
        """将小时数据扩展为15分钟间隔数据"""
        expanded_data = []
        for value in hourly_data:
            expanded_data.extend([value] * 4)  # 每小时4个15分钟间隔
        return expanded_data
    
    def calculate_power_limits(self, inputs: Dict) -> Tuple[float, float]:
        """计算储能系统充放电功率限制"""
        # 基于储能额定功率和系统限制
        max_charge_power = min(inputs['es_rated_power'], 
                              inputs['site_grid_limit'] * 0.8)  # 保留20%电网容量余量
        
        # 放电功率限制基于需量限制和电网限制的最小值
        demand_periods = self.find_demand_periods(inputs)
        if demand_periods:
            # 使用所有需量时段中最小的限制（最严格的）
            min_demand_target = min(period['total_demand_target'] for period in demand_periods)
            # 同时考虑site_grid_limit和demand_target的最小值
            effective_limit = min(inputs['site_grid_limit'], min_demand_target)
            max_discharge_power = min(inputs['es_rated_power'], 
                                    effective_limit * 0.8)
        else:
            max_discharge_power = min(inputs['es_rated_power'],
                                    inputs['site_grid_limit'] * 0.8)
        
        return max_charge_power, max_discharge_power
    
    def calculate_grid_power_and_cost(self, P_charge: np.ndarray, P_discharge: np.ndarray, 
                                    inputs: Dict) -> Tuple[np.ndarray, float]:
        """计算电网功率和总成本（包含需量费）"""
        # 扩展价格数据到15分钟间隔
        purchase_prices_15min = np.array(self.expand_hourly_to_15min(inputs['electricity_purchase_price_list']))
        
        # 获取光伏和负载数据
        pv_power = np.array(inputs['pv_predicted_list'])
        load_power = np.array(inputs.get('hybrid_load_predicted_list', [0] * self.time_intervals))
        
        # 计算购电功率（只能是正值或0，不考虑售电）
        P_grid_buy = np.maximum(0, load_power + P_charge - pv_power - P_discharge)
        
        # 计算电量费
        energy_cost = 0.0
        for i in range(self.time_intervals):
            energy_cost += P_grid_buy[i] * purchase_prices_15min[i] * (self.minutes_per_interval / 60)
        
        # 计算需量费 - 为每个时间段分别计算
        demand_cost = 0.0
        demand_periods = self.find_demand_periods(inputs)
        
        for period in demand_periods:
            # 在每个需量计费时段内找到最大购电功率
            if period['start_idx'] <= period['end_idx']:
                period_power = P_grid_buy[period['start_idx']:period['end_idx']+1]
                max_demand = np.max(period_power) if len(period_power) > 0 else 0
                demand_cost += max_demand * period['price']
        
        total_cost = energy_cost + demand_cost
        
        return P_grid_buy, total_cost
    
    def setup_optimization_problem(self, inputs: Dict) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """设置线性规划问题（含需量费优化和轻量级功率变化惩罚）"""
        # 获取功率限制
        max_charge_power, max_discharge_power = self.calculate_power_limits(inputs)
        
        # 决策变量：[P_charge[0:96], P_discharge[0:96], demand_peak_1, demand_peak_2, ..., delta_pos[1:96], delta_neg[1:96]]
        # P_charge[0:96]: 充电功率
        # P_discharge[0:96]: 放电功率  
        # demand_peak_i: 每个需量时段的峰值变量
        # delta_pos[1:96]: 正向功率变化量(95个)
        # delta_neg[1:96]: 负向功率变化量(95个)
        demand_periods = self.find_demand_periods(inputs)
        n_demand_vars = len(demand_periods)  # 每个需量时段一个峰值变量
        n_power_change_vars = 2 * (self.time_intervals - 1)  # 95个正向 + 95个负向变化
        n_vars = 2 * self.time_intervals + n_demand_vars + n_power_change_vars
        
        # 扩展价格数据
        purchase_prices_15min = np.array(self.expand_hourly_to_15min(inputs['electricity_purchase_price_list']))
        
        # 获取光伏和负载数据
        pv_power = np.array(inputs['pv_predicted_list'])
        load_power = np.array(inputs.get('hybrid_load_predicted_list', [0] * self.time_intervals))
        
        # 目标函数系数（最小化购电成本+需量费+轻量级功率变化惩罚）
        c = np.zeros(n_vars)
        
        # 电量费系数
        time_factor = self.minutes_per_interval / 60  # 15分钟转小时
        for i in range(self.time_intervals):
            c[i] = purchase_prices_15min[i] * time_factor  # P_charge 增加购电成本
            c[i + self.time_intervals] = -purchase_prices_15min[i] * time_factor  # P_discharge 减少购电成本
        
        # 需量费系数 - 为每个时间段分别设置
        demand_vars_start = 2 * self.time_intervals
        for i, period in enumerate(demand_periods):
            c[demand_vars_start + i] = period['price']  # 每个需量时段的峰值价格
        
        # 轻量级功率变化惩罚系数
        power_change_vars_start = demand_vars_start + n_demand_vars
        for i in range(self.time_intervals - 1):  # 95个时间点的变化
            c[power_change_vars_start + i] = self.power_change_penalty  # delta_pos[i]
            c[power_change_vars_start + (self.time_intervals - 1) + i] = self.power_change_penalty  # delta_neg[i]
        
        # 不等式约束矩阵 Ax <= b
        A_ub = []
        b_ub = []
        
        # 1. 储能功率约束
        for i in range(self.time_intervals):
            # 充电功率限制
            constraint = np.zeros(n_vars)
            constraint[i] = 1  # P_charge[i] <= max_charge_power
            A_ub.append(constraint)
            b_ub.append(max_charge_power)
            
            # 放电功率限制
            constraint = np.zeros(n_vars)
            constraint[i + self.time_intervals] = 1  # P_discharge[i] <= max_discharge_power
            A_ub.append(constraint)
            b_ub.append(max_discharge_power)
        
        # 2. 需量峰值约束 - 为每个时间段分别设置约束
        for period_idx, period in enumerate(demand_periods):
            for i in range(period['start_idx'], period['end_idx'] + 1):
                # 只有在该时间点属于该需量时段时，才设置约束
                # P_grid_buy[i] <= demand_peak_period_idx
                # load[i] + P_charge[i] - pv[i] - P_discharge[i] <= demand_peak_period_idx
                constraint = np.zeros(n_vars)
                constraint[i] = 1  # P_charge[i]
                constraint[i + self.time_intervals] = -1  # P_discharge[i]
                constraint[demand_vars_start + period_idx] = -1  # demand_peak for this period
                A_ub.append(constraint)
                b_ub.append(pv_power[i] - load_power[i])
        
        # 2.1 添加电网容量约束 - 确保购电功率不超过电网限制和需量限制的最小值
        for i in range(self.time_intervals):
            # 找到该时间点对应的需量限制
            demand_target_for_time_i = inputs['site_grid_limit']  # 默认使用电网限制
            for period in demand_periods:
                if period['start_idx'] <= i <= period['end_idx']:
                    demand_target_for_time_i = period['total_demand_target']
                    break
            
            # 购电功率约束: P_grid_buy[i] <= min(site_grid_limit, demand_target_for_time_i)
            # load[i] + P_charge[i] - pv[i] - P_discharge[i] <= min(site_grid_limit, demand_target_for_time_i)
            effective_limit = min(inputs['site_grid_limit'], demand_target_for_time_i)
            constraint = np.zeros(n_vars)
            constraint[i] = 1  # P_charge[i]
            constraint[i + self.time_intervals] = -1  # P_discharge[i]
            A_ub.append(constraint)
            b_ub.append(pv_power[i] - load_power[i] + effective_limit)
        
        # 3. SOC约束
        initial_energy = inputs['es_soc'] / 100 * inputs['es_total_energy']
        min_energy = inputs['min_soc'] / 100 * inputs['es_total_energy']
        max_energy = inputs['max_soc'] / 100 * inputs['es_total_energy']
        
        for i in range(self.time_intervals):
            # SOC上限约束
            constraint = np.zeros(n_vars)
            for j in range(i + 1):
                constraint[j] = self.efficiency * (self.minutes_per_interval / 60)  # 充电增加能量
                constraint[j + self.time_intervals] = -(self.minutes_per_interval / 60)  # 放电减少能量
            A_ub.append(constraint)
            b_ub.append(max_energy - initial_energy)
            
            # SOC下限约束
            constraint = np.zeros(n_vars)
            for j in range(i + 1):
                constraint[j] = -self.efficiency * (self.minutes_per_interval / 60)
                constraint[j + self.time_intervals] = (self.minutes_per_interval / 60)
            A_ub.append(constraint)
            b_ub.append(initial_energy - min_energy)
        
        # 4. 每日循环次数限制
        constraint = np.zeros(n_vars)
        for i in range(self.time_intervals):
            constraint[i] = (self.minutes_per_interval / 60)  # 总充电量
        A_ub.append(constraint)
        b_ub.append(self.max_cycles_per_day * inputs['es_total_energy'] * (inputs['max_soc'] - inputs['min_soc']))
        
        # 5. 轻量级功率变化约束
        for i in range(1, self.time_intervals):  # 从第2个时间点开始(索引1-95)
            # 正向功率变化约束: delta_pos[i-1] >= (P_net[i] - P_net[i-1])
            # 即: P_charge[i] - P_discharge[i] - P_charge[i-1] + P_discharge[i-1] - delta_pos[i-1] <= 0
            constraint = np.zeros(n_vars)
            constraint[i] = 1  # P_charge[i]
            constraint[i + self.time_intervals] = -1  # P_discharge[i]
            constraint[i - 1] = -1  # P_charge[i-1]
            constraint[(i - 1) + self.time_intervals] = 1  # P_discharge[i-1]
            constraint[power_change_vars_start + (i - 1)] = -1  # delta_pos[i-1]
            A_ub.append(constraint)
            b_ub.append(0)
            
            # 负向功率变化约束: delta_neg[i-1] >= (P_net[i-1] - P_net[i])
            # 即: P_charge[i-1] - P_discharge[i-1] - P_charge[i] + P_discharge[i] - delta_neg[i-1] <= 0
            constraint = np.zeros(n_vars)
            constraint[i - 1] = 1  # P_charge[i-1]
            constraint[(i - 1) + self.time_intervals] = -1  # P_discharge[i-1]
            constraint[i] = -1  # P_charge[i]
            constraint[i + self.time_intervals] = 1  # P_discharge[i]
            constraint[power_change_vars_start + (self.time_intervals - 1) + (i - 1)] = -1  # delta_neg[i-1]
            A_ub.append(constraint)
            b_ub.append(0)
        
        # 等式约束：结束时SOC等于开始时SOC
        A_eq = []
        b_eq = []
        
        # 5. SOC回归约束：总的能量变化为0
        constraint = np.zeros(n_vars)
        for i in range(self.time_intervals):
            constraint[i] = self.efficiency * (self.minutes_per_interval / 60)  # 充电增加能量
            constraint[i + self.time_intervals] = -(self.minutes_per_interval / 60)  # 放电减少能量
        A_eq.append(constraint)
        b_eq.append(0)  # 总能量变化为0
        
        # 变量边界（所有变量非负）
        bounds = [(0, None)] * n_vars
        
        return np.array(c), np.array(A_ub), np.array(b_ub), np.array(A_eq), np.array(b_eq), bounds
    
    def solve_optimization(self, inputs: Dict) -> Optional[Dict]:
        """求解优化问题"""
        try:
            c, A_ub, b_ub, A_eq, b_eq, bounds = self.setup_optimization_problem(inputs)
            
            # 使用scipy的线性规划求解器
            result = linprog(c, A_ub=A_ub, b_ub=b_ub, A_eq=A_eq, b_eq=b_eq, bounds=bounds, method='highs')
            
            if not result.success:
                print("Optimization failed:", result.message)
                return None
            
            # 提取解
            x = result.x
            P_charge = x[:self.time_intervals]
            P_discharge = x[self.time_intervals:2*self.time_intervals]
            
            # 计算电网功率和实际成本
            P_grid_buy, actual_cost = self.calculate_grid_power_and_cost(P_charge, P_discharge, inputs)
            
            # 计算储能净功率（正值为充电，负值为放电）
            es_power = P_charge - P_discharge
            
            # 提取需量峰值 - 为每个时间段分别提取
            demand_periods = self.find_demand_periods(inputs)
            n_demand_vars = len(demand_periods)
            demand_peaks = x[2*self.time_intervals:2*self.time_intervals + n_demand_vars] if n_demand_vars > 0 else []
            
            return {
                'es_power': es_power.tolist(),
                'grid_buy': P_grid_buy.tolist(),
                'demand_peaks': demand_peaks.tolist(),
                'objective_value': actual_cost,  # 总成本（电量费+需量费）
                'success': True
            }
            
        except Exception as e:
            print(f"Error occurred during optimization: {e}")
            return None
    
    def create_fallback_strategy(self, inputs: Dict) -> List[float]:
        """创建备用策略（基于价格的简单策略）"""
        purchase_prices_15min = self.expand_hourly_to_15min(inputs['electricity_purchase_price_list'])
        
        # 简单策略：电价低时充电，电价高时放电
        threshold = np.median(purchase_prices_15min)
        max_charge_power, max_discharge_power = self.calculate_power_limits(inputs)
        
        strategy = []
        for i in range(self.time_intervals):
            if purchase_prices_15min[i] < threshold:
                # 低电价时充电
                strategy.append(max_charge_power * 0.3)
            else:
                # 高电价时放电
                strategy.append(-max_discharge_power * 0.5)
        
        return strategy
    
    def optimize_energy_storage(self, inputs: Dict) -> Dict:
        """主优化函数"""
        try:
            # 验证输入
            self.validate_inputs(inputs)
            
            # 1. 执行负载规划
            load_planning_result = self.optimize_load_planning(inputs)
            
            # 2. 使用优化后的负载曲线更新输入
            optimized_inputs = inputs.copy()
            optimized_inputs['hybrid_load_predicted_list'] = load_planning_result['optimized_load_curve']
            
            # 3. 尝试求解储能优化问题
            result = self.solve_optimization(optimized_inputs)
            
            if result and result['success']:
                es_scheduling_strategy = result['es_power']
            else:
                print("Using fallback strategy...")
                es_scheduling_strategy = self.create_fallback_strategy(optimized_inputs)
            
            # 4. 返回结果（包含负载规划信息）
            return {
                'scheduling_time': int(time.time()),
                'es_scheduling_strategy': es_scheduling_strategy,
                'load_planning_result': load_planning_result
            }
            
        except Exception as e:
            print(f"Error occurred during optimization: {e}")
            # 返回保守策略
            return {
                'scheduling_time': int(time.time()),
                'es_scheduling_strategy': [0.0] * self.time_intervals,
                'load_planning_result': {
                    'need_load_planning': False,
                    'optimized_load_curve': inputs.get('hybrid_load_predicted_list', [0.0] * self.time_intervals),
                    'base_load_curve': [0.0] * self.time_intervals,
                    'storage_supplement': [0.0] * self.time_intervals
                }
            }

    def calculate_base_load_curve(self, inputs: Dict) -> List[float]:
        """计算基础负载曲线 base_load = min(hybrid_load, site_grid_limit, demand_target)"""
        hybrid_load = inputs['hybrid_load_predicted_list']
        site_grid_limit = inputs['site_grid_limit']
        
        # 获取需量限制（对每个时间点）
        demand_periods = self.find_demand_periods(inputs)
        
        base_load_curve = []
        for i in range(self.time_intervals):
            # 找到该时间点对应的需量限制
            demand_target = site_grid_limit  # 默认使用电网限制
            for period in demand_periods:
                if period['start_idx'] <= i <= period['end_idx']:
                    demand_target = period['total_demand_target']
                    break
            
            # 计算基础负载：三者中的最小值
            base_load = min(hybrid_load[i], site_grid_limit, demand_target)
            base_load_curve.append(base_load)
        
        return base_load_curve
    
    def calculate_load_deficit(self, inputs: Dict, base_load_curve: List[float]) -> Tuple[List[float], float]:
        """计算负载缺口和总缺口面积"""
        hybrid_load = inputs['hybrid_load_predicted_list']
        
        load_deficit = []
        total_deficit_area = 0
        
        for i in range(self.time_intervals):
            deficit = max(0, hybrid_load[i] - base_load_curve[i])
            load_deficit.append(deficit)
            total_deficit_area += deficit * (self.minutes_per_interval / 60)  # 转换为kWh
        
        return load_deficit, total_deficit_area
    
    def optimize_load_planning(self, inputs: Dict) -> Dict:
        """负载规划优化"""
        # 1. 计算基础负载曲线
        base_load_curve = self.calculate_base_load_curve(inputs)
        
        # 2. 计算负载缺口
        load_deficit, total_deficit_area = self.calculate_load_deficit(inputs, base_load_curve)
        
        # 3. 计算储能柜可用容量
        available_capacity = (inputs['max_soc'] - inputs['min_soc']) / 100 * inputs['es_total_energy']
        
        print(f"Load planning analysis:")
        print(f"Total load deficit: {total_deficit_area:.2f} kWh")
        print(f"Available storage capacity: {available_capacity:.2f} kWh")
        
        # 4. 判断是否需要启动负载规划
        if total_deficit_area <= available_capacity:
            print("No load planning needed - deficit within storage capacity")
            return {
                'need_load_planning': False,
                'optimized_load_curve': inputs['hybrid_load_predicted_list'],
                'base_load_curve': base_load_curve,
                'storage_supplement': [0.0] * self.time_intervals
            }
        
        print("Load planning activated - deficit exceeds storage capacity")
        
        # 5. 设置负载规划优化问题
        try:
            storage_supplement = self.solve_load_planning_optimization(inputs, base_load_curve, load_deficit)
            
            # 6. 计算优化后的负载曲线
            optimized_load_curve = []
            for i in range(self.time_intervals):
                optimized_load_curve.append(base_load_curve[i] + storage_supplement[i])
            
            return {
                'need_load_planning': True,
                'optimized_load_curve': optimized_load_curve,
                'base_load_curve': base_load_curve,
                'storage_supplement': storage_supplement
            }
            
        except Exception as e:
            print(f"Load planning optimization failed: {e}")
            return {
                'need_load_planning': False,
                'optimized_load_curve': inputs['hybrid_load_predicted_list'],
                'base_load_curve': base_load_curve,
                'storage_supplement': [0.0] * self.time_intervals
            }
    
    def solve_load_planning_optimization(self, inputs: Dict, base_load_curve: List[float], 
                                       load_deficit: List[float]) -> List[float]:
        """求解负载规划优化问题"""
        # 检查是否有售电价格数据
        if 'electricity_sale_price_list' not in inputs:
            raise ValueError("负载规划需要售电价格数据 electricity_sale_price_list")
        
        # 使用售电价格进行负载规划优化
        sale_prices_15min = np.array(self.expand_hourly_to_15min(inputs['electricity_sale_price_list']))
        
        # 决策变量：storage_supplement[0:96] - 96个时间点的储能补充量
        n_vars = self.time_intervals
        
        # 目标函数：最大化收益（实际上是最小化负的收益）
        c = np.zeros(n_vars)
        time_factor = self.minutes_per_interval / 60  # 15分钟转小时
        
        for i in range(self.time_intervals):
            # 负的售电价格表示收益（因为linprog是最小化）
            c[i] = -sale_prices_15min[i] * time_factor
        
        # 不等式约束 Ax <= b
        A_ub = []
        b_ub = []
        
        # 1. 每个时间点的储能补充量限制
        for i in range(self.time_intervals):
            # storage_supplement[i] <= min(load_deficit[i], es_rated_power)
            constraint = np.zeros(n_vars)
            constraint[i] = 1
            A_ub.append(constraint)
            b_ub.append(min(load_deficit[i], inputs['es_rated_power']))
        
        # 2. 总容量约束
        # sum(storage_supplement[i] * time_factor) <= available_capacity
        available_capacity = (inputs['max_soc'] - inputs['min_soc']) / 100 * inputs['es_total_energy']
        constraint = np.zeros(n_vars)
        for i in range(self.time_intervals):
            constraint[i] = time_factor
        A_ub.append(constraint)
        b_ub.append(available_capacity)
        
        # 变量边界（所有变量非负）
        bounds = [(0, None)] * n_vars
        
        # 求解优化问题
        result = linprog(c, A_ub=A_ub, b_ub=b_ub, bounds=bounds, method='highs')
        
        if not result.success:
            raise Exception(f"Load planning optimization failed: {result.message}")
        
        return result.x.tolist()


def calculate_detailed_results(inputs, result):
    """计算详细的分析结果（含需量费和负载规划）"""
    strategy = result['es_scheduling_strategy']
    load_planning_result = result.get('load_planning_result', {})
    
    # 使用优化后的负载曲线（如果有负载规划）
    optimized_load_curve = load_planning_result.get('optimized_load_curve', inputs.get('hybrid_load_predicted_list', [0] * 96))
    
    # 扩展价格数据到15分钟间隔
    purchase_prices_15min = []
    for price in inputs['electricity_purchase_price_list']:
        purchase_prices_15min.extend([price] * 4)
    
    # 计算购电功率
    pv_power = inputs['pv_predicted_list']
    load_power = optimized_load_curve
    
    grid_buy_power = []
    for i in range(96):
        P_charge = max(0, strategy[i])  # 充电功率
        P_discharge = max(0, -strategy[i])  # 放电功率
        P_grid_buy = max(0, load_power[i] + P_charge - pv_power[i] - P_discharge)
        grid_buy_power.append(P_grid_buy)
    
    # 计算需量费相关信息 - 为每个时间段分别计算
    demand_info = []
    optimizer = EnergyStorageOptimizer()
    demand_periods = optimizer.find_demand_periods(inputs)
    
    for period in demand_periods:
        # 在该时间段内找到最大需量
        period_grid_power = grid_buy_power[period['start_idx']:period['end_idx']+1]
        max_demand = max(period_grid_power) if period_grid_power else 0
        demand_cost = max_demand * period['price']
        
        demand_info.append({
            'start_idx': period['start_idx'],
            'end_idx': period['end_idx'],
            'max_demand': max_demand,
            'price': period['price'],
            'cost': demand_cost,
            'target': period['total_demand_target'],
            'start_time': inputs['demand_data'][0]['start_time'],  # 简化处理
            'end_time': inputs['demand_data'][0]['end_time']
        })
    
    return {
        'grid_buy_power': grid_buy_power,
        'purchase_prices_15min': purchase_prices_15min,
        'demand_info': demand_info,
        'optimized_load_curve': optimized_load_curve,
        'load_planning_result': load_planning_result
    }


def plot_results(inputs, result):
    """绘制结果图表（含需量费场景）"""
    try:
        import matplotlib.pyplot as plt
        
        # 创建时间轴
        time_points = [i * 15 / 60 for i in range(96)]  # 转换为小时
        
        # 计算详细结果
        detailed = calculate_detailed_results(inputs, result)
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # 1. 光伏发电和储能功率
        ax1.plot(time_points, inputs['pv_predicted_list'], label='PV Generation', color='orange')
        ax1.plot(time_points, result['es_scheduling_strategy'], label='Energy Storage Power', color='blue')
        ax1.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        ax1.set_xlabel('Time (hours)')
        ax1.set_ylabel('Power (kW)')
        ax1.set_title('PV Generation and Energy Storage Power Planning')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 购电价格变化
        ax2.plot(time_points, detailed['purchase_prices_15min'], label='Purchase Price', color='red')
        ax2.set_xlabel('Time (hours)')
        ax2.set_ylabel('Price (yuan/kWh)')
        ax2.set_title('Purchase Price Changes')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. SOC变化和需量限制
        soc_trajectory = []
        current_energy = inputs['es_soc'] / 100 * inputs['es_total_energy']
        for i, power in enumerate(result['es_scheduling_strategy']):
            # 储能功率：正值充电，负值放电
            if power > 0:  # 充电
                energy_change = power * 0.25 * 0.95  # 15分钟 * 效率
            else:  # 放电
                energy_change = power * 0.25  # 15分钟
            current_energy += energy_change
            soc = (current_energy / inputs['es_total_energy']) * 100
            soc_trajectory.append(soc)
        
        ax3.plot(time_points, soc_trajectory, label='SOC Prediction', color='purple')
        ax3.axhline(y=inputs['min_soc'], color='red', linestyle='--', label='Min SOC')
        ax3.axhline(y=inputs['max_soc'], color='red', linestyle='--', label='Max SOC')
        ax3.set_xlabel('Time (hours)')
        ax3.set_ylabel('SOC (%)')
        ax3.set_title('Battery SOC Change Prediction')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 购电功率和需量限制
        load_planning_result = detailed.get('load_planning_result', {})
        
        # 绘制原始负载、优化后负载（去掉基础负载）
        if load_planning_result.get('need_load_planning', False):
            ax4.plot(time_points, inputs['hybrid_load_predicted_list'], label='Original Load', color='gray', linestyle='--')
            ax4.plot(time_points, detailed['optimized_load_curve'], label='Optimized Load', color='red')
        else:
            ax4.plot(time_points, inputs['hybrid_load_predicted_list'], label='Load Demand', color='red')
        
        ax4.plot(time_points, inputs['pv_predicted_list'], label='PV Generation', color='orange')
        ax4.plot(time_points, detailed['grid_buy_power'], label='Grid Purchase Power', color='blue')
        
        # 添加电网容量限制线
        ax4.axhline(y=inputs['site_grid_limit'], color='purple', linestyle='-', linewidth=2, 
                   label=f'Site Grid Limit ({inputs["site_grid_limit"]}kW)')
        
        # 添加需量限制线 - 为每个时间段分别添加
        optimizer = EnergyStorageOptimizer()
        demand_periods = optimizer.find_demand_periods(inputs)
        
        for i, period in enumerate(demand_periods):
            start_time = period['start_idx'] * 15 / 60  # 转换为小时
            end_time = (period['end_idx'] + 1) * 15 / 60  # 转换为小时
            demand_target = period['total_demand_target']
            
            # 在该时间段内绘制需量限制线
            ax4.hlines(y=demand_target, xmin=start_time, xmax=end_time, 
                      colors='red', linestyles=':', linewidth=2, 
                      label=f'Demand Limit Period {i+1} ({demand_target}kW)' if i == 0 else '')
        
        ax4.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        ax4.set_xlabel('Time (hours)')
        ax4.set_ylabel('Power (kW)')
        ax4.set_title('Grid Purchase Power and Limits (with Load Planning)')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('energy_storage_optimization_result.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    except ImportError:
        print("matplotlib not installed, skipping chart generation")


def energy_storage_optimization_runner(inputs: Dict) -> Dict:
    """运行储能优化"""
    optimizer = EnergyStorageOptimizer()
    result = optimizer.optimize_energy_storage(inputs)
    return result


def main():
    """示例使用"""
    # 创建优化器实例
    optimizer = EnergyStorageOptimizer()
    
    # 示例输入数据（新格式，包含需量费）
    # sample_inputs = {
    #     'start_time': '2025-05-21 00:00:00',
    #     'pv_predicted_list': [0,0,0,0, 0,0,0,0, 0,0,0,0, 
    #                           0,0,0,0, 0,0,0,0, 0,0,0,0, 
    #                           2,2,2,2, 2.4,2.4,2.4,2.4, 3.6,3.6,3.6,3.6,
    #                           6,6,6,6, 6.4,6.4,6.4,6.4, 7.6,7.6,7.6,7.6, 
    #                           8.8,8.8,8.8,8.8, 10,10,10,10, 10.4,10.4,10.4,10.4,
    #                           10,10,10,10, 7.6,7.6,7.6,7.6, 6,6,6,6,
    #                           2.4,2.4,2.4,2.4, 0,0,0,0, 0,0,0,0, 
    #                           0,0,0,0, 0,0,0,0, 0,0,0,0],  # 模拟光伏发电曲线
    #     'hybrid_load_predicted_list': [0,0,0,0, 0,0,0,0, 0,0,0,0, #0-12
    #                                    0,0,0,0, 0,0,0,300, 300,300,300,0, #12-24 
    #                                    320,320,320,340, 240,240,280,280, 280,280,160,160, #24-36 
    #                                    160,160,160,400, 400,400,400,400, 400,600,600,600, #36-48
    #                                    600,600,600,480, 480,480,480,480, 40,50,50,50,     #48-60
    #                                     50,50,50,40, 50,50,50,50, 10,10,40,10,
    #                                     50,50,50,50, 50,250,250,250, 140, 80, 80, 80, 
    #                                     80,80,80,80, 80,80,80,120, 120,120,120,120],
    #     'electricity_sale_price_list': [1.4, 1.4, 1.4, 1.4, 1.4, 1.4, 1.4, 1.4, 
    #                                     2.06, 2.06, 2.7, 3.18, 2.06, 2.06, 2.7, 3.18, 
    #                                     3.18, 2.7, 2.7, 2.06, 2.06, 2.06, 2.06, 2.06],
    #     'electricity_purchase_price_list': [1.2, 1.1, 1.0, 0.9, 0.8, 0.9, 1.1, 1.3, 
    #                                       1.5, 1.4, 1.3, 1.2, 2.1, 2.2, 2.3, 2.4, 
    #                                        1.6, 1.8, 1.7, 1.5, 1.4, 1.3, 1.2, 1.1],
    #     'es_soc': 50,
    #     'es_total_energy': 210,
    #     'es_rated_power': 100,
    #     'site_grid_limit': 300,
    #     'demand_data': [
    #         {
    #             "start_time": "2025-05-21 00:00:00",
    #             "end_time": "2025-06-21 23:59:00",
    #             "price": 10.875,
    #             "unit": "dollar",
    #             "total_demand_target": 300,
    #             "target_demand_warning_ratio": 95.00
    #         }
    #      ],
    #     'min_soc': 20,
    #     'max_soc': 95
    # }
    sample_inputs = {
        'start_time': '2025-05-21 00:00:00',
        'pv_predicted_list': [0,0,0,0, 0,0,0,0, 0,0,0,0, 
                              0,0,0,0, 0,0,0,0, 0,0,0,0, 
                              2,2,2,2, 2.4,2.4,2.4,2.4, 3.6,3.6,3.6,3.6,
                              6,6,6,6, 6.4,6.4,6.4,6.4, 7.6,7.6,7.6,7.6, 
                              8.8,8.8,8.8,8.8, 10,10,10,10, 10.4,10.4,10.4,10.4,
                              10,10,10,10, 7.6,7.6,7.6,7.6, 6,6,6,6,
                              2.4,2.4,2.4,2.4, 0,0,0,0, 0,0,0,0, 
                              0,0,0,0, 0,0,0,0, 0,0,0,0],  # 模拟光伏发电曲线
        'hybrid_load_predicted_list': [120,120,120,0, 0,0,0,0, 0,0,0,0, 
                                       0,0,0,0, 0,0,0,0, 0,0,0,0, 
                                       120,120,120,240, 240,240,280,280, 280,280,160,160, 
                                       160,160,160,600, 600,600,600,600, 600,600,600,600,  # 增加负载峰值
                                       600,600,600,480, 480,480,480,480, 480,650,650,650,
                                       650,650,650,450, 450,450,450,450, 410,410,410,410,
                                       250,250,250,250, 250,250,250,250, 140, 80, 80, 80, 
                                       80,80,80,80, 80,80,80,120, 120,120,120,120],
        'electricity_purchase_price_list': [1.2, 1.1, 1.0, 0.9, 0.8, 0.9, 1.1, 1.3, 
                                          1.5, 1.4, 1.3, 1.2, 1.1, 1.2, 1.3, 1.4, 
                                          1.6, 1.8, 1.7, 1.5, 1.4, 1.3, 1.2, 1.1],
        'electricity_sale_price_list': [1.4, 1.4, 1.4, 1.4, 1.4, 1.4, 1.4, 1.4, 
                                        2.06, 2.06, 2.7, 3.18, 2.06, 2.06, 2.7, 3.18, 
                                        3.18, 2.7, 2.7, 2.06, 2.06, 2.06, 2.06, 2.06],
        'es_soc': 50,
        'es_total_energy': 210,
        'es_rated_power': 100,
        'site_grid_limit': 800,
        'demand_data': [
            {
                "start_time": "2025-05-21 00:00:00",
                "end_time": "2025-05-22 00:00:00",
                "price": 15.0,
                "unit": "dollar",
                "total_demand_target": 550,  # 提高需量限制
                "target_demand_warning_ratio": 95.00
            }
        ],
        'min_soc': 20,
        'max_soc': 95
    }
    
    # 执行优化
    result = optimizer.optimize_energy_storage(sample_inputs)
    
    print("Energy storage charge/discharge power planning results (with load planning and lightweight power change penalty):")
    print(f"Scheduling time: {result['scheduling_time']}")
    print(f"Power change penalty coefficient: {optimizer.power_change_penalty} yuan/kW (lightweight)")
    print(f"Strategy range: [{min(result['es_scheduling_strategy']):.2f}, {max(result['es_scheduling_strategy']):.2f}] kW")
    
    # 显示负载规划结果
    load_planning_result = result.get('load_planning_result', {})
    if load_planning_result.get('need_load_planning', False):
        print("\nLoad planning results:")
        storage_supplement = load_planning_result['storage_supplement']
        total_supplement = sum(storage_supplement) * 0.25  # 转换为kWh
        print(f"Total storage supplement: {total_supplement:.2f} kWh")
        print(f"Max supplement power: {max(storage_supplement):.2f} kW")
        print("Load planning applied - using optimized load curve for energy storage optimization")
    else:
        print("\nNo load planning needed - proceeding with original load curve")
    
    # 使用优化后的负载曲线进行成本计算
    optimized_load_curve = load_planning_result.get('optimized_load_curve', sample_inputs['hybrid_load_predicted_list'])
    
    # 计算总充电量和总放电量
    strategy = result['es_scheduling_strategy']
    total_charge_energy = sum(max(0, power) for power in strategy) * 0.25  # 15分钟转小时
    total_discharge_energy = sum(abs(min(0, power)) for power in strategy) * 0.25
    print(f"Total charging energy: {total_charge_energy:.2f} kWh")
    print(f"Total discharging energy: {total_discharge_energy:.2f} kWh")
    
    # 计算节省的金额
    # 扩展电价数据到15分钟间隔
    purchase_prices_15min = []
    for price in sample_inputs['electricity_purchase_price_list']:
        purchase_prices_15min.extend([price] * 4)
    
    # 计算无储能系统的成本（使用优化后的负载曲线）
    pv_power = sample_inputs['pv_predicted_list']
    load_power = optimized_load_curve
    
    cost_without_storage = 0
    for i in range(96):
        grid_buy_without_storage = max(0, load_power[i] - pv_power[i])
        cost_without_storage += grid_buy_without_storage * purchase_prices_15min[i] * 0.25
    
    # 计算有储能系统的购电成本
    cost_with_storage = 0
    for i in range(96):
        P_charge = max(0, strategy[i])  # 充电功率
        P_discharge = max(0, -strategy[i])  # 放电功率
        grid_buy_with_storage = max(0, load_power[i] + P_charge - pv_power[i] - P_discharge)
        cost_with_storage += grid_buy_with_storage * purchase_prices_15min[i] * 0.25
    
    # 计算需量费（无储能vs有储能）
    demand_cost_without = 0
    demand_cost_with = 0
    
    # 为每个需量时段分别计算需量费
    demand_periods = optimizer.find_demand_periods(sample_inputs)
    
    for period in demand_periods:
        # 该时间段内的功率数据
        period_indices = range(period['start_idx'], period['end_idx'] + 1)
        
        # 无储能的最大需量（在该时间段内）
        max_demand_without = 0
        for i in period_indices:
            if i < len(load_power):
                demand_without = max(0, load_power[i] - pv_power[i])
                max_demand_without = max(max_demand_without, demand_without)
        demand_cost_without += max_demand_without * period['price']
        
        # 有储能的最大需量（在该时间段内）
        max_demand_with = 0
        for i in period_indices:
            if i < len(strategy):
                P_charge = max(0, strategy[i])
                P_discharge = max(0, -strategy[i])
                grid_buy = max(0, load_power[i] + P_charge - pv_power[i] - P_discharge)
                max_demand_with = max(max_demand_with, grid_buy)
        demand_cost_with += max_demand_with * period['price']
    
    total_cost_without = cost_without_storage + demand_cost_without
    total_cost_with = cost_with_storage + demand_cost_with
    money_saved = total_cost_without - total_cost_with
    
    print(f"Cost without storage: {total_cost_without:.2f} yuan")
    print(f"Cost with storage: {total_cost_with:.2f} yuan")
    print(f"Money saved: {money_saved:.2f} yuan ({money_saved/total_cost_without*100:.1f}% savings)")
    
    plot_results(sample_inputs, result)


if __name__ == "__main__":
    main() 