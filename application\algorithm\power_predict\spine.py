from scipy.interpolate import CubicSpline
from scipy.interpolate import interp1d
import numpy as np
import matplotlib.pyplot as plt
import pdb

#params:
#x_array:has 4 x coordinates,like np.array([24,44,90,100])
#y_array:has 4 y coordinates,like np.array([34,74,44,10])
def spine(x_array,y_array):
    #cs = CubicSpline(x_array,y_array)
    cs = interp1d(x_array,y_array,kind='quadratic')
    startSoc = x_array[0]
    soc_list=[i/10.0 for i in range(int(startSoc*10),1000)]# to be the same as plotSoc.py
    x_smooth = np.array(soc_list)
    #x_smooth = np.linspace(0,100,1000)
    y_smooth = cs(x_smooth)

    #clip it
    max_val = np.max(y_array)
    min_val = np.min(y_array)
    y_smooth = np.clip(y_smooth,min_val,max_val)
    return x_smooth,y_smooth



'''
# 控制点
x = np.array([24, 44, 90, 100])
y = np.array([34, 74, 40, 10])
x_smooth,y_smooth = spine(x,y)

'''
'''
# 生成三次样条对象
cs = CubicSpline(x, y)  # 默认使用自然边界条件（二阶导为0）
x_smooth = np.linspace(0, 100, 100)  # 生成100个插值点
y_smooth = cs(x_smooth)
'''
'''
# 绘图
plt.scatter(x, y, color='red', label='控制点')
plt.plot(x_smooth, y_smooth, 'b-', label='三次样条曲线')
plt.legend()
plt.grid(True)
plt.title('三次样条插值示例')
plt.show()

'''
