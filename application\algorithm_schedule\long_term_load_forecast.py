import json
from datetime import datetime, timedelta
import traceback
from typing import Dict,  Optional
from application.algorithm.load_prediction.prediction_model_extended import predict_from_input
from application.db_operate.models import SiteLongTermLoadPredictionDB
from application.utils.get_input_data import  get_country_code_from_coordinates, get_site_basic_info, get_site_realtime_data, get_solar_radiation_data, get_time_field, get_weather_forecast, is_holiday, save_prediction_to_db
from application.utils.logger import setup_logger

INPUT_DATA_FILE = 'long_term_load_input_data.json' # 输入数据文件，为空则不生成

logger = setup_logger("long_term_load_forecast", direction="algorithm_schedule")

def get_long_term_load_input_data(site_no: str, current_time: datetime) -> Dict:
    """
    统一获取长期负载预测所需的所有输入数据，采用标准格式
    
    Args:
        site_no: 场站编号
        current_time: 当前时间（预测起始点）
    
    Returns:
        Dict: 完整的标准格式input_data，可直接用于预测模型
    """
    try:
        logger.info(f"开始获取场站{site_no}的标准格式输入数据")
        
        # 定义数据帧数
        data_frames = 97
        
        # 获取场站基本信息（包含坐标）
        site_info = get_site_basic_info(site_no)
        # 直接使用解析好的坐标
        latitude, longitude = site_info['latitude'], site_info['longitude']
        
        # 获取天气相关数据（扩展到过去48小时以支持96个滞后特征）
        start_time = current_time  
        end_time = current_time - timedelta(hours=25) # 扩展到25小时
        start_date = start_time.strftime('%Y-%m-%d')
        end_date = end_time.strftime('%Y-%m-%d')
        
        # 并行获取天气数据源
        basic_weather = get_weather_forecast(latitude, longitude, start_date, end_date)
        solar_data = get_solar_radiation_data(latitude, longitude, start_date, end_date)
        sample_realtime_data = get_site_realtime_data(site_no, current_time, data_frames)

        # 构建标准格式的input_data结构
        input_data = {
            "station_name": site_no,
            "station_id": site_no,
            "location": [latitude, longitude],
            "time_zone": site_info['time_zone'],
            "current_time": current_time.strftime("%Y-%m-%d %H:%M:%S"),
            "fields": [
                "Timestamp", "grid_power", "grid_supply", "power", "charging_consumption",
                "number_of_chargers", "active_chargers", "occupied_chargers",
                "available_chargers", "chargers_under_maintenance", "electricity_pricing",
                "outdoor_dry_bulb_temperature", "outdoor_relative_humidity",
                "diffuse_solar_irradiance", "direct_solar_irradiance", "holiday"
            ],
            "time_series": []
        }
        
        # 构建过去48小时的97个数据点（优化：避免循环中的数据库查询）
        # 根据坐标推断国家代码用于节假日检查
        country_code = get_country_code_from_coordinates(latitude, longitude)
        
        for i in range(data_frames):
            # 计算历史时间点（从48小时前到现在）
            timestamp = current_time - timedelta(minutes=15 * (data_frames - 1 - i))
            timestamp_str = timestamp.strftime("%Y-%m-%d %H:%M:%S")
            
            # 检查是否为节假日
            is_holiday_flag = is_holiday(timestamp, country_code)
            
            # 获取当前时间点的数据
            weather_idx = i % len(basic_weather) if basic_weather else 0
            solar_idx = i % len(solar_data) if solar_data else 0
            
            if basic_weather and solar_data:
                basic = basic_weather[weather_idx]
                solar = solar_data[solar_idx]
                
                # 使用预先获取的实时数据（不再在循环中查询数据库）
                # 现在realtime_data是数组，需要取对应索引的值
                realtime_idx = min(i, len(sample_realtime_data['grid_power']) - 1)
                  
                # 构建标准格式的时间序列数据行（使用预获取的运行数据）
                row = [
                    timestamp_str,                    # timestamp
                    sample_realtime_data['grid_power'][realtime_idx],      # grid_power (光伏发电功率)
                    sample_realtime_data['grid_supply'][realtime_idx],     # grid_supply (电网供电功率)
                    sample_realtime_data['power'][realtime_idx],           # power (总功率)
                    sample_realtime_data['charging_consumption'][realtime_idx],  # charging_consumption (充电消耗)
                    sample_realtime_data['number_of_chargers'][realtime_idx],     # number_of_chargers (充电枪数量)
                    sample_realtime_data['active_chargers'][realtime_idx],        # active_chargers (活跃充电枪数)
                    sample_realtime_data['occupied_chargers'][realtime_idx],      # occupied_chargers (占用充电枪数)
                    sample_realtime_data['available_chargers'][realtime_idx],     # available_chargers (可用充电枪数)
                    sample_realtime_data['chargers_under_maintenance'][realtime_idx],  # chargers_under_maintenance (维护中充电枪数)
                    sample_realtime_data['electricity_pricing'][realtime_idx],    # electricity_pricing (电价)
                    basic[2],                        # outdoor_dry_bulb_temperature (气温)
                    basic[3],                        # outdoor_relative_humidity (相对湿度)
                    solar.get('dhi', 0.0),           # diffuse_solar_irradiance (散射辐射)
                    solar.get('dni', 0.0),           # direct_solar_irradiance (直射辐射)
                    is_holiday_flag                  # holiday (根据实际日期和国家检查)
                ]
            else:
                # 使用默认值和预获取的实时数据
                realtime_idx = min(i, len(sample_realtime_data['grid_power']) - 1)
                
                row = [
                    timestamp_str,                    # timestamp
                    sample_realtime_data['grid_power'][realtime_idx],      # grid_power (光伏发电功率)
                    sample_realtime_data['grid_supply'][realtime_idx],     # grid_supply (电网供电功率)
                    sample_realtime_data['power'][realtime_idx],           # power (总功率)
                    sample_realtime_data['charging_consumption'][realtime_idx],  # charging_consumption (充电消耗)
                    sample_realtime_data['number_of_chargers'][realtime_idx],     # number_of_chargers (充电枪数量)
                    sample_realtime_data['active_chargers'][realtime_idx],        # active_chargers (活跃充电枪数)
                    sample_realtime_data['occupied_chargers'][realtime_idx],      # occupied_chargers (占用充电枪数)
                    sample_realtime_data['available_chargers'][realtime_idx],     # available_chargers (可用充电枪数)
                    sample_realtime_data['chargers_under_maintenance'][realtime_idx],  # chargers_under_maintenance (维护中充电枪数)
                    sample_realtime_data['electricity_pricing'][realtime_idx],    # electricity_pricing (电价)
                    25.0,                            # outdoor_dry_bulb_temperature (默认气温)
                    50.0,                            # outdoor_relative_humidity (默认湿度)
                    0.0,                             # diffuse_solar_irradiance (默认散射辐射)
                    0.0,                             # direct_solar_irradiance (默认直射辐射)
                    is_holiday_flag                  # holiday (根据实际日期和国家检查)
                ]
            
            input_data["time_series"].append(row)
        
        # json.dump(input_data, open('input_data0.json', 'w'), indent=4) # 保存输入数据到文件
        logger.info(f"场站{site_no}标准格式输入数据构建完成 - 共{len(input_data['time_series'])}行数据")
        return input_data
        
    except Exception as e:
        logger.error(f"获取场站{site_no}标准格式输入数据失败: {e}")
        # 返回默认数据
        logger.warning(f"使用默认数据构建标准格式input_data")
        
        data_frames = 96  # 确保在异常处理中也定义了data_frames
        
        input_data = {
            "station_name": site_no,
            "station_id": site_no,
            "location": [52.19340694027317, 5.430548227543284],
            "time_zone": "UTC",
            "current_time": current_time.strftime("%Y-%m-%d %H:%M:%S"),
            "fields": [
                "Timestamp", "grid_power", "grid_supply", "power", "charging_consumption",
                "number_of_chargers", "active_chargers", "occupied_chargers",
                "available_chargers", "chargers_under_maintenance", "electricity_pricing",
                "outdoor_dry_bulb_temperature", "outdoor_relative_humidity",
                "diffuse_solar_irradiance", "direct_solar_irradiance", "holiday"
            ],
            "time_series": []
        }
        
        # 生成默认的97行数据
        for i in range(data_frames):
            timestamp = current_time - timedelta(minutes=15 * (data_frames - 1 - i))
            timestamp_str = timestamp.strftime("%Y-%m-%d %H:%M:%S")
            row = [
                timestamp_str,    # timestamp
                0.0,             # grid_power
                0.0,             # grid_supply
                0.0,             # power
                0.0,             # charging_consumption
                1,               # number_of_chargers
                0,               # active_chargers
                0,               # occupied_chargers
                1,               # available_chargers
                0,               # chargers_under_maintenance
                0.15,            # electricity_pricing
                25.0,            # outdoor_dry_bulb_temperature
                50.0,            # outdoor_relative_humidity
                0.0,             # diffuse_solar_irradiance
                0.0,             # direct_solar_irradiance
                False            # holiday
            ]
            input_data["time_series"].append(row)
        
        return input_data


def long_term_load_forecast(site_no: str, data: Dict) -> Optional[Dict]:
    """
    长期负载预测算法调度入口
    :param site_no: 场站编号
    :param data: 相关输入数据
    :return: 预测结果
    """
    try:
        logger.info(f"开始执行长期负载预测算法 - 场站: {site_no}")
        try:
            # 获取当前时间（预测起始点）
            current_time = get_time_field(datetime.now())
            # 获取完整的input_data
            input_data = get_long_term_load_input_data(site_no, current_time)
            # 测试专用
            # if INPUT_DATA_FILE:
            #     if False: # 两不兼容
            #         json.dump(input_data, open(INPUT_DATA_FILE, 'w'), indent=4) # 保存输入数据到文件
            #     else:
            #         with open(INPUT_DATA_FILE, 'r') as f: # 使用本地输入数据
            #             input_data = json.load(f)
            # 执行长期负载预测
            predicted_power_list = predict_from_input(input_data)
            if not predicted_power_list or len(predicted_power_list) == 0:
                logger.warning(f"预测结果为空，将返回零值")
                predicted_power_list = [0.0] * 96
            max_power = max(predicted_power_list)
            logger.info(f"长期负载预测完成 - 场站: {site_no}, 预测峰值功率: {max_power:.2f}kW")
        except Exception as model_error:
            logger.error(f"预测模型调用失败，将返回零值: {model_error}")
            traceback.print_exc()
            predicted_power_list = [0.0] * 96
        
        # 保存预测结果并返回
        save_prediction_to_db(site_no, predicted_power_list, SiteLongTermLoadPredictionDB)
        result = {
            'predicted_time': int(current_time.timestamp()),
            'long_term_load_predicted_list': predicted_power_list
        }      
        return result
        
    except Exception as e:
        logger.error(f"长期负载预测算法执行失败: {e}")
        traceback.print_exc()
        return None


if __name__ == "__main__":
    # 测试及Debug时，需要首先执行测试数据生成脚本，请按顺序执行
    # 1. ems/application/db_operate/sql_scripts/long_term_load_forecast_0.py, 首先生成一个sql脚本，然后执行对应sql脚本，在数据库中生成测试数据
    # 2. ems/application/db_operate/sql_scripts/long_term_load_forecast_1.sql，在数据库中生成测试数据
    # 3. ems/application/db_operate/sql_scripts/long_term_load_forecast_2.sql，在数据库中生成测试数据
    # 4. 执行本文件，查看输出结果进行Debug及测试
    ret = long_term_load_forecast("SITE_002", {})
    print(f'len(ret):{len(ret["long_term_load_predicted_list"])}; ret: {ret}')