#############################################################################
# Copyright (C) 2023 AUTEL Netherlands B.V. - All Rights Reserved
# Unauthorized copying of this file, via any medium is strictly prohibited
#
# Proprietary and confidential
#############################################################################
import argparse
import os
import yaml
from datetime import datetime
from ml import Classifier, SUPPORTED_CLASSIFIERS, select_classifier
from application.algorithm.vehicle_identification.utils import load_and_analyze_data

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Train the prediciton model')
    parser.add_argument('--algo', choices=[k for k, v in SUPPORTED_CLASSIFIERS.items()], default='dtree',
                        help='select used algorithm')
    parser.add_argument('--train-dataset', required=True, help='training dataset')
    parser.add_argument('--test-dataset', required=True, help='testing dataset')
    parser.add_argument('--save-dir', default="./", help='save directory')
    parser.add_argument('--config', required=True, help='configure parameters')
    parser.add_argument('--epochs', default=3, help='get best model')
    args = parser.parse_args()

    save_dir = os.path.join(args.save_dir, datetime.now().strftime('%Y-%m-%d_%H-%M-%S'))
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    with open(args.config, 'r') as f:
        config = yaml.safe_load(f)

    x_train, y_train = load_and_analyze_data(args.train_dataset, save_dir, config, flag="train")
    x_test, y_test = load_and_analyze_data(args.test_dataset, save_dir, config, flag="test")

    classifier: Classifier = select_classifier(args.algo)
    metrics_file = os.path.join(save_dir, '{}_metrics.txt'.format(classifier.__class__.__name__))
    label_analysis_file = os.path.join(save_dir, '{}.json'.format(config['label_analysis_file']))
    with open(metrics_file, 'w') as f:
        string = "accuracy   recall   precision   f1   fpr   fnr\n"
        for epoch in range(int(args.epochs)):
            train_acc = classifier.train(x_train, y_train, save_dir, config)
            test_metrics = classifier.test(x_test, y_test, label_analysis_file, save_dir, epoch)
            print(f'train acc: {train_acc}')
            print(f'test metrics:\n {test_metrics}')
            string += test_metrics
            classifier.save(epoch)
        
        f.write(string)
