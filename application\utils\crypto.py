import os
from pathlib import Path

from cryptography.fernet import Fernet


class ConfigEncryptor:
    def __init__(self, key: str = None):
        """初始化加密器
        :param key: 密钥, 如果为None则使用环境变量中的密钥, 如果环境变量中也没有密钥, 则生成新的密钥
        """
        if not key:
            self.key = self._load_key()
        else:
            self.key = key

    def _load_key(self):
        """加载或生成密钥"""
        # 从环境变量获取密钥
        key_from_env = os.getenv('CONFIG_ENCRYPTION_KEY')
        if key_from_env:
            return key_from_env
        
        # 生成新密钥
        new_key = Fernet.generate_key()
        return new_key

    def encrypt_config(self, config_path, encrypted_path=None):
        """
        加密配置文件
        :param config_path: 原始配置文件路径
        :param encrypted_path: 加密后的文件路径，如果为None则使用原路径+.enc后缀
        """
        if encrypted_path is None:
            if isinstance(config_path, Path):
                encrypted_path = config_path.with_suffix(config_path.suffix + '.enc')
            else:
                encrypted_path = config_path + '.enc'

        # 读取原始配置
        with open(config_path, 'r') as f:
            config_data = f.read()

        # 加密数据
        f = Fernet(self.key)
        encrypted_data = f.encrypt(config_data.encode())

        # 保存加密后的数据
        with open(encrypted_path, 'wb') as f:
            f.write(encrypted_data)

        return encrypted_path

    def decrypt_config(self, encrypted_path, decrypted_path=None):
        """
        解密配置文件
        :param encrypted_path: 加密的配置文件路径
        :param decrypted_path: 解密后的文件路径，如果为None则使用原路径去掉.enc后缀
        """
        if decrypted_path is None:
            decrypted_path = encrypted_path[:-4] if encrypted_path.endswith('.enc') else encrypted_path

        # 读取加密数据
        with open(encrypted_path, 'rb') as f:
            encrypted_data = f.read()

        # 解密数据
        f = Fernet(self.key)
        decrypted_data = f.decrypt(encrypted_data)

        # 保存解密后的数据
        with open(decrypted_path, 'wb') as f:
            f.write(decrypted_data)

        return decrypted_path

    def get_decrypted_config(self, encrypted_path):
        """
        直接获取解密后的配置内容
        :param encrypted_path: 加密的配置文件路径
        :return: 解密后的配置内容
        """
        with open(encrypted_path, 'rb') as f:
            encrypted_data = f.read()

        f = Fernet(self.key)
        return f.decrypt(encrypted_data).decode()


if __name__ == "__main__":
    encryptor = ConfigEncryptor()
    encrypted_path = encryptor.encrypt_config("settings/prod.yaml")
    print(encrypted_path)
    decrypted_path = encryptor.decrypt_config(encrypted_path)
    print(decrypted_path)
    config_data = encryptor.get_decrypted_config(encrypted_path)
    print(config_data)
