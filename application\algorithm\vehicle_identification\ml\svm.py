#############################################################################
# Copyright (C) 2023 AUTEL Netherlands B.V. - All Rights Reserved
# Unauthorized copying of this file, via any medium is strictly prohibited
#
# Proprietary and confidential
#############################################################################
from sklearn import svm
from .base import Classifier


class SVMClassifier(Classifier):
    def _algorithm(self):
        return svm.SVC(kernel='linear', probability=True, C=10)
