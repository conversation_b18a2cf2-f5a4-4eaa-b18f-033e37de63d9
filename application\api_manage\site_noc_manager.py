import traceback
from typing import Dict, List

import requests

from application.db_operate.db_operate import DBOperate
from application.utils.constants import ResponseCode, APIEndpoints, DeviceConfig
from application.utils.logger import setup_logger
from application.utils.pile_utils import PileUtils

logger = setup_logger("AI_EMS")

# 获取数据库操作单例
db_operate = DBOperate()


class SiteNOCManager:
    """
    场站运维管理类
    
    该类负责处理场站运维相关的数据和接口，包括：
    1. 场站配置数据的处理
    2. 场站运维数据的订阅和取消订阅
    
    主要功能：
    - 处理场站配置信息
    - 管理场站运维数据流
    """

    def subscribe_site(self, site_no: str, pile_list: List[str]) -> bool:
        """
        订阅场站所有桩的运维kafka消息，并获取所有桩的模块信息并入库
        
        参数:
            site_no (str): 场站编号
            pile_list (List[str]): 充电桩列表
            
        说明:
            1. 检查场站AI状态：
               - 如果场站不存在，创建新记录并开启AI
               - 如果AI未开启，更新为开启状态
               - 如果AI已开启，直接返回
            2. 分离交流桩和直流桩
            3. 交流桩直接保存基础信息，不需要订阅和获取模块信息
            4. 直流桩订阅设备运维数据并获取模块信息
        """
        try:
            if not site_no:
                raise ValueError("场站编号不能为空")

            if not pile_list:
                raise ValueError(f"场站 {site_no} 未提供充电桩列表")

            # 分离交流桩和直流桩
            ac_piles, dc_piles = PileUtils.separate_pile_types(pile_list)
            
            # 处理交流桩：直接保存基础信息，不需要订阅和获取模块信息
            if ac_piles:
                logger.info(f"场站 {site_no} 检测到 {len(ac_piles)} 个交流桩: {ac_piles}")
                for ac_pile in ac_piles:
                    ac_pile_info = PileUtils.get_ac_pile_info(ac_pile, site_no)
                    if ac_pile_info:
                        # 保存交流桩信息到数据库
                        success = db_operate.save_pile_data(ac_pile_info, site_no)
                        if success:
                            logger.info(f"交流桩 {ac_pile} 信息保存成功，额定功率: {ac_pile_info['rated_power']}kW")
                        else:
                            logger.error(f"交流桩 {ac_pile} 信息保存失败")
                    else:
                        logger.error(f"交流桩 {ac_pile} 信息解析失败")

            # 处理直流桩：需要订阅和获取模块信息
            if dc_piles:
                logger.info(f"场站 {site_no} 检测到 {len(dc_piles)} 个直流桩: {dc_piles}")
                
                # TODO 例如第一次传10条桩 后面传了9条 那说明就是只有9条 删除了一条 需要删除数据库中对应的桩

                # TODO 批量订阅设备 运维订阅接口时覆盖逻辑，后续推动修改为场站级别的覆盖，能处理场站新增和减少桩的情况
                if not self.subscribe_piles(dc_piles):
                    logger.error(f"场站 {site_no} 直流桩设备订阅失败")
                    return False

                logger.info(f"批量订阅直流桩成功，订阅的设备编号：{dc_piles}")

                # 批量获取模块信息，传递site_no
                if not self._get_device_module_info_batch(dc_piles, site_no):
                    logger.error(f"场站 {site_no} 获取模块信息失败")
                    return False
            else:
                logger.info(f"场站 {site_no} 没有直流桩，跳过订阅和模块信息获取")

            logger.info(f"场站 {site_no} 桩信息处理完成 - 交流桩: {len(ac_piles)}, 直流桩: {len(dc_piles)}")
            return True

        except Exception as e:
            logger.error(f"订阅场站 {site_no} 失败: {str(e)}")
            raise

    def subscribe_piles(self, pile_sn_list: List[str]) -> bool:
        """
        调用运维平台批量设备订阅接口
        
        参数:
            pile_sn_list (List[str]): 充电桩序列号列表
            
        返回:
            bool: 订阅是否成功
        """
        try:
            topic_name = DeviceConfig.TOPIC_NAME

            payload = {
                "subType": 1,
                "topicName": topic_name,
                "subList": [{
                    "sn": pile_sn,
                    "bomCode": DeviceConfig.N1_BOM_CODES if pile_sn[2] == '7' else DeviceConfig.DC_FAST_BOM_CODES,
                    "eventCode": DeviceConfig.EVENT_CODES,
                    "isSub": True
                } for pile_sn in pile_sn_list]
            }
            response = requests.post(APIEndpoints.SUBSCRIBE, json=payload)

            if response.status_code == ResponseCode.SUCCESS:
                result = response.json()
                if result.get("code") == ResponseCode.SUCCESS:
                    logger.info(f"设备批量订阅成功: {pile_sn_list}")
                    return True

            logger.error(f"设备批量订阅失败: {response.text}")
            return False

        except Exception as e:
            logger.error(f"调用设备订阅接口失败: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def _get_device_module_info_batch(self, pile_sn_list: List[str], site_no: str) -> bool:
        """获取充电桩模块信息"""
        try:
            response = requests.post(APIEndpoints.CHARGE_MODULE, json=pile_sn_list)
            return self._handle_module_response_batch(response, pile_sn_list, site_no)

        except Exception as e:
            logger.error(f"获取充电桩模块信息失败: {str(e)}")
            print(traceback.format_exc())
            return False

    def _handle_module_response_batch(self, response, pile_sn_list: List[str], site_no: str) -> bool:
        """批量处理模块信息响应"""
        try:
            if response.status_code == ResponseCode.SUCCESS and response.json().get("code") == ResponseCode.SUCCESS:
                device_data_list = response.json().get("data", [])
                # 检查数据列表是否为空
                if not device_data_list:
                    logger.warning(f"设备 {pile_sn_list} 未返回模块信息数据")
                    return False

                try:
                    for device_data in device_data_list:
                        pile_sn = device_data.get("sn")
                        if not pile_sn:
                            logger.error("设备数据中缺少sn字段")
                            continue

                        # 保存或更新桩数据，补充site_no
                        if not db_operate.save_pile_data(device_data, site_no):
                            logger.error(f"保存桩数据失败: {pile_sn}")
                            continue

                        module_list = device_data.get("moduleList", [])
                        if not module_list:
                            logger.warning(f"设备 {pile_sn} 模块列表为空")
                            continue
                        # 保存或更新充电枪信息到数据库
                        gun_bus_list = device_data.get("gunBusList", [])
                        gun_max_curr_list = device_data.get("gunMaxCurr", [])
                        self._save_charger_info(gun_bus_list, gun_max_curr_list, pile_sn)

                        # 保存或更新模块信息
                        self._save_pile_module_info(module_list, pile_sn)

                    logger.info(f"批量处理模块信息成功: {pile_sn_list}")
                    return True

                except Exception as e:
                    logger.error(f"数据库操作失败: {str(e)}")
                    logger.error(traceback.format_exc())
                    return False

            logger.error(f"获取模块信息失败: {response.text}")
            return False

        except Exception as e:
            logger.error(f"处理模块信息响应失败: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def _save_charger_info(self, gun_bus_list: List[Dict], gun_max_curr_list: List[Dict], pile_sn: str) -> None:
        """
        保存充电枪信息到数据库
        
        参数:
            gun_bus_list (List[Dict]): 充电枪总线列表，包含gunNo和bomIndex
            gun_max_curr_list (List[Dict]): 充电枪最大电流列表，包含gunNo和maxCurr
            pile_sn (str): 充电桩序列号
            
        说明:
            该函数会保存以下信息到数据库：
            1. 充电枪与总线的映射关系（bom_index）
            2. 充电枪的最大电流值（max_curr）
            3. 充电枪的基本信息（pile_sn, gun_no）
        """
        # 创建gunNo到maxCurr的映射
        max_curr_map = {
            item['gunNo']: item['maxCurr']
            for item in gun_max_curr_list
        }

        for gun_bus in gun_bus_list:
            gun_no = gun_bus.get("gunNo")
            bom_index = gun_bus.get("bomIndex")
            # 更新或创建充电枪记录
            if gun_no is not None and bom_index is not None:
                charger_sn = f"{pile_sn}_{gun_no}"
                # 获取该枪的最大电流值
                max_curr = max_curr_map.get(gun_no)
                # 更新或创建充电枪记录
                db_operate.update_or_create_charger(charger_sn, bom_index, max_curr)
                logger.info(f"保存充电枪信息: {charger_sn}, bom_index={bom_index}, max_curr={max_curr}")

    def _calculate_total_power(self, module_list, pile_sn):
        """计算总功率"""
        total_power = 0
        for module in module_list:
            unit_power = module.get("unitPower", 0)
            unit_num = module.get("unitNum", 0)
            module_power = unit_power * unit_num
            total_power += module_power
            logger.info(f"模块功率: unitPower={unit_power}, unitNum={unit_num}, module_power={module_power}")
        logger.info(f"设备 {pile_sn} 计算得到的总功率: {total_power}")
        return total_power

    def _save_pile_module_info(self, module_list: List[Dict], pile_sn: str) -> None:
        """
        保存模块信息
        
        参数:
            module_list (List[Dict]): 模块信息列表
            pile_sn (str): 充电桩序列号
        """
        try:
            # 删除现有模块信息
            if not db_operate.delete_pile_module_info(pile_sn):
                logger.error(f"删除充电桩 {pile_sn} 的模块信息失败")
                return

            # 准备批量插入的数据
            modules_to_insert = []
            for module in module_list:
                module_data = {
                    'pile_sn': pile_sn,
                    'type': module.get('type', 'unknown'),
                    'module_no': module.get('moduleNo'),
                    'unit_power': module.get('unitPower', 0),
                    'unit_num': module.get('unitNum', 0)
                }
                modules_to_insert.append(module_data)

            # 批量插入新的模块信息
            if not db_operate.insert_pile_module_info(modules_to_insert):
                logger.error(f"批量插入充电桩 {pile_sn} 的模块信息失败")
                return

            logger.info(f"充电桩 {pile_sn} 的模块信息保存成功")

        except Exception as e:
            logger.error(f"保存充电桩 {pile_sn} 的模块信息失败: {str(e)}")
            logger.error(traceback.format_exc())

    def unsubscribe_site(self, site_no):
        """
        取消订阅场站运维数据

        参数:
            site_no (str): 场站编号，用于标识要取消订阅的场站

        说明:
            取消对该场站所有桩的运维数据订阅
        """
        try:
            # 1. 查找所有AI已开启的场站
            active_sites = db_operate.get_active_ai_sites()  # 返回所有is_ai_active=1的site_no列表
            # 2. 移除要取消的场站
            if site_no in active_sites:
                active_sites.remove(site_no)
            # 3. 查找这些场站下所有桩
            pile_list = db_operate.get_piles_by_sites(active_sites)  # 返回所有桩号列表
            # 4. 重新调用订阅接口
            return self.subscribe_piles(pile_list)
        except Exception as e:
            logger.error(f"Error in unsubscribe_site for {site_no}: {str(e)}")
