#############################################################################
# Copyright (C) 2023 AUTEL Netherlands B.V. - All Rights Reserved
# Unauthorized copying of this file, via any medium is strictly prohibited
#
# Proprietary and confidential
#############################################################################
import xgboost as xgb
from .base import Classifier


class XGBoostClassifier(Classifier):
    def _algorithm(self):
        return xgb.XGBClassifier(objective='multi:softmax')