#############################################################################
# Copyright (C) 2023 AUTEL Netherlands B.V. - All Rights Reserved
# Unauthorized copying of this file, via any medium is strictly prohibited
#
# Proprietary and confidential
#############################################################################
import os
import pickle
import numpy as np
from application.algorithm.vehicle_identification.lookup_table import capatable2Vehicle<PERSON>abel


def predict(features, start_soc=None, current_soc=None, energy=None):   #  secondary_prediction：用于二次预测的变量: start_soc, current_soc, energy(累积充电电能kWh)
    model = "model/28-04-2025/DecisionTreeClassifier.bin"
    model_path = os.path.join(os.path.dirname(__file__), model)
    if len(features["MACAddress"]) > 6:
        mac_addr = features["MACAddress"][:6]
    else:
        mac_addr = features["MACAddress"]
    data = [[mac_addr, float(features["MaximumCurrent"]), float(features["MaximumVoltage"]),
             float(features["MaximumPower"]), float(features["BatteryCapacity"])]]
    data = np.array(data)
    with open(model_path, 'rb') as f:
        classifier = pickle.load(f)

    car_label = classifier.predict(data)
    car_label = car_label[0]
    if start_soc is not None and current_soc is not None and energy is not None and current_soc-start_soc > 2:    #  至少充电2soc才可以用于估算电池容量
        bat_cap_calculated = energy * 100 / (current_soc - start_soc)
        car_label = capatable2VehicleLabel(car_label, bat_cap_calculated)

    return car_label


if __name__ == '__main__':

    input_param = {
        "MACAddress": "f07f0c06f142",
        "MaximumCurrent": 350.0,
        "MaximumVoltage": 459.0,
        "MaximumPower": 0,
        "BatteryCapacity": 0,
    }

    print(predict(input_param, 34, 64, 20))
