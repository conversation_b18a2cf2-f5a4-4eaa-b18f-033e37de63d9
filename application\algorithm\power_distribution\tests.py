import os
import json
import unittest

from n1_pile_distribution_dp import N1PileBestPowerDistribution
from linear_programming import LinearProgrammingPowerDistributionSolver


class TestCalculateObjectiveValue(unittest.TestCase):
    def setUp(self):
        self.modules_powers = [40, 80, 40, 80, 40, 80, 40, 80]

    def test_perfect_match_single_gun(self):
        """测试单个充电枪完美匹配的情况"""
        gun_demands = [80, 0, 0, 0]  # 第一个枪需要80kW
        gun_distribution = [[2], [], [], []]  # 使用模块2(80kW)

        objective_value = N1PileBestPowerDistribution(gun_demands, self.modules_powers).calculate_objective_value(
            gun_distribution)

        # 完美匹配，偏差应该为0
        self.assertEqual(objective_value, 0.0)

    def test_multiple_guns_perfect_match(self):
        """测试多个充电枪完美匹配的情况"""
        gun_demands = [80, 120, 0, 160]  # 第一个枪80kW，第二个枪120kW
        gun_distribution = [[2], [4, 1], [],
                            [8, 5, 7]]  # 枪0用模块2(80kW)，枪1用模块4+1(80+40=120kW), 枪3用模块8+5+7(80+40+40=160kW)

        objective_value = N1PileBestPowerDistribution(gun_demands, self.modules_powers).calculate_objective_value(
            gun_distribution)

        # 完美匹配，偏差应该为0
        self.assertEqual(objective_value, 0.0)

    def test_single_gun_with_deviation(self):
        """测试单个充电枪有偏差的情况"""
        gun_demands = [75, 0, 0, 0]  # 需要75kW
        gun_distribution = [[2], [], [], []]  # 使用模块2(80kW)，超出需求

        objective_value = N1PileBestPowerDistribution(gun_demands, self.modules_powers).calculate_objective_value(
            gun_distribution)

        # 计算期望偏差: |75/80 - 1| = |0.9375 - 1| = 0.0625
        expected_diff = round(abs(75 / 80 - 1), 5)
        self.assertEqual(objective_value, expected_diff)

    def test_multiple_guns_with_deviation(self):
        """测试多个充电枪都有偏差的情况"""
        gun_demands = [75, 135, 0, 220]
        gun_distribution = [[2], [4], [], [8, 5, 6]]  # 枪0:80kW, 枪1:80kW, 枪3:80+40+80=200kW

        objective_value = N1PileBestPowerDistribution(gun_demands, self.modules_powers).calculate_objective_value(
            gun_distribution)

        expected_diff = round(sum([abs(75 / 80 - 1), abs(135 / 80 - 1), abs(220 / 200 - 1)]) / 3, 5)
        self.assertEqual(objective_value, expected_diff)

    def test_single_module_multiple_power(self):
        """测试单个模块提供多倍功率的情况"""
        gun_demands = [0, 0, 160, 0]  # 第三个枪需要160kW
        gun_distribution = [[], [], [6, 1, 5], []]  # 使用模块6+1+5(80+40+40=160kW)

        objective_value = N1PileBestPowerDistribution(gun_demands, self.modules_powers).calculate_objective_value(
            gun_distribution)

        # 完美匹配，偏差应该为0
        self.assertEqual(objective_value, 0.0)

    def test_edge_case_zero_demand(self):
        """测试边界情况：需求为0的情况（虽然实际不会出现）"""
        gun_demands = [0.1, 0, 0, 0]  # 极小需求
        gun_distribution = [[2], [], [], []]  # 使用模块2(80kW)

        objective_value = N1PileBestPowerDistribution(gun_demands, self.modules_powers).calculate_objective_value(
            gun_distribution)

        # 计算期望偏差: |0.1/80 - 1| = |0.00125 - 1| = 0.99875
        expected_diff = round(abs(0.1 / 80 - 1), 5)
        self.assertEqual(objective_value, expected_diff)

    def test_high_precision_calculation(self):
        """测试高精度计算的情况"""
        gun_demands = [0, 77.777, 0, 0]
        gun_distribution = [[], [2], [], []]  # 使用模块2(80kW)

        objective_value = N1PileBestPowerDistribution(gun_demands, self.modules_powers).calculate_objective_value(
            gun_distribution)

        # 计算期望偏差并保留5位小数
        expected_diff = round(abs(77.777 / 80 - 1), 5)
        self.assertEqual(objective_value, expected_diff)

    def test_custom_cases(self):
        # test 平均绝对偏差
        test_distributions = [
            [[2, 1], [4, 3], [6, 7], [8]],
            [[2, 1], [4, 3], [6, 5], [8, 7]],
            [[2, 1, 7], [4, 3, 5], [6], [8]],
            [[2, 1, 7], [4, 3], [6, 5], [8]],
            [[2, 3, 7], [4, 1, 5], [6], [8]],
            [[2, 1, 3, 7], [4], [6], [8, 5]],
            [[2, 1, 3, 7], [4, 5], [6], [8]]
        ]
        n1_pile = N1PileBestPowerDistribution([140, 180, 90, 70], self.modules_powers)
        for distribution in test_distributions:
            objective_value = n1_pile.calculate_objective_value(distribution)
            print("calculate_objective_value:", distribution, objective_value)


class TestGetAllPossibleGunDistributions(unittest.TestCase):
    def setUp(self):
        self.modules_powers = [40, 80, 40, 80, 40, 80, 40, 80]
        self.directed_connections = {0: 2, 1: 4, 2: 6, 3: 8}

    def test_single_gun_distributions(self):
        """测试单个充电枪的所有可能分配方案"""
        gun_demands = [80, 0, 0, 0]  # 只有第一个枪需要80kW

        n1_pile = N1PileBestPowerDistribution(gun_demands, self.modules_powers, self.directed_connections)
        distributions = n1_pile.get_all_possible_gun_distributions()

        # 应该返回多个分配方案，每个方案都是4个枪的分配列表
        self.assertIsInstance(distributions, list)
        self.assertGreater(len(distributions), 0)

        # 每个分配方案都应该有4个枪的分配
        for distribution in distributions:
            self.assertEqual(len(distribution), 4)
            # 第一个枪应该有分配的模块，其他枪应该为空
            self.assertGreater(len(distribution[0]), 0)
            self.assertEqual(len(distribution[1]), 0)
            self.assertEqual(len(distribution[2]), 0)
            self.assertEqual(len(distribution[3]), 0)

    def test_two_guns_distributions(self):
        """测试两个充电枪的所有可能分配方案"""
        gun_demands = [80, 120, 0, 0]  # 前两个枪需要功率

        n1_pile = N1PileBestPowerDistribution(gun_demands, self.modules_powers, self.directed_connections)
        distributions = n1_pile.get_all_possible_gun_distributions()

        # 应该返回多个分配方案
        self.assertIsInstance(distributions, list)
        self.assertGreater(len(distributions), 0)

        # 验证每个分配方案的结构
        for distribution in distributions:
            self.assertEqual(len(distribution), 4)
            # 前两个枪应该有分配的模块
            self.assertGreater(len(distribution[0]), 0)
            self.assertGreater(len(distribution[1]), 0)
            # 后两个枪应该为空
            self.assertEqual(len(distribution[2]), 0)
            self.assertEqual(len(distribution[3]), 0)

            # 验证模块编号在有效范围内
            all_modules = distribution[0] + distribution[1]
            for module in all_modules:
                self.assertGreaterEqual(module, 1)
                self.assertLessEqual(module, 8)

    def test_all_guns_distributions(self):
        """测试所有充电枪都需要功率的分配方案"""
        gun_demands = [75, 135, 85, 220]  # 所有枪都需要功率

        n1_pile = N1PileBestPowerDistribution(gun_demands, self.modules_powers, self.directed_connections)
        distributions = n1_pile.get_all_possible_gun_distributions()

        # 应该返回分配方案
        self.assertIsInstance(distributions, list)
        self.assertGreater(len(distributions), 0)

        # 验证每个分配方案的结构
        for distribution in distributions:
            self.assertEqual(len(distribution), 4)
            # 所有枪都应该有分配的模块
            for gun_idx in range(4):
                self.assertGreater(len(distribution[gun_idx]), 0)

            # 验证所有模块都被分配且不重复
            all_modules = []
            for gun_modules in distribution:
                all_modules.extend(gun_modules)

            # 检查模块编号的唯一性
            self.assertEqual(len(all_modules), len(set(all_modules)))

            # 验证模块编号在有效范围内
            for module in all_modules:
                self.assertGreaterEqual(module, 1)
                self.assertLessEqual(module, 8)

    def test_no_guns_active(self):
        """测试没有充电枪需要功率的情况"""
        gun_demands = [0, 0, 0, 0]  # 所有枪都不需要功率

        n1_pile = N1PileBestPowerDistribution(gun_demands, self.modules_powers, self.directed_connections)
        distributions = n1_pile.get_all_possible_gun_distributions()

        # 应该返回一个空的分配方案
        self.assertIsInstance(distributions, list)
        self.assertEqual(len(distributions), 0)

    def test_single_gun_third_position(self):
        """测试第三个充电枪单独需要功率的情况"""
        gun_demands = [0, 0, 160, 0]  # 只有第三个枪需要160kW

        n1_pile = N1PileBestPowerDistribution(gun_demands, self.modules_powers, self.directed_connections)
        distributions = n1_pile.get_all_possible_gun_distributions()

        # 应该返回多个分配方案
        self.assertIsInstance(distributions, list)
        self.assertGreater(len(distributions), 0)

        # 验证每个分配方案的结构
        for distribution in distributions:
            self.assertEqual(len(distribution), 4)
            # 只有第三个枪应该有分配的模块
            self.assertEqual(len(distribution[0]), 0)
            self.assertEqual(len(distribution[1]), 0)
            self.assertGreater(len(distribution[2]), 0)
            self.assertEqual(len(distribution[3]), 0)

            # 第三个枪的分配应该包含其直连节点6
            self.assertIn(6, distribution[2])

    def test_distribution_validity(self):
        """测试分配方案的有效性"""
        gun_demands = [80, 0, 0, 160]  # 第一个和第四个枪需要功率

        n1_pile = N1PileBestPowerDistribution(gun_demands, self.modules_powers, self.directed_connections)
        distributions = n1_pile.get_all_possible_gun_distributions()

        # 验证每个分配方案都是有效的
        for distribution in distributions:
            # 第一个枪应该包含其直连节点2
            self.assertIn(2, distribution[0])
            # 第四个枪应该包含其直连节点8
            self.assertIn(8, distribution[3])

            # 验证没有模块被重复分配
            all_modules = distribution[0] + distribution[3]
            self.assertEqual(len(all_modules), len(set(all_modules)))

    def test_complex_scenario(self):
        """测试复杂场景的分配方案"""
        gun_demands = [75, 135, 0, 220]  # 三个枪需要不同功率

        n1_pile = N1PileBestPowerDistribution(gun_demands, self.modules_powers, self.directed_connections)
        distributions = n1_pile.get_all_possible_gun_distributions()

        # 应该有多种分配方案
        self.assertIsInstance(distributions, list)
        self.assertGreater(len(distributions), 1)

        # 验证每个方案的基本结构
        for distribution in distributions:
            self.assertEqual(len(distribution), 4)
            # 第0、1、3个枪应该有分配
            self.assertGreater(len(distribution[0]), 0)
            self.assertGreater(len(distribution[1]), 0)
            self.assertEqual(len(distribution[2]), 0)
            self.assertGreater(len(distribution[3]), 0)

            # 验证直连节点包含在分配中
            self.assertIn(2, distribution[0])  # 枪0的直连节点
            self.assertIn(4, distribution[1])  # 枪1的直连节点
            self.assertIn(8, distribution[3])  # 枪3的直连节点

    def test_7_guns_distributions(self):
        """测试7个充电枪的分配方案"""
        gun_demands = [80, 135, 65, 20, 30, 10, 20]
        directed_connections = {0: 2, 1: 4, 2: 6, 3: 8, 4: 1, 5: 3, 6: 5}

        n1_pile = N1PileBestPowerDistribution(gun_demands, self.modules_powers, directed_connections)
        distributions = n1_pile.get_all_possible_gun_distributions()
        print(distributions)

    def test_cases_length(self):
        directed_connections = {0: 2, 1: 4, 2: 6, 3: 8}

        cases = [
            [75, 135, 0, 220],
            [140, 180, 90, 70],
            [250, 35, 25, 80],
            [76, 0, 0, 350],
            [400, 0, 0, 0],
        ]
        for case in cases:
            n1_pile = N1PileBestPowerDistribution(case, self.modules_powers, directed_connections)
            distributions = n1_pile.get_all_possible_gun_distributions()
            print(case, "len:", len(distributions))


class TestN1PileCurrentLimit(unittest.TestCase):
    def setUp(self):
        self.modules_powers = [40, 80, 40, 80, 40, 80, 40, 80]
        self.directed_connections = {0: 2, 1: 4, 2: 6, 3: 8}
        self.switch_current_limit = 350.00
        
    def test_current_limit(self):
        gun_demands = [0, 0, 430, 0]
        n1_pile = N1PileBestPowerDistribution(gun_demands, self.modules_powers, switch_current_limit=self.switch_current_limit)
        
        ret = n1_pile._is_over_current_limit([2, 1, 3, 7, 8, 6], 2)
        self.assertEqual(ret, True)

        ret = n1_pile._is_over_current_limit([2, 3, 4, 8], 2)
        self.assertEqual(ret, True)

        ret = n1_pile._is_over_current_limit([2, 1, 4, 5], 2)
        self.assertEqual(ret, True)

        ret = n1_pile._is_over_current_limit([2, 1, 3, 7], 2)
        self.assertEqual(ret, False)


class TestN1PileBestPowerDistribution(unittest.TestCase):
    def setUp(self):
        self.modules_powers = [40, 80, 40, 80, 40, 80, 40, 80]
        self.directed_connections = {0: 2, 1: 4, 2: 6, 3: 8}

    def test_perfect_match_scenario(self):
        """测试完美匹配的场景"""
        gun_demands = [80, 120, 0, 160]  # 第一个枪80kW，第二个枪120kW，第四个枪160kW

        n1_pile = N1PileBestPowerDistribution(gun_demands, self.modules_powers, self.directed_connections)
        min_diff, best_distribution = n1_pile.get_best_power_distribution()

        # 完美匹配，偏差应该为0
        self.assertEqual(min_diff, 0.0)
        # 验证分配方案
        self.assertEqual(len(best_distribution), 4)
        self.assertGreater(len(best_distribution[0]), 0)  # 第一个枪有分配
        self.assertGreater(len(best_distribution[1]), 0)  # 第二个枪有分配
        self.assertEqual(len(best_distribution[2]), 0)  # 第三个枪无分配
        self.assertGreater(len(best_distribution[3]), 0)  # 第四个枪有分配

    def test_single_gun_scenario(self):
        """测试单个充电枪的场景"""
        gun_demands = [75, 0, 0, 0]  # 只有第一个枪需要75kW

        n1_pile = N1PileBestPowerDistribution(gun_demands, self.modules_powers, self.directed_connections)
        min_diff, best_distribution = n1_pile.get_best_power_distribution()

        # 验证偏差
        expected_diff = round(abs(75 / 80 - 1), 5)
        self.assertEqual(min_diff, expected_diff)
        # 验证分配方案
        self.assertEqual(len(best_distribution), 4)
        self.assertGreater(len(best_distribution[0]), 0)  # 第一个枪有分配
        self.assertEqual(len(best_distribution[1]), 0)  # 其他枪无分配
        self.assertEqual(len(best_distribution[2]), 0)
        self.assertEqual(len(best_distribution[3]), 0)

    def test_complex_scenario(self):
        """测试复杂场景"""
        gun_demands = [75, 135, 0, 220]  # 三个枪需要不同功率

        n1_pile = N1PileBestPowerDistribution(gun_demands, self.modules_powers, self.directed_connections)
        min_diff, best_distribution = n1_pile.get_best_power_distribution()

        # 验证分配方案的基本结构
        self.assertEqual(len(best_distribution), 4)
        self.assertGreater(len(best_distribution[0]), 0)  # 第一个枪有分配
        self.assertGreater(len(best_distribution[1]), 0)  # 第二个枪有分配
        self.assertEqual(len(best_distribution[2]), 0)  # 第三个枪无分配
        self.assertGreater(len(best_distribution[3]), 0)  # 第四个枪有分配

        # 验证直连节点包含在分配中
        self.assertIn(self.directed_connections[0], best_distribution[0])  # 枪0的直连节点
        self.assertIn(self.directed_connections[1], best_distribution[1])  # 枪1的直连节点
        self.assertIn(self.directed_connections[3], best_distribution[3])  # 枪3的直连节点

    def test_no_active_guns(self):
        """测试没有充电枪需要功率的情况"""
        gun_demands = [0, 0, 0, 0]  # 所有枪都不需要功率

        n1_pile = N1PileBestPowerDistribution(gun_demands, self.modules_powers, self.directed_connections)
        min_diff, best_distribution = n1_pile.get_best_power_distribution()

        # 没有活跃的枪，偏差应该为0
        self.assertEqual(min_diff, 0.0)
        # 应该返回空的分配方案
        self.assertEqual(best_distribution, [[], [], [], []])

    def test_high_power_demand(self):
        """测试高功率需求的场景"""
        gun_demands = [0, 0, 430, 0]  # 第三个枪需要430kW

        n1_pile = N1PileBestPowerDistribution(gun_demands, self.modules_powers, self.directed_connections)
        min_diff, best_distribution = n1_pile.get_best_power_distribution()

        # 验证分配方案
        self.assertEqual(len(best_distribution), 4)
        self.assertEqual(len(best_distribution[0]), 0)  # 第一个枪无分配
        self.assertEqual(len(best_distribution[1]), 0)  # 第二个枪无分配
        self.assertGreater(len(best_distribution[2]), 0)  # 第三个枪有分配
        self.assertEqual(len(best_distribution[3]), 0)  # 第四个枪无分配

        # 验证第三个枪的分配包含其直连节点
        self.assertIn(self.directed_connections[2], best_distribution[2])

    def test_multiple_guns_with_small_demands(self):
        """测试多个充电枪都有较小功率需求的场景"""
        gun_demands = [75, 81, 85, 20]  # 所有枪都需要功率，但需求较小

        n1_pile = N1PileBestPowerDistribution(gun_demands, self.modules_powers, self.directed_connections)
        min_diff, best_distribution = n1_pile.get_best_power_distribution()

        # 验证分配方案
        self.assertEqual(len(best_distribution), 4)
        # 所有枪都应该有分配
        for gun_idx in range(4):
            self.assertGreater(len(best_distribution[gun_idx]), 0)

        # 验证直连节点包含在分配中
        self.assertIn(self.directed_connections[0], best_distribution[0])
        self.assertIn(self.directed_connections[1], best_distribution[1])
        self.assertIn(self.directed_connections[2], best_distribution[2])
        self.assertIn(self.directed_connections[3], best_distribution[3])

    def test_custom_cases(self):
        # test 最优分配
        test_cases = [
            [75, 135, 0, 220],  # 0.09028 [[2], [4, 1], [], [8, 3, 5, 6]]
            [0, 0, 430, 0],
            [75, 190, 0, 210],
            [75, 81, 85, 20],
            [0, 0, 0, 0],
            [480, 480, 480, 480],
            [140, 180, 90, 70],  # 0.125, [[2, 1, 7], [4, 3, 5], [6], [8]]
        ]
        for gun_demands in test_cases:
            n1_pile = N1PileBestPowerDistribution(gun_demands, self.modules_powers, self.directed_connections)
            min_abs_diff, best_gun_distribution = n1_pile.get_best_power_distribution()
            print("get_best_power_distribution:", gun_demands, min_abs_diff, best_gun_distribution)


class TestLinearProgrammingPowerDistributionSolver(unittest.TestCase):
    def setUp(self):
        base_dir = os.path.dirname(os.path.abspath(__file__))
        self.test_data_dir = os.path.join(base_dir, "test_data")

    def load_test_data(self, filename):
        """加载测试数据"""
        with open(os.path.join(self.test_data_dir, filename), 'r') as f:
            return json.load(f)

    def test_basic_solving(self):
        """测试基本的求解功能"""
        input_data = self.load_test_data("test_case01.json")
        solver = LinearProgrammingPowerDistributionSolver(input_data)
        ret_values, pgun_solver_values = solver.get_solve_result()

        # 验证返回结果的基本结构
        self.assertIsNotNone(ret_values)
        self.assertIn("status", ret_values)
        self.assertIn("scheduling_time", ret_values)
        self.assertIn("site_charger_power_allocations", ret_values)

        # 验证功率分配结果不为空
        self.assertTrue(len(ret_values["site_charger_power_allocations"]) > 0)

    def test_multiple_test_cases(self):
        """测试多个测试用例"""
        test_files = ["test_case01.json", "test_case02.json", "test_case03.json"]

        for test_file in test_files:
            with self.subTest(test_file=test_file):
                input_data = self.load_test_data(test_file)
                solver = LinearProgrammingPowerDistributionSolver(input_data)
                ret_values, pgun_solver_values = solver.get_solve_result()

                # 验证结果的有效性
                self.assertIsNotNone(ret_values)
                self.assertTrue(len(ret_values["site_charger_power_allocations"]) > 0)


if __name__ == '__main__':
    # test = TestGetAllPossibleGunDistributions()
    # test.setUp()
    # test.test_cases_length()
    unittest.main()  # 运行所有测试用例
