import json
import traceback
from typing import Optional, Dict, Any
from application.db_operate.db_operate import DBOperate
from application.algorithm.energy_storage_optimization.energy_storage_optimization import \
    energy_storage_optimization_runner
from application.utils.logger import setup_logger

logger = setup_logger("storage_dispatch", direction="algorithm_schedule")


def storage_dispatch(site_no: str, data: Optional[Dict[str, Any]] = None, trigger: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """
    储能调度算法调度入口（改进版本）
    
    :param site_no: 场站编号
    :param data: 相关输入数据（当前未使用，保留用于未来扩展）
    :param trigger: 触发源（如有）
    :return: 调度结果，失败时返回None
    """
    logger.info(f'场站 {site_no} 储能调度开始 (触发器: {trigger})')
    
    try:
        # 参数验证
        if not site_no or not isinstance(site_no, str):
            logger.error("场站编号不能为空且必须为字符串")
            return None
            
        db_op = DBOperate()
        
        # 1. 获取储能实时数据
        es_realtime = db_op.get_es_realtime_data(site_no=site_no)
        if not es_realtime:
            logger.error(f'场站 {site_no} 无储能实时数据')
            return None
            
        # 2. 获取场站数据
        site = db_op.get_site(site_no=site_no)
        if not site:
            logger.error(f'场站 {site_no} 不存在')
            return None
            
        # 3. 获取充电桩数据
        pile = db_op.get_pile(site_no=site_no)
        if not pile:
            logger.error(f'场站 {site_no} 无充电桩数据')
            return None
            
        # 4. 验证和提取储能参数
        try:
            es_soc = int(es_realtime.es_soc) if es_realtime.es_soc is not None else 50
            min_soc = int(es_realtime.es_min_soc) if es_realtime.es_min_soc is not None else 20
            max_soc = int(es_realtime.es_max_soc) if es_realtime.es_max_soc is not None else 90
            
            # SOC值验证
            if not (0 <= es_soc <= 100):
                logger.warning(f'场站 {site_no} SOC值异常: {es_soc}，使用默认值50')
                es_soc = 50
            if not (0 <= min_soc <= 100):
                logger.warning(f'场站 {site_no} 最小SOC值异常: {min_soc}，使用默认值20')
                min_soc = 20
            if not (0 <= max_soc <= 100):
                logger.warning(f'场站 {site_no} 最大SOC值异常: {max_soc}，使用默认值90')
                max_soc = 90
            if min_soc >= max_soc:
                logger.warning(f'场站 {site_no} SOC范围异常: min={min_soc}, max={max_soc}，使用默认值')
                min_soc, max_soc = 20, 90
                
        except (ValueError, TypeError) as e:
            logger.error(f'场站 {site_no} SOC数据类型错误: {e}，使用默认值')
            es_soc, min_soc, max_soc = 50, 20, 90

        # 5. 获取光伏预测数据
        pv_predicted_list = db_op.get_pv_predicted_list(site_no=site_no)
        if not isinstance(pv_predicted_list, list):
            logger.warning(f'场站 {site_no} 光伏预测数据格式异常，使用空列表')
            pv_predicted_list = []

        # 6. 获取融合负载预测数据
        hybrid_load = db_op.get_hybrid_load_predicted(site_no=site_no)
        if not hybrid_load:
            logger.error(f'场站 {site_no} 获取融合负载预测失败')
            return None
            
        try:
            hybrid_load_predicted_list = json.loads(hybrid_load.power_list)
            if not isinstance(hybrid_load_predicted_list, list):
                raise ValueError("power_list不是列表格式")
        except (json.JSONDecodeError, ValueError) as e:
            logger.error(f'场站 {site_no} 融合负载预测数据JSON解析失败: {e}')
            return None
            
        try:
            start_time = hybrid_load.curve_start_time.strftime("%Y-%m-%d %H:%M:%S")
            date = hybrid_load.curve_start_time.strftime("%Y-%m-%d")
        except AttributeError as e:
            logger.error(f'场站 {site_no} 融合负载预测时间格式错误: {e}')
            return None
            
        logger.info(f'场站 {site_no} 融合负载预测: {len(hybrid_load_predicted_list)}个数据点')

        # 7. 获取电价数据
        logger.info(f'场站 {site_no} 预测日期: {date}')
        price_list = db_op.get_price_by_site_no_and_date(site_no=site_no, date=date)
        
        if not isinstance(price_list, list) or len(price_list) != 24:
            logger.error(f'场站 {site_no} 电价列表必须包含24个数据点，当前: {len(price_list) if isinstance(price_list, list) else "非列表"}')
            return None
            
        logger.info(f'场站 {site_no} 电价列表: {len(price_list)}个数据点')

        # 8. 获取需量数据
        demand_data = db_op.get_site_demand_data(site_no=site_no)
        if not isinstance(demand_data, list):
            logger.warning(f'场站 {site_no} 需量数据格式异常，使用空列表')
            demand_data = []
        logger.info(f'场站 {site_no} 需量数据: {len(demand_data)}条记录')

        # 9. 获取售电价格数据
        electricity_sale_price_list = db_op.get_future_24h_sell_price_from_db(site_no)
        if not isinstance(electricity_sale_price_list, list):
            logger.warning(f'场站 {site_no} 售电价格数据格式异常，使用空列表')
            electricity_sale_price_list = []

        # 10. 验证和转换充电桩额定功率
        try:
            es_rated_power = float(pile.rated_power)
            if es_rated_power <= 0:
                logger.warning(f'场站 {site_no} 充电桩额定功率异常: {es_rated_power}，使用默认值50.0')
                es_rated_power = 50.0
        except (ValueError, TypeError) as e:
            logger.error(f'场站 {site_no} 充电桩额定功率转换失败: {e}，使用默认值50.0')
            es_rated_power = 50.0

        # 11. 验证场站参数
        es_total_energy = getattr(site, 'es_total_energy', None)
        if es_total_energy is None or es_total_energy <= 0:
            logger.warning(f'场站 {site_no} 储能总容量异常: {es_total_energy}，使用默认值100')
            es_total_energy = 100
            
        site_grid_limit = getattr(site, 'site_grid_limit', None)
        if site_grid_limit is None or site_grid_limit <= 0:
            logger.warning(f'场站 {site_no} 电网功率限制异常: {site_grid_limit}，使用默认值1000')
            site_grid_limit = 1000

        # 12. 构建输入数据
        input_data = {
            'start_time': start_time,
            'pv_predicted_list': pv_predicted_list,
            'hybrid_load_predicted_list': hybrid_load_predicted_list,
            'electricity_purchase_price_list': price_list,
            'es_soc': es_soc,
            'es_total_energy': es_total_energy,
            'es_rated_power': es_rated_power,
            'site_grid_limit': site_grid_limit,
            'demand_data': demand_data,
            'min_soc': min_soc,
            'max_soc': max_soc,
            'electricity_sale_price_list': electricity_sale_price_list
        }
        
        logger.info(f'场站 {site_no} 储能调度输入数据准备完成')
        logger.debug(f'场站 {site_no} 输入数据详情: {input_data}')
        
        # 13. 调用算法
        result = {}
        try:
            result = energy_storage_optimization_runner(input_data)
            logger.info(f'场站 {site_no} 储能调度算法执行成功')
            logger.debug(f'场站 {site_no} 储能调度结果: {result}')
            
            # 14. 保存结果
            try:
                db_op.save_energy_storage_task(site_no=site_no, result=result)
                logger.info(f'场站 {site_no} 储能调度结果保存成功')
            except Exception as save_e:
                logger.error(f'场站 {site_no} 储能调度结果保存失败: {str(save_e)}')
                # 保存失败不影响返回结果
                
        except Exception as algo_e:
            logger.error(f'场站 {site_no} 储能优化算法调用失败: {str(algo_e)}')
            logger.error(f'场站 {site_no} 算法异常详情: {traceback.format_exc()}')
            return None
            
        return result
        
    except Exception as e:
        logger.error(f'场站 {site_no} 储能调度过程发生未预期错误: {str(e)}')
        logger.error(f'场站 {site_no} 错误详情: {traceback.format_exc()}')
        return None


if __name__ == '__main__':
    # 测试调用
    result = storage_dispatch(site_no='SITE001', data=None)
    print(f"调度结果: {result}")
