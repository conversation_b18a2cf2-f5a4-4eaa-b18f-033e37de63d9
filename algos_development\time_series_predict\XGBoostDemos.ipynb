{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "\n", "sys.path.append(os.getcwd())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## XGBoost挖掘特征"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 数据集处理"]}, {"cell_type": "markdown", "metadata": {}, "source": ["数据集下载地址: [Pima Indians Diabetes Database](https://link.zhihu.com/?target=https%3A//github.com/susanli2016/Machine-Learning-with-Python/blob/master/diabetes.csv)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from sklearn import metrics\n", "from sklearn.model_selection import train_test_split\n", "import xgboost as xgb\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Pregnancies</th>\n", "      <th>Glucose</th>\n", "      <th>BloodPressure</th>\n", "      <th>SkinThickness</th>\n", "      <th>Insulin</th>\n", "      <th>BMI</th>\n", "      <th>DiabetesPedigreeFunction</th>\n", "      <th>Age</th>\n", "      <th>Outcome</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>6</td>\n", "      <td>148</td>\n", "      <td>72</td>\n", "      <td>35</td>\n", "      <td>0</td>\n", "      <td>33.6</td>\n", "      <td>0.627</td>\n", "      <td>50</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>85</td>\n", "      <td>66</td>\n", "      <td>29</td>\n", "      <td>0</td>\n", "      <td>26.6</td>\n", "      <td>0.351</td>\n", "      <td>31</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>8</td>\n", "      <td>183</td>\n", "      <td>64</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>23.3</td>\n", "      <td>0.672</td>\n", "      <td>32</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>89</td>\n", "      <td>66</td>\n", "      <td>23</td>\n", "      <td>94</td>\n", "      <td>28.1</td>\n", "      <td>0.167</td>\n", "      <td>21</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>137</td>\n", "      <td>40</td>\n", "      <td>35</td>\n", "      <td>168</td>\n", "      <td>43.1</td>\n", "      <td>2.288</td>\n", "      <td>33</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Pregnancies  Glucose  BloodPressure  SkinThickness  Insulin   BMI  \\\n", "0            6      148             72             35        0  33.6   \n", "1            1       85             66             29        0  26.6   \n", "2            8      183             64              0        0  23.3   \n", "3            1       89             66             23       94  28.1   \n", "4            0      137             40             35      168  43.1   \n", "\n", "   DiabetesPedigreeFunction  Age  Outcome  \n", "0                     0.627   50        1  \n", "1                     0.351   31        0  \n", "2                     0.672   32        1  \n", "3                     0.167   21        0  \n", "4                     2.288   33        1  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df_source = pd.read_csv('../data/diabetes.csv')\n", "df_source.head()"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["x, y = df_source.iloc[:, :-1], df_source.iloc[:, -1]\n", "train_x, test_x, train_y, test_y = train_test_split(x, y, test_size=0.2, random_state=7)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 模型构建&训练"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0]\ttrain-auc:0.78310\n", "[1]\ttrain-auc:0.85370\n", "[2]\ttrain-auc:0.86749\n", "[3]\ttrain-auc:0.87625\n", "[4]\ttrain-auc:0.88332\n", "[5]\ttrain-auc:0.88262\n", "[6]\ttrain-auc:0.88437\n", "[7]\ttrain-auc:0.88099\n", "[8]\ttrain-auc:0.88132\n", "[9]\ttrain-auc:0.88030\n", "[10]\ttrain-auc:0.87882\n", "[11]\ttrain-auc:0.87916\n", "[12]\ttrain-auc:0.88075\n", "[13]\ttrain-auc:0.88055\n", "[14]\ttrain-auc:0.88249\n", "[15]\ttrain-auc:0.88272\n", "[16]\ttrain-auc:0.88332\n", "[17]\ttrain-auc:0.88426\n", "[18]\ttrain-auc:0.88364\n", "[19]\ttrain-auc:0.88298\n", "[20]\ttrain-auc:0.88344\n", "[21]\ttrain-auc:0.88399\n", "[22]\ttrain-auc:0.88440\n", "[23]\ttrain-auc:0.88379\n", "[24]\ttrain-auc:0.88318\n", "[25]\ttrain-auc:0.88313\n", "[26]\ttrain-auc:0.88368\n", "[27]\ttrain-auc:0.88390\n", "[28]\ttrain-auc:0.88352\n", "[29]\ttrain-auc:0.88410\n", "[30]\ttrain-auc:0.88483\n", "[31]\ttrain-auc:0.88499\n", "[32]\ttrain-auc:0.88524\n", "[33]\ttrain-auc:0.88414\n", "[34]\ttrain-auc:0.88351\n", "[35]\ttrain-auc:0.88428\n", "[36]\ttrain-auc:0.88421\n", "[37]\ttrain-auc:0.88509\n", "[38]\ttrain-auc:0.88552\n", "[39]\ttrain-auc:0.88560\n", "[40]\ttrain-auc:0.88575\n", "[41]\ttrain-auc:0.88561\n", "[42]\ttrain-auc:0.88552\n", "[43]\ttrain-auc:0.88479\n", "[44]\ttrain-auc:0.88506\n", "[45]\ttrain-auc:0.88508\n", "[46]\ttrain-auc:0.88533\n", "[47]\ttrain-auc:0.88637\n", "[48]\ttrain-auc:0.88696\n", "[49]\ttrain-auc:0.88691\n", "[50]\ttrain-auc:0.88727\n", "[51]\ttrain-auc:0.88745\n", "[52]\ttrain-auc:0.88774\n", "[53]\ttrain-auc:0.88794\n", "[54]\ttrain-auc:0.88805\n", "[55]\ttrain-auc:0.88826\n", "[56]\ttrain-auc:0.88870\n", "[57]\ttrain-auc:0.88863\n", "[58]\ttrain-auc:0.88866\n", "[59]\ttrain-auc:0.88978\n"]}], "source": ["dtrain = xgb.DMatrix(train_x, label=train_y)\n", "dtest = xgb.DMatrix(test_x)\n", "watchlist = [(dtrain, 'train')]\n", "\n", "# booster:\n", "params={'booster':'gbtree',\n", "        'objective': 'binary:logistic',\n", "        'eval_metric': 'auc',\n", "        'max_depth': 5,\n", "        'lambda': 10,\n", "        'subsample': 0.75,\n", "        'colsample_bytree': 0.75,\n", "        'min_child_weight': 2,\n", "        'eta': 0.025,\n", "        'seed': 0,\n", "        'nthread': 8,\n", "        'gamma': 0.15,\n", "        'learning_rate': 0.01}\n", "\n", "# 建模与预测：50棵树\n", "bst = xgb.train(params, dtrain, num_boost_round=60, evals=watchlist)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 模型预测&评估"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Precesion: 0.5306\n", "Recall: 0.9123\n", "F1-score: 0.6710\n", "Accuracy: 0.6688\n", "AUC: 0.8578\n"]}], "source": ["ypred = bst.predict(dtest)\n", "\n", "# 设置阈值、评价指标\n", "y_pred = (ypred >= 0.3) * 1\n", "print ('Precesion: %.4f' %metrics.precision_score(test_y, y_pred))\n", "print ('Recall: %.4f' % metrics.recall_score(test_y, y_pred))\n", "print ('F1-score: %.4f' %metrics.f1_score(test_y, y_pred))\n", "print ('Accuracy: %.4f' % metrics.accuracy_score(test_y, y_pred))\n", "print ('AUC: %.4f' % metrics.roc_auc_score(test_y, ypred))"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["测试集每个样本的得分\n", " (154,)\n", "测试集每棵树所属的节点数\n", " (154, 60)\n", "特征的重要性\n", " (154, 9)\n"]}], "source": ["ypred = bst.predict(dtest)\n", "print(\"测试集每个样本的得分\\n\", ypred.shape)\n", "ypred_leaf = bst.predict(dtest, pred_leaf=True)\n", "print(\"测试集每棵树所属的节点数\\n\", ypred_leaf.shape)\n", "ypred_contribs = bst.predict(dtest, pred_contribs=True)\n", "print(\"特征的重要性\\n\", ypred_contribs.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n", "findfont: Font family 'Arial Unicode MS' not found.\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["xgb.plot_importance(bst, height=0.8, title='Important features influencing diabetes', ylabel='Feature')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## XGBoost电商预估"]}, {"cell_type": "markdown", "metadata": {}, "source": ["数据算法竞赛：[跨境电商智能算法大赛](https://tianchi.aliyun.com/competition/entrance/231718/introduction)\n", "\n", "方案：[冠军的解决方案](https://github.com/RainFung/Tianchi-AntaiCup-International-E-commerce-Artificial-Intelligence-Challenge)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## DRW Crypto Market Prediction"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Kaggle竞赛：[DRW Crypto Market Prediction](https://www.kaggle.com/competitions/drw-crypto-market-prediction/data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}