import json
from typing import List
import re
from datetime import datetime


def is_float(text):
    # 匹配可选的正负号，后跟数字(可能有小数点)，可能有科学计数法表示
    pattern = r'^[+-]?(\d+(\.\d*)?|\.\d+)([eE][+-]?\d+)?$'
    return bool(re.match(pattern, text.strip()))


def is_datetime_format(text):
    # 匹配YYYY-MM-DD HH:MM:SS.microseconds格式
    pattern = r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}(\.\d{1,6})?$'
    return bool(re.match(pattern, text.strip()))


def get_format_value(text: str):
    if text.isdigit():
        format_value = int(text)
    elif is_float(text):
        format_value = float(text)
    elif is_datetime_format(text):
        format_value = datetime.strptime(text, '%Y-%m-%d %H:%M:%S.%f')
    else:
        format_value = text
    return format_value


def parse_data_lines(data_lines: List[str]) -> List[dict]:
    if not data_lines or len(data_lines) < 2:
        return []
    
    # 获取列名
    header_line = data_lines[0]
    headers = [h.strip() for h in header_line.split(',')]

    result = []
    for i in range(1, len(data_lines)):
        line = data_lines[i]
        values = [v.strip() for v in line.split(',')]

        item = {}
        for i, header in enumerate(headers):
            item[header] = get_format_value(values[i])
        result.append(item)
    return result


def parse_charging_data(file_path) -> dict:
    """解析充电数据"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    config_data = data[0]
    charging_data = parse_data_lines(data[1])
    return {"config_data": config_data, "charging_data": charging_data}
