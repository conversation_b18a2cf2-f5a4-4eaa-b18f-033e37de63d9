-- ChargerRealtimeDataDB 测试数据生成脚本（优化版）
-- 生成3天的充电枪实时数据，每天96个采样点（每15分钟一次）
-- 从PileDB表中读取充电桩信息，动态生成数据

-- 设置测试参数
SET @site_no = 'SITE001';
SET @start_date = '2025-06-24 00:00:00';
SET @days = 3;
SET @samples_per_day = 96; -- 每15分钟一次，24小时 * 4 = 96次

-- 清空现有数据（可选）
DELETE FROM ChargerRealtimeData WHERE site_no = @site_no;

-- 充电枪状态列表
SET @status_list = 'Available,Preparing,Charging,SuspendedEV,SuspendEVSE,Finished,Reserved,Unavailable,Faulted';

-- 创建临时表存储充电桩信息
DROP TEMPORARY TABLE IF EXISTS temp_pile_info;
-- 获取充电桩信息
CREATE TEMPORARY TABLE temp_pile_info AS
SELECT 
    pile_sn,
    gun_num,
    rated_power,
    site_no
FROM Pile 
WHERE site_no = @site_no;

-- 创建存储过程生成测试数据
DELIMITER $$

CREATE PROCEDURE GenerateChargerRealtimeTestData()
BEGIN
    DECLARE curr_date DATE DEFAULT @start_date;
    DECLARE curr_time TIME DEFAULT '00:00:00';
    DECLARE sample_count INT DEFAULT 0;
    DECLARE day_count INT DEFAULT 0;
    DECLARE done INT DEFAULT FALSE;
    
    -- 游标变量
    DECLARE pile_sn_var VARCHAR(50);
    DECLARE gun_num_var INT;
    DECLARE rated_power_var DECIMAL(10,2);
    DECLARE connector_num INT;
    
    -- 声明游标
    DECLARE pile_cursor CURSOR FOR 
        SELECT pile_sn, gun_num, rated_power FROM temp_pile_info;
    
    -- 声明异常处理
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    WHILE day_count < @days DO
        SET curr_time = '00:00:00';
        SET sample_count = 0;
        
        WHILE sample_count < @samples_per_day DO
            -- 打开游标
            OPEN pile_cursor;
            
            pile_loop: LOOP
                FETCH pile_cursor INTO pile_sn_var, gun_num_var, rated_power_var;
                
                IF done THEN
                    LEAVE pile_loop;
                END IF;
                
                -- 为每个充电桩的每个充电枪生成数据
                SET connector_num = 1;
                WHILE connector_num <= gun_num_var DO
                    INSERT INTO ChargerRealtimeData (site_no, ts, pile_sn, connector, status, power, ocpp_limit, created_at)
                    VALUES (
                        @site_no,
                        UNIX_TIMESTAMP(CONCAT(curr_date, ' ', curr_time)) * 1000,
                        pile_sn_var,
                        connector_num,
                        CASE 
                            WHEN connector_num = 1 THEN 'Available'
                            WHEN connector_num = 2 THEN 'Charging'
                            WHEN connector_num = 3 THEN 'Preparing'
                            WHEN connector_num = 4 THEN 'Available'
                            WHEN connector_num = 5 THEN 'Finished'
                            ELSE 'Available'
                        END,
                        CASE 
                            WHEN connector_num = 2 THEN FLOOR(20 + RAND() * (rated_power_var * 0.8))
                            ELSE 0
                        END,
                        rated_power_var,
                        NOW()
                    );
                    SET connector_num = connector_num + 1;
                END WHILE;
                
            END LOOP;
            
            CLOSE pile_cursor;
            SET done = FALSE;
            
            -- 增加15分钟
            SET curr_time = ADDTIME(curr_time, '00:15:00');
            SET sample_count = sample_count + 1;
        END WHILE;
        
        -- 增加一天
        SET curr_date = DATE_ADD(curr_date, INTERVAL 1 DAY);
        SET day_count = day_count + 1;
    END WHILE;
END$$

DELIMITER ;

-- 执行存储过程生成测试数据
CALL GenerateChargerRealtimeTestData();

-- 删除存储过程
DROP PROCEDURE IF EXISTS GenerateChargerRealtimeTestData;

-- 删除临时表
DROP TEMPORARY TABLE IF EXISTS temp_pile_info;

-- 验证数据生成情况
SELECT 
    DATE(FROM_UNIXTIME(ts/1000)) as date,
    COUNT(*) as total_records,
    COUNT(DISTINCT pile_sn) as unique_piles,
    COUNT(DISTINCT CONCAT(pile_sn, '_', connector)) as unique_chargers,
    COUNT(DISTINCT status) as unique_statuses
FROM ChargerRealtimeData 
WHERE site_no = @site_no
GROUP BY DATE(FROM_UNIXTIME(ts/1000))
ORDER BY date;

-- 查看各状态分布
SELECT 
    status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM ChargerRealtimeData WHERE site_no = @site_no), 2) as percentage
FROM ChargerRealtimeData 
WHERE site_no = @site_no
GROUP BY status
ORDER BY count DESC;

-- 查看充电桩功率分布
SELECT 
    p.pile_sn,
    p.gun_num,
    p.rated_power,
    COUNT(c.id) as data_points,
    AVG(c.power) as avg_power,
    MAX(c.power) as max_power
FROM Pile p
LEFT JOIN ChargerRealtimeData c ON p.pile_sn = c.pile_sn AND c.site_no = @site_no
WHERE p.site_no = @site_no
GROUP BY p.pile_sn, p.gun_num, p.rated_power
ORDER BY p.pile_sn;
