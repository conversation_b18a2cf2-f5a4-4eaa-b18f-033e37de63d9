#############################################################################
# Copyright (C) 2023 AUTEL Netherlands B.V. - All Rights Reserved
# Unauthorized copying of this file, via any medium is strictly prohibited
#
# Proprietary and confidential
#############################################################################
from .base import Classifier
from sklearn.linear_model import LogisticRegression


class LogisticRegressionClassifier(Classifier):
    def _algorithm(self):
        return LogisticRegression(max_iter=1000)
