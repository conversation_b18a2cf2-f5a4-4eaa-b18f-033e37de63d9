"""
充电桩工具类
处理不同类型充电桩的特殊逻辑
"""

from typing import Optional, Dict, Any
from application.utils.logger import setup_logger

logger = setup_logger("pile_utils", direction="utils")


class PileUtils:
    """充电桩工具类"""
    
    # 交流桩桩号前缀
    AC_PILE_PREFIX = "AL"
    
    @classmethod
    def is_ac_pile(cls, pile_sn: str) -> bool:
        """
        判断是否为交流桩
        
        Args:
            pile_sn: 充电桩序列号
            
        Returns:
            bool: 是否为交流桩
        """
        if not pile_sn or len(pile_sn) < 6:
            return False
        return pile_sn.startswith(cls.AC_PILE_PREFIX)
    
    @classmethod
    def extract_ac_pile_power(cls, pile_sn: str) -> Optional[float]:
        """
        从交流桩桩号中提取功率
        桩号格式: AL7019A2GR7C00010X，第5、6位为功率（19kW）
        
        Args:
            pile_sn: 交流桩序列号
            
        Returns:
            Optional[float]: 提取的功率，如果提取失败返回None
        """
        if not cls.is_ac_pile(pile_sn):
            return None
            
        if len(pile_sn) < 6:
            logger.warning(f"交流桩桩号长度不足: {pile_sn}")
            return None
            
        try:
            # 提取第5、6位字符作为功率
            power_str = pile_sn[4:6]
            power = float(power_str)
            logger.debug(f"从桩号 {pile_sn} 提取功率: {power}kW")
            return power
        except (ValueError, IndexError) as e:
            logger.error(f"从桩号 {pile_sn} 提取功率失败: {str(e)}")
            return None
    
    @classmethod
    def get_ac_pile_info(cls, pile_sn: str, site_no: str) -> Optional[Dict[str, Any]]:
        """
        获取交流桩信息
        
        Args:
            pile_sn: 交流桩序列号
            site_no: 场站编号
            
        Returns:
            Optional[Dict]: 交流桩信息，如果不是交流桩返回None
        """
        if not cls.is_ac_pile(pile_sn):
            return None
            
        rated_power = cls.extract_ac_pile_power(pile_sn)
        if rated_power is None:
            logger.error(f"无法从桩号 {pile_sn} 提取功率")
            return None
            
        return {
            "pile_sn": pile_sn,
            "pile_type": "1",
            "gun_num": 1,  # 交流桩只有一把枪
            "rated_power": rated_power,
            "site_no": site_no
        }
    
    @classmethod
    def filter_dc_piles(cls, pile_list: list) -> list:
        """
        过滤出直流桩列表（排除交流桩）
        
        Args:
            pile_list: 充电桩列表
            
        Returns:
            list: 直流桩列表
        """
        dc_piles = [pile for pile in pile_list if not cls.is_ac_pile(pile)]
        if len(dc_piles) != len(pile_list):
            ac_piles = [pile for pile in pile_list if cls.is_ac_pile(pile)]
            logger.info(f"过滤掉 {len(ac_piles)} 个交流桩: {ac_piles}")
            logger.info(f"剩余 {len(dc_piles)} 个直流桩: {dc_piles}")
        return dc_piles
    
    @classmethod
    def separate_pile_types(cls, pile_list: list) -> tuple:
        """
        分离交流桩和直流桩
        
        Args:
            pile_list: 充电桩列表
            
        Returns:
            tuple: (交流桩列表, 直流桩列表)
        """
        ac_piles = []
        dc_piles = []
        
        for pile in pile_list:
            if cls.is_ac_pile(pile):
                ac_piles.append(pile)
            else:
                dc_piles.append(pile)
                
        return ac_piles, dc_piles 