import json
import pdb
import sys
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime,timedelta

#tarCurr, outCurr, tarVol, outVol, maxPower, SOC

import pdb
#para:
#json_data: json object,get by json.load(f)
def getPowerSocCurve(json_data):
    soc_list=[i/10.0 for i in range(1000)]# the resolution is 0.1 percent
    power_list=[0 for _ in range(1000)]
    vol_list=[0 for _ in range(1000)]
    soc_previous=-1
    one_soc_list=[]
    power_list_limited=[]
    soc_list_limited=[]
    for i,item in enumerate(json_data):
        if isinstance(item,dict):
            for k,v in item.items():
                #print(f"-{k}:{v}")
                if k=="MaximumCurrent":
                    maxim_current=v
                if k=="MaximumVoltage":
                    maxim_voltage=v
        elif isinstance(item,list):
            #print(" - List content:")
            cnt=0
            for list_item in item:
                #pdb.set_trace()
                cnt=cnt+1
                if cnt<2:
                    continue
                #print(f"---{list_item}")
                parts=list_item.split(',')
                target_cur = float(parts[0])
                out_vol=float(parts[3])
                soc=parts[5]
                soc_int=int(soc.split("_")[0]) #"55_p0"->55
                target_power = target_cur*out_vol/1000 #unit is kw
                max_power = float(parts[4])
                if soc_int!=soc_previous:
                    #a new soc comes,and deal with the data of the previous soc
                    num = len(one_soc_list)
                    if num>10:
                        average_num = int(num/10) #xx.0 --xx.9,each 0.1 percent has average_num items
                        for j in range(10):
                            vol_sum=0
                            power_sum=0
                            secc_power_sum=0
                            for k in range(average_num):
                                vol_sum+=one_soc_list[j*average_num+k][0] #get the vol from tuple
                                power_sum+=one_soc_list[j*average_num+k][1] #get the power from tuple
                                secc_power_sum+=one_soc_list[j*average_num+k][2] #get the secc max power from tuple

                            power_aver = power_sum/average_num
                            # print(f"power_aver is {power_aver},power_sum is {power_sum},soc_previous is {soc_previous}")
                            power_list[soc_previous*10+j]=power_aver

                            vol_aver = vol_sum/average_num
                            vol_list[soc_previous*10+j]=vol_aver

                            secc_power_aver = secc_power_sum/average_num
                            if power_aver+5000 >secc_power_aver:
                                #is limited by secc
                                #print(f"limited by evse,power:{power_aver},max_power:{secc_power_aver},soc:{soc_previous+j/10.0}")
                                power_list_limited.append(power_aver)
                                soc_list_limited.append(soc_previous+j/10.0)

                    elif num==0:
                        pass
                        # print(f"starting to process a new curve")
                    else:
                        #it must be the starging soc,just consider it as xx.9,which has num items
                        vol_sum=0
                        power_sum=0
                        j=9
                        for k in range(num):
                            vol_sum+=one_soc_list[k][0] #get the vol from tuple
                            power_sum+=one_soc_list[k][1] #get the power from tuple

                        power_aver = power_sum/num
                        index = soc_previous*10+j
                        #print(f"power_aver is {power_aver},power_sum is {power_sum},soc_previous is {soc_previous}")
                        if index >999:
                            index = 999 #considering the case where soc is at 100
                            #print(f"the index of powerlist is invalid,index:{soc_previous*10+j}")
                        power_list[index]=power_aver

                        vol_aver = vol_sum/num
                        vol_list[index]=vol_aver 
                        
                        #print("now deal with the case:num<10")

                    soc_previous=soc_int
                    one_soc_list=[]
                else:
                    one_soc_list.append((out_vol,target_power,max_power*1000))
    return soc_list,power_list

'''
if len(sys.argv)==1:
    print("it plot the power and voltage's trend based on soc,and can plot several curve at one figure") 
    print("usage:plotSoc.py xxx.json [xxx.json] [xxx.json]")
    exit(0)

allcurve_name=[]
curve_list=[]
for para in sys.argv[1:]:
    with open(para,"r",encoding="utf-8") as f:
        curve_list.append(json.load(f))
        allcurve_name.append(para[-12:-5]) #e00ee106cceb_17.json ->cceb_17

allcurve_power_show=[]
allcurve_soc_show=[]
allcurve_vol_show=[]
allcurve_limited_soc=[]
allcurve_limited_power=[]
curve_index=0

for curve in curve_list:
    soc_list,power_list=getPowerSocCurve(curve) #curve is json data
    power_show=[]
    soc_show=[]
    vol_show=[]
    for i in range(1000):
        if power_list[i]>0:
            power_show.append(power_list[i])
            soc_show.append(soc_list[i])
            #vol_show.append(vol_list[i])
            #print(f"out_vol is {vol_list[i]}")
    allcurve_power_show.append(power_show)
    allcurve_soc_show.append(soc_show)
    #allcurve_vol_show.append(vol_show)

    #allcurve_limited_soc.append(soc_list_limited)
    #allcurve_limited_power.append(power_list_limited)
    curve_index = curve_index+1

fig_name=""
if curve_index ==1:
    fig_name = sys.argv[1][-20:-5] #xxxe00ee1004db1_59.json->e00ee1004db1_59
else:
    #concatenate each name's mac part to substitue a name
    fig_name=sys.argv[1][-12:-5]
    for i in range(curve_index-1):
      fig_name = fig_name + "," + sys.argv[2+i][-12:-5]

fig_name = fig_name + "_soc.png"

plt.figure()
'''

'''
plt.subplot(2,1,1)
index=0
for name in allcurve_name:
    plt.plot(allcurve_soc_show[index],allcurve_vol_show[index],label=name)
    index=index+1

plt.legend()
plt.title('out_vol')

plt.subplot(2,1,2)
'''
'''
index=0
for name in allcurve_name:
    plt.plot(allcurve_soc_show[index],allcurve_power_show[index],label=name)
    #plt.scatter(allcurve_limited_soc[index],allcurve_limited_power[index],marker='x')
    index=index+1

plt.legend()
plt.title('targe_power')
plt.savefig(fig_name)
print(f"the picture is saved as {fig_name}")
plt.show()
'''    
              
            
            
            




