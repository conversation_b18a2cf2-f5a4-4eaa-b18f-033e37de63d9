# 导入必要的库
import pytz
from datetime import datetime
import logging

# 获取logger
logger = logging.getLogger(__name__)

# 国家和地区时区映射表 - 支持中文、英文和ISO国家代码
TIMEZONE_MAPPING = {
    # 常见的ISO国家代码
    "CN": "Asia/Shanghai",           # 中国
    "NL": "Europe/Amsterdam",        # 荷兰
    "DE": "Europe/Berlin",           # 德国
    "FR": "Europe/Paris",            # 法国
    "GB": "Europe/London",           # 英国
    "UK": "Europe/London",           # 英国
    "IT": "Europe/Rome",             # 意大利
    "ES": "Europe/Madrid",           # 西班牙
    "BE": "Europe/Brussels",         # 比利时
    "CH": "Europe/Zurich",           # 瑞士
    "AT": "Europe/Vienna",           # 奥地利
    "SE": "Europe/Stockholm",        # 瑞典
    "NO": "Europe/Oslo",             # 挪威
    "DK": "Europe/Copenhagen",       # 丹麦
    "FI": "Europe/Helsinki",         # 芬兰
    "PL": "Europe/Warsaw",           # 波兰
    "CZ": "Europe/Prague",           # 捷克
    "HU": "Europe/Budapest",         # 匈牙利
    "GR": "Europe/Athens",           # 希腊
    "PT": "Europe/Lisbon",           # 葡萄牙
    "IE": "Europe/Dublin",           # 爱尔兰
    "RU": "Europe/Moscow",           # 俄罗斯
    "JP": "Asia/Tokyo",              # 日本
    "KR": "Asia/Seoul",              # 韩国
    "IN": "Asia/Kolkata",            # 印度
    "SG": "Asia/Singapore",          # 新加坡
    "US": "America/New_York",        # 美国（东部时区）
    "CA": "America/Toronto",         # 加拿大
    "AU": "Australia/Sydney",        # 澳大利亚
    "NZ": "Pacific/Auckland",        # 新西兰
    "BR": "America/Sao_Paulo",       # 巴西
    "MX": "America/Mexico_City",     # 墨西哥
    "ZA": "Africa/Johannesburg",     # 南非
    "EG": "Africa/Cairo",            # 埃及
    
    # 欧洲主要国家
    "德国": "Europe/Berlin",
    "Germany": "Europe/Berlin",
    "法国": "Europe/Paris",
    "France": "Europe/Paris",
    "英国": "Europe/London",
    "United Kingdom": "Europe/London",
    "UK": "Europe/London",
    "意大利": "Europe/Rome",
    "Italy": "Europe/Rome",
    "西班牙": "Europe/Madrid",
    "Spain": "Europe/Madrid",
    "荷兰": "Europe/Amsterdam",
    "Netherlands": "Europe/Amsterdam",
    "比利时": "Europe/Brussels",
    "Belgium": "Europe/Brussels",
    "瑞士": "Europe/Zurich",
    "Switzerland": "Europe/Zurich",
    "奥地利": "Europe/Vienna",
    "Austria": "Europe/Vienna",
    "瑞典": "Europe/Stockholm",
    "Sweden": "Europe/Stockholm",
    "挪威": "Europe/Oslo",
    "Norway": "Europe/Oslo",
    "丹麦": "Europe/Copenhagen",
    "Denmark": "Europe/Copenhagen",
    "芬兰": "Europe/Helsinki",
    "Finland": "Europe/Helsinki",
    "波兰": "Europe/Warsaw",
    "Poland": "Europe/Warsaw",
    "捷克": "Europe/Prague",
    "Czech Republic": "Europe/Prague",
    "Czech": "Europe/Prague",
    "匈牙利": "Europe/Budapest",
    "Hungary": "Europe/Budapest",
    "希腊": "Europe/Athens",
    "Greece": "Europe/Athens",
    "葡萄牙": "Europe/Lisbon",
    "Portugal": "Europe/Lisbon",
    "爱尔兰": "Europe/Dublin",
    "Ireland": "Europe/Dublin",
    "俄罗斯": "Europe/Moscow",
    "Russia": "Europe/Moscow",

    # 亚洲主要国家
    "中国": "Asia/Shanghai",
    "China": "Asia/Shanghai",
    "日本": "Asia/Tokyo",
    "Japan": "Asia/Tokyo",
    "韩国": "Asia/Seoul",
    "South Korea": "Asia/Seoul",
    "Korea": "Asia/Seoul",
    "印度": "Asia/Kolkata",
    "India": "Asia/Kolkata",
    "新加坡": "Asia/Singapore",
    "Singapore": "Asia/Singapore",
    "马来西亚": "Asia/Kuala_Lumpur",
    "Malaysia": "Asia/Kuala_Lumpur",
    "泰国": "Asia/Bangkok",
    "Thailand": "Asia/Bangkok",
    "越南": "Asia/Ho_Chi_Minh",
    "Vietnam": "Asia/Ho_Chi_Minh",
    "菲律宾": "Asia/Manila",
    "Philippines": "Asia/Manila",
    "印度尼西亚": "Asia/Jakarta",
    "Indonesia": "Asia/Jakarta",
    "巴基斯坦": "Asia/Karachi",
    "Pakistan": "Asia/Karachi",
    "孟加拉国": "Asia/Dhaka",
    "Bangladesh": "Asia/Dhaka",
    "斯里兰卡": "Asia/Colombo",
    "Sri Lanka": "Asia/Colombo",
    "伊朗": "Asia/Tehran",
    "Iran": "Asia/Tehran",
    "沙特阿拉伯": "Asia/Riyadh",
    "Saudi Arabia": "Asia/Riyadh",
    "阿联酋": "Asia/Dubai",
    "United Arab Emirates": "Asia/Dubai",
    "UAE": "Asia/Dubai",
    "以色列": "Asia/Jerusalem",
    "Israel": "Asia/Jerusalem",
    "土耳其": "Europe/Istanbul",
    "Turkey": "Europe/Istanbul",
    "哈萨克斯坦": "Asia/Almaty",
    "Kazakhstan": "Asia/Almaty",
    "乌兹别克斯坦": "Asia/Tashkent",
    "Uzbekistan": "Asia/Tashkent",
    "蒙古": "Asia/Ulaanbaatar",
    "Mongolia": "Asia/Ulaanbaatar",
    "朝鲜": "Asia/Pyongyang",
    "North Korea": "Asia/Pyongyang",
    "台湾": "Asia/Taipei",
    "Taiwan": "Asia/Taipei",
    "香港": "Asia/Hong_Kong",
    "Hong Kong": "Asia/Hong_Kong",
    "澳门": "Asia/Macau",
    "Macau": "Asia/Macau",
    "Macao": "Asia/Macau",

    # 美国主要时区
    "美国东部": "America/New_York",
    "US Eastern": "America/New_York",
    "Eastern Time": "America/New_York",
    "ET": "America/New_York",
    "美国中部": "America/Chicago",
    "US Central": "America/Chicago",
    "Central Time": "America/Chicago",
    "CT": "America/Chicago",
    "美国山地": "America/Denver",
    "US Mountain": "America/Denver",
    "Mountain Time": "America/Denver",
    "MT": "America/Denver",
    "美国太平洋": "America/Los_Angeles",
    "US Pacific": "America/Los_Angeles",
    "Pacific Time": "America/Los_Angeles",
    "PT": "America/Los_Angeles",
    "美国阿拉斯加": "America/Anchorage",
    "US Alaska": "America/Anchorage",
    "Alaska Time": "America/Anchorage",
    "AKT": "America/Anchorage",
    "美国夏威夷": "Pacific/Honolulu",
    "US Hawaii": "Pacific/Honolulu",
    "Hawaii Time": "Pacific/Honolulu",
    "HST": "Pacific/Honolulu",

    # 其他重要国家
    "加拿大": "America/Toronto",
    "Canada": "America/Toronto",
    "墨西哥": "America/Mexico_City",
    "Mexico": "America/Mexico_City",
    "巴西": "America/Sao_Paulo",
    "Brazil": "America/Sao_Paulo",
    "阿根廷": "America/Argentina/Buenos_Aires",
    "Argentina": "America/Argentina/Buenos_Aires",
    "智利": "America/Santiago",
    "Chile": "America/Santiago",
    "秘鲁": "America/Lima",
    "Peru": "America/Lima",
    "哥伦比亚": "America/Bogota",
    "Colombia": "America/Bogota",
    "委内瑞拉": "America/Caracas",
    "Venezuela": "America/Caracas",
    "澳大利亚": "Australia/Sydney",
    "Australia": "Australia/Sydney",
    "新西兰": "Pacific/Auckland",
    "New Zealand": "Pacific/Auckland",
    "南非": "Africa/Johannesburg",
    "South Africa": "Africa/Johannesburg",
    "埃及": "Africa/Cairo",
    "Egypt": "Africa/Cairo",
    "尼日利亚": "Africa/Lagos",
    "Nigeria": "Africa/Lagos",
    "肯尼亚": "Africa/Nairobi",
    "Kenya": "Africa/Nairobi",
    "摩洛哥": "Africa/Casablanca",
    "Morocco": "Africa/Casablanca"
}

# 反向映射：时区到国家/地区
TIMEZONE_TO_COUNTRY = {v: k for k, v in TIMEZONE_MAPPING.items()}


def convert_utc_to_local_time(current_time, region, site_no=None):
    """
    将UTC时间转换为指定地区的当地时间，自动处理夏令时
    
    Args:
        current_time (datetime): 需要转换的时间（可以是naive datetime或带时区的datetime）
        region (str): 地区代码（如CN、NL等）或地区名称
        site_no (str, optional): 场站编号，用于日志记录
        
    Returns:
        datetime: 转换后的当地时间（带时区信息），如果转换失败返回None
        
    Examples:
        >>> utc_time = datetime(2024, 7, 15, 10, 0, 0)
        >>> local_time = convert_utc_to_local_time(utc_time, "NL", "NL001")
        >>> print(local_time)  # 2024-07-15 12:00:00+02:00 CEST (夏令时)
        
        >>> winter_time = datetime(2024, 1, 15, 10, 0, 0)
        >>> local_time = convert_utc_to_local_time(winter_time, "NL", "NL001")
        >>> print(local_time)  # 2024-01-15 11:00:00+01:00 CET (冬令时)
    """
    try:
        # 根据region获取时区
        timezone_str = get_timezone_with_fallback(region, "UTC")
        site_info = f"场站 {site_no}" if site_no else f"地区 {region}"
        logger.debug(f"{site_info} region={region}, 使用时区={timezone_str}")
        
        # 将当前时间转换为场站当地时区
        local_tz = pytz.timezone(timezone_str)
        
        # 处理时区信息
        if current_time.tzinfo is None:
            # 如果是naive datetime，假设为UTC时间
            current_time = current_time.replace(tzinfo=pytz.UTC)
        elif current_time.tzinfo != pytz.UTC:
            # 如果已有时区但不是UTC，先转换为UTC
            current_time = current_time.astimezone(pytz.UTC)
            
        # 转换为当地时间
        local_time = current_time.astimezone(local_tz)
        logger.debug(f"{site_info} UTC时间 {current_time} 转换为当地时间 {local_time}")
        
        return local_time
        
    except Exception as e:
        error_info = f"场站 {site_no}" if site_no else f"地区 {region}"
        logger.error(f"{error_info} 时区转换失败: {e}")
        return None


def get_timezone(country_name):
    """
    根据国家/地区名称获取对应的时区

    Args:
        country_name (str): 国家/地区名称（支持中文或英文）

    Returns:
        str: 对应的时区字符串，如果未找到则返回None
    """
    return TIMEZONE_MAPPING.get(country_name)


def get_timezone_with_fallback(country_name, default_timezone="UTC"):
    """
    根据国家/地区名称获取对应的时区，如果未找到则返回默认时区

    Args:
        country_name (str): 国家/地区名称（支持中文或英文）
        default_timezone (str): 默认时区，默认为UTC

    Returns:
        str: 对应的时区字符串
    """
    return TIMEZONE_MAPPING.get(country_name, default_timezone)


def list_supported_countries():
    """
    获取所有支持的国家/地区列表

    Returns:
        list: 支持的国家/地区名称列表
    """
    return list(TIMEZONE_MAPPING.keys())


def search_countries(keyword):
    """
    根据关键词搜索国家/地区

    Args:
        keyword (str): 搜索关键词

    Returns:
        dict: 匹配的国家/地区及其对应的时区
    """
    results = {}
    keyword_lower = keyword.lower()

    for country, timezone in TIMEZONE_MAPPING.items():
        if keyword_lower in country.lower():
            results[country] = timezone

    return results


if __name__ == "__main__":
    # 示例用法
    print("中国时区:", get_timezone("中国"))
    print("日本时区:", get_timezone("日本"))
    print("德国时区:", get_timezone("德国"))
    print("美国东部时区:", get_timezone("美国东部"))

    print("\n时区对应的国家:")
    print("Asia/Shanghai 对应:", get_timezone("Asia/Shanghai"))
    print("Europe/Berlin 对应:", get_timezone("Europe/Berlin"))

    print(f"\n总共支持 {len(TIMEZONE_MAPPING)} 个国家/地区")
    print("欧洲国家:", [k for k in TIMEZONE_MAPPING.keys() if "Europe" in TIMEZONE_MAPPING[k]])
    print("亚洲国家:", [k for k in TIMEZONE_MAPPING.keys() if "Asia" in TIMEZONE_MAPPING[k]])
