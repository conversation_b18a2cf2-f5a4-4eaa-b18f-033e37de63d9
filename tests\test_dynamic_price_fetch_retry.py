import unittest
from unittest.mock import patch, MagicMock
import time

from application.algorithm_schedule.dynamic_price_fetch import send_electricity_price_with_retry


class TestDynamicPriceFetchRetry(unittest.TestCase):
    """测试动态电价获取的重试机制"""

    @patch('application.algorithm_schedule.dynamic_price_fetch.EnergyCloudAPI.send_electricity_price_curve')
    @patch('time.sleep')  # Mock sleep to speed up tests
    def test_send_electricity_price_with_retry_success_first_attempt(self, mock_sleep, mock_send):
        """测试第一次尝试就成功的情况"""
        # 准备测试数据
        price_segments = {
            "biz_seq": "test_biz_seq_12345678",
            "electricity_price": [
                {
                    "region": "NO1",
                    "grid_name": "Nord Pool",
                    "time_zone": "UTC+0",
                    "freq": "T-1",
                    "can_use": True,
                    "price_data": []
                }
            ]
        }

        # 模拟成功响应
        mock_send.return_value = True

        # 执行测试
        result = send_electricity_price_with_retry(price_segments)

        # 验证结果
        self.assertTrue(result)
        mock_send.assert_called_once_with(price_segments=price_segments)
        mock_sleep.assert_not_called()  # 第一次成功，不应该有sleep

    @patch('application.algorithm_schedule.dynamic_price_fetch.EnergyCloudAPI.send_electricity_price_curve')
    @patch('time.sleep')  # Mock sleep to speed up tests
    def test_send_electricity_price_with_retry_success_after_retries(self, mock_sleep, mock_send):
        """测试重试后成功的情况"""
        # 准备测试数据
        price_segments = {
            "biz_seq": "test_biz_seq_12345678",
            "electricity_price": [
                {
                    "region": "NO1",
                    "grid_name": "Nord Pool",
                    "time_zone": "UTC+0",
                    "freq": "T-1",
                    "can_use": True,
                    "price_data": []
                }
            ]
        }

        # 模拟前两次失败，第三次成功
        mock_send.side_effect = [
            False,  # 第1次失败
            False,  # 第2次失败
            True    # 第3次成功
        ]

        # 执行测试
        result = send_electricity_price_with_retry(price_segments)

        # 验证结果
        self.assertTrue(result)
        self.assertEqual(mock_send.call_count, 3)
        self.assertEqual(mock_sleep.call_count, 2)  # 应该有2次sleep（1分钟和2分钟）
        
        # 验证sleep的调用参数（转换为秒）
        expected_sleep_calls = [unittest.mock.call(60), unittest.mock.call(120)]  # 1分钟和2分钟
        mock_sleep.assert_has_calls(expected_sleep_calls)

    @patch('application.algorithm_schedule.dynamic_price_fetch.EnergyCloudAPI.send_electricity_price_curve')
    @patch('time.sleep')  # Mock sleep to speed up tests
    def test_send_electricity_price_with_retry_all_attempts_fail(self, mock_sleep, mock_send):
        """测试所有重试都失败的情况"""
        # 准备测试数据
        price_segments = {
            "biz_seq": "test_biz_seq_12345678",
            "electricity_price": [
                {
                    "region": "NO1",
                    "grid_name": "Nord Pool",
                    "time_zone": "UTC+0",
                    "freq": "T-1",
                    "can_use": True,
                    "price_data": []
                }
            ]
        }

        # 模拟所有尝试都失败
        mock_send.return_value = False

        # 执行测试
        result = send_electricity_price_with_retry(price_segments)

        # 验证结果
        self.assertFalse(result)
        self.assertEqual(mock_send.call_count, 8)  # 1次初始 + 7次重试 = 8次
        self.assertEqual(mock_sleep.call_count, 7)  # 应该有7次sleep
        
        # 验证sleep的调用参数（转换为秒）
        expected_sleep_calls = [
            unittest.mock.call(60),    # 1分钟
            unittest.mock.call(120),   # 2分钟
            unittest.mock.call(240),   # 4分钟
            unittest.mock.call(480),   # 8分钟
            unittest.mock.call(960),   # 16分钟
            unittest.mock.call(1920),  # 32分钟
            unittest.mock.call(3840)   # 64分钟
        ]
        mock_sleep.assert_has_calls(expected_sleep_calls)

    @patch('application.algorithm_schedule.dynamic_price_fetch.EnergyCloudAPI.send_electricity_price_curve')
    @patch('time.sleep')  # Mock sleep to speed up tests
    def test_send_electricity_price_with_retry_exception_handling(self, mock_sleep, mock_send):
        """测试异常处理和重试"""
        # 准备测试数据
        price_segments = {
            "biz_seq": "test_biz_seq_12345678",
            "electricity_price": [
                {
                    "region": "NO1",
                    "grid_name": "Nord Pool",
                    "time_zone": "UTC+0",
                    "freq": "T-1",
                    "can_use": True,
                    "price_data": []
                }
            ]
        }

        # 模拟前两次抛出异常，第三次成功
        mock_send.side_effect = [
            Exception("Network error"),  # 第1次异常
            Exception("Timeout error"),  # 第2次异常
            True  # 第3次成功
        ]

        # 执行测试
        result = send_electricity_price_with_retry(price_segments)

        # 验证结果
        self.assertTrue(result)
        self.assertEqual(mock_send.call_count, 3)
        self.assertEqual(mock_sleep.call_count, 2)  # 应该有2次sleep


if __name__ == '__main__':
    unittest.main()
