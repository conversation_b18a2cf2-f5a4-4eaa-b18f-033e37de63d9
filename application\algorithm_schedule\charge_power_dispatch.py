import datetime
import json
import time

from sqlalchemy import select, insert

from application.algorithm.power_distribution.linear_programming import LinearProgrammingPowerDistributionSolver
from application.db_operate.models import PVAndMeterRealtimeDataDB, ESRealtimeDataDB, PileDB, SiteDB, \
    ChargerSessionDB, ChargerNonSuppressPowerPredictionDB, EVModelDB, PileTaskDB
from application.db_operate.db_operate import DBOperate
from application.algorithm_schedule.status_enumeration import ChargePileStatus
from application.utils.logger import setup_logger

logger = setup_logger("charge_power_dispatch", direction="algorithm_schedule")


class ChargePowerDispatch:
    def __init__(self, site_no: str, data: dict, ts_delta: int = 2):
        self.site_no, self.data = site_no, data
        # 获取初始化输入参数
        self.input_params = self._get_init_input_params()

        # UTC 时间的毫秒时间戳  <=> int(time.time() * 1000)  # self.cur_ts_ms = 1750320321000
        self.cur_ts_ms = int(datetime.datetime.now(datetime.UTC).timestamp() * 1000)
        self.ts_delta = ts_delta
        # 获取数据库操作实例
        self.db_operate = DBOperate()

    @staticmethod
    def _get_init_input_params() -> dict:
        input_params = {
            "pv_power": None,
            "es_power": None,
            "site_grid_limit": None,
            "site_demand_limit": None,
            "site_pile_info": None,
            "module_info": {},
            "demand_info": {}
        }
        return input_params

    def _set_basic_info(self):
        with self.db_operate.get_db() as db:
            # 获取 pv_power，时间过滤条件，获取离当前时间最近的数据
            stmt = select(PVAndMeterRealtimeDataDB).where(
                PVAndMeterRealtimeDataDB.site_no == self.site_no,
                PVAndMeterRealtimeDataDB.ts >= self.cur_ts_ms - self.ts_delta * 1000,
                PVAndMeterRealtimeDataDB.ts <= self.cur_ts_ms + self.ts_delta * 1000
            )
            # 获取所有符合条件的数据
            pv_and_meter_realtime_data_list = list(db.scalars(stmt).all())
            # 检查是否有数据，如果没有数据则设置默认值
            if pv_and_meter_realtime_data_list:
                # 找到离 cur_ts_ms 最近的数据
                closest_data = min(pv_and_meter_realtime_data_list, key=lambda x: abs(x.ts - self.cur_ts_ms))
                self.input_params["pv_power"] = closest_data.pv_power
                logger.debug(f"场站 {self.site_no} 找到PV数据，ts={closest_data.ts}, pv_power={closest_data.pv_power}")
            else:
                # 如果没有找到数据，设置默认值并记录警告
                self.input_params["pv_power"] = 0
                logger.warning(f"场站 {self.site_no} 在时间范围 [{self.cur_ts_ms - self.ts_delta * 1000}, {self.cur_ts_ms + self.ts_delta * 1000}] 内未找到PV数据，使用默认值0")

            # 获取 es_power
            stmt = select(ESRealtimeDataDB).where(
                ESRealtimeDataDB.site_no == self.site_no,
                ESRealtimeDataDB.ts >= self.cur_ts_ms - self.ts_delta * 1000,
                ESRealtimeDataDB.ts <= self.cur_ts_ms + self.ts_delta * 1000
            )
            es_realtime_data_list = list(db.scalars(stmt).all())
            # 检查是否有数据，如果没有数据则设置默认值
            if es_realtime_data_list:
                closest_data = min(es_realtime_data_list, key=lambda x: abs(x.ts - self.cur_ts_ms))
                self.input_params["es_power"] = closest_data.es_power
                logger.debug(f"场站 {self.site_no} 找到ES数据，ts={closest_data.ts}, es_power={closest_data.es_power}")
            else:
                # 如果没有找到数据，设置默认值并记录警告
                self.input_params["es_power"] = 0
                logger.warning(f"场站 {self.site_no} 在时间范围 [{self.cur_ts_ms - self.ts_delta * 1000}, {self.cur_ts_ms + self.ts_delta * 1000}] 内未找到ES数据，使用默认值0")

            # 获取 site_grid_limit 和 site_demand_limit
            stmt = select(SiteDB).where(SiteDB.site_no == self.site_no)
            site_info = db.scalar(stmt)
            if site_info:
                self.input_params["site_grid_limit"] = site_info.site_grid_limit
                logger.debug(f"场站 {self.site_no} 找到站点信息，grid_limit={site_info.site_grid_limit}")
            else:
                # 如果没有找到场站信息，设置默认值并记录错误
                self.input_params["site_grid_limit"] = 100  # 默认100kW
                logger.error(f"场站 {self.site_no} 未找到站点信息，使用默认值：grid_limit=100")
            
            # 从SiteDemandDataDB获取当前时间对应的demand_limit，传递site_info避免重复查询
            current_time = datetime.datetime.fromtimestamp(self.cur_ts_ms / 1000, tz=datetime.timezone.utc)
            self.input_params["site_demand_limit"] = self.db_operate.get_current_demand_limit(self.site_no,
                                                                                              current_time, site_info)

            # 获取 场站下的充电桩列表
            stmt = select(PileDB.pile_sn).where(PileDB.site_no == self.site_no).order_by(PileDB.pile_sn.asc())
            self.input_params["site_pile_info"] = list(db.scalars(stmt).all())

    def _set_module_info(self):
        # 构建module_info，根据pile_sn_list查询所有模块信息
        module_info = []
        with self.db_operate.get_db() as db:
            stmt = select(PileDB).where(
                PileDB.pile_sn.in_(self.input_params["site_pile_info"])).order_by(PileDB.pile_sn.asc())
            pile_info_list = list(db.scalars(stmt).all())

        for pile_info in pile_info_list:
            # TODO： 模块与充电枪的直连信息需要完善，当前数据库中没有
            item = {"sn": pile_info.pile_sn, "chargeType": pile_info.pile_type, "gunNum": pile_info.gun_num,
                    "ratedPower": pile_info.rated_power,
                    "moduleList": [], "gunBusList": [], "gunMaxCurr": []}

            modules = self.db_operate.query_module_list_by_pile_sn(pile_info.pile_sn)
            module_list = [{"moduleNo": obj.module_no, "unitNum": obj.unit_num,
                            "unitPower": obj.unit_power} for obj in modules]
            module_list.sort(key=lambda x: x["moduleNo"])
            item["moduleList"] = module_list

            # TODO： 充电枪与充电桩的直连信息需要完善，当前数据库中没有
            item["gunBusList"] = []
            item["gunMaxCurr"] = []

            module_info.append(item)

        self.input_params["module_info"] = module_info

    def _set_demand_info(self):
        with self.db_operate.get_db() as db:
            # 获取所有正在充电的充电枪
            stmt = select(ChargerSessionDB).where(
                ChargerSessionDB.site_no == self.site_no,
                ChargerSessionDB.status.in_([ChargePileStatus.VEHICLE_CONNECTED, ChargePileStatus.FORMAL_CHARGING])
            )
            charger_session_list = list(db.scalars(stmt).all())
            local_id_list = [obj.local_id for obj in charger_session_list]
            local_id_to_charger_session_dict = {obj.local_id: obj for obj in charger_session_list}

            if local_id_list:
                stmt = select(EVModelDB).where(EVModelDB.local_id.in_(local_id_list))
                ev_model_list = list(db.scalars(stmt).all())
                local_id_to_ev_model_dict = {obj.local_id: obj for obj in ev_model_list}
                logger.debug(f"场站 {self.site_no} 正在充电的设备数量: {len(local_id_list)}, 找到车辆模型数量: {len(ev_model_list)}")
            else:
                ev_model_list = []
                local_id_to_ev_model_dict = {}
                logger.info(f"场站 {self.site_no} 没有正在充电的设备")

            # 将时间戳转换为datetime对象进行比较
            cur_datetime = datetime.datetime.fromtimestamp(self.cur_ts_ms / 1000, tz=datetime.timezone.utc)
            # 获取所有正在充电的充电枪的非压制功率预测曲线
            stmt = select(ChargerNonSuppressPowerPredictionDB).where(
                ChargerNonSuppressPowerPredictionDB.local_id.in_(local_id_list),
                ChargerNonSuppressPowerPredictionDB.curve_start_time <= cur_datetime,
                ChargerNonSuppressPowerPredictionDB.curve_end_time >= cur_datetime
            )
            charger_non_suppress_power_prediction_list = list(db.scalars(stmt).all())

            # 构建demand_info
            for pred_item in charger_non_suppress_power_prediction_list:
                local_id = pred_item.local_id
                # 检查local_id是否存在于字典中
                if local_id not in local_id_to_charger_session_dict:
                    logger.warning(f"local_id {local_id} 不存在于充电会话字典中，跳过该预测数据")
                    continue
                if local_id not in local_id_to_ev_model_dict:
                    logger.warning(f"local_id {local_id} 不存在于车辆模型字典中，跳过该预测数据")
                    continue
                
                charger_sn = local_id_to_charger_session_dict[local_id].charger_sn
                utc_timestamp = int(pred_item.curve_start_time.replace(tzinfo=datetime.timezone.utc).timestamp())
                dic_item = {
                    "predicted_time": utc_timestamp,
                    "vehicle_non_suppressed_power_list": json.loads(pred_item.power_list),
                    "vehicle_vol": local_id_to_ev_model_dict[local_id].max_voltage
                }
                self.input_params["demand_info"][charger_sn] = dic_item

    def get_dispatch_result(self):
        try:
            self._set_basic_info()

            self._set_module_info()

            self._set_demand_info()

            # 检查site_demand_limit是否为None，如果为None则不进行功率分配
            if self.input_params["site_demand_limit"] is None:
                logger.warning(f"场站 {self.site_no} 没有找到demand数据，跳过功率分配")
                return {"status": False, "scheduling_time": int(time.time()), "site_charger_power_allocations": {}}

            solver = LinearProgrammingPowerDistributionSolver(self.input_params)
            ret_values, pgun_solver_values = solver.get_solve_result()
            logger.info(f"场站 {self.site_no} 充电功率调度输入参数: {str(self.input_params)}")
            logger.info(f"场站 {self.site_no} 充电功率调度结果: {json.dumps(pgun_solver_values)}")
            return ret_values
        except Exception as e:
            logger.error(f"场站 {self.site_no} 充电功率调度失败: {e}", exc_info=True)
            return {"status": False, "scheduling_time": int(time.time()), "site_charger_power_allocations": {}}


def charge_power_dispatch(site_no: str, data: dict):
    """
    充电桩功率分配算法调度入口
    :param site_no: 场站编号
    :param data: 相关输入数据
    :return: 分配结果
    """
    chd = ChargePowerDispatch(site_no, data)
    ret_values = chd.get_dispatch_result()

    # 将 ret_values 写入数据库
    with chd.db_operate.get_db() as db:
        ms_id = f"{site_no}_{int(time.time() * 1000)}"  # 毫秒时间戳
        scheduling_time = datetime.datetime.fromtimestamp(ret_values["scheduling_time"], tz=datetime.timezone.utc)
        stmt = insert(PileTaskDB).values(
            id=ms_id,
            site_no=site_no, generate_time=scheduling_time,
            pile_power_list=json.dumps(ret_values["site_charger_power_allocations"]))
        db.execute(stmt)
        db.commit()
    return ret_values


if __name__ == "__main__":
    # 测试及Debug时，需要首先执行测试数据生成脚本 
    # 1. ems/application/db_operate/sql_scripts/charge_power_dispatch.sql，在数据库中生成测试数据
    # 2. 执行本文件，查看输出结果进行Debug及测试
    ret = charge_power_dispatch("SITE001", {})
    print(ret)
