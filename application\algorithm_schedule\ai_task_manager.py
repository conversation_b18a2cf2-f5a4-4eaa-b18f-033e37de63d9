import threading
from datetime import datetime, timedelta

import pytz

from application.algorithm_schedule.charge_power_dispatch import charge_power_dispatch
from application.algorithm_schedule.dynamic_price_fetch import dynamic_price_fetch
from application.algorithm_schedule.fused_load_forecast import fused_load_forecast
from application.algorithm_schedule.long_term_load_forecast import long_term_load_forecast
from application.algorithm_schedule.non_suppress_power import non_suppress_power
from application.algorithm_schedule.pv_control import pv_control
# 导入所有算法调度脚本
from application.algorithm_schedule.pv_forecast import pv_forecast
from application.algorithm_schedule.storage_dispatch import storage_dispatch
from application.config import ConfigInterval
from application.kafka_operate.send_message_queue_manager import send_control_result_to_queue
from application.utils.event_bus import EventType, event_bus
from application.utils.logger import setup_logger
from application.api_manage.energy_cloud_api import EnergyCloudAPI

logger = setup_logger("ai_task_manager", direction="algorithm_schedule")


def _is_diff_large(new, old, threshold=10):
    """
    比较预测曲线的差异是否超过阈值
    
    支持两种数据格式：
    1. 光伏预测: {'pv_predicted_list': [功率数组]}
    2. 融合负载: [(datetime, 功率值), ...]
    
    Args:
        new: 新的预测结果
        old: 旧的预测结果  
        threshold: 功率差异阈值（kW）
    
    Returns:
        bool: 差异是否超过阈值
    """
    try:
        # 如果任一值为None，认为有差异
        if new is None or old is None:
            return True

        # 提取功率数组
        new_powers = _extract_power_array(new)
        old_powers = _extract_power_array(old)
        
        # 如果提取失败，认为有差异
        if new_powers is None or old_powers is None:
            return True
            
        # 比较功率数组，从头到尾逐点比较（即使长度不同也没关系）
        min_len = min(len(new_powers), len(old_powers))
        if min_len == 0:
            return len(new_powers) != len(old_powers)
            
        # 逐点比较，任意一点差异超过阈值就返回True
        for i in range(min_len):
            if abs((new_powers[i] or 0) - (old_powers[i] or 0)) > threshold:
                return True
                
        # 如果长度不同，也认为有差异
        return len(new_powers) != len(old_powers)
        
    except Exception as e:
        logger.error(f"比较预测曲线差异时出错: {e}")
        return True


def _extract_power_array(data):
    """
    从不同格式的预测结果中提取功率数组
    
    Args:
        data: 预测结果数据
        
    Returns:
        list: 功率数组，提取失败时返回None
    """
    try:
        # 1. 光伏预测字典格式: {'pv_predicted_list': [...]}
        if isinstance(data, dict) and 'pv_predicted_list' in data:
            return data.get('pv_predicted_list', [])
        
        # 2. 融合负载tuple列表格式: [(datetime, power), ...]
        elif isinstance(data, list) and data and len(data[0]) >= 2:
            return [item[1] for item in data if len(item) >= 2]
        
        # 3. 直接的数值列表: [power1, power2, ...]
        elif isinstance(data, list):
            return data
            
        else:
            logger.warning(f"无法提取功率数组，未知数据格式: {type(data)}")
            return None
            
    except Exception as e:
        logger.error(f"提取功率数组时出错: {e}")
        return None


class SiteTaskManager:
    def __init__(self, site_no):
        self.site_no = site_no
        self.tasks = []
        self.running = True
        self._stop_event = threading.Event()  # 添加停止事件
        self.last_fused_load_result = None
        self.last_pv_forecast_result = None
        self.last_dynamic_price_biz_seq = None
        self.last_buy_price = None
        self.last_short_term_forecast_time = None
        self.first_run_complete = False

        # 订阅插枪事件
        self._forecast_lock = threading.Lock()  # 添加锁以保护事件触发的预测操作
        event_bus.subscribe(EventType.CHARGER_PLUGGED, self._handle_charger_plugged)
        # 动态电价变化事件
        event_bus.subscribe(EventType.DYNAMIC_PRICE_CHANGE, self._handle_dynamic_price_change)

        self._register_all_tasks()

    def _handle_dynamic_price_change(self):
        logger.info(f'场站{self.site_no}动态电价发生变化,触发储能调度')
        self.storage_dispatch(trigger='dynamic_price_diff')

    def stop(self):
        """停止任务管理器"""
        self.running = False
        self._stop_event.set()  # 设置停止事件，唤醒所有在 sleep 的线程
        logger.info(f"已发送停止信号: {self.site_no}")

        # 直接等待所有线程退出
        for task in self.tasks:
            if task.is_alive():
                task.join(timeout=1)
                if task.is_alive():
                    logger.warning(f"线程 {task.name} 未能在1秒内退出，可能存在问题")
                else:
                    logger.info(f"线程 {task.name} 已成功退出")

        logger.info(f"场站 {self.site_no} 任务清理完成")
        
        # 取消事件订阅
        event_bus.unsubscribe(EventType.CHARGER_PLUGGED, self._handle_charger_plugged)

    def _handle_charger_plugged(self, site_no: str, charger_sn: str) -> None:
        """
        处理插枪事件

        Args:
            site_no: 场站编号
            charger_sn: 充电枪编号
        """
        # 只处理本场站的事件
        if site_no != self.site_no:
            return

        # 检查是否需要触发预测
        with self._forecast_lock:
            current_time = datetime.now()
            logger.info(f"Triggering load forecast for site {site_no} due to charger {charger_sn} plug-in")
            self.short_term_load_forecast()
            self.last_short_term_forecast_time = current_time

    def pv_forecast_and_control(self):
        """合并光伏预测和控制任务"""
        # 执行光伏预测
        result = pv_forecast(self.site_no, data={})
        EnergyCloudAPI.send_power_predict_curves(self.site_no, pv_power_curve=result.get("pv_predicted_list"),
                                                 pv_start_time=result.get("predicted_time"), )
        
        # 比较光伏预测结果的差异（自动识别dict类型并比较pv_predicted_list数组）
        if self.last_pv_forecast_result is None or _is_diff_large(result, self.last_pv_forecast_result, 5):
            self.storage_dispatch(trigger='pv_forecast_diff')
        self.last_pv_forecast_result = result

        # 执行光伏控制
        control_result = pv_control(self.site_no, data={})
        if control_result:
            send_control_result_to_queue(control_result, control_type="pv_control", site_no=self.site_no)

    def long_term_load_forecast(self):
        """长期负载预测获取任务"""

        # 执行长期负载预测
        long_term_load_res = long_term_load_forecast(self.site_no, data={})
        EnergyCloudAPI.send_power_predict_curves(self.site_no,
                                                 load_power_curve=long_term_load_res.get(
                                                     "long_term_load_predicted_list"),
                                                 load_start_time=long_term_load_res.get("predicted_time"))

    def daily_dynamic_price_fetch(self):
        """首次启动立即获取电价，以后每天在CET时间的12:30获取电价。"""

        cet = pytz.timezone('CET')

        while self.running:
            if not self.first_run_complete:
                # 首次运行时立即执行
                logger.info("首次运行立即获取电价")
                self._perform_dynamic_price_fetch()
                self.first_run_complete = True

            now = datetime.now(tz=cet)

            # 计算下一个CET时间的12:30
            next_exec_time = (now + timedelta(days=0)).replace(hour=12, minute=30, second=0, microsecond=0)
            if now >= next_exec_time:
                # 如果今天的时间已经过了12:30，就设定为明天的12:30
                next_exec_time += timedelta(days=1)

            sleep_duration = (next_exec_time - now).total_seconds()
            logger.info(f"{self.site_no} 距离下次执行动态电价获取剩余: {sleep_duration}")

            # 休眠直到下一个12:30 PM CET
            if self._stop_event.wait(timeout=sleep_duration):
                break

            self._perform_dynamic_price_fetch()

    def _perform_dynamic_price_fetch(self):
        try:
            cet = pytz.timezone('CET')
            now = datetime.now(tz=cet)
            today = now.date()
            noon = now.replace(hour=12, minute=0, second=0, microsecond=0)
            if now >= noon:
                # 如果时间大于等于12:00，获取当天和明天的电价
                tomorrow = today + timedelta(days=1)
                if not self.first_run_complete:
                    dynamic_price_fetch(self.site_no, today)
                    price = dynamic_price_fetch(self.site_no, tomorrow)
                else:
                    price = dynamic_price_fetch(self.site_no, tomorrow)
            else:
                # 获取当天的电价
                price = dynamic_price_fetch(self.site_no, today)
                logger.info(f'price:{price}')
            
            # 添加价格数据有效性检查
            if price and price.get("biz_seq"):
                if self.last_dynamic_price_biz_seq is None or price.get("biz_seq") != self.last_dynamic_price_biz_seq:
                    self.storage_dispatch(trigger='dynamic_price_diff')
                    logger.info(f"场站 {self.site_no} 电价数据变化，触发储能调度")
                self.last_dynamic_price_biz_seq = price.get("biz_seq")
            else:
                logger.warning(f"场站 {self.site_no} 获取电价数据失败或数据无效，跳过储能调度触发")
        except Exception as e:
            logger.error(f"perform fetch dynamic price failed: {e}")

    def short_term_load_forecast(self):
        # 检查是否在配置的时间间隔内已经进行过短期负载预测
        current_time = datetime.now(pytz.UTC)
        if (self.last_short_term_forecast_time and (current_time - self.last_short_term_forecast_time) <
                timedelta(seconds=ConfigInterval.SHORT_TERM_LOAD_FORECAST_INTERVAL)):
            return

        # 对场站内所有充电车辆进行非压制功率预测（预测到充电结束）
        # 并自动进行场站总需求功率曲线叠加，保存到SiteDemandPrediction表
        non_suppress_power(self.site_no, data={})

        # 统计场站车辆总demand后需要触发融合负载预测
        fused_result = self.fused_load_forecast()
        if self.last_fused_load_result is None or _is_diff_large(fused_result, self.last_fused_load_result):
            self.storage_dispatch(trigger='fused_load_diff')
        self.last_fused_load_result = fused_result
        self.last_short_term_forecast_time = current_time

    def fused_load_forecast(self):
        return fused_load_forecast(self.site_no, data={})

    def storage_dispatch(self, trigger):
        result = storage_dispatch(self.site_no, data={}, trigger=trigger)
        if result:
            EnergyCloudAPI.send_power_predict_curves(self.site_no, es_power_curve=result.get("es_scheduling_strategy"),
                                                     es_start_time=result.get("scheduling_time"))
            send_control_result_to_queue(result, control_type="storage_dispatch", site_no=self.site_no)

    def charge_power_dispatch(self):
        result = charge_power_dispatch(self.site_no, data={})
        if result:
            send_control_result_to_queue(result, control_type="charge_power_dispatch", site_no=self.site_no)

    def _register_all_tasks(self):
        # 注册所有定时任务，间隔参数从ConfigInterval类读取
        self._schedule_task(self.pv_forecast_and_control, ConfigInterval.PV_FORECAST_INTERVAL)
        self._schedule_task(self.long_term_load_forecast, ConfigInterval.LONG_TERM_LOAD_FORECAST_INTERVAL)
        self._schedule_task(self.short_term_load_forecast, ConfigInterval.SHORT_TERM_LOAD_FORECAST_INTERVAL)
        self._schedule_task(self.charge_power_dispatch, ConfigInterval.CHARGE_POWER_DISPATCH_INTERVAL)
        # self._schedule_task(self.daily_dynamic_price_fetch, 0)

    def _schedule_task(self, func, interval):
        def loop():
            while self.running:
                try:
                    func()
                    # 使用 Event 的 wait 替代 sleep，可以被提前唤醒
                    if not self._stop_event.wait(timeout=interval):
                        # 如果是因为超时退出，说明是正常等待
                        continue
                    # 如果是因为事件被设置退出，说明收到了停止信号
                    break
                except Exception as e:
                    logger.error(f"任务执行出错: {e}", exc_info=True)
                    
        t = threading.Thread(target=loop, daemon=True, name=f"task_{func.__name__}")
        t.start()
        self.tasks.append(t)
