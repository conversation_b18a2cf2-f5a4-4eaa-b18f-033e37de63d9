import os
import sys
from pathlib import Path

from crypto import ConfigEncryptor


def main(config_file):
    if not os.path.exists(config_file):
        print(f"Error: Config file {config_file} not found")
        sys.exit(1)

    # 创建加密器
    encryptor = ConfigEncryptor()

    # 加密配置文件
    encrypted_file = encryptor.encrypt_config(config_file)
    print(f"\nConfig file encrypted successfully: {encrypted_file}")

    # 打印使用说明
    print("\nTo use the encrypted config:")
    print("1. Set the environment variable:")
    print(f"   export CONFIG_ENCRYPTION_KEY='{encryptor.key}'")
    print("2. Update your config path to point to the encrypted file:")
    print(f"   export AVANTML_SDK_CONFIG_PATH='{encrypted_file}'")


if __name__ == "__main__":
    # 获取项目根路径
    project_root = Path(__file__).parent.parent.parent
    main(project_root / 'application' / "settings" / "config.yaml")
