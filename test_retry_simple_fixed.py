#!/usr/bin/env python3
"""
简化的动态电价重试机制测试（修复版）
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('retry_test_simple.log', mode='w', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def test_config_and_initialization():
    """测试配置和初始化"""
    logger = setup_logging()
    
    logger.info("=" * 50)
    logger.info("测试动态电价重试机制配置和初始化")
    logger.info("=" * 50)
    
    try:
        from application.algorithm_schedule.dynamic_price_scheduler import DynamicPriceScheduler, DynamicPriceRetryConfig
        
        # 测试配置
        logger.info("配置验证:")
        logger.info(f"  支持的国家: {DynamicPriceRetryConfig.SUPPORTED_COUNTRIES}")
        logger.info(f"  每国家最大重试次数: {DynamicPriceRetryConfig.MAX_RETRIES_PER_COUNTRY}")
        logger.info(f"  重试间隔: {DynamicPriceRetryConfig.RETRY_INTERVALS} 分钟")
        logger.info(f"  最大总重试时间: {DynamicPriceRetryConfig.MAX_TOTAL_RETRY_TIME} 分钟")
        logger.info(f"  电价发布时间: {DynamicPriceRetryConfig.PRICE_PUBLISH_TIME}:10")
        
        # 验证配置的合理性
        assert len(DynamicPriceRetryConfig.SUPPORTED_COUNTRIES) == 6, "应该支持6个国家"
        assert DynamicPriceRetryConfig.MAX_RETRIES_PER_COUNTRY > 0, "最大重试次数应该大于0"
        assert len(DynamicPriceRetryConfig.RETRY_INTERVALS) > 0, "重试间隔列表不能为空"
        assert DynamicPriceRetryConfig.MAX_TOTAL_RETRY_TIME > 0, "最大总重试时间应该大于0"
        
        logger.info("✅ 配置验证通过")
        
        # 测试调度器初始化
        logger.info("\n测试调度器初始化:")
        scheduler = DynamicPriceScheduler()
        
        logger.info(f"  调度器站点: {scheduler.site_no}")
        logger.info(f"  支持的国家数量: {len(scheduler.countries_retry_count)}")
        logger.info(f"  初始重试计数: {scheduler.countries_retry_count}")
        logger.info(f"  初始成功状态: {scheduler.countries_success_status}")
        
        # 验证初始化状态
        assert scheduler.site_no == 'ALL', "站点编号应该是 'ALL'"
        assert len(scheduler.countries_retry_count) == 6, "应该有6个国家的重试计数"
        assert len(scheduler.countries_success_status) == 6, "应该有6个国家的成功状态"
        assert all(count == 0 for count in scheduler.countries_retry_count.values()), "初始重试计数应该都是0"
        assert all(not status for status in scheduler.countries_success_status.values()), "初始成功状态应该都是False"
        
        logger.info("✅ 调度器初始化验证通过")
        
        # 测试状态获取
        logger.info("\n测试状态获取:")
        status = scheduler.get_retry_status()
        
        logger.info("状态信息:")
        logger.info(f"  支持的国家: {status['supported_countries']}")
        logger.info(f"  重试次数统计: {status['countries_retry_count']}")
        logger.info(f"  成功状态: {status['countries_success_status']}")
        logger.info(f"  最后获取日期: {status['last_fetch_date']}")
        logger.info(f"  配置信息: {status['config']}")
        
        # 验证状态信息
        assert 'countries_retry_count' in status, "状态应该包含重试次数"
        assert 'countries_success_status' in status, "状态应该包含成功状态"
        assert 'supported_countries' in status, "状态应该包含支持的国家"
        assert 'config' in status, "状态应该包含配置信息"
        assert status['supported_countries'] == DynamicPriceRetryConfig.SUPPORTED_COUNTRIES, "支持的国家列表应该一致"
        
        logger.info("✅ 状态获取验证通过")
        
        return True
        
    except ImportError as e:
        logger.error(f"导入模块失败: {e}")
        return False
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_data_validation():
    """测试数据验证逻辑"""
    logger = logging.getLogger(__name__)
    
    logger.info("\n" + "=" * 50)
    logger.info("测试数据验证逻辑")
    logger.info("=" * 50)
    
    try:
        from application.algorithm_schedule.dynamic_price_scheduler import DynamicPriceScheduler
        
        scheduler = DynamicPriceScheduler()
        
        # 测试无效数据
        invalid_cases = [
            (None, "空数据"),
            ({}, "空字典"),
            ({'prices': []}, "空prices数组"),
            ({'prices': [{'price': 100, 'deliveryStart': '2024-01-01T00:00:00Z', 'deliveryEnd': '2024-01-01T01:00:00Z'}] * 12}, "数据不完整（12小时）"),
            ({'prices': [{'price': 100}] * 24}, "缺少必要字段"),
        ]
        
        for invalid_data, description in invalid_cases:
            is_valid = scheduler._is_valid_country_price_data(invalid_data, "TEST")
            logger.info(f"无效数据测试 ({description}): {is_valid} (期望: False)")
            assert not is_valid, f"无效数据应该返回 False: {description}"
        
        # 测试有效数据
        valid_data = {
            'prices': [
                {
                    'price': 100 + i,
                    'deliveryStart': f'2024-01-01T{i:02d}:00:00Z',
                    'deliveryEnd': f'2024-01-01T{(i+1) % 24:02d}:00:00Z' if i < 23 else '2024-01-02T00:00:00Z'
                } for i in range(24)
            ],
            'marketMainCurrency': 'EUR'
        }
        
        is_valid = scheduler._is_valid_country_price_data(valid_data, "TEST")
        logger.info(f"有效数据测试: {is_valid} (期望: True)")
        assert is_valid, "有效数据应该返回 True"
        
        logger.info("✅ 数据验证测试通过")
        return True
        
    except Exception as e:
        logger.error(f"数据验证测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_retry_intervals():
    """测试重试间隔逻辑"""
    logger = logging.getLogger(__name__)
    
    logger.info("\n" + "=" * 50)
    logger.info("测试重试间隔逻辑")
    logger.info("=" * 50)
    
    try:
        from application.algorithm_schedule.dynamic_price_scheduler import DynamicPriceRetryConfig
        
        intervals = DynamicPriceRetryConfig.RETRY_INTERVALS
        max_retries = DynamicPriceRetryConfig.MAX_RETRIES_PER_COUNTRY
        
        logger.info(f"重试间隔配置: {intervals}")
        logger.info(f"最大重试次数: {max_retries}")
        
        # 模拟重试间隔计算
        logger.info("重试间隔计算模拟:")
        for attempt in range(max_retries + 1):
            if attempt < max_retries:
                wait_minutes = intervals[min(attempt, len(intervals) - 1)]
                logger.info(f"  第 {attempt + 1} 次尝试失败后，等待 {wait_minutes} 分钟")
            else:
                logger.info(f"  第 {attempt + 1} 次尝试失败后，不再重试")
        
        # 计算总的最大等待时间
        total_wait = sum(intervals[:max_retries])
        logger.info(f"单个国家最大等待时间: {total_wait} 分钟")
        
        # 验证配置合理性
        assert total_wait <= DynamicPriceRetryConfig.MAX_TOTAL_RETRY_TIME, "单个国家等待时间不应超过最大总重试时间"
        assert all(interval > 0 for interval in intervals), "所有重试间隔都应该大于0"
        assert intervals == sorted(intervals), "重试间隔应该是递增的"
        
        logger.info("✅ 重试间隔逻辑验证通过")
        return True
        
    except Exception as e:
        logger.error(f"重试间隔测试失败: {e}")
        return False

def test_scheduler_timing():
    """测试调度器时间计算"""
    logger = logging.getLogger(__name__)
    
    logger.info("\n" + "=" * 50)
    logger.info("测试调度器时间计算")
    logger.info("=" * 50)
    
    try:
        from application.algorithm_schedule.dynamic_price_scheduler import DynamicPriceScheduler
        import pytz
        
        scheduler = DynamicPriceScheduler()
        
        # 测试下次执行时间计算
        next_exec_seconds = scheduler._calculate_next_execution_time()
        next_exec_hours = next_exec_seconds / 3600
        
        logger.info(f"距离下次执行时间: {next_exec_hours:.2f} 小时")
        
        # 验证时间计算的合理性
        assert 0 <= next_exec_seconds <= 24 * 3600, "下次执行时间应该在0-24小时之间"
        
        # 测试CET时区
        cet = pytz.timezone('CET')
        now = datetime.now(tz=cet)
        logger.info(f"当前CET时间: {now}")
        
        # 计算下一个12:10
        next_exec_time = (now + timedelta(days=0)).replace(hour=12, minute=10, second=0, microsecond=0)
        if now >= next_exec_time:
            next_exec_time += timedelta(days=1)
        
        logger.info(f"下次执行时间: {next_exec_time}")
        
        logger.info("✅ 调度器时间计算验证通过")
        return True
        
    except Exception as e:
        logger.error(f"调度器时间测试失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("开始测试动态电价重试机制（简化版）")
    logger.info(f"测试时间: {datetime.now()}")
    
    tests = [
        ("配置和初始化", test_config_and_initialization),
        ("数据验证", test_data_validation),
        ("重试间隔", test_retry_intervals),
        ("调度器时间", test_scheduler_timing),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                logger.info(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
    
    logger.info(f"\n{'='*60}")
    logger.info(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有测试都通过了！")
        logger.info("动态电价重试机制基础功能验证成功")
        return True
    else:
        logger.error("❌ 部分测试失败")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
