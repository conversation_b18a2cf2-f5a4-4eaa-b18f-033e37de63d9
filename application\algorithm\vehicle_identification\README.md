## Prediction algorithm

A machine learning implementation for vehicle handshake prediction

A couple demo samples are provided with in the .json, .csv files.

### Prerequisit

Install python3

Install dependencies
$ pip install -r requirements.txt


### Usage

**Training**

$ python train.py --algo dtree --train-dataset data-26-09-2024.json --test-dataset data-26-09-2024.json --config ./config.yaml

This will generate a .bin model file and a .png confusion matrix in the same folder.

**Prediction**

$ python predict.py --model DecisionTreeClassifier.bin --dataset sample.csv

