import os
import unittest
from unittest.mock import patch, MagicMock
from datetime import datetime

# 设置必要的环境变量
os.environ['CONFIG_ENCRYPTION_KEY'] = "tTZ256Kp4RTO2wOuZEocOxd3UuX0cbEXEeC2bJXh_0E="
os.environ['PYTHONPATH'] = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

from application.utils.data_change_detector import DataChangeDetector, data_change_detector
from application.kafka_operate.ems_kafka_message_handler import EMSMessageHandler


class TestDataChangeDetection(unittest.TestCase):
    """测试数据变化检测功能"""
    
    def setUp(self):
        """测试前准备"""
        self.detector = DataChangeDetector(cache_ttl_hours=1, cache_maxsize=100)
        self.handler = EMSMessageHandler()
        
        # 测试数据
        self.site_no = "TEST_SITE_001"
        self.demand_data = [
            {
                "version": "20250115_120000",
                "start_time": "2025-01-15T00:00:00",
                "end_time": "2025-01-15T23:59:59",
                "price": 8.5,
                "unit": "CNY/kW",
                "total_demand_target": 100,
                "target_demand_warning_ratio": 90.0
            }
        ]
        
        self.sell_price_data = [
            {
                "version": "20250115_120000", 
                "start_time": "2025-01-15T00:00:00",
                "end_time": "2025-01-15T23:59:59",
                "price": 0.6,
                "unit": "CNY/kWh"
            }
        ]
        
        # Mock数据库操作（仅用于消息处理器测试）
        self.handler.db_operate = MagicMock()
    
    def test_calculate_data_hash(self):
        """测试数据哈希计算"""
        # 相同数据应该产生相同哈希
        hash1 = self.detector._calculate_data_hash(self.demand_data)
        hash2 = self.detector._calculate_data_hash(self.demand_data)
        self.assertEqual(hash1, hash2)
        
        # 不同数据应该产生不同哈希
        modified_data = self.demand_data.copy()
        modified_data[0] = {**modified_data[0], "price": 9.0}
        hash3 = self.detector._calculate_data_hash(modified_data)
        self.assertNotEqual(hash1, hash3)
        
        # 验证哈希长度（SHA256应该是64字符）
        self.assertEqual(len(hash1), 64)
    
    def test_cache_operations(self):
        """测试缓存操作"""
        test_hash = "abcd1234567890"
        
        # 测试设置和获取缓存
        self.detector._set_cached_hash(self.site_no, "demand_data", test_hash)
        cached_hash = self.detector._get_cached_hash(self.site_no, "demand_data")
        self.assertEqual(cached_hash, test_hash)
        
        # 测试不存在的缓存
        no_cache = self.detector._get_cached_hash(self.site_no, "sell_price")
        self.assertIsNone(no_cache)
        
        # 测试清空指定缓存
        self.detector.clear_cache(self.site_no, "demand_data")
        cleared_cache = self.detector._get_cached_hash(self.site_no, "demand_data")
        self.assertIsNone(cleared_cache)
    
    def test_first_time_data_processing(self):
        """测试首次处理数据"""
        # 首次处理应该返回True（数据有变化）
        is_changed, data_hash = self.detector.is_data_changed(
            self.site_no, "demand_data", self.demand_data
        )
        
        self.assertTrue(is_changed)
        self.assertIsNotNone(data_hash)
        self.assertEqual(len(data_hash), 64)  # SHA256长度
    
    def test_same_data_no_change(self):
        """测试相同数据不会被认为有变化"""
        # 先处理一次数据，在缓存中设置哈希值
        is_changed, data_hash = self.detector.is_data_changed(
            self.site_no, "demand_data", self.demand_data
        )
        self.assertTrue(is_changed)  # 首次应该有变化
        
        # 更新缓存
        self.detector.update_data_hash(self.site_no, "demand_data", data_hash)
        
        # 再次处理相同数据，应该返回False（数据无变化）
        is_changed2, data_hash2 = self.detector.is_data_changed(
            self.site_no, "demand_data", self.demand_data
        )
        
        self.assertFalse(is_changed2)
        self.assertEqual(data_hash, data_hash2)
    
    def test_different_data_has_change(self):
        """测试不同数据会被认为有变化"""
        # 先处理原始数据
        is_changed1, data_hash1 = self.detector.is_data_changed(
            self.site_no, "demand_data", self.demand_data
        )
        self.assertTrue(is_changed1)
        
        # 更新缓存
        self.detector.update_data_hash(self.site_no, "demand_data", data_hash1)
        
        # 修改数据
        modified_data = self.demand_data.copy()
        modified_data[0] = {**modified_data[0], "price": 9.0}
        
        # 处理修改后的数据，应该返回True（数据有变化）
        is_changed2, data_hash2 = self.detector.is_data_changed(
            self.site_no, "demand_data", modified_data
        )
        
        self.assertTrue(is_changed2)
        self.assertNotEqual(data_hash1, data_hash2)
    
    def test_unsupported_data_type(self):
        """测试不支持的数据类型"""
        with self.assertRaises(ValueError):
            self.detector.is_data_changed(
                self.site_no, "unsupported_type", self.demand_data
            )
    
    @patch('application.utils.data_change_detector.data_change_detector.is_data_changed')
    @patch('application.utils.data_change_detector.data_change_detector.update_data_hash')
    def test_message_handler_integration(self, mock_update_hash, mock_is_changed):
        """测试消息处理器集成"""
        # 模拟数据有变化
        mock_is_changed.return_value = (True, "test_hash_value")
        mock_update_hash.return_value = True
        
        # 模拟数据库操作成功
        self.handler.db_operate.check_site_status.return_value = (True, True, True)
        self.handler.db_operate.update_site_basic_info.return_value = True
        self.handler.db_operate.batch_insert_site_demand_data_db.return_value = True
        self.handler.db_operate.batch_insert_site_sell_price_data_db.return_value = True
        
        # 构造测试消息
        test_message = {
            "station": {
                "site_no": self.site_no,
                "es_total_energy": 100
            },
            "ts": 1642344000000,
            "demand_data": [
                {
                    "start_time": "2025-01-15T00:00:00",
                    "end_time": "2025-01-15T23:59:59", 
                    "price": 8.5,
                    "unit": "CNY/kW",
                    "total_demand_target": 100,
                    "target_demand_warning_ratio": 90.0
                }
            ],
            "sell_price": [
                {
                    "start_time": "2025-01-15T00:00:00",
                    "end_time": "2025-01-15T23:59:59",
                    "price": 0.6,
                    "unit": "CNY/kWh"
                }
            ]
        }
        
        # 处理消息
        self.handler.process_message(test_message)
        
        # 验证变化检测被调用 (station_data, demand_data, sell_price)
        self.assertEqual(mock_is_changed.call_count, 3)
        
        # 验证数据库操作被调用
        self.handler.db_operate.check_site_status.assert_called_once()
        self.handler.db_operate.update_site_basic_info.assert_called_once()
        self.handler.db_operate.batch_insert_site_demand_data_db.assert_called_once()
        self.handler.db_operate.batch_insert_site_sell_price_data_db.assert_called_once()
        
        # 验证哈希更新被调用
        self.assertEqual(mock_update_hash.call_count, 3)
    
    @patch('application.utils.data_change_detector.data_change_detector.is_data_changed')
    def test_message_handler_no_change(self, mock_is_changed):
        """测试消息处理器在数据无变化时的行为"""
        # 模拟数据无变化
        mock_is_changed.return_value = (False, "test_hash_value")
        
        # 模拟数据库操作成功
        self.handler.db_operate.check_site_status.return_value = (True, True, True)
        self.handler.db_operate.update_site_basic_info.return_value = True
        
        # 构造测试消息
        test_message = {
            "station": {
                "site_no": self.site_no,
                "es_total_energy": 100
            },
            "ts": 1642344000000,
            "demand_data": [
                {
                    "start_time": "2025-01-15T00:00:00",
                    "end_time": "2025-01-15T23:59:59",
                    "price": 8.5,
                    "unit": "CNY/kW",
                    "total_demand_target": 100,
                    "target_demand_warning_ratio": 90.0
                }
            ]
        }
        
        # 处理消息
        self.handler.process_message(test_message)
        
        # 验证变化检测被调用 (station_data 和 demand_data)
        self.assertEqual(mock_is_changed.call_count, 2)
        
        # 验证场站状态检查被调用，但由于数据无变化，站点和需求数据都不会更新
        self.handler.db_operate.check_site_status.assert_called_once()
        self.handler.db_operate.update_site_basic_info.assert_not_called()
        self.handler.db_operate.batch_insert_site_demand_data_db.assert_not_called()
    
    def test_cache_info(self):
        """测试缓存信息获取"""
        cache_info = self.detector.get_cache_info()
        
        self.assertIn("cache_size", cache_info)
        self.assertIn("maxsize", cache_info)
        self.assertIn("ttl", cache_info)
        self.assertIn("supported_data_types", cache_info)
        
        # 验证支持的数据类型
        expected_types = {"station_data", "demand_data", "sell_price", "purchase_price_data"}
        self.assertEqual(set(cache_info["supported_data_types"]), expected_types)


if __name__ == '__main__':
    unittest.main() 