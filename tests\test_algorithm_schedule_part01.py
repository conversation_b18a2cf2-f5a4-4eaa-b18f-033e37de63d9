import unittest

from application.algorithm_schedule.charge_power_dispatch import charge_power_dispatch
from application.algorithm_schedule.long_term_load_forecast import long_term_load_forecast


class TestChargePowerDispatch(unittest.TestCase):
    def setUp(self):
        msg = """
测试及Debug时，需要首先执行测试数据生成脚本，请按顺序执行
    1. ems/application/db_operate/sql_scripts/charge_power_dispatch.sql，在数据库中生成测试数据
    2. 执行本文件，查看输出结果进行Debug及测试"""
        print("TestChargePowerDispatch setUp:", msg)
        self.site_no = "SITE001"
        self.data = {}

    def test_charge_power_dispatch(self):
        ret = charge_power_dispatch(self.site_no, self.data)
        
        self.assertIsInstance(ret['scheduling_time'], int)
        self.assertIsInstance(ret['site_charger_power_allocations'], dict)
        print("test_charge_power_dispatch:\n", ret)


class TestLongTermLoadForecast(unittest.TestCase):
    def setUp(self):
        msg = """
测试及Debug时，需要首先执行测试数据生成脚本，请按顺序执行
    1. ems/application/db_operate/sql_scripts/long_term_load_forecast_0.py, 首先生成一个sql脚本，然后执行对应sql脚本，在数据库中生成测试数据
    2. ems/application/db_operate/sql_scripts/long_term_load_forecast_1.sql，在数据库中生成测试数据
    3. ems/application/db_operate/sql_scripts/long_term_load_forecast_2.sql，在数据库中生成测试数据
    4. 执行本文件，查看输出结果进行Debug及测试"""
        print("TestLongTermLoadForecast setUp:", msg)
        self.site_no = "SITE001"
        self.data = {}

    def test_long_term_load_forecast(self):
        ret = long_term_load_forecast(self.site_no, self.data)

        self.assertIsInstance(ret['predicted_time'], int)

        self.assertIsInstance(ret['long_term_load_predicted_list'], list)
        self.assertEqual(len(ret['long_term_load_predicted_list']), 96)
        print("test_long_term_load_forecast:\n", ret)


if __name__ == "__main__":
    unittest.main()
