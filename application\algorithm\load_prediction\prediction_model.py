import pandas as pd
import helper
import pickle

# Load model from file
with open('xgb_model.pkl', 'rb') as f:
    model = pickle.load(f)

# ========= define feature engineering parameters ==================
window_features = {"total_power_demand_KWh": 96}
horizon_features = {}
forecast_features = {}
target_feature = "total_power_demand_KWh"
forecast_horizon = 96

# ========= define preprocessing parameters ==================
time_granularity = 15 # in minutes
time_granularity_str = str(time_granularity) + 'min'

evse_time_interval = 30 
evse_time_interval = str(evse_time_interval) + "s"

evse_cols = {'create_time': True,
             'power': True,
             'evse_sn': True,
             'location_id': True,
             'location_name': False,
             'total_electrical_power': False}

file_path = 'where_data_is_read_from.txt'  # give your actual file path

# =================input data ===============================

# @@@@@@@@@@@@@@@ EXECUTION @@@@@@@@@@@@@@@@@@@@@@@@@
# ======= ==== = Read the data into a pandas dataFrame ==============
evse_data = pd.read_csv(file_path, delimiter='|')
evse_data = evse_data.drop_duplicates()
evse_data['create_time'] = pd.to_datetime(evse_data['create_time'], unit='ms')

evse_cols_to_keep = [col for col in evse_cols if evse_cols[col] == True]
evse_data = evse_data[evse_cols_to_keep]

# ====== ====== preprocess the evse data ===== =========== ========= 
evse_data_processor = helper.evse_sn_data_processing(data = evse_data, time_intervals= evse_time_interval)
evse_processed_data = evse_data_processor.chargers_data

# ==  ===== ======= process evse data === ==== ======================
evcs_data_processor = helper.evcs_data_processing(data = evse_processed_data, time_granularity=time_granularity_str)
evcs_data = evcs_data_processor.charging_stations_data

#====================== Feature Engineering =========================

prediction_feature_generation  = helper.feature_engineering(df=evcs_data, 
                                          target_feature=target_feature, 
                                          horizon_features=horizon_features, 
                                          window_features=window_features,
                                          forecast_features=forecast_features, 
                                          forecast_horizon=forecast_horizon,
                                          time_granularity=time_granularity)

prediction_engineered_data = prediction_feature_generation.df


feature_columns = []
feature_columns = feature_columns + [f'{feature}_{lag}' for feature in window_features for lag in range(1, window_features[feature] + 1)]
# feature_columns = feature_columns + [f'{feature}_{lag}' for feature in horizon_features for lag in range(1, horizon_features[feature] + 1)]
# feature_columns = feature_columns + [f'{feature}_{lag}' for feature in forecast_features for lag in range(1, forecast_features[feature] + 1)]
feature_columns = feature_columns + ['future_DayOfWeek', 'future_MinuteOfDay', 'horizon', ] 
feature_columns = feature_columns + ['total_power_demand_KWh', 'MinuteOfDay'] #, 'Hour']#, 'Month']
del prediction_feature_generation


input_data = prediction_engineered_data[feature_columns]
# yttt = prediction_engineered_data['target']

predictions = model.predict(input_data)

def load_prediction():
    return model.predict(input_data)

if __name__ == "__main__":
    load_prediction_values = load_prediction()