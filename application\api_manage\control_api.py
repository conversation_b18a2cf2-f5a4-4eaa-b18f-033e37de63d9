from threading import Lock
from typing import List, Dict, Any

from fastapi import APIRouter
from pydantic import BaseModel

from application.algorithm_schedule.ai_task_manager import SiteTaskManager
from application.api_manage.site_noc_manager import SiteNOCManager
from application.db_operate.db_operate import DBOperate
from application.kafka_operate.send_message_queue_manager import site_heartbeat_manager
from application.utils.logger import setup_logger

logger = setup_logger("AI_EMS")
router = APIRouter()

# 全局线程安全site_no集合和任务管理器
active_sites = set()
active_sites_lock = Lock()
site_task_managers = {}  # site_no -> SiteTaskManager
site_manager = SiteNOCManager()
db_operate = DBOperate()


class SiteControlRequest(BaseModel):
    site_no: str
    status: str
    pile_list: List[str]
    bizSeq: str  # 幂等序列号


def create_response(res_code: int, res_mesg: str, biz_seq: str) -> Dict[str, Any]:
    """创建标准响应格式"""
    return {
        "res_code": res_code,
        "res_mesg": res_mesg,
        "bizSeq": biz_seq
    }


def _register_all_tasks():
    """
    服务启动时自动恢复所有AI已开启场站的定时任务
    """
    active_sites_db = db_operate.get_active_ai_sites()
    with active_sites_lock:
        for site_no in active_sites_db:
            if site_no not in active_sites:
                active_sites.add(site_no)
                site_task_managers[site_no] = SiteTaskManager(site_no)
                # 启动场站心跳
                site_heartbeat_manager.start_site_heartbeat(site_no)
    logger.info(f"已自动恢复所有AI已开启场站的定时任务和心跳: {active_sites_db}")


@router.post("/control_ems_ai", tags=["AI Control"], summary="控制EMS AI能力")
async def control_ems_ai(request: SiteControlRequest):
    """
    控制EMS AI能力的接口，管理全局site_no列表和定时任务
    
    请求参数:
    - site_no: 场站编号
    - pile_list: 场站的所有充电桩桩号列表
    - status: 开启或关闭某个光储充场站的ai能力 ("start"/"stop")
    - bizSeq: 幂等序列号
    
    返回:
    - 200: 请求成功
    - 400: 请求参数错误
    - 500: 服务器内部错误
    """
    try:
        logger.info(f"Received control request for site {request.site_no} with status {request.status}")

        if request.status == "start":
            # 先检查是否已经成功开启
            if db_operate.check_idempotency(request.bizSeq, request.site_no, request.status):
                logger.info(f"Site {request.site_no} AI is already active")
                return create_response(200, "received", request.bizSeq)

            # 订阅运维kafka数据并获取桩模块信息
            if not site_manager.subscribe_site(request.site_no, request.pile_list):
                logger.error(f"Failed to subscribe site {request.site_no}")
                return create_response(500, "Failed to subscribe site", request.bizSeq)

            # 订阅成功后，更新幂等性状态
            if not db_operate.update_site_status(request.site_no, request.status):
                logger.error(f"Failed to update site {request.site_no} status")
                return create_response(500, "Failed to update site status", request.bizSeq)

            with active_sites_lock:
                if request.site_no not in active_sites:
                    active_sites.add(request.site_no)
                    site_task_managers[request.site_no] = SiteTaskManager(request.site_no)
                    # 启动场站心跳
                    site_heartbeat_manager.start_site_heartbeat(request.site_no)
            logger.info(f"Site {request.site_no} added to active AI list and tasks started.")
            return create_response(200, "success", request.bizSeq)

        elif request.status == "stop":
            # 先检查是否已经成功关闭
            if db_operate.check_idempotency(request.bizSeq, request.site_no, request.status):
                logger.info(f"Site {request.site_no} AI is already inactive")
                return create_response(200, "received", request.bizSeq)

            if not site_manager.unsubscribe_site(request.site_no):
                logger.error(f"Failed to unsubscribe site {request.site_no}")
                return create_response(500, "Failed to unsubscribe site", request.bizSeq)

            # 取消订阅成功后，更新幂等性状态
            if not db_operate.update_site_status(request.site_no, request.status):
                logger.error(f"Failed to update site {request.site_no} status")
                return create_response(500, "Failed to update site status", request.bizSeq)

            with active_sites_lock:
                if request.site_no in active_sites:
                    active_sites.remove(request.site_no)
                    site_task_managers[request.site_no].stop()
                    del site_task_managers[request.site_no]
                    # 停止场站心跳
                    site_heartbeat_manager.stop_site_heartbeat(request.site_no)
            logger.info(f"Site {request.site_no} removed from active AI list and tasks stopped.")
            return create_response(200, "success", request.bizSeq)

        else:
            return create_response(400, "Invalid status parameter", request.bizSeq)

    except Exception as e:
        logger.error(f"Error processing request for site {request.site_no}: {str(e)}")
        return create_response(500, str(e), request.bizSeq)

# 在模块加载时自动注册所有任务（服务启动时执行）
_register_all_tasks()
