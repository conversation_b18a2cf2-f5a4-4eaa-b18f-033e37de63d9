import os
from typing import List


class APIEndpoints:
    """API 接口地址"""
    
    # 环境配置映射
    ENV_CONFIG = {
        # 中国生产环境
        "cn-northwest-1-prod": {
            "base_url": "https://gateway-eneprod.auteltech.cn/api/ops-device-mgr",
            "energy_cloud_base_url": ""
        },
        # 中国测试环境
        "cn-northwest-1-test": {
            "base_url": "https://autel-cloud-energy-gateway-enetest.auteltech.cn/api/ops-device-mgr",
            "energy_cloud_base_url": "https://maxipower-enetest.auteltech.cn/api-b/cloud-ems-app/invoking-ai/"
        },
        # 美西开发环境（默认）
        "dev": {
            "base_url": "https://autel-cloud-energy-gateway-enedev.auteltech.cn/api/ops-device-mgr",
            "energy_cloud_base_url": ""
        },
        # 本地开发环境
        "local": {
            "base_url": "https://autel-cloud-energy-gateway-enetest.auteltech.cn/api/ops-device-mgr",
            "energy_cloud_base_url": ""
        }
    }
    
    @classmethod
    def _get_environment(cls) -> str:
        """获取当前环境"""
        environment = os.environ.get("ENVIRONMENT", "local").lower()
        
        # 环境变量映射，支持更多变体
        env_mapping = {
            "cn-northwest-1-prod": "cn-northwest-1-prod",
            "cn-northwest-1-test": "cn-northwest-1-test",
            "dev": "dev"
        }
        
        return env_mapping.get(environment, "dev")
    
    @classmethod
    def _get_config(cls) -> dict:
        """获取当前环境的配置"""
        env = cls._get_environment()
        config = cls.ENV_CONFIG.get(env, cls.ENV_CONFIG["local"])
        
        # 记录当前使用的环境配置
        print(f"EMS API配置 - 当前环境: {env}")
        print(f"BASE_URL: {config['base_url']}")
        
        return config


# 在类定义完成后初始化配置
_current_config = APIEndpoints._get_config()

# 动态添加类属性 - 这样IDE可以正确识别
APIEndpoints.BASE_URL = _current_config["base_url"]
APIEndpoints.ENERGY_CLOUD_BASE_URL = _current_config["energy_cloud_base_url"]
APIEndpoints.SUBSCRIBE = f"{APIEndpoints.BASE_URL}/subscribe"
APIEndpoints.CHARGE_MODULE = f"{APIEndpoints.BASE_URL}/device/charge-module"


class DeviceConfig:
    """设备相关配置"""
    # BOM 代码
    DC_FAST_BOM_CODES: List[str] = ["B0001", "B0002"]  # DC Fast桩

    N1_BOM_CODES: List[str] = ["B0805", "B0609"]  # N+1桩

    # 事件代码
    EVENT_CODES: List[str] = [
        "ccuStartCompletionFrameEvent"
    ]

    # Kafka 主题
    TOPIC_NAME: str = "real-data"


class ResponseCode:
    """响应状态码"""
    SUCCESS: int = 200
    ERROR: int = 500


class DBConfig:
    """数据库相关配置"""
    DEFAULT_MODULE_NO: int = 1
    DEFAULT_UNIT_NUM: int = 2
    DEFAULT_MIN_POWER: float = 0
    DEFAULT_BOM_INDEX: str = "1"
