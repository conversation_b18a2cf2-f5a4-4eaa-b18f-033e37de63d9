from typing import List


class APIEndpoints:
    """API 接口地址"""
    # 运维生产环境
    BASE_URL: str = "https://gateway-eneprod.auteltech.cn/api/ops-device-mgr"
    # 运维开发环境
    # BASE_URL: str = "https://autel-cloud-energy-gateway-enedev.auteltech.cn/api/ops-device-mgr"
    # 运维测试环境
    # BASE_URL: str = "https://autel-cloud-energy-gateway-enetest.auteltech.cn/api/ops-device-mgr"
    # BASE_URL: str = "http://localhost:8080/api/ops-device-mgr"

    # 运维美西测试
    # BASE_URL: str = "https://gateway-enetestus.autel.com/api/ops-device-mgr"

    SUBSCRIBE: str = f"{BASE_URL}/subscribe"
    CHARGE_MODULE: str = f"{BASE_URL}/device/charge-module"
    STARTUP_FRAME: str = f"{BASE_URL}/device/last-startup-frame"
    CHARGE_PROFILE: str = (f"https://gateway-enetestus.autel.com/api/openapi-app/openapi/smartCharge/delivery"
                           f"/chargeProfile")  # 新增充电配置接口

    ENERGY_CLOUD_BASE_URL: str = "https://maxipower-enetest.auteltech.cn/api-b/cloud-ems-app/invoking-ai/"


class DeviceConfig:
    """设备配置"""
    ACCESS_KEY: str = "REpqUkpKZlBNZXFMd1V2UlhTR2hrdVJzdUhvTU8zNlk="  # 测试环境
    # ACCESS_KEY: str = "ajNuT1ZCY1JvVk16TVJxbzVFZ09ocVFGQjFTdEltYs="  # 生产环境

    """设备相关配置"""
    # BOM 代码
    DC_FAST_BOM_CODES: List[str] = ["B0001", "B0002"]  # DC Fast桩

    N1_BOM_CODES: List[str] = ["B0805", "B0609"]  # N+1桩

    # 事件代码
    EVENT_CODES: List[str] = [
        "ccuStartCompletionFrameEvent"
    ]

    # Kafka 主题
    TOPIC_NAME: str = "real-data"


class ResponseCode:
    """响应状态码"""
    SUCCESS: int = 200
    ERROR: int = 500


class DBConfig:
    """数据库相关配置"""
    DEFAULT_MODULE_NO: int = 1
    DEFAULT_UNIT_NUM: int = 2
    DEFAULT_MIN_POWER: float = 0
    DEFAULT_BOM_INDEX: str = "1"
