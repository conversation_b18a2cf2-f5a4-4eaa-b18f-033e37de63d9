2025-07-18 17:28:58,287 - __main__ - INFO - 测试开始时间: 2025-07-18 17:28:58
2025-07-18 17:28:58,288 - __main__ - INFO - 日志文件: log.txt
2025-07-18 17:28:58,288 - __main__ - INFO - Python版本: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-18 17:28:58,288 - __main__ - INFO - 工作目录: D:\AiCodearts\ems\ems_zjc\ems
2025-07-18 17:28:58,288 - __main__ - INFO - ============================================================
2025-07-18 17:28:58,288 - __main__ - INFO - 开始测试 storage_dispatch 方法
2025-07-18 17:28:58,288 - __main__ - INFO - ============================================================
2025-07-18 17:28:58,588 - AI_EMS - INFO - execute init_config................
2025-07-18 17:28:58,605 - AI_EMS - INFO - AVANTML_SDK_CONFIG_PATH: D:\AiCodearts\ems\ems_zjc\ems\application\settings\config.yaml
2025-07-18 17:29:02,777 - AI_EMS - INFO - 数据库 'ems' 已存在，跳过初始化
2025-07-18 17:29:04,546 - AI_EMS - INFO - 检测到 1 个Kafka brokers, 使用副本因子: 1
2025-07-18 17:29:04,546 - AI_EMS - INFO - 所有必需的Kafka topics已存在
2025-07-18 17:29:06,175 - matplotlib - DEBUG - matplotlib data path: D:\Python310\lib\site-packages\matplotlib\mpl-data
2025-07-18 17:29:06,175 - matplotlib - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-07-18 17:29:06,175 - matplotlib - DEBUG - interactive is False
2025-07-18 17:29:06,175 - matplotlib - DEBUG - platform is win32
2025-07-18 17:29:06,282 - matplotlib - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-07-18 17:29:06,282 - matplotlib.font_manager - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v330.json
2025-07-18 17:29:06,600 - __main__ - INFO - 测试参数:
2025-07-18 17:29:06,600 - __main__ - INFO -   site_no: SITE001
2025-07-18 17:29:06,600 - __main__ - INFO -   data: None
2025-07-18 17:29:06,616 - __main__ - INFO -   trigger: test_manual
2025-07-18 17:29:06,617 - __main__ - INFO - ----------------------------------------
2025-07-18 17:29:06,617 - __main__ - INFO - 开始调用 storage_dispatch...
2025-07-18 17:29:06,617 - storage_dispatch - INFO - 场站 SITE001 储能调度开始 (触发器: test_manual)
2025-07-18 17:29:06,617 - db_operate - INFO - 数据库连接初始化成功
2025-07-18 17:29:13,849 - storage_dispatch - INFO - 场站 SITE001 融合负载预测: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
2025-07-18 17:29:13,849 - storage_dispatch - INFO - 场站 SITE001 预测日期: 2025-07-17
2025-07-18 17:29:14,811 - storage_dispatch - INFO - 场站 SITE001 电价列表: [0.1039, 0.0984, 0.09, 0.0852, 0.0865, 0.0892, 0.1044, 0.1099, 0.1059, 0.0931, 0.085, 0.0771, 0.0702, 0.0555, 0.0502, 0.0663, 0.0753, 0.0829, 0.0966, 0.1112, 0.122, 0.1331, 0.1251, 0.1142]
2025-07-18 17:29:15,268 - application.utils.timezone_mapping - DEBUG - 场站 SITE001 region=NL, 使用时区=Europe/Amsterdam
2025-07-18 17:29:15,837 - application.utils.timezone_mapping - DEBUG - 场站 SITE001 UTC时间 2025-07-18 09:29:14.811713+00:00 转换为当地时间 2025-07-18 11:29:14.811713+02:00
2025-07-18 17:29:15,837 - db_operate - INFO - local_time:2025-07-18 11:29:14.811713+02:00
2025-07-18 17:29:16,656 - storage_dispatch - INFO - 场站 SITE001 需量数据: [{'start_time': '2025-07-17 00:00:00', 'end_time': '2025-07-21 23:59:00', 'price': 10.875, 'unit': 'dollar', 'total_demand_target': 600, 'target_demand_warning_ratio': 95.0}]
2025-07-18 17:29:17,520 - db_operate - INFO - 场站 SITE001 yesterday 获取到 6 条电价数据，版本: 20250717_140000
2025-07-18 17:29:18,060 - db_operate - INFO - 场站 SITE001 today 获取到 6 条电价数据，版本: 20250718_140000
2025-07-18 17:29:18,314 - db_operate - WARNING - 场站 SITE001 tomorrow (2025-07-19) 没有找到电价数据
2025-07-18 17:29:18,330 - db_operate - INFO - 场站 SITE001 获取到未来24小时 24 条电价数据
2025-07-18 17:29:18,330 - db_operate - INFO - price data:[{'start_time': '2025-07-18 17:29:16', 'end_time': '2025-07-18 18:29:16', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-18 18:29:16', 'end_time': '2025-07-18 19:29:16', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-18 19:29:16', 'end_time': '2025-07-18 20:29:16', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-18 20:29:16', 'end_time': '2025-07-18 21:29:16', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-18 21:29:16', 'end_time': '2025-07-18 22:29:16', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-18 22:29:16', 'end_time': '2025-07-18 23:29:16', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-18 23:29:16', 'end_time': '2025-07-19 00:29:16', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-19 00:29:16', 'end_time': '2025-07-19 01:29:16', 'price': 0.2699, 'unit': 'CNY'}, {'start_time': '2025-07-19 01:29:16', 'end_time': '2025-07-19 02:29:16', 'price': 0.2699, 'unit': 'CNY'}, {'start_time': '2025-07-19 02:29:16', 'end_time': '2025-07-19 03:29:16', 'price': 0.2699, 'unit': 'CNY'}, {'start_time': '2025-07-19 03:29:16', 'end_time': '2025-07-19 04:29:16', 'price': 0.2699, 'unit': 'CNY'}, {'start_time': '2025-07-19 04:29:16', 'end_time': '2025-07-19 05:29:16', 'price': 0.2699, 'unit': 'CNY'}, {'start_time': '2025-07-19 05:29:16', 'end_time': '2025-07-19 06:29:16', 'price': 0.2699, 'unit': 'CNY'}, {'start_time': '2025-07-19 06:29:16', 'end_time': '2025-07-19 07:29:16', 'price': 0.2699, 'unit': 'CNY'}, {'start_time': '2025-07-19 07:29:16', 'end_time': '2025-07-19 08:29:16', 'price': 0.2699, 'unit': 'CNY'}, {'start_time': '2025-07-19 08:29:16', 'end_time': '2025-07-19 09:29:16', 'price': 0.6951, 'unit': 'CNY'}, {'start_time': '2025-07-19 09:29:16', 'end_time': '2025-07-19 10:29:16', 'price': 0.6951, 'unit': 'CNY'}, {'start_time': '2025-07-19 10:29:16', 'end_time': '2025-07-19 11:29:16', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-19 11:29:16', 'end_time': '2025-07-19 12:29:16', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-19 12:29:16', 'end_time': '2025-07-19 13:29:16', 'price': 0.6951, 'unit': 'CNY'}, {'start_time': '2025-07-19 13:29:16', 'end_time': '2025-07-19 14:29:16', 'price': 0.6951, 'unit': 'CNY'}, {'start_time': '2025-07-19 14:29:16', 'end_time': '2025-07-19 15:29:16', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-19 15:29:16', 'end_time': '2025-07-19 16:29:16', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-19 16:29:16', 'end_time': '2025-07-19 17:29:16', 'price': 1.176, 'unit': 'CNY'}]
2025-07-18 17:29:18,793 - storage_dispatch - INFO - 场站 SITE001 储能调度输入数据: {'start_time': '2025-07-17 03:00:00', 'pv_predicted_list': [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 45.99738898916826, 50.916832079294565, 55.784493858425535, 60.579530251525874, 63.296500495567365, 67.74556799342068, 72.06573597846008, 76.23850482812945, 86.95647351512672, 91.10141321384573, 95.03092000763502, 98.72816714513475, 99.07961767778652, 102.16931262533951, 104.9908845395615, 107.5322510163403, 105.10208061996619, 106.96851817890254, 108.53906038234055, 109.806981927451, 123.26306444856367, 123.98384729552714, 124.35416671637975, 124.37243694650893, 123.87312143995764, 123.189479590386, 122.15853520626369, 120.78470294971704, 143.5672433374826, 141.1069951796782, 138.25978872244758, 135.03781614095166, 114.52395260592019, 111.1013700970637, 107.39233263521433, 103.41272288820562, 107.65168266121526, 102.80142667202819, 97.7164275562323, 92.41846006070467, 116.5280366822009, 108.94759421017837, 101.17604723987611, 93.24667472853066, 74.49006579771995, 67.37044467453097, 60.203155379326006, 53.01888934520941, 53.10218056018799, 44.84877770471458, 36.682248914496, 28.63756451956021, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], 'hybrid_load_predicted_list': [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], 'electricity_purchase_price_list': [0.1039, 0.0984, 0.09, 0.0852, 0.0865, 0.0892, 0.1044, 0.1099, 0.1059, 0.0931, 0.085, 0.0771, 0.0702, 0.0555, 0.0502, 0.0663, 0.0753, 0.0829, 0.0966, 0.1112, 0.122, 0.1331, 0.1251, 0.1142], 'es_soc': 50, 'es_total_energy': 210, 'es_rated_power': 480.0, 'site_grid_limit': 800, 'demand_data': [{'start_time': '2025-07-17 00:00:00', 'end_time': '2025-07-21 23:59:00', 'price': 10.875, 'unit': 'dollar', 'total_demand_target': 600, 'target_demand_warning_ratio': 95.0}], 'min_soc': 20, 'max_soc': 95, 'electricity_sale_price_list': [1.176, 1.176, 1.176, 1.176, 1.176, 1.176, 1.176, 0.2699, 0.2699, 0.2699, 0.2699, 0.2699, 0.2699, 0.2699, 0.2699, 0.6951, 0.6951, 1.176, 1.176, 0.6951, 0.6951, 1.176, 1.176, 1.176]}
2025-07-18 17:29:18,811 - storage_dispatch - INFO - 场站 SITE001 储能调度结果: {'scheduling_time': 1752830958, 'es_scheduling_strategy': [-7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.0, -0.0, -0.0, -0.0, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, -0.0, -0.0, -0.0, -0.0, -0.0, -0.0, -0.0, -0.0, -0.0, 0.0, 0.0, 0.0, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007], 'load_planning_result': {'need_load_planning': False, 'optimized_load_curve': [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], 'base_load_curve': [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], 'storage_supplement': [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}}
2025-07-18 17:29:19,918 - __main__ - INFO - storage_dispatch 调用完成，耗时: 13.301 秒
2025-07-18 17:29:19,918 - __main__ - INFO - ----------------------------------------
2025-07-18 17:29:19,918 - __main__ - INFO - 调用成功!
2025-07-18 17:29:19,918 - __main__ - INFO - 返回结果类型: <class 'dict'>
2025-07-18 17:29:19,918 - __main__ - INFO - 返回结果内容:
2025-07-18 17:29:19,918 - __main__ - INFO -   scheduling_time: 1752830958
2025-07-18 17:29:19,918 - __main__ - INFO -   es_scheduling_strategy: [-7.0, -7.0, ..., -23.625000000000007, -23.625000000000007] (共96个元素)
2025-07-18 17:29:19,934 - __main__ - INFO -   load_planning_result: {'need_load_planning': False, 'optimized_load_curve': [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], 'base_load_curve': [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], 'storage_supplement': [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}
2025-07-18 17:29:19,937 - __main__ - INFO - ============================================================
2025-07-18 17:29:19,938 - __main__ - INFO - 测试完成
2025-07-18 17:29:19,938 - __main__ - INFO - ============================================================
2025-07-18 17:29:19,938 - __main__ - INFO - 
============================================================
2025-07-18 17:29:19,938 - __main__ - INFO - 开始多场景测试
2025-07-18 17:29:19,938 - __main__ - INFO - ============================================================
2025-07-18 17:29:19,938 - __main__ - INFO - 
--- 场景 1: 正常场站测试 ---
2025-07-18 17:29:19,938 - storage_dispatch - INFO - 场站 SITE001 储能调度开始 (触发器: manual)
2025-07-18 17:29:26,671 - storage_dispatch - INFO - 场站 SITE001 融合负载预测: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
2025-07-18 17:29:26,680 - storage_dispatch - INFO - 场站 SITE001 预测日期: 2025-07-17
2025-07-18 17:29:27,737 - storage_dispatch - INFO - 场站 SITE001 电价列表: [0.1039, 0.0984, 0.09, 0.0852, 0.0865, 0.0892, 0.1044, 0.1099, 0.1059, 0.0931, 0.085, 0.0771, 0.0702, 0.0555, 0.0502, 0.0663, 0.0753, 0.0829, 0.0966, 0.1112, 0.122, 0.1331, 0.1251, 0.1142]
2025-07-18 17:29:28,200 - application.utils.timezone_mapping - DEBUG - 场站 SITE001 region=NL, 使用时区=Europe/Amsterdam
2025-07-18 17:29:28,200 - application.utils.timezone_mapping - DEBUG - 场站 SITE001 UTC时间 2025-07-18 09:29:27.739915+00:00 转换为当地时间 2025-07-18 11:29:27.739915+02:00
2025-07-18 17:29:28,201 - db_operate - INFO - local_time:2025-07-18 11:29:27.739915+02:00
2025-07-18 17:29:28,868 - storage_dispatch - INFO - 场站 SITE001 需量数据: [{'start_time': '2025-07-17 00:00:00', 'end_time': '2025-07-21 23:59:00', 'price': 10.875, 'unit': 'dollar', 'total_demand_target': 600, 'target_demand_warning_ratio': 95.0}]
2025-07-18 17:29:29,554 - db_operate - INFO - 场站 SITE001 yesterday 获取到 6 条电价数据，版本: 20250717_140000
2025-07-18 17:29:30,046 - db_operate - INFO - 场站 SITE001 today 获取到 6 条电价数据，版本: 20250718_140000
2025-07-18 17:29:30,314 - db_operate - WARNING - 场站 SITE001 tomorrow (2025-07-19) 没有找到电价数据
2025-07-18 17:29:30,314 - db_operate - INFO - 场站 SITE001 获取到未来24小时 24 条电价数据
2025-07-18 17:29:30,314 - db_operate - INFO - price data:[{'start_time': '2025-07-18 17:29:28', 'end_time': '2025-07-18 18:29:28', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-18 18:29:28', 'end_time': '2025-07-18 19:29:28', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-18 19:29:28', 'end_time': '2025-07-18 20:29:28', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-18 20:29:28', 'end_time': '2025-07-18 21:29:28', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-18 21:29:28', 'end_time': '2025-07-18 22:29:28', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-18 22:29:28', 'end_time': '2025-07-18 23:29:28', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-18 23:29:28', 'end_time': '2025-07-19 00:29:28', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-19 00:29:28', 'end_time': '2025-07-19 01:29:28', 'price': 0.2699, 'unit': 'CNY'}, {'start_time': '2025-07-19 01:29:28', 'end_time': '2025-07-19 02:29:28', 'price': 0.2699, 'unit': 'CNY'}, {'start_time': '2025-07-19 02:29:28', 'end_time': '2025-07-19 03:29:28', 'price': 0.2699, 'unit': 'CNY'}, {'start_time': '2025-07-19 03:29:28', 'end_time': '2025-07-19 04:29:28', 'price': 0.2699, 'unit': 'CNY'}, {'start_time': '2025-07-19 04:29:28', 'end_time': '2025-07-19 05:29:28', 'price': 0.2699, 'unit': 'CNY'}, {'start_time': '2025-07-19 05:29:28', 'end_time': '2025-07-19 06:29:28', 'price': 0.2699, 'unit': 'CNY'}, {'start_time': '2025-07-19 06:29:28', 'end_time': '2025-07-19 07:29:28', 'price': 0.2699, 'unit': 'CNY'}, {'start_time': '2025-07-19 07:29:28', 'end_time': '2025-07-19 08:29:28', 'price': 0.2699, 'unit': 'CNY'}, {'start_time': '2025-07-19 08:29:28', 'end_time': '2025-07-19 09:29:28', 'price': 0.6951, 'unit': 'CNY'}, {'start_time': '2025-07-19 09:29:28', 'end_time': '2025-07-19 10:29:28', 'price': 0.6951, 'unit': 'CNY'}, {'start_time': '2025-07-19 10:29:28', 'end_time': '2025-07-19 11:29:28', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-19 11:29:28', 'end_time': '2025-07-19 12:29:28', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-19 12:29:28', 'end_time': '2025-07-19 13:29:28', 'price': 0.6951, 'unit': 'CNY'}, {'start_time': '2025-07-19 13:29:28', 'end_time': '2025-07-19 14:29:28', 'price': 0.6951, 'unit': 'CNY'}, {'start_time': '2025-07-19 14:29:28', 'end_time': '2025-07-19 15:29:28', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-19 15:29:28', 'end_time': '2025-07-19 16:29:28', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-19 16:29:28', 'end_time': '2025-07-19 17:29:28', 'price': 1.176, 'unit': 'CNY'}]
2025-07-18 17:29:30,774 - storage_dispatch - INFO - 场站 SITE001 储能调度输入数据: {'start_time': '2025-07-17 03:00:00', 'pv_predicted_list': [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 45.99738898916826, 50.916832079294565, 55.784493858425535, 60.579530251525874, 63.296500495567365, 67.74556799342068, 72.06573597846008, 76.23850482812945, 86.95647351512672, 91.10141321384573, 95.03092000763502, 98.72816714513475, 99.07961767778652, 102.16931262533951, 104.9908845395615, 107.5322510163403, 105.10208061996619, 106.96851817890254, 108.53906038234055, 109.806981927451, 123.26306444856367, 123.98384729552714, 124.35416671637975, 124.37243694650893, 123.87312143995764, 123.189479590386, 122.15853520626369, 120.78470294971704, 143.5672433374826, 141.1069951796782, 138.25978872244758, 135.03781614095166, 114.52395260592019, 111.1013700970637, 107.39233263521433, 103.41272288820562, 107.65168266121526, 102.80142667202819, 97.7164275562323, 92.41846006070467, 116.5280366822009, 108.94759421017837, 101.17604723987611, 93.24667472853066, 74.49006579771995, 67.37044467453097, 60.203155379326006, 53.01888934520941, 53.10218056018799, 44.84877770471458, 36.682248914496, 28.63756451956021, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], 'hybrid_load_predicted_list': [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], 'electricity_purchase_price_list': [0.1039, 0.0984, 0.09, 0.0852, 0.0865, 0.0892, 0.1044, 0.1099, 0.1059, 0.0931, 0.085, 0.0771, 0.0702, 0.0555, 0.0502, 0.0663, 0.0753, 0.0829, 0.0966, 0.1112, 0.122, 0.1331, 0.1251, 0.1142], 'es_soc': 50, 'es_total_energy': 210, 'es_rated_power': 480.0, 'site_grid_limit': 800, 'demand_data': [{'start_time': '2025-07-17 00:00:00', 'end_time': '2025-07-21 23:59:00', 'price': 10.875, 'unit': 'dollar', 'total_demand_target': 600, 'target_demand_warning_ratio': 95.0}], 'min_soc': 20, 'max_soc': 95, 'electricity_sale_price_list': [1.176, 1.176, 1.176, 1.176, 1.176, 1.176, 1.176, 0.2699, 0.2699, 0.2699, 0.2699, 0.2699, 0.2699, 0.2699, 0.2699, 0.6951, 0.6951, 1.176, 1.176, 0.6951, 0.6951, 1.176, 1.176, 1.176]}
2025-07-18 17:29:30,811 - storage_dispatch - INFO - 场站 SITE001 储能调度结果: {'scheduling_time': 1752830970, 'es_scheduling_strategy': [-7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.0, -0.0, -0.0, -0.0, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, -0.0, -0.0, -0.0, -0.0, -0.0, -0.0, -0.0, -0.0, -0.0, 0.0, 0.0, 0.0, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007], 'load_planning_result': {'need_load_planning': False, 'optimized_load_curve': [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], 'base_load_curve': [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], 'storage_supplement': [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}}
2025-07-18 17:29:31,725 - __main__ - INFO - 执行时间: 11.787 秒
2025-07-18 17:29:31,725 - __main__ - INFO - 结果: 成功
2025-07-18 17:29:31,725 - __main__ - INFO - 
--- 场景 2: 不存在的场站测试 ---
2025-07-18 17:29:31,725 - storage_dispatch - INFO - 场站 NONEXISTENT_SITE 储能调度开始 (触发器: test)
2025-07-18 17:29:32,177 - db_operate - WARNING - 场站 NONEXISTENT_SITE 没有找到储能实时数据
2025-07-18 17:29:32,641 - storage_dispatch - ERROR - 场站 NONEXISTENT_SITE 无储能实时数据
2025-07-18 17:29:32,641 - __main__ - INFO - 执行时间: 0.915 秒
2025-07-18 17:29:32,641 - __main__ - INFO - 结果: 失败
2025-07-18 17:29:32,641 - __main__ - INFO - 
--- 场景 3: 空场站编号测试 ---
2025-07-18 17:29:32,641 - storage_dispatch - INFO - 场站  储能调度开始 (触发器: test)
2025-07-18 17:29:33,113 - db_operate - WARNING - 场站  没有找到储能实时数据
2025-07-18 17:29:33,581 - storage_dispatch - ERROR - 场站  无储能实时数据
2025-07-18 17:29:33,581 - __main__ - INFO - 执行时间: 0.940 秒
2025-07-18 17:29:33,581 - __main__ - INFO - 结果: 失败
2025-07-18 17:29:33,581 - __main__ - INFO - 
--- 场景 4: 带数据的测试 ---
2025-07-18 17:29:33,581 - storage_dispatch - INFO - 场站 SITE001 储能调度开始 (触发器: scheduled)
2025-07-18 17:29:38,226 - storage_dispatch - INFO - 场站 SITE001 融合负载预测: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
2025-07-18 17:29:38,226 - storage_dispatch - INFO - 场站 SITE001 预测日期: 2025-07-17
2025-07-18 17:29:39,209 - storage_dispatch - INFO - 场站 SITE001 电价列表: [0.1039, 0.0984, 0.09, 0.0852, 0.0865, 0.0892, 0.1044, 0.1099, 0.1059, 0.0931, 0.085, 0.0771, 0.0702, 0.0555, 0.0502, 0.0663, 0.0753, 0.0829, 0.0966, 0.1112, 0.122, 0.1331, 0.1251, 0.1142]
2025-07-18 17:29:39,678 - application.utils.timezone_mapping - DEBUG - 场站 SITE001 region=NL, 使用时区=Europe/Amsterdam
2025-07-18 17:29:39,680 - application.utils.timezone_mapping - DEBUG - 场站 SITE001 UTC时间 2025-07-18 09:29:39.209768+00:00 转换为当地时间 2025-07-18 11:29:39.209768+02:00
2025-07-18 17:29:39,683 - db_operate - INFO - local_time:2025-07-18 11:29:39.209768+02:00
2025-07-18 17:29:40,420 - storage_dispatch - INFO - 场站 SITE001 需量数据: [{'start_time': '2025-07-17 00:00:00', 'end_time': '2025-07-21 23:59:00', 'price': 10.875, 'unit': 'dollar', 'total_demand_target': 600, 'target_demand_warning_ratio': 95.0}]
2025-07-18 17:29:41,098 - db_operate - INFO - 场站 SITE001 yesterday 获取到 6 条电价数据，版本: 20250717_140000
2025-07-18 17:29:41,571 - db_operate - INFO - 场站 SITE001 today 获取到 6 条电价数据，版本: 20250718_140000
2025-07-18 17:29:41,800 - db_operate - WARNING - 场站 SITE001 tomorrow (2025-07-19) 没有找到电价数据
2025-07-18 17:29:41,803 - db_operate - INFO - 场站 SITE001 获取到未来24小时 24 条电价数据
2025-07-18 17:29:41,804 - db_operate - INFO - price data:[{'start_time': '2025-07-18 17:29:40', 'end_time': '2025-07-18 18:29:40', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-18 18:29:40', 'end_time': '2025-07-18 19:29:40', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-18 19:29:40', 'end_time': '2025-07-18 20:29:40', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-18 20:29:40', 'end_time': '2025-07-18 21:29:40', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-18 21:29:40', 'end_time': '2025-07-18 22:29:40', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-18 22:29:40', 'end_time': '2025-07-18 23:29:40', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-18 23:29:40', 'end_time': '2025-07-19 00:29:40', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-19 00:29:40', 'end_time': '2025-07-19 01:29:40', 'price': 0.2699, 'unit': 'CNY'}, {'start_time': '2025-07-19 01:29:40', 'end_time': '2025-07-19 02:29:40', 'price': 0.2699, 'unit': 'CNY'}, {'start_time': '2025-07-19 02:29:40', 'end_time': '2025-07-19 03:29:40', 'price': 0.2699, 'unit': 'CNY'}, {'start_time': '2025-07-19 03:29:40', 'end_time': '2025-07-19 04:29:40', 'price': 0.2699, 'unit': 'CNY'}, {'start_time': '2025-07-19 04:29:40', 'end_time': '2025-07-19 05:29:40', 'price': 0.2699, 'unit': 'CNY'}, {'start_time': '2025-07-19 05:29:40', 'end_time': '2025-07-19 06:29:40', 'price': 0.2699, 'unit': 'CNY'}, {'start_time': '2025-07-19 06:29:40', 'end_time': '2025-07-19 07:29:40', 'price': 0.2699, 'unit': 'CNY'}, {'start_time': '2025-07-19 07:29:40', 'end_time': '2025-07-19 08:29:40', 'price': 0.2699, 'unit': 'CNY'}, {'start_time': '2025-07-19 08:29:40', 'end_time': '2025-07-19 09:29:40', 'price': 0.6951, 'unit': 'CNY'}, {'start_time': '2025-07-19 09:29:40', 'end_time': '2025-07-19 10:29:40', 'price': 0.6951, 'unit': 'CNY'}, {'start_time': '2025-07-19 10:29:40', 'end_time': '2025-07-19 11:29:40', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-19 11:29:40', 'end_time': '2025-07-19 12:29:40', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-19 12:29:40', 'end_time': '2025-07-19 13:29:40', 'price': 0.6951, 'unit': 'CNY'}, {'start_time': '2025-07-19 13:29:40', 'end_time': '2025-07-19 14:29:40', 'price': 0.6951, 'unit': 'CNY'}, {'start_time': '2025-07-19 14:29:40', 'end_time': '2025-07-19 15:29:40', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-19 15:29:40', 'end_time': '2025-07-19 16:29:40', 'price': 1.176, 'unit': 'CNY'}, {'start_time': '2025-07-19 16:29:40', 'end_time': '2025-07-19 17:29:40', 'price': 1.176, 'unit': 'CNY'}]
2025-07-18 17:29:42,289 - storage_dispatch - INFO - 场站 SITE001 储能调度输入数据: {'start_time': '2025-07-17 03:00:00', 'pv_predicted_list': [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 45.99738898916826, 50.916832079294565, 55.784493858425535, 60.579530251525874, 63.296500495567365, 67.74556799342068, 72.06573597846008, 76.23850482812945, 86.95647351512672, 91.10141321384573, 95.03092000763502, 98.72816714513475, 99.07961767778652, 102.16931262533951, 104.9908845395615, 107.5322510163403, 105.10208061996619, 106.96851817890254, 108.53906038234055, 109.806981927451, 123.26306444856367, 123.98384729552714, 124.35416671637975, 124.37243694650893, 123.87312143995764, 123.189479590386, 122.15853520626369, 120.78470294971704, 143.5672433374826, 141.1069951796782, 138.25978872244758, 135.03781614095166, 114.52395260592019, 111.1013700970637, 107.39233263521433, 103.41272288820562, 107.65168266121526, 102.80142667202819, 97.7164275562323, 92.41846006070467, 116.5280366822009, 108.94759421017837, 101.17604723987611, 93.24667472853066, 74.49006579771995, 67.37044467453097, 60.203155379326006, 53.01888934520941, 53.10218056018799, 44.84877770471458, 36.682248914496, 28.63756451956021, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], 'hybrid_load_predicted_list': [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], 'electricity_purchase_price_list': [0.1039, 0.0984, 0.09, 0.0852, 0.0865, 0.0892, 0.1044, 0.1099, 0.1059, 0.0931, 0.085, 0.0771, 0.0702, 0.0555, 0.0502, 0.0663, 0.0753, 0.0829, 0.0966, 0.1112, 0.122, 0.1331, 0.1251, 0.1142], 'es_soc': 50, 'es_total_energy': 210, 'es_rated_power': 480.0, 'site_grid_limit': 800, 'demand_data': [{'start_time': '2025-07-17 00:00:00', 'end_time': '2025-07-21 23:59:00', 'price': 10.875, 'unit': 'dollar', 'total_demand_target': 600, 'target_demand_warning_ratio': 95.0}], 'min_soc': 20, 'max_soc': 95, 'electricity_sale_price_list': [1.176, 1.176, 1.176, 1.176, 1.176, 1.176, 1.176, 0.2699, 0.2699, 0.2699, 0.2699, 0.2699, 0.2699, 0.2699, 0.2699, 0.6951, 0.6951, 1.176, 1.176, 0.6951, 0.6951, 1.176, 1.176, 1.176]}
2025-07-18 17:29:42,306 - storage_dispatch - INFO - 场站 SITE001 储能调度结果: {'scheduling_time': 1752830982, 'es_scheduling_strategy': [-7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, -7.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.0, -0.0, -0.0, -0.0, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, 33.15789473684212, -0.0, -0.0, -0.0, -0.0, -0.0, -0.0, -0.0, -0.0, -0.0, 0.0, 0.0, 0.0, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007, -23.625000000000007], 'load_planning_result': {'need_load_planning': False, 'optimized_load_curve': [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], 'base_load_curve': [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], 'storage_supplement': [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}}
2025-07-18 17:29:43,270 - __main__ - INFO - 执行时间: 9.689 秒
2025-07-18 17:29:43,271 - __main__ - INFO - 结果: 成功
2025-07-18 17:29:43,273 - __main__ - INFO - 
============================================================
2025-07-18 17:29:43,275 - __main__ - INFO - 多场景测试总结
2025-07-18 17:29:43,275 - __main__ - INFO - ============================================================
2025-07-18 17:29:43,275 - __main__ - INFO - 正常场站测试: ✅ 成功 (耗时: 11.787s)
2025-07-18 17:29:43,275 - __main__ - INFO - 不存在的场站测试: ❌ 失败 (耗时: 0.915s)
2025-07-18 17:29:43,275 - __main__ - INFO - 空场站编号测试: ❌ 失败 (耗时: 0.940s)
2025-07-18 17:29:43,275 - __main__ - INFO - 带数据的测试: ✅ 成功 (耗时: 9.689s)
2025-07-18 17:29:43,275 - __main__ - INFO - 
============================================================
2025-07-18 17:29:43,275 - __main__ - INFO - 所有测试完成
2025-07-18 17:29:43,275 - __main__ - INFO - ============================================================
2025-07-18 17:29:43,275 - __main__ - INFO - 请查看 log.txt 文件获取完整的日志信息
