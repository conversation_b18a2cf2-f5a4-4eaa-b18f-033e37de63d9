#############################################################################
# Copyright (C) 2023 AUTEL Netherlands B.V. - All Rights Reserved
# Unauthorized copying of this file, via any medium is strictly prohibited
#
# Proprietary and confidential
#############################################################################
from .base import Classifier
from .svm import SVMClassifier
from .lreg import LogisticRegressionClassifier
from .dtree import DecisionTreeClassifier
from .xgboost import XGBoostClassifier

SUPPORTED_CLASSIFIERS = {
    'svm': SVMClassifier,
    'lreg': LogisticRegressionClassifier,
    'dtree': DecisionTreeClassifier,
    'xgb': XGBoostClassifier,
}


def select_classifier(name):
    if name in SUPPORTED_CLASSIFIERS:
        return SUPPORTED_CLASSIFIERS[name]()
    return None
