{"cells": [{"cell_type": "code", "execution_count": 1, "id": "cde1dab1", "metadata": {}, "outputs": [{"data": {"text/plain": ["['D:\\\\WorkSpace\\\\smartcharging',\n", " 'D:\\\\ProgramsFiles\\\\Python3.12\\\\python312.zip',\n", " 'D:\\\\ProgramsFiles\\\\Python3.12\\\\DLLs',\n", " 'D:\\\\ProgramsFiles\\\\Python3.12\\\\Lib',\n", " 'D:\\\\ProgramsFiles\\\\Python3.12',\n", " 'd:\\\\WorkSpace\\\\smartcharging\\\\.venv',\n", " '',\n", " 'd:\\\\WorkSpace\\\\smartcharging\\\\.venv\\\\Lib\\\\site-packages',\n", " 'd:\\\\WorkSpace\\\\smartcharging\\\\.venv\\\\Lib\\\\site-packages\\\\win32',\n", " 'd:\\\\WorkSpace\\\\smartcharging\\\\.venv\\\\Lib\\\\site-packages\\\\win32\\\\lib',\n", " 'd:\\\\WorkSpace\\\\smartcharging\\\\.venv\\\\Lib\\\\site-packages\\\\Pythonwin']"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import sys\n", "\n", "sys.path"]}, {"cell_type": "code", "execution_count": 2, "id": "c0932d25", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 3, "id": "f27fa27f", "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['config_data', 'charging_data'])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from utils.parser import parse_charging_data\n", "\n", "parse_data = parse_charging_data(\"tests/data/BMW_i4@M50_80.7kWh,BMW_i4@eDrive40_80.7kWh.json\")\n", "parse_data.keys()"]}, {"cell_type": "code", "execution_count": 4, "id": "a58ad1b2", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'MACAddress': '0c86c70356a8',\n", " 'MaximumCurrent': 500.0,\n", " 'MaximumVoltage': 488.0,\n", " 'MaximumPower': 250000.0,\n", " 'BatteryCapacity': [None, None, 80690.3],\n", " 'BatteryReq': None,\n", " 'VehicleLabel': 'BMW_i4@M50_80.7kWh,BMW_i4@eDrive40_80.7kWh',\n", " 'Source': 'log_DE0240B1GN8C00003G_CCU2_20240423_000000.log'}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["parse_data[\"config_data\"]"]}, {"cell_type": "code", "execution_count": 31, "id": "9d39d5b4", "metadata": {}, "outputs": [{"data": {"text/plain": ["80.69030000000001"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["80690.3 / 1000"]}, {"cell_type": "code", "execution_count": 5, "id": "11723bf7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tarCurr</th>\n", "      <th>outCurr</th>\n", "      <th>tarVol</th>\n", "      <th>outVol</th>\n", "      <th>maxPower</th>\n", "      <th>SOC</th>\n", "      <th>BatteryCapacity2</th>\n", "      <th>time</th>\n", "      <th>EVMaxVolLimit</th>\n", "      <th>EVMaxCurrLimit</th>\n", "      <th>EVMaxPowerLimit</th>\n", "      <th>OtherCCUPlugin</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.0</td>\n", "      <td>0.00</td>\n", "      <td>478.0</td>\n", "      <td>386.2</td>\n", "      <td>2.3</td>\n", "      <td>24</td>\n", "      <td>0.0</td>\n", "      <td>2024-04-23 08:31:00.120</td>\n", "      <td>488.0</td>\n", "      <td>500.0</td>\n", "      <td>250000.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>6.0</td>\n", "      <td>0.00</td>\n", "      <td>478.0</td>\n", "      <td>386.7</td>\n", "      <td>2.3</td>\n", "      <td>24</td>\n", "      <td>0.0</td>\n", "      <td>2024-04-23 08:31:00.501</td>\n", "      <td>488.0</td>\n", "      <td>500.0</td>\n", "      <td>250000.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>6.0</td>\n", "      <td>0.48</td>\n", "      <td>478.0</td>\n", "      <td>386.2</td>\n", "      <td>2.3</td>\n", "      <td>24</td>\n", "      <td>0.0</td>\n", "      <td>2024-04-23 08:31:00.881</td>\n", "      <td>488.0</td>\n", "      <td>500.0</td>\n", "      <td>250000.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>6.0</td>\n", "      <td>1.84</td>\n", "      <td>478.0</td>\n", "      <td>387.2</td>\n", "      <td>2.3</td>\n", "      <td>24</td>\n", "      <td>0.0</td>\n", "      <td>2024-04-23 08:31:01.260</td>\n", "      <td>488.0</td>\n", "      <td>500.0</td>\n", "      <td>250000.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>6.0</td>\n", "      <td>1.84</td>\n", "      <td>478.0</td>\n", "      <td>386.7</td>\n", "      <td>2.3</td>\n", "      <td>24</td>\n", "      <td>0.0</td>\n", "      <td>2024-04-23 08:31:01.641</td>\n", "      <td>488.0</td>\n", "      <td>500.0</td>\n", "      <td>250000.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   tarCurr  outCurr  tarVol  outVol  maxPower  SOC  BatteryCapacity2  \\\n", "0      1.0     0.00   478.0   386.2       2.3   24               0.0   \n", "1      6.0     0.00   478.0   386.7       2.3   24               0.0   \n", "2      6.0     0.48   478.0   386.2       2.3   24               0.0   \n", "3      6.0     1.84   478.0   387.2       2.3   24               0.0   \n", "4      6.0     1.84   478.0   386.7       2.3   24               0.0   \n", "\n", "                     time  EVMaxVolLimit  EVMaxCurrLimit  EVMaxPowerLimit  \\\n", "0 2024-04-23 08:31:00.120          488.0           500.0         250000.0   \n", "1 2024-04-23 08:31:00.501          488.0           500.0         250000.0   \n", "2 2024-04-23 08:31:00.881          488.0           500.0         250000.0   \n", "3 2024-04-23 08:31:01.260          488.0           500.0         250000.0   \n", "4 2024-04-23 08:31:01.641          488.0           500.0         250000.0   \n", "\n", "   OtherCCUPlugin  \n", "0               0  \n", "1               0  \n", "2               0  \n", "3               0  \n", "4               0  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["charging_data = parse_data[\"charging_data\"]\n", "df_charging_data = pd.DataFrame(charging_data)\n", "df_charging_data.head()\n"]}, {"cell_type": "code", "execution_count": 28, "id": "316989ff", "metadata": {}, "outputs": [{"data": {"text/plain": ["datetime.datetime(1970, 1, 21, 4, 4, 21, 60120)"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["from datetime import datetime\n", "\n", "datetime.fromtimestamp(df_charging_data.iloc[0].time.timestamp())"]}, {"cell_type": "code", "execution_count": 29, "id": "7d1f3d54", "metadata": {}, "outputs": [{"data": {"text/plain": ["100"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["df_charging_data.iloc[-1].SOC\n"]}, {"cell_type": "code", "execution_count": 6, "id": "ed2f9357", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tarCurr</th>\n", "      <th>outCurr</th>\n", "      <th>tarVol</th>\n", "      <th>outVol</th>\n", "      <th>maxPower</th>\n", "      <th>SOC</th>\n", "      <th>BatteryCapacity2</th>\n", "      <th>time</th>\n", "      <th>EVMaxVolLimit</th>\n", "      <th>EVMaxCurrLimit</th>\n", "      <th>EVMaxPowerLimit</th>\n", "      <th>OtherCCUPlugin</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>11017</th>\n", "      <td>7.0</td>\n", "      <td>6.37</td>\n", "      <td>478.0</td>\n", "      <td>462.4</td>\n", "      <td>79.9</td>\n", "      <td>99</td>\n", "      <td>46949.06</td>\n", "      <td>2024-04-23 09:40:47.924</td>\n", "      <td>488.0</td>\n", "      <td>500.0</td>\n", "      <td>250000.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11018</th>\n", "      <td>5.0</td>\n", "      <td>6.36</td>\n", "      <td>478.0</td>\n", "      <td>462.4</td>\n", "      <td>79.9</td>\n", "      <td>99</td>\n", "      <td>46949.06</td>\n", "      <td>2024-04-23 09:40:48.305</td>\n", "      <td>488.0</td>\n", "      <td>500.0</td>\n", "      <td>250000.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11019</th>\n", "      <td>0.0</td>\n", "      <td>6.36</td>\n", "      <td>478.0</td>\n", "      <td>462.4</td>\n", "      <td>79.9</td>\n", "      <td>100</td>\n", "      <td>284547.39</td>\n", "      <td>2024-04-23 09:40:48.685</td>\n", "      <td>488.0</td>\n", "      <td>500.0</td>\n", "      <td>250000.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11020</th>\n", "      <td>0.0</td>\n", "      <td>6.36</td>\n", "      <td>478.0</td>\n", "      <td>462.4</td>\n", "      <td>79.9</td>\n", "      <td>100</td>\n", "      <td>284547.39</td>\n", "      <td>2024-04-23 09:40:49.066</td>\n", "      <td>488.0</td>\n", "      <td>500.0</td>\n", "      <td>250000.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11021</th>\n", "      <td>0.0</td>\n", "      <td>3.84</td>\n", "      <td>478.0</td>\n", "      <td>462.4</td>\n", "      <td>79.9</td>\n", "      <td>100</td>\n", "      <td>284547.39</td>\n", "      <td>2024-04-23 09:40:49.446</td>\n", "      <td>488.0</td>\n", "      <td>500.0</td>\n", "      <td>250000.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       tarCurr  outCurr  tarVol  outVol  maxPower  SOC  BatteryCapacity2  \\\n", "11017      7.0     6.37   478.0   462.4      79.9   99          46949.06   \n", "11018      5.0     6.36   478.0   462.4      79.9   99          46949.06   \n", "11019      0.0     6.36   478.0   462.4      79.9  100         284547.39   \n", "11020      0.0     6.36   478.0   462.4      79.9  100         284547.39   \n", "11021      0.0     3.84   478.0   462.4      79.9  100         284547.39   \n", "\n", "                         time  EVMaxVolLimit  EVMaxCurrLimit  EVMaxPowerLimit  \\\n", "11017 2024-04-23 09:40:47.924          488.0           500.0         250000.0   \n", "11018 2024-04-23 09:40:48.305          488.0           500.0         250000.0   \n", "11019 2024-04-23 09:40:48.685          488.0           500.0         250000.0   \n", "11020 2024-04-23 09:40:49.066          488.0           500.0         250000.0   \n", "11021 2024-04-23 09:40:49.446          488.0           500.0         250000.0   \n", "\n", "       OtherCCUPlugin  \n", "11017               0  \n", "11018               0  \n", "11019               0  \n", "11020               0  \n", "11021               0  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df_charging_data.tail()"]}, {"cell_type": "code", "execution_count": 7, "id": "893d84e4", "metadata": {}, "outputs": [{"data": {"text/plain": ["64.0"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["200 * 320 / 1000"]}, {"cell_type": "code", "execution_count": 8, "id": "b22c5104", "metadata": {}, "outputs": [{"data": {"text/plain": ["(11022, 12)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df_charging_data.shape\n"]}, {"cell_type": "code", "execution_count": 9, "id": "11ed80b1", "metadata": {}, "outputs": [], "source": ["from utils.curves import curve, concat_curves_vertical"]}, {"cell_type": "code", "execution_count": 19, "id": "d31edfc5", "metadata": {}, "outputs": [], "source": ["def curve_all_data(df_charging_data):\n", "    x = df_charging_data.time.to_list()\n", "\n", "    y1 = {\n", "        \"outCurr\": df_charging_data.outCurr.to_list(),\n", "        \"tarCurr\": df_charging_data.tarCurr.to_list(),\n", "    }\n", "    fig1, ax1 = curve(x, y1, title=\"Current\", xlabel=\"time (s)\", ylabel=\"current (A)\")\n", "\n", "    y2 = {\n", "        \"outVol\": df_charging_data.outVol.to_list(),\n", "        \"tarVol\": df_charging_data.tarVol.to_list(),\n", "    }\n", "    fig2, ax2 = curve(x, y2, title=\"Voltage\", xlabel=\"time (s)\", ylabel=\"voltage (V)\")\n", "\n", "    y3 = {\n", "        \"outPower\": (df_charging_data.outCurr * df_charging_data.outVol / 1000).to_list(),\n", "        \"maxPower\": df_charging_data.maxPower.to_list(),\n", "    }\n", "    fig3, ax3 = curve(x, y3, title=\"Power\", xlabel=\"time (s)\", ylabel=\"power (KW)\")\n", "\n", "    y4 = {\n", "        \"SOC\": df_charging_data.SOC.to_list(),\n", "    }\n", "    fig4, ax4 = curve(x, y4, title=\"SOC\", xlabel=\"time (s)\", ylabel=\"SOC (%)\")\n", "\n", "    figure_list = [(fig1, ax1), (fig2, ax2), (fig3, ax3), (fig4, ax4)]\n", "\n", "    combined_fig = concat_curves_vertical(figure_list, titles=[\"Current\", \"Voltage\", \"Power\", \"SOC\"], figsize=(7, None), spacing=0.4, dpi=150)\n", "    return combined_fig"]}, {"cell_type": "code", "execution_count": 21, "id": "2311a6f8", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAnsAAAY9CAYAAAC2epr1AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAAEAAElEQVR4nOzdB3hTVRsH8H+S7kUZhTIKlL2HZZUle8oQPoYgSwREEAFFRYaALBEVQRRkoyKgsmTJkr33lL33KqV0N/mec0pC06ZNR5qkN//f81yS3Htz7+nblrw9U6XT6XQgIiIiIkVS27oARERERJR5mOwRERERKRiTPSIiIiIFY7JHREREpGBM9oiIiIgUjMkeERERkYIx2SMiIiJSMCZ7RERERArGZI+IiIhIwZjsERERESkYkz0icjiXL19Gv379UKRIEbi5ucHHxwe1atXC999/j4iICGRF69evx5gxY2xdDCKyQyqujUtEjmTdunXo0KEDXF1d0b17d5QrVw7R0dHYvXs3/vrrL/Ts2RM///wzspqBAwdi5syZ4H/pRJSYU5I9REQKdfXqVXTu3BmFChXCtm3bkDdvXsOxAQMG4NKlSzIZzKgXL17A09MzyX6RiEVGRsLd3T3D9yAiSi024xKRw5gyZQrCwsIwb948o0RPr1ixYvjwww9x7do1qFQqLFy4MMk5Yn/C5lLxXOw7e/YsunTpguzZs6N27dryWOHChfHGG2/gn3/+QZUqVWSSN3v2bHksJCQEgwcPRkBAgKxlFPf+6quvoNVqDdfWl2Pq1KmytrFo0aLy3KpVq+LQoUOG80RtpKjV05dPvxERCazZIyKH8ffff8t+ejVr1rT4tUXTcPHixTFx4kSjptTz58/jrbfekn0E+/Tpg5IlSyI8PByvv/46bt++LfcXLFgQe/fuxfDhw3H37l1MmzbN6NpLlizB8+fP5bkiiRNJa7t27XDlyhU4OzvL/Xfu3MHmzZvxyy+/WPxrI6KsjckeETmE0NBQmVy1adMmU65fsWJFmZQlJpqGN27ciKZNmxr2jR8/Xg4SOXbsmEwQBZGw5cuXD19//TU++ugjWeOnd+PGDVy8eFHWGgoiYRRfh6gxFDWHwcHBKFGihEz23n777Uz5+ogo62IzLhE5TLIneHt7Z8r133vvPZP7AwMDjRI94Y8//kCdOnVk8vbo0SPD1qhRI8TFxWHnzp1G53fq1MmQ6AnivYKo2SMiMoc1e0TkEMT0KoJoDs0MIqlL7X5RS3fy5En4+fmZfM+DBw+MXotm3oT0id/Tp08zUGIichRM9ojIYZI90Ux6+vRps+cmN7hB1LolJ7kRtqb2i0EYjRs3xieffGLyPaJJNiGNRmPyPE6zQkSpwWSPiByG6N8mRrXu27dP9nNLjr7mTIyYTej69esWKYcYVStGBYtmW0vh6FsiSg777BGRwxA1aWL+u3fffRf3799PclwMmhCraIhawFy5ciXpO/fjjz9apBwdO3aUCacYYJGYSDBjY2PTfE39vH6JE1QiItbsEZHDEDVqYsSsGPBQunRpoxU0xNQnYuCEmLNOEAnh5MmT5aOYI08kfhcuXLBIOYYNG4Y1a9bImkZxv6CgIDkR86lTp/Dnn3/K+fVEspkW4hrCoEGD5IAQ0fQrJpAmImKyR0QOpXXr1nJwhJjiZPXq1fjpp5/kRMUVKlTAN998I+fCE0aPHo2HDx/K5Gv58uVo3rw5NmzYgNy5c2e4DB4eHtixY4eck08kmIsXL5a1iaKv3tixY5EtW7Y0X1PMu/fBBx9g6dKl+PXXX2V/PiZ7RCRwbVwiIiIiBWOfPSIiIiIFY7JHREREpGBM9oiIiIgUjMkeERERkYIx2SMiIiJSMCZ7RERERArGefZerlN5584deHt7c8khIiIiyhLE7HnPnz+X636r1cnX3zHZA2SiFxAQYOtiEBEREaXZzZs3UaBAgWSPM9kDZI2ePlhiFvv0iomJwaZNm9CkSRM4OztbsITKwRiZxxiZxxiljPExjzEyjzGy/xiFhobKyip9HpMcJntiGZGXTbci0ctosieWQRLX4C+GaYyReYyReYxRyhgf8xgj8xijrBMjc13QOECDiIiISMGY7BEREREpGJM9IiIiIgVjnz0iIiIyTOURGxuLuLg42R/NyckJkZGR8jUlldkx0mg08voZnRaOyR4REREhOjoad+/eRXh4uCHx8/f3lzNVcA5a06wRIzEAJG/evHBxcUn3NZjsEREROTixuMDVq1dlTZKYoFckFiKRCQsLg5eXV4oT9jp63MIyKUYi/iIBf/jwofzeFC9ePN33YLJHRETk4ERSIRIXMWebqEkSxGux383NjcleMjI7Ru7u7nJKl+vXrxvukx787hEREZHEpE6Z3xN+V8l2YqNFPbWtS0FERKRoTPbINiJCgK+LAks62bokREREisZkj2xCdX4dEBUKXPzH1kUhIiJSNCZ7ZBsq/ugREZHtjRkzBpUqVTJ57NixY+jQoQPy5MkjB0eIEbF9+vTBhQsXkJXwE5dswynBfEFarS1LQkRElMTatWtRo0YNREVF4bfffsO5c+fw66+/Ilu2bBg1ahTSS4yqTW6C5szCZI9sQ5Ng+HjMC1uWhIiIkpnnLSI6DuHRsVbfxL1TSyRjgwYNQu7cuWXtW+3atXHo0CF5bOHChfD19TU6f9WqVYYJkMXxsWPH4sSJE3Kf2MQ+MbF0r1690KJFC6xZswaNGjVCYGAgqlevjqlTp2L27NmG9xcqVCjZ6yesOZw7d668hn76FHHOTz/9hNatW8PT0xMTJkxAZuE8e2QbCYeSR4UBrt62LA0RESUSEROH4G/32+TeZ8c1hYdL6lKUTz75BH/99RcWLVokE68pU6agadOmuHTpktn3durUCadPn8bGjRuxZcsWuU/U3InXjx49ktc2JXECaY4oiyjjihUr5MTVCRPByZMnY9q0aXJZtMzCZI9sQhf3qrr64bF18Hv9XZuWh4iIsp4XL17I2jFRw9a8eXO5b86cOdi8eTPmzZsHPz8/s5MWi9UvRKIllj3Tu3jxonwsVaqURcopmm4XL16cpDxdunSRNYiZjcke2URcTAycXz6/cO44/F63cYGIiMiIu7MG+4bWgLePt9UnWxb3To3Lly/Lvm61atUy7BMrTlSrVk32sTOX7CUnLc3IqSFqHE2VpUqVKrAGm/bZE9l4hQoV4OPjI7fg4GBs2LDBcLxevXqGNnT99t577xld48aNG2jZsqVc3kW01w8bNgyxsbE2+GooLbRxrzqo1rr3i03LQkRESYnPXHcXjWxOtfaWsM9bRogkVZcocUvNQIgSJUrIx//++88i1xd98kxJbr+ikr0CBQrItuojR47g8OHDaNCgAdq0aYMzZ84YzhFDnO/evWvYRFu8XlxcnEz0RPXo3r17ZXu9qModPXq0jb4iSq24WONfhujw5zYrCxERZU1FixaFi4sL9uzZY5RsiQEaZcqUkbVpz58/l829esePHze6hni/yCcSatKkCXLlymWUcyQUEhIiH8X1w8LCUry+PbBpsteqVSs50kXMWyOyaDESRbSd79//qkOoqLET7ej6TdQA6m3atAlnz56VQ6HFSBfRXv/ll19i5syZyQ5tJvugFUulJXDp8EablYWIiLImUTPWv39/2aonBlWInEBUEonRtL1795ajZz08PPD555/LJt8lS5bISqGEChcujKtXr8okTQzKEKN7xXXF6Nl169bJ0bJi8Ma1a9dkxZQYtKFvZdRff8SIEcle3x7YTZ89kVX/8ccfMjsWzbl6Ym4bkcyJRE8kh2JuGxFYYd++fShfvryc7FBPjMAR33hRO1i5cmWT9xLfSLHphYaGGv4ayMg8N/r3ZuZcOVmdPjaxUZFG+2/tW4HiwW1tVCr7wp8j8xijlDE+5jFGxkQcRHOkVquVm6BvntTvt1cTJ06UOUS3bt1kLZ7oBye6hIlRtcLixYvx6aefyoEbogVRtP6JZE3/Nb355ptypGz9+vVljZ0Y2NGzZ0+Zc+zevVu2QIqBFCJXCAgIkOeNGzdOvj979uxyGhYxqlYkh6aur4+jqRgmjHdyxHFxDfE9SjiSNy0/vyqdpXshptGpU6dkchcZGSlr9URWLGr7hJ9//ll2asyXLx9Onjwpv1mi06UYuiz07dsX169fxz//vFpyS2TzIiNfv369YWROYuKbIubVSUzcW59IUubyv70B1R/8bnj9BxrBpXJ3m5aJiMhR6UejimRGNGuS/RAtlTdv3sS9e/eSjEkQOY9IRJ89e2bU8ml3NXslS5aUVaeioH/++Sd69OiBHTt2yLZ2kczpiRq8vHnzomHDhrKqVLTTp9fw4cMxdOhQw2t9ti7a6FMKljkiwxbDvRs3bixHA1HyMSpcMAB48Gq/p5MOjV8m+Y6OP0fmMUYpY3zMY4yMiQoXkVCIShf9pL+iLkjUlHl7e1tswITS6KwQI/G9EVPE1K1b1/C9SdwyaY7Nkz3xF0SxYsXk86CgINmp8vvvvzfMTp2QaBvXT04okj3xV8jBgweNzrl//758TDhfTmKurq5yS0z8wlvil95S11Eylc64M6wrYhmzRPhzZB5jlDLGxzzGKJ5oBhXJihhdqp9mRd+8qN9PSVkjRuK64vqmflZT+7OrtsfAJexPl5B+hIuo4RNE869oBn7w4FUVkfhLTdTOiZpBsv+pV+J08X8JOelMf8+JiIgoY2xasyeaU0W/uoIFC8pqUNFnbvv27bIPnn5Ui+i/lzNnTtlnb8iQIbIaU8zNJ4hmV5HUiU6ZYni0aM8eOXIkBgwYYLLmjuyH7uXUK2FwRzaEw1nHTtJERESKS/ZEjVz37t3l/Hli1IxI4kSiJ/pQiL4DYqizWC9OjNAVferat28vkzk9MSpl7dq1cvStqOUTAzNEnz8xSobsnDZxssepcoiIiBSX7InhzckRyZ0YqGGOGK0rRt5S1qzZe6FzA1SAC5txiYiIMoXd9dkjx6B7WbMXDnf56AI24xIREWUGJntkGy8HaISr4+c1dNKyGZeIiCgzMNkjm9DFxU8MGfUy2XNFNOK0Np3fm4iISJGY7JFtxL1cNs3JSz66qmIQHWu/y/EQERFlVUz2yDZeTqoc5xTfZ88N0Yix47UXiYjIPtWrVw+DBw+22PUuXbqEXr16oUCBAnIat8DAQLz11ls4fPgwsiome2QTOm18sheriU/2XBGDGNbsERGRjdafFURCJ1bzunDhglzJ6+zZs1i5ciVKlSqFjz76CBlZoUS/2oap+2Y2JntkGzqtUbLnpopBbByTPSIiu6HTATHhQPQL62/i3qnQs2dPOU2bWGZVLCkmNrEoQ+/evWWNnFhTtmTJkvJ44ve1bdsWEyZMQL58+eQ5Yp1bsb948eLYtWsXWrZsKZdmrVSpEr744gusXr1avlcs/iDuExISYrTCl9h37do1+XrhwoXw9fXFmjVr5OIPoobwxo0bKFy4ML788ks5x7BY7atv376wBpuvjUuOXbMXo3m1qHNMdCTwcioWIiKysZhw+M4sbZt7f34HcPE0e5pI4kQtXLly5QwLKmTPnl02wf7xxx9yBa69e/fKpEostdqxY0fDe7du3SoTLrHMqj5hO3PmjFy9y9Q6tyJ5S4vw8HB89dVXmDt3rixH7ty55f6pU6di9OjRMoG0FiZ7ZBuGZtz40bhCePgL8Wtqw0IREVFWIlbfcnFxgYeHB/z9/Q37x44da3geGBiIffv2Yfny5UbJnlh1SyRi4v2COC6IJltLiImJwY8//oiKFSsa7W/QoEGGmoTTg8ke2bQZV6d5tYbx06vHgUIFbFgoIiIycPZAyIBz8PH2NlnTldn3zoiZM2di/vz5suk0IiJC9o0TzbEJlS9f3pDoCaIZ15LEtcUysIlVqVIF1sZkj2zjZUdVlVpj2FX20HCg3hs2LBQRERmoVPFJl2hOtXaylwFLly7Fxx9/jG+++QbBwcHw9vbG119/jQMHDhidJ2r2EipRooR8/O+//1C5cuVkr69PfBMmh6IWLzHRX1D040ss8X2tIet890hR4rSxSZI9rxc3bFgiIiLKikQNmhjtqrdnzx7UrFkT77//vkzaihUrJgdtmCNq/sRgCpEkmho5qx+Q4efnJx/v3r1rOCb6+9kzJntkExFR8X8FxehUeKFK8FfOi0e2KxQREWU5YoSrqLUTI2EfPXokR9OKKVT++ecfOXhj1KhROHTokNnriFq4BQsWyPfUqVMH69evx5UrV3Dy5Ek5ardNmzbyPJE8BgQEYMyYMbh48aK8z3fffQd7xmSPbML1ZYWeVqfCnXdf/UX04MBS2xWKiIiyHNFkq9FoZK2cqHVr2rQp2rVrh06dOqF69ep4/PixrOVLjWrVqslEUSR0ffr0QenSpdG6dWs5SnfatGnyHGdnZ/z++++yuVfUBooRwfqRwPaKffbIJtQvB2jkzuaJYvn8cFIbiArqq8i9cwTQYKCti0dERFmE6GsnRtsmJGroxJbQpEmTDM/FPHgpXW/RokUp3rNWrVqyxk8094aGhsopXBL24RPz9YktMf08fNbGmj2ykZf9IdQaWXVeQnPPcCT84XXbFYuIiEhhmOyRTWv25GgvABcbv/orS/dLW1sVi4iISHGY7JFNqBE/ckqniu+8V7paY8Mxz9ArNisXERGR0jDZI9t42bdB9TLZc3J6NQWL9PC8LUpFRESkOEz2yCbUupdzIqle/QieabjY8DxmeS9bFIuIyKFZehUJso/vCZM9sgnVywEa2gTJXtk6bRANZ/nc+eEZQ+0fERFlLjGdiBAeHm7rolAi+u+J/nuUHpx6hWxChZeJXIIVNITNzbai5ca68vntZUORv7N9T1RJRKQEYp46X19fPHjwQL728PCQNUpiTdnIyEjrr42bRWi12kyLkYi/SPTE90R8b8T3KL2Y7JFNm3FVCWr2hBbVK+DZBg9kU4Uj/3/zgfBRgEcOG5WSiMhx+Pv7y0d9wieSjYiIiGTXeCVYJUYi0dN/b9KLyR7ZtBlXlyjZE78sO6vNRqtD3eJ3TAkEvggxTNFCRESZQ/z/mzdvXuTOnRsxMTFy27lzJ+rWrZuhJkQli8nkGIlrZqRGT4/JHtmEWt8f7+Vo3ISaN2kOJFzG8OoOoEg96xWOiMiBieRCv8XGxsLNzY3JXjKySoxs2gj/008/oUKFCnKZEbEFBwdjw4YNhuOiDXzAgAHImTMnvLy80L59e9y/f9/oGjdu3EDLli1l/wLx18iwYcNk4Mm+qV7Os6cy0cfBydkZ+1/7+tWOxfGLTxMREVEWS/YKFCiAyZMn48iRI3Lh4QYNGqBNmzZywWFhyJAh+Pvvv/HHH39gx44duHPnjlzcWC8uLk4meqJz5N69e+VadmK9u9GjR9vwq6K0rKChn1Q5serN3jZ6HXd2rVXKRUREpDQ2TfZatWqFFi1aoHjx4nLh4QkTJsgavP379+PZs2eYN28evv32W5kEBgUFyUWNRVInjgubNm3C2bNn8euvv6JSpUpo3rw5vvzyS8ycOVMmgGT/o3GTS/ZULh6IVLkbXmuWd7Va2YiIiJTEbvrsiVo6UYP34sUL2ZwravtEx8dGjRoZzilVqhQKFiyIffv2oUaNGvKxfPnyyJMnj+Gcpk2bon///rJ2sHLlyibvFRUVJTe90NBQ+ajvkJpe+vdm5BpKp4+N6uVoXK1Ol2y8zrX+G5VXv/r+xx5bAl25DlA6/hyZxxiljPExjzEyjzGy/xil9r42T/ZOnTolkzvRP0/U6q1cuRJlypTB8ePH4eLiIoccJyQSu3v37snn4jFhoqc/rj+WnEmTJmHs2LFJ9ouaQtH3L6M2b96c4Wsonja+X+XFS1egi4w0eYoYwzErejBmu0yTr51W98e//z1CqEchOAL+HJnHGKWM8TGPMTKPMbLfGKV2EmybJ3slS5aUiZ1otv3zzz/Ro0cP2T8vMw0fPhxDhw41qtkLCAhAkyZN5ECRjGTY4hveuHFjux6VY0v6GGlEBwItULxkKTR/vXay57ds0RwxE2bAWRVfE1j//CjEfHwVcPWGUvHnyDzGKGWMj3mMkXmMkf3HSN8yaffJnqi9K1asmHwu+uUdOnQI33//PTp16iT73YWEhBjV7onRuPrJBcXjwYMHja6nH62b0gSErq6ucktMfKMs8c2y1HUcYYCGxsnJbKzO9LuKkrMLw0kV/x7nqYHAmGdQOv4cmccYpYzxMY8xMo8xst8YpfaeantcekT0pxOJn/gitm7dajh2/vx5OdWKaPYVxKNoBtbP9i2IDFvUzommYLJf6peTKpuaZy+xsvmy4dQbxqNxdaf+zKyiERERKYpNa/ZEc6oYQSsGXTx//hxLlizB9u3b8c8//yBbtmzo3bu3bG7NkSOHTOA++OADmeCJwRmCaHYVSV23bt0wZcoU2U9v5MiRcm4+UzV3ZIdr46Yi2RMqV60FrEvw/r96A0XqA545M6mEREREymDTmj1RI9e9e3fZb69hw4ayCVckeqLtW/juu+/wxhtvyMmUxVIkoml2xYoVRjNXr127Vj6KJPDtt9+W1xs3bpwNvypKSzMu0rBw9FdlVhrv+LoIEMdRYkRERHZbsyfm0UuJWH5EzJkntuQUKlQI69evz4TSUWZSG1bQSP2af592bIAtE+qhUcz2Vzu/zOUQ/feIiIjSy+767JGjNeOm7Uew9rC/ku7c96OFSkVERKQ8TPbI+nQ6qNPYZ0/PzcUJixsexNa4BBNm/zMciHpu4UISEREpA5M9sjqVfiRuGvvs6XWrXQKP2/yCQ9oSr3ZOKgCE3rVQCYmIiJSDyR5ZnUo/OCOFtXFTfL9KhY5VAvCi5SzjA9+WAh5ftkQRiYiIFIPJHtnAyyZcANHx4zTS5fVqr6FadKKEb8ZrGSgXERGR8jDZI5vW7OX0dk//dVQq7BvfGR87jzTaH3fvbIbKR0REpCRM9sjGffbS3oybkEatwtTPBhvvmxUMxEZn6LpERERKwWSPrE6le9WMq07HAI0kNM64ocpnvG+8n1h7L+PXJiIiyuKY7JFNa/ZUasvM63284RL8pw0w3jkuOxAXa5HrExERZVVM9simffZUlqjZA9A8uCL+rvUnWkZNMD6wnxMuExGRY2OyRzYQ34wbp1NBrVJZ5IrOGjWGNS2FNRMGGB/YPAq4fVRO5ExEROSImOyRzWr24qC2WLKXcMDGlVz1jHfOqQ/8/aFF70NERJRVMNkjm/XZ08pkz/LXf/76l0l3Hl1k+RsRERFlAUz2yGajcUWyJ+bKs7TyZcvjneiPEarzMNqvPbfO4vciIiKyd0z2yGY1e/HNuJa/vlqtwrwJIxHVb7/x/mVdgD97A9oMLNtBRESUxTDZI5v12dPCcgM0ktxDpYJfvkKI0iWa2uX0n8DpFZlyTyIiInvEZI9s4FWfvUzK9QzGBy7CD7FtjHeueJe1e0RE5DCY7JHN+uxlxmjcxPq2boB9hd9Hy6iJxgdO/5Wp9yUiIrIXTPbIhs24mV+zF5DDA7+9WwNLRvU1PrCiD3D898y9ORERkR1gskc2nHol8/rsJZbNwxmLNe2Md656D3hyxSr3JyIishUme2Tj0bjWSfYEvzYTcEZbyGhfzOwGXF2DiIgUjcke2W6ePV3mTL2SnKbl8mLr63+hX/Rgwz7nqKfAWF9A+2q9XiIiIiVhskc28KoZNzMmVU5p/r1BDYtj0mfDkhzTHZprtXIQERFZE5M9svHauNa/fw4fLxzXFjUu04ZhwIP/2KRLRESKw2SPbLpcmjX77CX0oNViLIxtYrzzx+rAr4kGcRAREWVxNk32Jk2ahKpVq8Lb2xu5c+dG27Ztcf78eaNz6tWrJ5v6Em7vvfee0Tk3btxAy5Yt4eHhIa8zbNgwxMbGWvmrIXsejZtYw6CycHpjatIDl7cB0eG2KBIREZHykr0dO3ZgwIAB2L9/PzZv3oyYmBg0adIEL168MDqvT58+uHv3rmGbMmWK4VhcXJxM9KKjo7F3714sWrQICxcuxOjRo23wFVFam3FtlOtBo1bh7RqF8Gmxv/FLbCPjgxPzcsAGEREpRqKFQ61r48aNRq9FkiZq5o4cOYK6desa9osaO39/f5PX2LRpE86ePYstW7YgT548qFSpEr788kt8+umnGDNmDFxcXDL966C0st6kyuZ89XZd/HE4EFhbzvjAuOzAyIeAE39+iIgoa7NpspfYs2fP5GOOHDmM9v/222/49ddfZcLXqlUrjBo1SiaAwr59+1C+fHmZ6Ok1bdoU/fv3x5kzZ1C5cuUk94mKipKbXmhoqHwUNYtiSy/9ezNyDaUTsYmO0/fZU0EDrc3j1baiP9Zenoo3zn1sfGC8H2KGXgLcfa1aHv4cmccYpYzxMY8xMo8xsv8Ypfa+Kp3OPoYfarVatG7dGiEhIdi9e7dh/88//4xChQohX758OHnypKyxq1atGlasWCGP9+3bF9evX8c///xjeE94eDg8PT2xfv16NG/ePMm9RI3f2LFjk+xfsmSJIYmkzOPx6Bga3/wOx7VFcD1oDOxBrBY4d+4YRkZ/l+TY6kqLYPMqSCIiokREvtOlSxdZWebj4wO7r9kTffdOnz5tlOjpkzk9UYOXN29eNGzYEJcvX0bRosbTZ6TW8OHDMXToUKOavYCAANlfMKVgpSbDFn0PGzduDGdn53RfR8lEjPb9fkQ+16k0aNGiBexF6zdaoM/8mhh6eyhKq28a9r/xbAG0Xf60Wjn4c2QeY5Qyxsc8xsg8xsj+Y6RvmTTHLpK9gQMHYu3atdi5cycKFCiQ4rnVq1eXj5cuXZLJnmjaPXjwoNE59+/fl4/J9fNzdXWVW2LiG2WJb5alrqNY2vjKZB1Udhenn/s2xvYTq1B6VZBhn+bqdmgm5AJGPgCckv7cZBb+HJnHGKWM8TGPMTKPMbLfGKX2njYdjStakEWit3LlSmzbtg2BgYFm33P8+HH5KGr4hODgYJw6dQoPHjwwnCOybFFDV6ZMmUwsPVligIa9EVP71K9UDCNjeiU9OD43cOFVdwEiIqKsQG3rplsx8EL0lRNz7d27d09uERER8rhoqhUja8Xo3GvXrmHNmjXo3r27HKlboUIFeY5oehVJXbdu3XDixAnZd2/kyJHy2qZq78ie5tmzv2RPL3+jgVgTF5z0wJKOQGy0LYpERESULjb9tP3pp59kp0IxcbKoqdNvy5Ytk8fFtCliShWR0JUqVQofffQR2rdvj7///ttwDY1GI5uAxaOo5Xv77bdlQjhu3DgbfmWUogQraNir/vWLYU2x8ZgS0zHpwUkFmPAREVGWYdM+e+YGAotBE2LiZXPEaF0x8pay1qTK9pzsCbPefg0hoV8D3y83PhAXBfw9CHhzlq2KRkRElGr2/WlLym7GVdn3j5+TRo1c2X0xtvIuDI02XqIPJ34HHpwDoo1XeyEiIrI39v1pSwpvxs0ac9d90aYChnw0KumBH2sAE/MBNw7YolhERESpwmSPrC6rNOMmFJDTC2feuYROuklJD85vAlzbY4tiERERmZV1Pm1JMbLCaFxTyhb0w7Kx72NqTIekBxe2ADaPtkWxiIiIUpS1Pm1JGbLAaNyU1O+VzEjvPd8DMZHWLg4REVGKsuanLWVpWWWARnKCiuXDL02O4aC2ZNKDE/IAWzntDxER2Y+s+WlLiuizp8vCP37dahbBjbrf4ufYlkkP7voGug2fAnExtigaERFRxubZ02q1cu67Xbt24fr16wgPD4efnx8qV66MRo0aybnxiFJTsxcHDbKy1vVqYlSIDxoeqYetrsOMjqkOzALENvg04MvfCSIisp1UV62IJczGjx8vk7kWLVpgw4YNCAkJkStXXLp0CV988YVc21Yc279/f+aWmrK0rDga1xQXJzW++l8F/P55dxSO/A0toyYkPWlaOUMfRSIiIruu2StRooRcjmzOnDlo3LgxnJ2dk5wjavrEOredO3fGiBEj0KdPH0uXlxQgq47GTU5ubzdcm/wGwqJigUkjkp5weRtQrKEtikZERJT6T9tNmzZh+fLlsubOVKKnX7Zs+PDhuHjxIho0aGDJcpISR+Nm0QEayfFydcKBJquTHvi1HbBppC2KRERElPpkr3Tp0qk67/Tp0zIZLFq0aEbKRQql3jUVwY//VFTNXkKvVa+LT2JM1GjvnRG/ERERWZlFPm2fP3+On3/+GdWqVUPFihUtcUlSIp0Omp2TDS/dEQWlcdao0f39UVgQ2zTpQVG7NyYbcOuwLYpGREQOKkPJ3s6dO9GjRw/kzZsXU6dOlU23HJxBybq01ehlc+0OKFG5/NnQbthsw+tYXaJfs7kNgdA71i8YERE5pDQne/fu3cPkyZNRvHhxdOjQAT4+PoiKisKqVavk/qpVq2ZOSSnrW97N6OXi2MZQqmzZsqNPka0oHLkE7wduTHrCt6WBJ1dsUTQiInIwaUr2WrVqhZIlS+LkyZOYNm0a7ty5gxkz2A+JUkFMMBwTbnjZL3owvojtASWb070Krk1uiVndq+Gj6PeSnjC9MnB4vi2KRkREDiRNyZ6YW693794YO3YsWrZsKefYI0qV5/cMT/fElcVxrzo4NbY5HIFarcLEcRPRJfrzpAfXDonvx/f4si2KRkREDiBNyd7u3bvlYIygoCBUr14dP/zwAx49epR5pSPl2P+j4emo2F7YPrSOnKrEUbg6aTB2YArzTs54DYiJtGaRiIjIQaQp2atRo4acVPnu3bvo168fli5dinz58skl1DZv3iwTQSKT8+q9TPauaP1RuVAeOGmUN+2KOcXz5cCShvtwUZvf9AkT8nC1DSIisrh0feJ6enrinXfekTV9p06dwkcffSQHZ+TOnRutW7e2fCkpa7t/2vD0s5g+qJnHcROazrVKo3H01zimLWb6hLG+wNNr1i4WEREpWIarV8SAjSlTpuDWrVv4/fffLVMqUhTt/p8Mz+9kqwwnx6vUM+q/d3VSC9xttwpfxJgeoOL8YxU0Pj0EiHhq9fIREZHyWOxjVwzWaNu2LdasWWOpS5JCqI//Jh83xFXFgp5BcHQqlQotKgWg54fjkz3HI+YxnL8tDjy6aNWyERGRAyd77733nqy9S41ly5bht9/iP+DJwV34x/B0dmwrFM7padPi2JPA3D54K3oE3on+GIUjk/l9+aGKtYtFRESOmuz5+fmhbNmyaNGiBX766SccOnQIt2/fxuPHj3Hp0iVZo/fJJ58gICAA3333HcqXL5+5JaesYUlHw9OKNRratCj2qHPHrnAr2wLHRjVB7+iPTJ80h3EjIiIrJHtffvklLly4gFq1auHHH3+UI3MLFiwoB2WIfnvdu3fHlStX5GhdsWRahQoVMlAsUqJqgTltXQS706ZSfvzYNQjZPV3w9YjPUDrSxCTLtw/Hz8V3bQ+nZyEiojRL00RnefLkwYgRI+T29OlT3LhxAxEREciVKxeKFi0q+yIRGZz8w/D0qjYP6pbIZdPi2Lscni44O6kd/h39PeprTiQ9YWGL+MdBx4AcRaxePiIicrABGtmzZ0fFihVlDV+xYsXSlehNmjRJrqXr7e0tawjFAI/z588bnRMZGYkBAwYgZ86c8PLyQvv27XH//n2jc0TSKVb08PDwkNcZNmwYYmNj0/ulkYXoTr1K9qbmnQpvN2eblicrEL9HTl2XGV6Pj+lqepm1E6/OISIiSolNJ8HYsWOHTOREs6+YlDkmJgZNmjTBixcvDOcMGTIEf//9N/744w95vliPt127dobjcXFxMtGLjo7G3r17sWjRIixcuBCjR4+20VdFkjYOqovxgzPEFCMD29azdYmyjBpFc2Gs+3CMiHkHsdUHYFrsq593g5V9gdC7tigeERFlMTZdr2rjxo1Gr0WSJmrmjhw5grp16+LZs2eYN28elixZggYNGshzFixYgNKlS8sEUdQqbtq0CWfPnsWWLVtkM3OlSpVk/8JPP/0UY8aMgYuLS5L7RkVFyU0vNDRUPopkU2zppX9vRq6hFOpDc6FfOXm36+sYmcvdKL6MUfJEbEoVK40C5XqgSuGcuFdlMvDziqQnflsKMSMcc7lC/hyljPExjzEyjzGy/xil9r4qnc5+1mcSo3qLFy8uV+UoV64ctm3bhoYNG8r+gb6+vobzChUqhMGDB8taP1GDJ0YCHz9+3HD86tWrKFKkCI4ePYrKlSsnuY9IAseOHZtkv0gqRVMwZZBOizbHe8qnT3VeGOP/Exrks5sfsyyp4LGJqIz/kuw/598OF/K2tUmZiIjItsLDw9GlSxdZOebj45PseXazEr1YX1ckcGK0r0j0hHv37smauYSJniBq8MQx/TnideLj+mOmDB8+HEOHDjWq2RNTxogm5JSClZoMWzRHN27cGM7Ojts/TXX7CPAy914WVx+TejWF88u1cBkj80zFKLJOdfz13btor9lldG7peytQMqcKcW1miQ5/cBT8OUoZ42MeY2QeY2T/MdK3TJqTrmRPNKmuWLEiSRImbioGWYgaubQSffdOnz4t19vNbK6urnJLTHyjLPHNstR1sqzV7xme/hLbCO+5ZV6slSxhjJxz+KPmx3+gz7c/YI7KeOUN9Zm/5IaPLgDexn/4KB1/jlLG+JjHGJnHGNlvjFJ7z3QN0Ni+fbscEJGYGDm7a5dxzUNqDBw4EGvXrsW///6LAgUKGPb7+/vL+4SEhBidL0bjimP6cxKPztW/1p9DViTmgXt61fCyTJn4WlrKuLzZ3PG/jt3QJOornNUWSnrCNyWAxW0A++mZQUREdiBNyd7JkyflJohBEfrXYjt27JgcTJE/f/5UX090FxSJ3sqVK2VtYGBgoNHxoKAgmbVu3brVsE9MzSKmWgkODpavxaPo4/fgwQPDOaJKVTTHlilTJi1fHlnCmZWGpwe1JTGmdVmbFkdpmpTJg5G92kPVfzea+f6d9IQr24Hl3WxRNCIislNpasYVI13FPGBi04+OTcjd3R0zZsxIU9OtGBSxevVqOdeevo9dtmzZ5LXEY+/evWX/uhw5csgE7oMPPpAJnhiJK4h+diKp69atG6ZMmSKvMXLkSHltU021lMluHjA8HR7zLjZ683tgSeJ3r24JP/l84+C6mPP7XPQ5/67xSef+BiJCAHfjbhZEROSY0pTsiVGuojZOjHQ9ePCgXC9XTwykENOmaDT6CTfME2vsCvXqGc/BJqZX6dkzfjSnWGdXrVbLyZTFdClNmzaVy7XpifuJJuD+/fvLJNDT0xM9evTAuHHj0vKlkaUcWSAfonROaFinjmFgBmWOPm91wLKRv6KT03bjA18VAj48AWQvbKuiERFRVkz2xJQn+pGzlpCaWV/c3Nwwc+ZMuaVUrvXr11ukTJQBt48annaKHo3lTUratDiOIuiDX9Ht+x/wi8tk4wPfV4x//Pgi4JXbJmUjIiLbS/fUKxcvXpQDKkRfucTJH1evcFCr3jc8DX69KVycWKtnDcXyeOPj994D5idK9l7S/d4Zqj5pHyFPREQOnOzNmTNHNpvmypVLjnhNuC6ueM5kz0E9PGc0kICsp2LB7HjP9SvUebEJf8XVwQrXMcbzHn5TCui2CshdyqblJCKiLJLsjR8/HhMmTJBLkhFJd+NHaQsTYrpgWL5sNi2OI3rv7c7Yc6kRFtQohJbjnLHOdcSrg8/vAj9WB4aeA3zy2bKYRERkZelqZxPLl3Xo0MHypaGsa3Ydw9P7/vXYhGsDlQJ8MaB+MWRzd8bSL/qZPunb0sCTV/MgEhGR8qXrE1kkeps2bbJ8aUgRchUub+siODxvtxRmVZ9eCXh225rFISKirNaMW6xYMYwaNQr79+9H+fLlkyzXMWjQIEuVj7KC0LuGp6e1hdG7jvHk2GQb37sPQMGw41gVVxuLXL4yPvhdGWDMM1sVjYiI7D3Z+/nnn+Hl5YUdO3bILSExQIPJnoNZPcDwtG30OFzydbdpcShevS6f4Mftl/C/crmA1YmSPWFMNqD+CKDmB4Azv2dEREqVrmRPTK5MZHA5fjm7tXE18Euf2rYuDb1UMcAXs7tVkc+7//EpFieu3RP+nQBsnwR88dT6BSQiIqvIUC/66OhouVZtbGys5UpEWcuDV9OtfBfbHsFFc9q0OGTanHGfYmiZHegZPSzpQZ02fom16Be2KBoREdljshceHi7XrPXw8EDZsmVx48YNuV+sWzt5sumJXUmZdKv6G55XrRK/XjHZH1cnDb7tWAn9evdH4cglmBjzlvEJy94GJnJKFiIiJUpXsjd8+HCcOHEC27dvl8uZ6TVq1AjLli2zZPnInsXFQnXnmHy6MLYJ+tcrausSkRmi5vXa5JZo9O5E0yc8+M/aRSIiIntM9latWoUffvgBtWvXNlo9Q9TyXb582ZLlI3t26g/D07893kShnJ42LQ6lXrXAHPgqpnPSA2LiZTFwIzbaFsUiIiJ7SfYePnyI3LmTLqz+4sULo+SPsoYHzyOh0+nS/sYLGwxPK5Tl3HpZTdsPpqJ51CTTB//oYe3iEBGRPSV7VapUwbp16wyv9Qne3LlzERwcbLnSUab7+8QdVJuwFSNWnU7bGyNDgbOr5dMInQveq188cwpImaakvzd6vPkGjuR8I+nB8+uBJZ1sUSwiIrKHqVcmTpyI5s2b4+zZs3Ik7vfffy+f7927N8m8e2Tfvv7nvHxccuAGJr6Zhto5MV3HS2FwRx6fV303KevoXK0gUHE2MCl/0oMXNkI33h+q1jOAClwekYjIoWr2RF89MUBDJHpiBQ2xdJpo1t23bx+CgoIsX0rKNOludd//o+HpO6am86Csw9ULCxocRJfoz5McUsVGACveNdTiEhGRA9TsxcTEoF+/fnK5tDlz5mROqci+3TpiePpO9Meo36CpTYtDGderbkl0qfkxpk24icjoGHzmvNT4hOXdgWwBQPfVgFcemSASEZFCa/bEOrh//fVX5pSGrC5dFXtzGxievijYEEMbl7BkkciGc/F1//hblOv0BdpGjUt6wrObwIzXgFlcJYWISPHNuG3btpXTrxC9V7+YrYtAFpTD0wVvVMiHlRMHoZl3Mn/UPb0aPz3LQdbsExEpdoBG8eLFMW7cOOzZs0f20fP0NJ5fbdCgQZYqH9mbC/8Yns6NbY6ugVweTYnECPuNHzXCzlHlUVdzyvRJ6z+O33quBwrXsnYRiYgoM5O9efPmwdfXF0eOHJFb4g8JJntZR5rnRVzS0fD0bKkP4O6isXyhyG6sLfMNxp48gXc0G9HVaavpkxa2AKr2AbxyA3WHZWDUDxER2UWyJybfFcukidG37u7umVIoslP3jGt4GlQItFlRyDo+alkRfxfwQ/nAjnhz4VqsjB1g+sRDL5t0ffIBRRsCPnmtWk4iIrJgnz2R7Ilm3Fu3bqX1rWSH0lQHk6Bjfp2o79CsrH9mFInsiJg/8d06RVChgC9WjnwbU2ocwGuRs5J/w+oBwLelgG9KA9f3Auf+tmZxiYjIEsmeWq2Wyd7jx4/T+layR+lscevcpC6cNOka30NZ2CfNSmHn2A5o7rkMJSMXJn/i8zvAgubAsreBGwfEX4nWLCYRESWQrk/ryZMnY9iwYTh9Oo1LbFHWdepPw9NxMd3QIaiATYtDtuPl6oQNw5rh/OQ30RFTsDauRspvmN8EGOsLRL+wVhGJiCijyV737t1x8OBBVKxYUfbby5Ejh9GWWjt37kSrVq2QL18+OVAg8XQuPXv2lPsTbs2aNTM658mTJ+jatSt8fHzkoJHevXsjLCwsPV+WQ1KntjP9X70NT+fHNYeft2vmFYqyjE6tWmJgzCDMi21u/uSJ+YCpJYEnV6xRNCIiysho3GnTpsESXrx4IRPGd955B+3atTN5jkjuFixYYHjt6mqcZIhE7+7du9i8ebNc3aNXr17o27cvlixZYpEyKp3GXLIXGw1MMR6IsX5QnbSP4iVFavdaflQu6Its7o3Q+ddDeO3mYnzivCz5N4TdA6ZXjh/E0XExV+IgIrLXZK9Hjx4WuXnz5s3llhKR3Pn7mx4IcO7cOWzcuBGHDh1ClSpV5L4ZM2agRYsWmDp1qqwxpJSp1Skkbec3AL93Nto1KHogpvgZz6tIjksk/UX84hO2pe/Vxs0HZfH59144oy2E1a6jk3/j5a3ApPxA+Y5A0fpApS7WKzQRkYNJV7J348aNFI8XLFgQlqKf5iV79uxo0KABxo8fj5w54yfy3bdvn2y61Sd6QqNGjeQgkgMHDuDNN980ec2oqCi56YWGhspHUTMotvTSvzcj17C2kPBow/OE5VZvGwvNvhlJzo8o2RYaaBETo3WYGFlbVo5RHl9PaF/rgWJxWtQ4OgPZVWHY4Do8+TecWh6/reqP2LdXQVeotuJjZA2Mj3mMkXmMkf3HKLX3VenEXCppJJKplJrx4uLi0npJeb2VK1fKpdj0li5dCg8PDwQGBuLy5cv4/PPP4eXlJZM8jUaDiRMnYtGiRTh//rzRtURyOHbsWPTv39/kvcaMGSOPJyaafsX9HMmH+5ygghYFVA/xUbUccNGGocUp03OpFY38BeOr6uCRrj8RyNEceaTCtecqNHI+iZi7p9DbaYPZ9xwqPAD3fSohTsM+oURE5oSHh6NLly549uyZHLuQnHR9bB87dixJZin2ffvtt5gwYQIspXPnV02I5cuXR4UKFVC0aFFZ29ewYcN0X3f48OEYOnSoUc1eQEAAmjRpkmKwzBFxEH0HGzduDGdnZ2QFQw9sxnTNdLTUHITuvC9UkSEmz/s4ph8W9KqG4CIZWx4tK8bI2pQSoxaGZ83k/Jwrv2yLNzV7UnxP1Wsz5WNs5+XQFW2g+BhlFsbHPMbIPMbI/mOkb5k0J13JnhhUkZhoShV95L7++utkB1tkVJEiRZArVy5cunRJJnuiL9+DBw+MzomNjZUjdJPr56fvB5h4oIcgvlGW+GZZ6jrW4KRWyURPMJXovRU9Aie0RdGxZinULWm5SZSzUoxsRWkxut9wOpr9sxljnRfilDYQ1dXnUF59zeS5TktfLsvnkQt4fz/g5ecQMbI0xsc8xsg8xsh+Y5Tae1q0Qa5kyZJysERmEat2iMmc8+aNX4opODgYISEhcn3eoKAguW/btm3QarWoXr16ppVDMSJD0VmXfNNahcif4e6TE7s/rIscni5WLRopT7/Xi6JxWX94u/bC7UuP0Gr5CfjhKd5zWpt8E2/4I2BqMaDmIKDI60CxRtYuNhFRludkiWpD0UQjpj8RfeHE6hqpJebDE7V0elevXsXx48cN8/WJfnXt27eXtXSiz94nn3yCYsWKoWnTpvL80qVLy6lZ+vTpg1mzZsnq1IEDB8rmX47ENeP5feCbEhhr4o+C0TE98FdcXSwd2BjlC2SzRelIgUS/3KIvR+62e60A6hT3Qy4vF2h1XdFgxFxUUF3BNJcfTb957/T4rco7wBvfWbfgRESOmOyJEbCJB2iIhE/0exODKlLr8OHDqF+/vuG1vh+dmNrlp59+wsmTJ+UADFF7J5I30afuyy+/NGqC/e2332SCJ5p1xcARkRxOnz49PV+WY4iJhHbvDKj/HW/ycLHIxXinbgkcaVwCbs4aqxePHId+Ym6NCtg84V38tP0SGm0ujMFOK/CGZr/pNx2eD6idgUamf36JiMhCyZ5oKk2Y7Ikky8/PT9a6OTml/pL16tWTSWJy/vnnH7PXEDWAnEA5dbQn/4R6Re8ky6bsiiuHOprT2BQXhFUf1EO5/KzNI+vSqFUY2KA4ugUXxruLymNt2EX4Pj2Jyc5zk558cDacD85GPbcAqN0OAQ1GAs5utig2EZFykz2RpFEWEnoX+LZUkiSvX/QQ5H+tKXZcj0Low9t4DB+c9/e2USGJgGzuzvjjvZoAauLh8yiUnlAT7zutwQdOq5KeG3kTEHNBiu2zm4Bb+kfSExEpWbqSvUmTJiFPnjxymbOE5s+fj4cPH+LTTz+1VPkoIx5eQNzittA8v53k0LQqWzD7jaqG1w9CI+Hj7gxnTbqWSybKlGbeA2Na48u/i2HWiTi85/R38idPDoh/FLV8pVsDuUqIToJWKysRkT1L1yf77NmzUapUqST7y5YtKwdKkI1d3QXtV0WAmVWTJHpLY+vhvx6nMDhBoifk9nFjHz2yOz5uzvi6Q0W0+ngOjr5zDf2LbUPTqMk4qy1k+g3bxgMzqwFHFlq7qEREyqrZu3fvnmH6k4REvz0xKpdsJDLUUMOROIuvGvkjvu5eH53LcJQyZT35fd3l9tPbQdBqX0PL6WUx8PGXhjkiE4vb9AU0lboCTpwyiIgoXTV7YtTtnj1JZ8IX+zjliZU9vQ7snQGMyfaqKSuRv6r/gUOTu6IeEz1SALVahTUDgjHHZxAmxHQxeY4m+hkw3g9RS7oB2rQv30hEBEev2RPz2g0ePFjOa9egQfySRlu3bpXz4H300UeWLiOZEv4EuumVoIp8ZvLwPV12HH9zBxpXKIj2avZdIuXpVUIL7+Ij8clvnpjiPMfkOa4X1gDjcgADDwO5Uj8HKBERHD3ZGzZsmFzJ4v3330d0dLTc5+bmJgdmiHVnKRNEhQG3DwOL2xh2mUrhxsR0R6+hk1EopyeaWbWARNZXp3huVB01CUNWv42Vx25jitNsdHTakfTEH6rIh6gPTsI1ZzL9/YiIFCpdyZ6YY++rr77CqFGjcO7cObi7u8uVM0ytN0tp8PgycOoPIDYKcM+OuNC7eHHzBC64VUSVK8msLPDSEW1xRLVbiFEVyso5y4gchbebM77rVAnvvV4Ul2dOM+xfElsfXZz+NTrXdUYFPMxTB34tRwIFa9igtERE1pehtXG9vLxQtarxqE5KIzGpdFwMtCvfg/rMX0aHxNhYMXNYFSTtHymE6DwxPc94dP1fBwTl5vx45NhK+nvjPiIMr1uP+hO3HtyG69za8FO9WuLR7/4uYH5T6KCC6rPrgBsnESciZctQskfpTO4ubwOiQqHb+wNUomk2FSNlTmiLoKL6CkbG9ELuBgPQq1Zh+Lo5Y7RVCk2UNSyLq4+6mlOypjvI1QleAYWgG3MDR44dRtCaRkbnqqADJhfE5f9tQtFy1W1WZiKizMZkz8pijv4G578HyOfmGluf69yxq8hQ5A7ujKDiAXKSWK4ISpS8ddrqeBA1Gud1ATiZoNtJ0GtVgQoPcOe7esj34qzRe4r+2QQxf2gQ9sYsZK/a0SblJiLKTEz2rCzq36/hnMyxnXHlsavceLj75EKNYrlQo2getGD/O6I0UOGQLumE75KTK/IN24fY6CicO30U5de8GsLkrIpD9nV9ALEJLaYCVd/lKhxEpAhM9qzp4Bx4hV0z2rUvrgzC8tZAqUY9ULdEJdS1WeGIsj4xOClOq0vxHCcXV5R/LRgxAUdwftko+D4+igK6e8Ynrf9YbtGtZ8GlcmcmfUSUpTHZsybxAZLAhQLtUe2deRw9S2QDzn7FUG7g7/L5s/M7cXTdHNQPXWN0jsua93Br8zTk+nCHnF6KiCgrYrJnI3NiW6DPu/NtXQwiApCtZF3UL1kXz0/8De+VbxsdKxDxHzA5j9G+yPa/wK18ayuXkojIisulUcZdK9LV1kUgUpy82TJW++ZdsRWut1+LFZXm4j+v5Efouv3VDdoxvtDOaQQ8vJChexIRZTbW7NlIz5av27oIRIqzoGdVjPn7DAY3KpHuaxQqXweFyotnHXDlxi3s3LEZPS8PTnKeWkzdcvsQMLMqYp08oO63C+ochQEN/1slIvvC/5VsxMc9uTG5RJRexfN447d3LbcyRpGCBVCkWy/EPq6PIyunodCd9fDX3k9ynlNsODAzSD5/XqAeXF/rBBeVFijRHPDMabHyEBGlB5M9K3ruWQjeL67jli4X/D1dbF0cIkolp5yFUf3daYDuO2hvHMDJi9dw795tNLs0Lsm53re2A2J7KcynGLxK1gOajAec3a1cciIiJntWFeJdQiZ7W7J3Rk8Nu0sSZTkqFdSFaqBSofjaw/N3+2Dtjr1ofXYoiqtvm3yLV+gl4JDY5uKqXwN4NB+HPB4qwCt3/EZElMmY7FmTTisf1OzTQ6QIJfP6oGTnZoiIboxI6BC38n14nluW7PmBD7cBi7cZXj/OUQkeeUvDxVkDTeHaQKW3rFRyInIkzDqsSKWLe/mEtXpESuLuool/0ulnhN4fgT1nruLCpQv48M4wo/NCdJ7wVb0wvM755DggNuH4r8Cq9wzH4grWhqbzL4BHDit9FUSkVEz2bFCzB9XLDwYiUhyfPIXQXGwN6gHoi4joODwKi0KsVoelh27AL+Qk3j3f1+x1NDd2A1MCDa+vlOiNnEWD4JEtF5w9fIF8leQScERE5jDZs0HNnk7Nmj0iR6r1C8jhIZ8Pb14agNg6ydcPLh7GqcM7ob2yA9GR4WipOZjsdYpcmAeILYHTBbuh1IP1gJcfonKWhbb+cHj7F8/kr4iIshome9ak1TfjsmaPiIDcxaugYfEqAIbK1+HhYdhz+hoKrH0LpdU3zL6/3I1f4p9EPobTo/+A83/Jl2JipzaiKfhOBaDbivi1fcVIYNGFRO0EaDj1E5EjsWkV086dO9GqVSvky5cPKpUKq1atMjqu0+kwevRo5M2bF+7u7mjUqBEuXrxodM6TJ0/QtWtX+Pj4wNfXF71790ZYWBjskeplM66OyR4RmeDh4YXG1cqh9LhTCP/8MTZ3vIC9b1/Gmb43sLX27zjm0zBN19PcPwlMLQZ8XRSYmA+Y4A98mQsRM2oi8t+p0B2YDd3hBdCF3sm0r4mIHLxm78WLF6hYsSLeeecdtGvXLsnxKVOmYPr06Vi0aBECAwMxatQoNG3aFGfPnjUsSi4Svbt372Lz5s2IiYlBr1690LdvXyxZsgR252Wyp2IzLhGZ4eHihMZlXq3JWzZfC6BRCzy8fwvXrlxE0cKF4Z09N5YsWYgeNz5P07XdH58Bdpwx2ndBVwAlVLegzVsJ6kZjgKL1Lfa1EJEDJ3vNmzeXmymiVm/atGkYOXIk2rQRDRLA4sWLkSdPHlkD2LlzZ5w7dw4bN27EoUOHUKWKaAoBZsyYgRYtWmDq1KmyxtAu++xxNC4RpZNfngJy0+vxzgCERvbFjRVjcCdCg0fl3kW5vF7YtXsXchQoDvdd41Aq6jRKqW+meF2R6Anqu8eBX9omOR7pmgsunRZC7Z4NcHIDYsLju6b45I3vmuL9KjElIvtit332rl69inv37smmW71s2bKhevXq2Ldvn0z2xKNoutUneoI4X61W48CBA3jzzTdNXjsqKkpueqGhofJR1AyKLb307032GvpkT6fO0H2yMrMxIsYoFRgjY+4aoGSHMSj58rWIy1VPoHGVwnAOju/Xd/nwOpzdtxGPXsQg1CUPPoyYmaZ7uEU9Aha/kezxCFc/RJVoBa/Cr0FXvlN8P0E7xp8h8xgj+49Rau9rt8meSPQEUZOXkHitPyYec+c2noHeyckJOXLkMJxjyqRJkzB27Ngk+zdt2gQPj/hRcxkhmpRNqRAeP7/WoydPsH79ejiy5GJErzBG5jFGaYmPCijaHGLWPrEtiwnC9YehKOqjhZOzC1QuHgh79gSnHkSifOg2dFRvh5NY3zeV3KMewv3UfEBsfw807D/v+zrOF+4Jnbi/vhx2lAjyZ8g8xsh+YxQeHp61k73MNHz4cAwdGj/6TV+zFxAQgCZNmsiBHhnJsMU3vHHjxnB2Tjra7fHFSUAYkCt3HtnU7IjMxYgYo9RgjDIvPh3kv+9BJ/6gDgnB5Xm9UTLimNx7SZcfNdTn0nS9kiE7UPL4DqN9z3xKIK5yTzhHh8A9X2nALRt0OYoCPvlhLfwZMo8xsv8Y6Vsms2yy5+/vLx/v378vR+PqideVKlUynPPgwQOj98XGxsoRuvr3m+Lq6iq3xMQ3yhLfrOSuozYM0HBy+F8cS8VayRgj8xijzI2Pv58f/D9bY3idS/wTGYorp/bBt3Q9nD28DVu2bkIp3RV0dtqe6utmC70A7Eg6qCTGLRfUrh5QuWeP38TawXWGArnF3ISZgz9D5jFG9huj1N7TbpM9MfpWJGxbt241JHcigxV98fr37y9fBwcHIyQkBEeOHEFQUJDct23bNmi1Wtm3z/68bBJRc+oVIsqi3HxQpGpT+bR2veZyS0gbGwMVdHgUFo0jl2/jwOVH8Ds5C+87vUoak+Mc+QiIFFV/CeYYPLXc8PReuxXIE1gWKrVz/Ooh4v9SMX8gEdlvsifmw7t06ZLRoIzjx4/LPncFCxbE4MGDMX78eBQvXtww9YoYYdu2bfxIsdKlS6NZs2bo06cPZs2aJatTBw4cKAdv2NtI3ITz7HFtXCJSKrVTfE2Dn68LmgWVlBs61sKxrctw4d8liIQzYr3yoXj4UdRVn0rTtf1XJJ2i61n+evAuVBE6Fy9oSjSJX0aOiOwn2Tt8+DDq1381l5O+H12PHj2wcOFCfPLJJ3IuPjFvnqjBq127tpxqRT/HnvDbb7/JBK9hw4ZyFG779u3l3Hz2SD/1Cmv2iMjRVG7YSW4JiT/Qjx3cidv3HyAszglHbzzF289+RpDaePL8lGS7vR0Qm7B9QpLjYmCINlsANG/9DviXy/gXQpQF2TTZq1evnpxPLzliVY1x48bJLTmiFtAuJ1BOsWaPyR4RkehvVK3Wq1VBuompsWJ749CtZwg/sAgVL/6A57FOCEDysyuYI5qUNaJZeFYtw76oyu9AVaaNHEGM2ChRkAx/LUT2zG777CnRqwEaTPaIiExxddKgauEcQOEhAIbA19RJWi3+2/gTth05jajoWAzRLE/bPY7NB47NRxPx4uxHCHXNA6+4UKgrvQUEDwRyFrXQV0NkH5js2aQZl332iIjSTa1GqRYDUEo/g5XuZ1y+eRt5s3vg2v1Q/LNiPoaEf5/qy/lE3Y9/cnh+/PZS7PuH4ZSjYHxrjMbEx6VW/AGvY9ccsntM9qzqZZO1imEnIrIYlQpFC8YvIVfGOwfKfCK6/rzq/nNn9xLsu3wf0eFhiPEphIqXZqKi7j+zl3X6sYrRtDCqukPh5OaDaI0bXHZ+JSZPlceeV3oX3sVqxSd9OYsBecpmypdJlF7MOqxI/bJmT8XRuEREVpOvdhe0r51wT3dcPH8Gf27aiYZBJVBtU9JRvianhdkUPzegS6Jj3sfnAmJ7SeeeHSjbDqrmXwEa9gck22OyZ4sBGmzGJSKyqcJFSqBMwUuoXLUuUOOprB08fO0xzp08iPp352GOri063f8GZXA1zddWRTwFDs+L317SdlsDdb4KgEgEiayMyZ4VqfCyzx5H4xIR2Y+Xf4BXCcyFKoGiI2ALxK+e3g3b/vwJ14//iwIesSgZeRIF1Q8Nb/srrg6cEIdYaNBesyvlW/zSWj6GueeHS7nWcCnfFihYI1O/LCI9Jnu2GI1rqqMvERHZnQb/6w+I7aXt+w7g+uMXqFUlCO39s706UafDjn37sW73IUwJH5Xs9bwibgOHforfBFHTJ2oCe20ACtXM1K+FHBezDhs047LPHhFR1lQvOJmlOFUqvF4zWG7AIJw6fwmR/07BpbytoDs0D12c/jX9PpHoCQvil527X6ILctbsDqds+YDshTLryyAHw2TPilT6tXE1bMYlIlKy8iWLASV/RlUxWrdpM9yJjMW5nSvgdXg6qquTHwmc58ISQGyisjCoF1Qtv025n3dMBHBoLhAVBkSHAc4egDYGyB4IvNZdJqFETPZsUrPHZI+IyFF4uznLLV/rrkDrroiNCsf1nb+i6J5hKb5PdWQBcGQBQpp8D9/KbYFnNxG96E24RLzqN5iSsL1z4PX+dkB89oikT+3E5M9BMdmzIrW+Zo+jcYmIHJaTqweKNu4LiO2la1vn4mSIK6qdHAV/1cum3Zd8N30IiM3EtC8p8Xp8Cvgyp9G+2IK1ocpVHJFeAYiNjsCdx89RtMxrcCndHHDzyeBXRvaKyZ5N5tlj2ImI6JXCDd9FYQCRb3TC5AW/oGr4LjQMXWnx+zjd2A3c2A3Pl6/lEJOLwJ6dbVHrw0UWvx/ZB2YdNuizx7VxiYjIFDdXF3z2Xm8AYluItSduofiqN1BSd9Voypc9Zb/E2Lbl4O6swd1nkXgRHYvLD16giJ8nnoZHo9yvleGjC031fWs9XYU4rQ4aNZt5lYjJnrXodFC/XC5NxQEaRESUCm9ULABUPI7nkTF4emEfVLERaFe5Cdon6HsXkMNDPpbyf9UMe6vDH9j091w8eh6B95zWyn2/xjbE205b5fMonTMWxDVFblUI2ml2y32X7jxAyQJ5rPwVkjUw2bMW/eoZHKBBRETpGeRRoW6qzy9Qpgb+V+blpM1xsUD0c7z9cvUO7bO7cPHOg/dE/3GdDhjrK/c/2b8E+N+QzPkCyKY4UsBatC9XzxA4QIOIiKxFTOSfYJk2dba8UOk/h1QqvHCOH8RR8PSPtiohZTJmHdbycnCGoOYKGkREZCeeVOonH/PjAR48DbF1cSgTMNmzQc2e4S8qIiIiGwtoPNDwfPeP79u0LJQ5mHXYoGaPU68QEZHdcPHEHU1++bRdzDqEPb1n6xKRhTHZs0nNHgdoEBGR/XDt8ZfhufP3FeQ0LKQcTPZsMRqXU68QEZEdyVmwNE7laSOfuyIKJ6d3tHWRyIKY7Nkg2QuLSjAyl4iIyA6U77fA8LxyyCacXTvdpuUhy2GyZ+Vm3FidGvl83W1dGiIiImNqDSKH3TC8rHhiHCJuHLZpkcgymOxZeYBGHNRQczkaIiKyQ26e2XC/+07D686Pp+PSX2NtWibKOCZ7Vq7Z00INTYJlboiIiOxJniIVsb3en4bXpf+bgTNrf7BpmUjByd6YMWOgUqmMtlKlShmOR0ZGYsCAAciZMye8vLzQvn173L9/H3Zfs8dkj4iI7Fi9eo2xrclGw+uyh0fgxuk9Ni0TKTTZE8qWLYu7d+8att274xdsFoYMGYK///4bf/zxB3bs2IE7d+6gXbt2sEtaraFmj624RERk7+pUrYJt7k0Nrwv+2QKIibBpmUihyZ6TkxP8/f0NW65cueT+Z8+eYd68efj222/RoEEDBAUFYcGCBdi7dy/2798Pex2Nq4WKffaIiChLeFr8LaPX1359tdoGZR12v5TDxYsXkS9fPri5uSE4OBiTJk1CwYIFceTIEcTExKBRo0aGc0UTrzi2b98+1KhRI9lrRkVFyU0vNDRUPorriS299O81eY2YKDi/bMbVxsVl6D5ZWYoxIokxMo8xShnjYx5jZJ6IjZNGjfCPb8FjagG5r/D1P/Hs6WR4ePnaunh2IcbGP0epva9Kp9PZ7TTZGzZsQFhYGEqWLCmbcMeOHYvbt2/j9OnTsvm2V69eRkmbUK1aNdSvXx9fffVVin0BxbUSW7JkCTw8PDLla/GJuIH6/43EQ102/FpsBor4ZMptiIiILM7p9j60fPCT4fWaSgugU3GBAFsLDw9Hly5dZGunj49P1kz2EgsJCUGhQoVk0627u3u6kz1TNXsBAQF49OhRisFKTYa9efNmNG7cGM7Ooh4vgXsn4TyvAe7psuNGj0OoHOCYfxWlGCOSGCPzGKOUMT7mMUZpj9HVn/6HEk+2y2NhOnc4f3YVaie7byBU9M+RyF9E9zZzyV6W+i75+vqiRIkSuHTpkgxsdHS0TADFfj0xGlf07UuJq6ur3BIT3yhLfLNMXkejNvTZc7HQfbIyS8VayRgj8xijlDE+5jFGqY9RiYEr8HR8UWTXPoWXKgJhkwvDa8w9gDNMwFY/R6m9p90P0EhINOlevnwZefPmlQMyxBe5detWw/Hz58/jxo0bsm+f3eFoXCIiysrUGrh/8p/hpZcqEnsntUAWaiB0WHad7H388cdySpVr167JUbZvvvkmNBoN3nrrLWTLlg29e/fG0KFD8e+//8oBG6JZVyR6KQ3OsPVo3Dgd59kjIqKsSQyWfP7mL4bXNaP3QjXWFwi9a9NyUcrsuhn31q1bMrF7/Pgx/Pz8ULt2bTmtingufPfdd1Cr1XIyZdEHr2nTpvjxxx9hz5Mqy6lXmOwREVEW5V2xNa5mP4XA+eVf7fy2FELe3gTfYtVtWTTKisne0qVLzf6FMXPmTLnZPcM8e2JtXFsXhoiIKP0CCxbE1XfPIXBuacM+31+bQPdFiFztiuwL0w5bJHv8RSAioiwusEA+hHXfbLQvKjb+s47sC5M9a9GyGZeIiJTFq0g1RLy1Uj5/oPNlsmenmOzZpGbP1oUhIiKyDLds8f3oc6tCELewla2LQyYw2bPF2ris2SMiIoVQaVwMz3Pc3weMyQZEPbdpmcgYkz0bJHsaVu0REZGSTSqAmOPLbV0KeonJnrXn2YOak40TEZFyRL8wudt5VR+8OLEa4KTLNsdkz0piY2Pkow5quDlz8WgiIlKI3GUQrvLANZ0/DlT40uiQ58ruwFhfRFzabbPiEZM9q9ElqNlzcWLYiYhIIZzd4PzpJXh/dBTV2w0CRj/FpsBPjU5x/7Ulbs7rDsTFV3yQdTHrsBJtHAdoEBGRMjm7eSKnj2f8C7UaTXp8jrVvHDY6J+DmauDLXIhY8zEQ/sQ2BXVQTPasRPdynj2dGKDBZI+IiBTujSrFgZEP8FDnY7Tf/egcYEogtLun26xsjobJnpXoXq6NG6fjAA0iInIQTq7INeYGrg+8hd9LGSd36i2j5DQtsadXAZHPbFZER2DXa+MqiS7u1QoanHqFiIgchVgrt1AubxTq3APXL1RC9t9bwkf3ah4+pz97yMc495zQ9NsB+AbYsLTKxJo9K9FpuTYuERE5tkIlKsLni1u42sF4TV1BE/EYmFYO4ROLQvv0hk3Kp1RM9qxE+7IZl8ulERGRowssWw26EfdxMqAbwuBudMwj+hHU35eXTbzRf/YFnlyxWTmVgsmelQdoiGZcUaVNRETkyFTObqjQ+wd4jbmHu5024oS2SJJzXE4vA6ZXxrlvmiFmWQ+O4k0nJnvWbsZVMeREREQJ5S0djIrjjiHq09vYGDA0yfHSz/fB+dwqOYpX1PjFrf0YePGYq3OkEjMPK9fsMeRERESmubp7oVnvL4Axz3Djg7sYlHM25sS2SHKe5vAc4OsicnWOB7sXI+7qHpuUN6vgaFwrJ3tiBQ0iIiJKWcGcHpj+QWcAnfHfhfPYvGIe6oRvQSX1ZaPzcm/5QD7e0+SFf9zd+J2jnwBqLk2qx2TPSnTal1XN7K9HRESUJqVKlESpz6YgMiYOu64+xr1T29HhVB+jcwyJnjAuh+Gptv18qHMEyjn/xDq+jvg5zGTPSnTa2PhH1uwRERGli5uzBnVK5AZKdATad8TdZxE4efY/PDiwDN1CfjL5HvVf7xieh2pyQOtfAb63twNVegMtvnaIGkAme1ai03GABhERkSXlzeaOvMGVAbHpJuHBqa04fCcSe46dwoSoyfKcaJ0GLqr4rlQ+cU8AkegJh+fFbwk1mwxU6xf/XF8DqICaQCZ71mJYG5fJHhERkcWpVMhdoRFaVABaNHsDwHBceRiGW08jcGXbfDS+Oxv7tGXxP83O5K+x8bP4LbFOvwKhdwAvUavYHHB2Q1bCZM/KNXs6BfyFQERElBUU8fOSW90SnwL4FP8D8PjiAcTsn4std91wKMQL37v8aP5Cy942ernWow0K5fCAr7szml5ZCedjz4HyHYF2P9tlTSCTPatPqqz8vgFERET2Kmfx6kDx6hDpm0zhtBOg1cbh8o0buPQwAn8fvYKR9z5EPlXyEzi/Eb4aCE+089RyuT1wKwxntQoxheoid6fpsAdM9qwk+8m58U/sMOMnIiJyWGo11Go1ihcpiuJFgObVywFoLQ9FRMdh1fHbeHLqH9R5ugpPInU4E5nT8NZOmu3IpQo1ulzuyGvxT85dxZ17nyOfvz9sTTHJ3syZM/H111/j3r17qFixImbMmIFq1arBXkQ6+8I54iFCdZ62LgoRERGlgruLBm9VKwhUE9O8xE/1UitOK6eAcXfWIDwiEkuW/oySBXLgaZQacU5ueBgWhc5nB8JJpcXjkBAme5aybNkyDB06FLNmzUL16tUxbdo0NG3aFOfPn0fu3LlhD+KiI+Tjutiq8dXGRERElOU4a9RyE9xdneHpF4gKDVvA2dnZcE74mKFwQiS0UYnbem1DEUNDv/32W/Tp0we9evVCmTJlZNLn4eGB+fPnw17oouO/4f65stu6KERERJSJVM7u8jG3m32s3Zvla/aio6Nx5MgRDB8+3LBPtL03atQI+/btM/meqKgouemFhsa3t8fExMgtvfTvNXUNTVwkoAJqlMiXoXtkdSnFiOIxRuYxRiljfMxjjMxjjNIfIzcPL+DZU/i5xmZq/FJ7bZVOp7OPtDOd7ty5g/z582Pv3r0IDg427P/kk0+wY8cOHDhwIMl7xowZg7FjxybZv2TJElkjmBmeHP4d7roIxJXuAC8v70y5BxEREdleg7OfwjvqLnYXG47H3qUz7T7h4eHo0qULnj17Bh8fH+XW7KWHqAUUffwS1uwFBASgSZMmKQYrNRn25s2b0bhxY6O2e2Fb0arIl80NpfwdO9FLKUYUjzEyjzFKGeNjHmNkHmOU/hipSrohNi4K1QtUAzxejd61NH3LpDlZPtnLlSsXNBoN7t+/b7RfvPZPZgSMq6ur3BIT3yhL/ECbuk7TcvkyfF0lsVSslYwxMo8xShnjYx5jZB5jlI4YlWoKa0jt9yXLD9BwcXFBUFAQtm7datin1Wrl64TNukRERESOKMvX7AmiSbZHjx6oUqWKnFtPTL3y4sULOTqXiIiIyJEpItnr1KkTHj58iNGjR8tJlStVqoSNGzciT548ti4aERERkU0pItkTBg4cKDciIiIiUlCfPSIiIiJKHpM9IiIiIgVjskdERESkYEz2iIiIiBSMyR4RERGRgilmNG5G6JcHTu2yIyktmyLWqRPX4WzjpjFG5jFG5jFGKWN8zGOMzGOM7D9G+rxFn8ckh8kegOfPn8tHsT4uERERUVbLY7Jly5bscZXOXDroAMTyanfu3IG3tzdUKlWGMmyRMN68eRM+Pj4WLaNSMEbmMUbmMUYpY3zMY4zMY4zsP0YihROJXr58+aBWJ98zjzV7ouOiWo0CBQpY7HriG85fjJQxRuYxRuYxRiljfMxjjMxjjOw7RinV6OlxgAYRERGRgjHZIyIiIlIwJnsW5Orqii+++EI+kmmMkXmMkXmMUcoYH/MYI/MYI+XEiAM0iIiIiBSMNXtERERECsZkj4iIiEjBmOwRERERKRiTPSIiIiIFY7JHREREpGCKTfYmTZqEqlWryiXQcufOjbZt2+L8+fNG5/Tr1w9FixaFu7s7/Pz80KZNG/z3338pXlcMXh49ejTy5s0r39eoUSNcvHjR6JwLFy7Ia+XKlUvOqF27dm38+++/KV53xYoVqFKlCnx9feHp6YlKlSrhl19+SfO9lRyjhJYuXSqXthNlTuu9lRyjhQsXyrgk3Nzc3NJ876wSo6NHj6Jx48by9yZnzpzo27cvwsLCzJb5jz/+QKlSpWRsypcvj/Xr16f53kqO0ZkzZ9C+fXsULlxY/gxNmzbN5HkzZ86U54g4Vq9eHQcPHoSjxGjOnDmoU6cOsmfPLjdx3cRfvyV/jrJafBztM+1oOv8vstpnmk6hmjZtqluwYIHu9OnTuuPHj+tatGihK1iwoC4sLMxwzuzZs3U7duzQXb16VXfkyBFdq1atdAEBAbrY2Nhkrzt58mRdtmzZdKtWrdKdOHFC17p1a11gYKAuIiLCcE7x4sXl/cTxCxcu6N5//32dh4eH7u7du8le999//9WtWLFCd/bsWd2lS5d006ZN02k0Gt3GjRvTdG8lx0hPlCV//vy6OnXq6Nq0aZPmeys5RqKsPj4+8hz9du/ePUXG6Pbt27rs2bPr3nvvPd1///2nO3jwoK5mzZq69u3bp1jePXv2yN+tKVOmyN+3kSNH6pydnXWnTp1ijF4S53388ce633//Xefv76/77rvvkpyzdOlSnYuLi27+/Pm6M2fO6Pr06aPz9fXV3b9/3yFi1KVLF93MmTN1x44d0507d07Xs2dPeZ9bt26l+t5Kjo8jfabdTmeMrPmZpthkL7EHDx6I+QTlNzk5IpDiHPGDaYpWq5X/8X399deGfSEhITpXV1f5n6Lw8OFDeY2dO3cazgkNDZX7Nm/enKYyV65cWX4QpfbejhAj8Qspfonmzp2r69Gjh9EvBmMUn+yJ/xiSo6QYif+0c+fOrYuLizOcc/LkSXndixcvJnvvjh076lq2bGm0r3r16rp+/fql+t5Kj1FChQoVMpnsVatWTTdgwADDa3GPfPny6SZNmqRztBjp/2/y9vbWLVq0KNX3dqT4KPkzbXYGYmStzzTFNuMm9uzZM/mYI0cOk8dfvHiBBQsWIDAwEAEBASbPuXr1Ku7duyerURMuQCyaL/bt2ydfi+rbkiVLYvHixfKasbGxmD17tqxSDgoKMrxPNH2MGTPG5H1EEr5161ZZ/Vy3bt1U39sRYjRu3Dh5Xu/evdN1b0eIkWg6KFSokLy/aKIQTXJpuXdWiVFUVBRcXFygVr/6b0w0cwi7d+9ONkbi/QmvKzRt2tRwXcbIvOjoaBw5csTo3uIe4rWjxig8PBwxMTGG8mb2z1FWio/SP9OiMhAja32mOUSyp9VqMXjwYNSqVQvlypUzOvbjjz/Cy8tLbhs2bMDmzZvlN80UEXQhT548RvvFa/0x0ea+ZcsWHDt2TPYbEH1Zvv32W2zcuFH269ATfQZEX6zEP5iiHOL+LVu2xIwZM2QfgNTeW+kxEr808+bNk31l0ntvpcdIJIjz58/H6tWr8euvv8oy16xZE7du3Ur1vbNKjBo0aCCff/311zL5ePr0KT777DN57O7du8nGSLwnpesyRuY9evQIcXFxjFECn376KfLly2f4YM7Mn6OsEh9H+UxrkM4YWfMzzSGSvQEDBuD06dOyA2RiXbt2lR+oO3bsQIkSJdCxY0dERkam+17iLxhxP5Gp79q1S3bYFR0uW7VqZfRNF3/lDBw40Oi94kP9+PHjOHToECZMmIChQ4di+/btsAZ7j9Hz58/RrVs3+UuRlg8lR4qREBwcjO7du8vO0K+//rrsJC06IYtaQaXFqGzZsli0aBG++eYbeHh4wN/fX/6FLv4jTPgXtqnfNVtijJQXo8mTJ8uyrly5MsmAKEeOj6N8ppVNR4ys/pmmUzjRp6RAgQK6K1eumD03KipKdoBfsmSJyeOXL1+WbfCiQ25CdevW1Q0aNEg+37Jli06tVuuePXtmdE6xYsXS3Jeld+/euiZNmqT63kqOkbieuK7o4KvfVCqV3MRz0d/C0WOUnP/973+6zp07p/reWSVGCYlBKM+fP5cdsUXcli9fnuy9RWfsxH3QRo8eratQoUK67q3EGJnrsyfKJn7vVq5cabS/e/fusgO5I8VI9KcS/WQPHTqUoXsrNT6O8JmWnhhZ+zNNsTV7omZEZNDiL61t27bJLDs17xGbaH83RVxDZOwiO9cLDQ3FgQMHZI2Kvt+GkDCb178W1cppIc7XlyU191ZyjMQ0GadOnZJ/Jeq31q1bo379+vK56G/h6DEyRTS1ibiJYfupvXdWiVFC4i9o0SSzbNkyWbOibyoyRbw/4XUF0Yyjvy5jZJ5o8hJ9RxPeW/xciteOFKMpU6bgyy+/lN0rxDQjGbm3EuPjKJ9p6YmR1T/TdArVv39/+dfW9u3bjaahCA8Pl8dFxjxx4kTd4cOHddevX5fTMYgh2Dly5Ehx6gAxDFpML7B69Wo52kaMnEk4DFqMosyZM6euXbt2cuj3+fPn5RQGYmoH8VqvQYMGuhkzZhhei7Js2rRJlksMVZ86darOyclJN2fOnFTfW+kxSizxyCXGSKcbO3as7p9//pHlEtMKiBo9Nzc3OTWG0mIkiK9dfJ0iPj/88IPO3d1d9/333xtdJ3GMxP3F75b4HRNTZnzxxRcmp15x5BiJ2g5RmyC2vHnzyp898TzhyEIx9YoYFbhw4UL5f1bfvn1lWRJP9aPUGInriqln/vzzT6PyilqdtNxbqfFxpM+09MbImp9pik32RB5rahNTU+jnxWnevLkcLi3+oxdVvmLeJDFHTkrEUOhRo0bp8uTJI/+ja9iwofzmJiSq80VVtfgBEkPxa9SooVu/fn2SphHxIaM3YsQI2UQnPpjFfD3BwcHyP9O03lvJMUrNL4ajx2jw4MFyXinxISSuLeaZOnr0qGJj1K1bNxkf8fWKZtjFixcnuY6pnyPRtFKiRAn5vrJly+rWrVvHGCWIkZj3y1R5X3/9daP3iQ8u/c+bmIpl//79DhMj8dpUeROeY8mfo6wWH0f7TOuWzv+LrPWZphL/pK0ukIiIiIiyCsX22SMiIiIiJntEREREisZkj4iIiEjBmOwRERERKRiTPSIiIiIFY7JHREREpGBM9oiIiIgUjMkeERERkYIx2SMiSmD79u1QqVQICQmxyf3FOpilS5eW6xqbI9ZkrVSpUprX3SYix8Jkj4gcVr169TB48GCjfTVr1sTdu3eRLVs2m5Tpk08+wciRI6HRaMye26xZMzg7O+O3336zStmIKGtiskdElICLiwv8/f1l7Z617d69G5cvX0b79u1T/Z6ePXti+vTpmVouIsramOwRkUMSSdKOHTvw/fffy8RObNeuXUvSjLtw4UL4+vpi7dq1KFmyJDw8PPC///0P4eHhWLRoEQoXLozs2bNj0KBBRk2vUVFR+Pjjj5E/f354enqievXq8topWbp0KRo3bgw3NzfDvhMnTqB+/frw9vaGj48PgoKCcPjwYcPxVq1aydciSSQiMsXJ5F4iIoUTSd6FCxdQrlw5jBs3Tu7z8/OTCV9iIrETtWciGXv+/DnatWuHN998UyaB69evx5UrV2RtXK1atdCpUyf5noEDB+Ls2bPyPfny5cPKlStls+upU6dQvHhxk2XatWsXunTpYrSva9euqFy5Mn766SfZtHv8+HHZdKtXsGBB5MmTR763aNGiFo4SESkBkz0ickiiT55oshU1daLZNiUxMTEy2dInU6Jm75dffsH9+/fh5eWFMmXKyNq3f//9VyZ7N27cwIIFC+SjSPQEUcsnBlSI/RMnTjR5n+vXrxvO1xPXGDZsGEqVKiVfm0oUxXvEe4mITGGyR0RkhkgIE9aaiZo00XwrEr2E+x48eCCfi9o70aRbokQJo+uIpt2cOXMme5+IiAijJlxh6NChePfdd2Vy2ahRI3To0CFJDZ67u7usfSQiMoXJHhGRGQmbTQXRp8/UPv0UKGFhYbLJ9ciRI0lG1SZMEBPLlSsXnj59arRvzJgxsml33bp12LBhA7744gvZNCyakfWePHkim6CJiExhskdEDks046ZmPru0En3sxHVFTV+dOnXS9D7Rzy8xUUMotiFDhuCtt96STcH6ZC8yMlIOzhDvJSIyhaNxichhiabYAwcOyEEZjx49stjkxCIxEwMrunfvjhUrVuDq1as4ePAgJk2aJGvoktO0aVM5/UrCZl0x0EOM4hV98vbs2YNDhw7JSZf19u/fD1dXVwQHB1uk7ESkPEz2iMhhiUEToplVDLAQzaBiMISliNo3kex99NFHcsqWtm3bykRNjJ5NjkgQz5w5g/Pnz8vXomyPHz+W1xEJZMeOHdG8eXOMHTvW8J7ff/9dvk/0KyQiMkWl0+l0Jo8QEZHViZG3oaGhmD17ttlzRW2kSCTFPHuBgYFWKR8RZT2s2SMisiMjRoxAoUKFUtWkLJqff/zxRyZ6RJQi1uwRERERKRhr9oiIiIgUjMkeERERkYIx2SMiIiJSMCZ7RERERArGZI+IiIhIwZjsERERESkYkz0iIiIiBWOyR0RERKRgTPaIiIiIFIzJHhEREZGCMdkjIiIiUjAme0REREQKxmSPiIiISMGY7BEREREpGJM9IiIiIgVjskdERESkYEz2iIiIiBSMyR4RERGRgjHZIyIiIlIwJntERERECsZkj4iIiEjBmOwRERERKRiTPSIiIiIFY7JHREREpGBM9oiIiIgUjMkeERERkYIx2SMiIiJSMCZ7RERERArGZI+IiIhIwZjsERERESkYkz0iIiIiBWOyR0RERKRgTPaIiIiIFIzJHhEREZGCMdkjIiIiUjAme0REREQKxmSPiIiISMGY7BEREREpGJM9IiIiIgVjskdERESkYEz2iIiIiBSMyR4RERGRgjHZIyIiIlIwJntERERECsZkj4iIiEjBmOwRERERKRiTPSIiIiIFY7JHREREpGBM9oiIiIgUjMkeERERkYIx2SMiSodr165BpVJh4cKFti4KEVGKmOwRkUNo3bo1PDw88Pz582TP6dq1K1xcXPD48eN03WP9+vUYM2ZMBkpJRGR5TPaIyCGIRC4iIgIrV640eTw8PByrV69Gs2bNkDNnznQne2PHjs1gSYmILIvJHhE5TM2et7c3lixZYvK4SPRevHghk0IiIiVhskdEDsHd3R3t2rXD1q1b8eDBgyTHRRIokkGRFF65cgUdOnRAjhw5ZNNvjRo1sG7duhSv37NnT8ycOVM+F3359Jve1KlTUbNmTVlrKMoSFBSEP//8M8l1RO3joEGDkCtXLkN5bt++La+VuIlY7H/nnXeQJ08euLq6omzZspg/f34GokRESuRk6wIQEVmLqLVbtGgRli9fjoEDBxr2P3nyBP/88w/eeusthIaGyqRMNOuKpEskZ+I9IukSydmbb75p8tr9+vXDnTt3sHnzZvzyyy9Jjn///ffyGqIM0dHRWLp0qUwo165di5YtWxoljaJ83bp1k0nmjh07jI7r3b9/Xx4XSaD4Wvz8/LBhwwb07t1bfg2DBw+2WNyIKIvTERE5iNjYWF3evHl1wcHBRvtnzZqlE/8d/vPPP7rBgwfL57t27TIcf/78uS4wMFBXuHBhXVxcnNx39epVed6CBQsM5w0YMEDuMyU8PNzodXR0tK5cuXK6Bg0aGPYdOXJEvl+UIaGePXvK/V988YVhX+/eveXX8ujRI6NzO3furMuWLVuS+xGR42IzLhE5DI1Gg86dO2Pfvn1y6pSETbiiKbRhw4ZykEW1atVQu3Ztw3EvLy/07dtXvufs2bPpurdoutV7+vQpnj17hjp16uDo0aOG/Rs3bpSP77//vtF7P/jgA6PXOp0Of/31F1q1aiWfP3r0yLA1bdpUXjvhdYnIsTHZIyKHoh+AoR+ocevWLezatUsmgSIZvH79OkqWLJnkfaVLl5aP4nh6iOZa0ezq5uYm+wKKZteffvpJJmZ64tpqtRqBgYFG7y1WrJjR64cPHyIkJAQ///yzvE7CrVevXvIcU/0Sicgxsc8eETkUMTCiVKlS+P333/H555/LR1E7lpmjcEUyKfrr1a1bFz/++CPy5s0LZ2dnLFiwINnRwSnRarXy8e2330aPHj1MnlOhQoUMl5uIlIHJHhE5HJHYjRo1CidPnpTJVvHixVG1alV5rFChQjh//nyS9/z333+G48lJOPo2IdHkKmr0xCAQMWpWTyR7CYlri0Tu6tWrskx6ly5dMjpP1OCJkbpxcXFo1KhRqr9uInJMbMYlIoejr8UbPXo0jh8/blSr16JFCxw8eFD269MT8++JJtPChQujTJkyyV7X09NTPoom1oRE87BIBEVypif6/61atcroPNHfThC1fwnNmDEjyfXat28vk8jTp08nKYdo5iUi0mPNHhE5HNEnTkyvIiZSFhIme5999pls2m3evLmcekX0rxNTr4jaNpFciT51KTURC+J9InHTDwgRU6d8++23cnWOLl26yP50Yk4+0RdP1C4mfL9I4qZNmyaXbNNPvXLhwoUkNYeTJ0/Gv//+i+rVq6NPnz4yCRVTyIiBGVu2bJHPiYgkWw8HJiKyhZkzZ8rpTKpVq5bk2OXLl3X/+9//dL6+vjo3Nzd5ztq1a43OMTX1ipja5YMPPtD5+fnpVCqV0TQs8+bN0xUvXlzn6uqqK1WqlHyfmEol8X/DL168kFO45MiRQ+fl5aVr27at7vz58/K8yZMnG517//59eW5AQIDO2dlZ5+/vr2vYsKHu559/tmCkiCirU4l/mPcSEdkv0dRcuXJl/Prrr1zOjYjSjH32iIjsiFguLTHRrCuaj8VoXiKitGKfPSIiOzJlyhQcOXIE9evXh5OTk1wCTWxiUueAgABbF4+IsiA24xIR2RGxtu7YsWPlSh1hYWEoWLCgXCd3xIgRMvkjIkorJntERERECsY+e0REREQKxmSPiIiISMHYAeTlOpN37tyRyw8lt9wRERERkT0RPfGeP3+OfPnypTjhO5M9QCZ6HOVGREREWdHNmzdRoECBZI8z2QNkjZ4+WD4+Pum+TkxMDDZt2oQmTZrA2dnZgiVUDsbIPMbIPMYoZYyPeYyReYyR/ccoNDRUVlbp85jkMNlLsN6kSPQymux5eHjIa/AXwzTGyDzGyDzGKGWMj3mMkXmMUdaJkbkuaHYzQEMs6i0KO3jwYMO+e/fuyfml/P394enpiddee00uRJ6QWOxbLB8kAu3r64vevXvLuamIiIiIyE6SvUOHDmH27NmoUKGC0f7u3bvj/PnzWLNmDU6dOoV27dqhY8eOOHbsmOEckeidOXNGTkS6du1a7Ny5U840T0RERER2kOyJWjiRsM2ZMwfZs2c3OrZ371588MEHqFatGooUKYKRI0fK2juxlJBw7tw5bNy4EXPnzkX16tVRu3ZtzJgxA0uXLpWDLoiIiIgcnc377A0YMAAtW7ZEo0aNMH78eKNjNWvWxLJly+RxkeQtX74ckZGRqFevnjy+b98+ub9KlSqG94jriOHHBw4cwJtvvmnynlFRUXJL2MFR3/YutvTSvzcj11A6xsg8xsg8xihljI95jJF5jJH9xyi197Vpsidq4I4ePSqbcU0RyV2nTp2QM2dOuSak6AS5cuVKFCtWzNCnL3fu3EbvEeflyJFDHkvOpEmT5NqTiYkRNeIeGSWalClljJF5jJF5jFHKGB/zGCPzGCP7jVF4eLh9J3timpMPP/xQBsjNzc3kOaNGjUJISAi2bNmCXLlyYdWqVbLP3q5du1C+fPl033v48OEYOnRokqHLYuh0Rkfjiq+ncePGHLmUDMbIPMbIPMYoZYyPeYyReYyR/cdI3zJpt8me6Hf34MEDOcJWLy4uTg6w+OGHH+TADPF4+vRplC1bVh6vWLGiTPRmzpyJWbNmyVG64hoJxcbGyhG64lhyXF1d5ZaY+EZZ4ptlqesoGWNkHmNkHmOUMsbHPMbIPMbIfmOU2nvaLNlr2LChHGGbUK9evVCqVCl8+umnhqrJxMt/aDQaubyZEBwcLGv+ROIYFBQk923btk0eFwM2iIiIiBydzZI9MdtzuXLljPaJufRE/zyxX1SNir55/fr1w9SpU+V+0Yyrn2JFKF26NJo1a4Y+ffrImj7xnoEDB6Jz585ynTi7EvtqQIgiqTQJnr+c3FFl88HeyqbTGcdc/zrh89ReQ74n/o8oQHz/dAkexYM2/nusjU1w/VTeIzPExkCtjYn/vVLpy00GjI95jJF5jFHGY6R2FrVWgKOPxk2panL9+vX47LPP0KpVKzlFi0j+Fi1ahBYtWhjO++2332SCJ2oKRS1g+/btMX36dNidqcWByGe2LoXtDbtu6xIow57pwOZRr163mwOs6BP/vPp7wMllQMRTKJVouGglnpywdUnsE+NjHmNkHmNkgRj12gAUqglbs6tkb/v27UavixcvnmTFjMTEyNslS5ZkcsnIUlT/iVrZlNfwo1RImOgJ+kRPODDL6sUhIiL7ZVfJnqINNu6fmKVd3QUs65qut6pCb8EzMjvw5DLglELH0pjwV82HcTGAxjm+ZlTjEt88HBsJaFzj94vjLh7xTYyx0fHvc3IB1Al+vJ0zPqWO1cTGwDPynvkY2ZtijYDwx8CdlyvcfHYj024VExOLTZs3oUljsfg4/xtLjPExjzEyjzGyQIycPWEP+N2zFrdsUAx333S/VbNjEhqJJ+csWSBlEeldloyRR07jfpqZ+TOviUGsxgNw8xF9PjLvPlkV42MeY2QeY6SYGDHZo7QrGAwEvg7kKg4UbQgsfSvVb9U5eyJWq5OTX6vkAAATojKpb6Nr1ki4ddDJKYRsEqO0ylkcKNEUuLIdaDQmvpPys1tAzQ9sXTIiInqJyR6lnVoD9Fjz6vWY1CcesTExcuCNGGST7PxAYzIpKRueec2KlpSqGNmz9/fZugRERJSA7ccDExEREVGmYbJH9uftvwBPP8tdL6A68M4my12PiIgoC2EzLtnnqM5hl2xdCiIiIkVgzR4RERGRgjHZIyIiIlIwJntERERECsZkj4iIiEjBmOwRERERKRiTPSIiIiIFY7JHREREpGBM9oiIiIgUjMkeERERkYIx2SMiIiJSMCZ7RERERArGZI+IiIhIwZjsERERESkYkz0iIiIiBWOyR0RERKRgTPaIiIiIFIzJHhEREZGCMdkjIiIiUjAme0REREQKxmSPiIiISMGY7BEREREpGJM9IiIiIgVjskdERESkYEz2iIiIiBSMyR4RERGRgjHZIyIiIlIwJntERERECsZkj4iIiEjBmOwRERERKRiTPSIiIiIFY7JHREREpGBM9oiIiIgUjMkeERERkYIx2SMiIiJSMCdbF4CIiIjsS2RMHF7/egfuhzrhw32bYE+8XJ0QFhWbqnN9PZzRpEweNC7jjz6LD6NeST/cexaJ/+49N5zTumI+rDlxRz53d9ZApQLerR2I6dsuoVlZf+T1dYOnixPy+Lhi1OozqF0sF64+eoH2QQWQP5sLroWoMHfWftwLjcLD51EILpJT3qdqYA68VjA77AGTPSIiIgcVp9XhTkgEnDVqhEXFwEmthkatwtxdV3A/NAr2KLWJnhASHoPlh2/JTdh+/mGSc/SJnhAREycfRaInbDxzL8n5uy89ij9n68WXezQAQg3H9115LDfh/PhmcHUSx22LyR4REZGDKvr5elsXQdFO3HyGaoE5bF0MJntERESORKfTIXA4kzxrEE3C9oDJHhERkYMkeaIZc+Sq02bPfa2gL5b1qYb169ejRYsWcHZ2tkoZs5qYmJgsESMme0RERA7guy0XE/Qze+WjxiVQs1hOtP9pH8rm85EDFjpXK2iTMpLCp16ZPHkyVCoVBg8ebLR/3759aNCgATw9PeHj44O6desiIiLCcPzJkyfo2rWrPObr64vevXsjLCzMBl8BERGR/Xn6IhqFP1tnMtHrUr0gPmhYHEGFcuDa5JZYN6gO+r1eFNnc7beWirJozd6hQ4cwe/ZsVKhQIUmi16xZMwwfPhwzZsyAk5MTTpw4AbX6VY4qEr27d+9i8+bNsjq1V69e6Nu3L5YsWWKDr4SIiMg+HLjyGFP+OY8j15+aPC6SO3IMNk/2RC2cSNjmzJmD8ePHGx0bMmQIBg0ahM8++8ywr2TJkobn586dw8aNG2WyWKVKFblPJIWi7Xzq1KnIly+fFb8SIiIi+5mepNPP+00ey+Hpgh3D6lm9TOTAyd6AAQPQsmVLNGrUyCjZe/DgAQ4cOCATwZo1a+Ly5csoVaoUJkyYgNq1axtq/kTTrT7RE8R1RM2feO+bb75p8p5RUVFy0wsNjZ8fR9QMii299O/NyDWUjjEyjzEyjzFKGePjuDGavPE85u25nuzxd2oWwvDmJVP1tSs1RpZk6xil9r42TfaWLl2Ko0ePypq5xK5cuSIfx4wZI2vpKlWqhMWLF6Nhw4Y4ffo0ihcvjnv37iF37txG7xNNvTly5JDHkjNp0iSMHTs2yf5NmzbBw8Mjw1+XaFKmlDFG5jFG5jFGKWN8HCdGRx6psPhi8pP3FvXWYVC5OEB3GevXX3bIGGUmW8UoPDzcvpO9mzdv4sMPP5QBcnNzS3Jcq9XKx379+sl+eELlypWxdetWzJ8/XyZs6SX6AA4dOtSoZi8gIABNmjSRAz0ykmGLr6dx48Z2PQTblhgj8xgj8xijlDE+jhMjsQKG2D4cuyXJseV9quFpRAzyZXNDyTxechCkI8YoM9k6RvqWSbtN9o4cOSKbal977TXDvri4OOzcuRM//PADzp8/L/eVKVPG6H2lS5fGjRs35HN/f395jYRiY2PlCF1xLDmurq5yS0x8oyzxzbLUdZSMMTKPMTKPMUoZ46PsGIkRtskZ1rQkqhX1g6PHyFqcbRSj1N7TZsmeaI49deqU0T5Rgyf65X366acoUqSIHGChT/r0Lly4gObNm8vnwcHBCAkJkYljUFCQ3Ldt2zZZK1i9enUrfjVERETW8d3mC/jexDQqekv71kCNIjmtWiaybzZL9ry9vVGuXDmjfWIuvZw5cxr2Dxs2DF988QUqVqwo++wtWrQI//33H/78809DLZ+YmqVPnz6YNWuWrE4dOHAgOnfuzJG4RESkCNGxWizaew0T1p9L8bwl71aX67A6aexmCl2yEzYfjZsSMcFyZGSknIJFNM2KpE+0jRctWtRwzm+//SYTPFFTKEbhtm/fHtOnT7dpuYmIiCxlxraLmLHtUorndK4agJrFclmtTJS12FWyt3379iT7xBx7CefZS0yMvOUEykREpBRarQ6Vxm1CaGSs2XP3DW+AvNncrVIuyrrsKtkjIiJyROtO3sWAJUdTdW5OTxccGdU408tEysFkj4iIyIp0Oh0iYuIQHh2HyJePqUn0Vg+ohUI5PeDr4WKVcpJyMNkjIiKyok/+PIk/jtxK03vGty2HigG+mVYmUjYme0RERJnsp+2X8dXG/1J9frcahfBlW+MZK4jSi8keERGRBT0Ki8LywzfllCkFsnugYA6PVCV61ya3tEr5yPEw2SMiIrKQqNg4VBmfdOmylOTycsUvvatlWpmImOwRERGlg6i5C/pyM55HmZ8iJbErE1tArU7bWrVE6cVkj4iIKAVPXkTj/L3n2HD6Li7eD4OHiwYHrz2RU6CkJtHbNKQuSuTxtkpZiUxhskdERJSCJt/twKOw6CT7nycz6XH/ekWx59IjuGjU6Fu3CBM9sjkme0RE5PDE3HdVJ2yVgysyQkyP8mmzUhYrF5ElMNkjIiKHcv3xC3SavQ/3Qp3w4b5Ncl/JPN7pSvQ4gpayAiZ7RESkuFq6+EcgVqtDrFYLtUolXwuvf510Hfbz95+neM0V79dEeFQcnDUq5PN1lytgFPPzypwvgMjCmOwREZFiDF9xCr8fvJGu94o+dtFx2iT7365REK8VzG6B0hHZBpM9IiLKEraeu4/eiw4ne7xDUIE0L0OmVy0wB5b3C85A6YjsF5M9IiKyiciYOPkYFavF4WtP4OftisgYrWx2ffoiBjm9XBCn1SGPj6s8L6VETzCX6C15tzoqFYxfXzY6OgZbNm/CGy2aw8nJCU4atcW+LiJ7w2SPiIisLiZOi8rjNstkzlTTaXq1rZQPh649RWhkjNHUKGNbl0XNYrkMr51VOjirAY1axUSPFI/JHhERWcT0rRfx7eYLqTq3iJ+nHOSQWr4ezggJj0nxnK0fvY6iHDRBlASTPSIiMhrJeuT6U7lChJNahScvYhAWFSNXkBA1ZcXzeONOSASqFM4OPy9XFMv9KrlKbaInXHn4ItXn5vB0wdFRjdP8tRBRPCZ7RERksObEHXy49Hiyx/+7Fz9FiUgIM2JY05Kyz56YEuXYzRDsvPAwyTltKuWDj5szPm3OSYqJMoLJHhGRgomJgquM35Kqc4v6eeJyGmrc9Mrm85GPZ+6EpnjeO7UCMX/PVfk4oH6xNN+HiNKHyR4RURYhBh1sOXsfSw/elM2syfm8eUlcuq/Cs0M3MXrNuVRfPz2J3i+9q6FOcb9Unz+6VZk034OIMobJHhFRFvHOgkM4nIrm04kbzgPQYPmV1Cd6wrK+NeTjtccvcOlBGNycNbKfnmhqDYmIlq/FAIhn4dFoUDoPsns4o1BOz3R/PURkHUz2iIhs6MydZ2g5fXeK57iJOULkvHSpn6KkfHYt/P39EaPVYfv5pP3hEhvfthyqF8kpn+sfiUgZmOwREWWyUatO45f91432lc7rg5DwaNx9Fmn2/WlJ8oQPGxRFkYjzaNGiEpydndNcXiJSFiZ7REQZnKpErAARFaNFjFaL2Did3O+sUcnHWK0uSaInnLub8mCGhHZ/Wt/wPDpWi5iX99BBJ6dH8XR1gquTBmGRsfBw1SCbqxrr14umXCIiJntERBlS9ot/EB6d+smB02rGW5VRILtHquejE2JiUp58mIgcC5M9IqJU2n/lMTr/vN9i18vv645VA2rJNWGJiDILkz0iomRcevAc+688gbebEwrn9Exzond1Uotkj6lUKtkELB6JiDITkz0iIhO2n3+AngsOpfv9K96vaTaRY6JHRNbAZI+ICEDDb7ana1LhFuX98WPXoEwpExGRJTDZIyKHtO2/+1i877pc43V489JmE72KAb5YPaCW1cpHRGQpTPaIyOGcvv0M7yw8bHj9+cpTSc4Z1rQkzt4Jxbl7oehZszA6VQ2wcimJiCyDyR4RKZ5Wq0ORz9en6T39Xy8KtZp96ogo62OyR0SK9OB5JKpN2Jrq869Nbpmp5SEishUme0SkKGI6EyE1id5X7ctDo1ajTaV8VigZEZFtMNkjIsX49/wD9ErFdCltK+VDhyoBqFUsl1XKRURkS0z2iCjL2nHhId795Zh8XiavD86msN7spiF1USKPtxVLR0SUBZM9rVaLHTt2YNeuXbh+/TrCw8Ph5+eHypUro1GjRggI4Gg1IspcEdFx0MZpce058N3LRE8wlegdGtEIYt5iHzdnuDiprVxSIiL7kKr//SIiIjB+/HiZzLVo0QIbNmxASEgINBoNLl26hC+++AKBgYHy2P79lls3kogooa3n7qP06I0oO3YLvjud9G/VfnWLGJ4fHNFQrjmby8uViR4RObRU1eyVKFECwcHBmDNnDho3bgxnZ+ck54iaviVLlqBz584YMWIE+vTpkxnlJSIHtPr4bXy49HiK52jUKgxvUVpuRESUxmRv06ZNKF065f9ACxUqhOHDh+Pjjz/GjRs3UnNZIiKTHoRGYtifJ2WfPHNOj20KL1d2PyYiSk6q/ocUid7p06dRrlw5s+eKWr+iRYum5rJEREaiY7UIi4pF4+924llEjMlzdgyrB2jjsGXbv+jUiokeEZE5qf5fskKFCqhatSreffdd2VTr7c1RbURkOaGRMagwZlOK53zarBQK5fRETEwMcrkBrs4aq5WPiEjxyZ4YhbtgwQJ89NFHGDJkCNq3by8Tvzp16mRuCYlIsTaevov3fj1q9ry/+gcjqFAOq5SJiMhhkz2R1IltxowZWL58ORYuXIjXX38dxYoVQ+/evdGjRw/4+/tnbmmJSBFTp/x19Bac1Cp8tuJUsuet/aA2yuXPZtWyEREpUZrnI/D09ESvXr1kTd+FCxfQoUMHzJw5EwULFkTr1q0zp5REpBhi6pSRq06bTPREAihMaV+BiR4RkYVkqGezqNX7/PPPDSNx161bZ6lyEZFCPA6LQusf9uB2SITZc8+Oa8Y58YiILCzd/6vu3LkTPXv2lE23w4YNQ7t27bBnz550F2Ty5MlQqVQYPHiwyYXNmzdvLo+vWrXK6JiY5qVly5bw8PBA7ty5ZVliY2PTXQ4iyjitVoe5u66g/tTtCBq/JcVEr3PVAFyb3FJuTPSIiGxcs3fnzh3ZV09sYuWMmjVrYvr06ejYsaNs3k2vQ4cOYfbs2XLErynTpk2TiV5icXFxMtETCefevXtx9+5ddO/eXU7/MnHixHSXh4gy5qcdl/H1P+dTPKdigWzoUr0g/hfEZRaJiOwi2RM1a1u2bEGuXLlkQvXOO++gZMmSGS5AWFgYunbtKlfnEEuyJXb8+HF88803OHz4MPLmzZtksuezZ8/KcuXJkweVKlXCl19+iU8//RRjxoyBi4uLyXtGRUXJTS80NH5NTTGdg9jSS//ejFxD6Rgj5cao/LgtiIzRmj2vemB2zO32GtxeTpuijYsV0+Y5RIyshfExjzEyjzGy/xil9r4qnWgjTQUx+EKMun3jjTfkmriWIkbx5siRA9999x3q1asnEzZRkyeEh4ejSpUqmDRpEtq0aSNr91auXIm2bdvK46NHj8aaNWtkQqh39epVFClSBEePHkXlypVN3lMkgmPHjk2yXyz3JpqDiShlUXHAuhtq7LiX+mbXPqXiUC57qv67ISKiVBB5UpcuXfDs2TP4+PhkvGZPJFWWtnTpUpmUiWZcU8R8fqKpWCR6pty7d0/W6CWkfy2OJUcMJhk6dKhRzV5AQACaNGmSYrBSk2Fv3rw52fWDiTHK6jHS/204ePlJ7Lh33+z533Uoj0cvolE5wFc22zpCjOwB42MeY2QeY2T/MdK3TJqTqmTvvffew8iRI1GgQAGz5y5btkwOkBBNsym5efMmPvzwQxkkNzc3k8nltm3bcOzYMViaq6ur3BIT3yhLfLMsdR0lY4yyXowKf5a20fabhtRFiTzeDhUje8P4mMcYmccY2W+MUnvPVCV7fn5+KFu2LGrVqoVWrVrJptV8+fLJJO3p06ey39zu3btlTZ3Y//PPP5u95pEjR/DgwQO89tprRgMuxCjfH374Af3798fly5fh6+tr9D6xcoeY3Hn79u1yYMbBgweNjt+/H1/bwAmeidIuKjYOJUduTLK/Q5D5P/SE2d2C0LQsf/eIiOxJqpI9Mehh4MCBmDt3Ln788UeZ3CUk1slt1KiRTPKaNWuWqhs3bNgQp04ZT6oqJmsuVaqUHGAhBoL069fP6Hj58uVl3z6RcArBwcGYMGGCTBrFtCuCqCkUTbFlypRJVTmIHHn+O41ahYfPo+TAijidDqNXnzZ57h9Hbpncf3RUYzhpVHBz0kDMh+yk4dQpRET2JtV99kRfuBEjRshN1OaJ+e0iIiJkUla0aFGTU6OkRCSI5cqVM9onpm/JmTOnYb+p2jmxUkdgYKB8LvrYiaSuW7dumDJliuynJ5qbBwwYYLKZlojibTpzD31/OZKm95TN54Mzd171D1n5fk3k8DQ94p2IiLL4ChrZs2eXm62JUcFr166VTb6ilk8ki2J077hx42xdNCK7ERunRa+Fh7Dr4qN0X+PbjhXR7rXUNeUSEZGClkuzNNEPLyWmZokRS7WtX78+E0tFlHVWrVh36i7uhETg6qMXKJbbC4/ConH98Ys0J3rv1yuKT5qVyrSyEhGRgyZ7RJR+I1adxu8Hb6T7/Xl8XPG/oALwcHFCv7pFLFo2IiKyHSZ7RFnQs4gYVBy7Kd3vz5vNDXl83ODr4YzvOlZCdva9IyJSLCZ7RHbu7rMInLj5DJM3nMO1x+Fyn5MY+poGez5rgPy+7plUQiIiUlyyJyZNFv3rxDx4YpkOMbL2zp07csoTLy8vy5eSyIEFT9qWZF+s1vSyY3WK55K1duJ420r58TQ8GqXz+jDRIyJyYGlO9q5fvy7n0hNTr0RFRcklQkSy99VXX8nXs2bNypySEjmIXZce4cN9TvhwX9qbaX/pXT1TykRERA6U7IklzsQKGidOnJBz4um9+eab6NOnj6XLR+QQfjtwHSNWnk77+96tjlrFcmVKmYiIyEGTvV27dmHv3r1wcTHu0F24cGHcvn3bkmUjUqw4rQ5anQ5RsVo5D15qEr1lfWvA3UUDHzdnFM7lieeRMfB243qVRERk4WRPq9XKNWwTu3XrlmzOJaKUhUXFotwX/6T6/Lol/PBV+/LIm8243x0TPSIiypRkTyxRNm3aNLkOriCWSQsLC8MXX3yBFi1apPVyRA5Tk1f089RP/v11tVi0bdUCzs5M6IiIyMrJ3jfffIOmTZvKNWkjIyPlaNyLFy/KNXJ///33DBaHSBmJ3f3QSIRHx+Lus0iooMK+KymvYCFG0S7oWRVqlQqxsTHYsGGD1cpLRETKluZkr0CBAnJwxtKlS3Hy5ElZq9e7d2907doV7u6c3oGoy5z9OHD1SZqaaX/uFgQnjdpQW05ERGTTefacnJzw9ttvW6wQRFndmTvP0HL67lSfP7d7FTQqkydTy0RERJSuZG/NmjUm94vaCDc3NxQrVgyBgYGMLine6dvP8NHyE3itUHaza9KKZcmOj25itbIRERGlO9lr27atTOx0OuMZ/PX7xGPt2rWxatUqZM+ePa2XJ8oSHj6Pwhsz4mvyzt9/bvKcRqVzy6lVahTJiR41C1u5hERERPHiOwmlwebNm1G1alX5+OzZM7mJ59WrV8fatWuxc+dOPH78GB9//HFaL01k156+iEbhz9bJreqELSme27lqAOb2qCpXtBhQvxi8XLkMNRERZaEVNMS0KzVr1jTsa9iwoWzC7du3L86cOSOnZnnnnXcsXVYiq7vyMAyf/HkSh68/NXvugc8bIo+Pm1XKRURElGnJ3uXLl+Hj45Nkv9h35coV+bx48eJ49CjlqSaIsoIG3+xI8Xi1wBz4qHEJ5PJ2ZaJHRETKSPaCgoIwbNgwLF68GH5+fnLfw4cP8cknn8jmXUHMuxcQEGD50hJZgWimTY282dywvF9wppeHiIjIqsnevHnz0KZNGznfnj6hu3nzJooUKYLVq1fL12LuvZEjR2aoYETWEhOnxcx/L2Halotmz53dLQhNy/pbpVxEREQ2SfZKliyJs2fPYtOmTbhw4YJhX+PGjaFWqw0jdonsXVRsHEIjYmWit3DvtWTPW/JudRTN7SUHWXhyoAUREWUx6frkEklds2bN5EaUFX27+QKmbzVfk9e/XlHULJbLKmUiIiKym2TvxYsX2LFjB27cuIHo6GijY4MGDbJU2YgsKjImDqVGbUzVuZcntoBGzWXLiIjIAZO9Y8eOoUWLFggPD5dJX44cOeTIWw8PD+TOnZvJHtmdZYdu4NO/TqV4TpMyefBz9ypWKxMREZHdJntDhgxBq1atMGvWLGTLlg379++Hs7OzXCtXzMFHZE998u6GRCab6JXO64Mp7SvAz9sV/tk4bQoRESlTmpO948ePY/bs2bLfnkajQVRUlByJO2XKFPTo0QPt2rXLnJISpcHfJ+7gg9+PpXjOmoG14KxJ8yIyREREyk72RC2eftStaLYV/fZKly4ta/nEFCxEtnLx/nM0/m5niuf0CC6EsW3KWa1MREREWS7Zq1y5Mg4dOiRXyXj99dcxevRo2Wfvl19+Qbly/BAl2zh9+xnemLE7xQEXYryFSsVBF0RE5FjS3IY1ceJE5M2bVz6fMGECsmfPjv79+8tVNETzLpG1/bL/eoqJ3l/9g+XIWiZ6RETkiNJcs1elyqsRi6IZd+PG1E1lQWRpF+4/R5MUmm2ndaqEtpXzW7VMREREWb5mr0GDBggJCUmyPzQ0VB4jsobfDlxPNtHb81kDXJvckokeERFRepK97du3J5lIWYiMjMSuXbssVS6iZO299AgjVp42eWx+zyrI7+tu9TIRERFl+WbckydPGp6LtXHv3btneB0XFyebc/PnZ00KZZ6Q8GhUGrfZ5DGueEFERJTBZK9SpUqyg7vYTDXXuru7Y8aMGam9HFGajF59Gov3XTd5TDTZEhERUQaTvatXr0Kn08kJlA8ePAg/Pz/DMRcXFzlYQ0yyTGRpp249M5noda1eEKPeKGOTMhERESku2StUqJB81Gq1mVkeIiO3noaj1Q9Jp1WpHpgDE94sb5MyERERKS7ZW7NmTaov2Lp164yUh8jgx+2XMGXj+ST72WxLRERk4WSvbdu2qbqY6M8nBmsQZdSziJgkid6f7wUjqFB2m5WJiIgoK0pVssemW7Km+6GRqD5xq9G+RqXzoErhHDYrExERkcOsoEGU2XPodZl7wGjfV+3Lo1PVgjYrExERkUNNqizs2LEDrVq1QrFixeQm+ulxQmXKqJg4bZJEL5eXKxM9IiIiayZ7v/76Kxo1agQPDw8MGjRIbmKOvYYNG2LJkiUZKQs5uOIjNhi99nTRYOvQ121WHiIiIodsxp0wYQKmTJmCIUOGGPaJhO/bb7/Fl19+iS5duli6jOQAJm/4L8m+02ObykE/REREZMWavStXrsgm3MREU66YeJkorR4+j8KsHZeTTK/CRI+IiMgGyV5AQAC2bjUeKSls2bJFHiNKq6oTthi9/u/LZjYrCxERERy9Gfejjz6SzbbHjx9HzZo15b49e/Zg4cKF+P777zOjjKRghT9bZ/R6fNtycHPmsntEREQ2S/b69+8Pf39/fPPNN1i+fLncV7p0aSxbtgxt2rSxWMFI+bRandHrYU1L4u0a8cvyERERkQ2nXnnzzTexe/duPH78WG7ieUYTvcmTJ8s+WoMHD5avnzx5gg8++AAlS5aUo30LFiwoaxSfPXtm9L4bN26gZcuWcnRw7ty5MWzYMMTGxmaoLGQdey8/Nno9oH4xm5WFiIhIqdKc7L377rvYvn27RQtx6NAhzJ49GxUqVDDsu3PnjtymTp2K06dPy2bijRs3onfv3oZzxNJsItGLjo7G3r17sWjRInne6NGjLVo+yhzj1p4xPD/weUObloWIiEip0pzsPXz4EM2aNZODMUQtmui7lxFhYWHo2rUr5syZg+zZX617Wq5cOfz1119y5G/RokXRoEEDOe3L33//bai527RpE86ePSvn/qtUqRKaN28up3+ZOXOmTADJvl24HyYfe9UqjDw+brYuDhERkSKluc/e6tWr8fTpU/zxxx9yEmUxv16pUqVkwibm2CtcuHCarjdgwABZOycmah4/fnyK54omXB8fHzg5xRd73759KF++PPLkyWM4p2nTprJf4ZkzZ1C5cmWT14mKipKbXmhoqHyMiYmRW3rp35uRayidPjZXH8THXOhdsyBjlgB/jsxjjFLG+JjHGJnHGNl/jFJ7X5VOpzPuJZ9Gt27dwu+//4758+fj4sWLaeovt3TpUllbJ5px3dzcUK9ePVlDN23atCTnPnr0CEFBQXj77bfle4S+ffvi+vXr+OeffwznhYeHw9PTE+vXr5c1faaMGTMGY8eOTbJfJK+i7x9lvuOPVVhwIX7U7ffB7GNJRESUViLnERVt+sowi9XsJc4oDx8+jAMHDuDatWtGNWzm3Lx5Ex9++CE2b94sE72UiJo3UftXpkwZmahl1PDhwzF06FCj64tm6SZNmqQYrNTEQ3w9jRs3hrOzc4bLqUT6GJUsUw64cA4eLhq0aNHE1sWyK/w5Mo8xShnjYx5jZB5jZP8x0rdMmpOuZO/ff/+VtWCiT51Wq0W7du2wdu1a2a8utY4cOYIHDx7gtddeMxpwsXPnTvzwww+ymVWj0eD58+eyj6C3tzdWrlxpFEwxBczBgweNrnv//n3DseS4urrKLTFxbUt8syx1HSWLjI2vUH69hB9jlQz+HJnHGKWM8TGPMTKPMbLfGKX2nmlO9vLnzy+nRREJ2M8//ywHUJhKnMxp2LAhTp06ZbSvV69esv/fp59+KhM9kbGKPnji+mvWrElSAxgcHCybdEXSKKZdEUSGLWrnRC0g2a+wqDj56OmaocplIiIiMiPNn7SiGbVDhw7w9fVFRoiaOjHiNiHR1y5nzpxyv0j0RLOqaI8Wo23Fa311pZ+fn0wGxXGR1HXr1g1TpkzBvXv3MHLkSDnoIz0JKFnP8Zsh8tGLyR4REVGmSvMnbZ8+fWANR48elX0BhWLFjCfbvXr1qhz1KxI+0XwsRt+KWj6RLPbo0QPjxo2zShkp/bzd4n/0nkVwlBcREVFmsqtqlYSTNYuRuakZKFyoUCE58paylvDo+GbcSgEZqyEmIiKiTFgujSijXrxM9tiMS0RElLmY7JFNa/Y4QIOIiChzMdkjm3gRFT+Rsqdr/MTKRERElDmY7JGNkz3W7BEREWUmJntkE+yzR0REZB1M9sgmnobHT7nCmj0iIqLMxWSPrO7l4hmSz8v59oiIiChzMNkjq4uI764nebtxvUUiIqLMxGSPrC5KG//IWj0iIqLMx2SPrO7l2Az21yMiIrICJntksz577i6cY4+IiCizMdkjq4vSquSjpwtr9oiIiDIbkz2yqpg4LXbcjU/2WLNHRESU+ZjskVW9NfcQzj+L/7GLikkwBwsRERFlCiZ7ZDVPXkTjxK1nhtcFc3ratDxERESOgMkeWYVOp0ODb7Yb7fuseSmblYeIiMhRMNkjq/hy7TmEvFwiTZj9dmXk93W3aZmIiIgcAYdDUqYb9PsxrDlxx/A6KJcWDUr62bRMREREjoI1e5SpDl17YpToCd2Lv1xCg4iIiDIdkz3KVB1m7TM8z+bujAvjGtu0PERERI6GyR5lmm7zDhi93v5xPahU8XPsERERkXUw2aNMMWbNGey6+MhoX3ZPF5uVh4iIyFEx2SOLexQWhYV7rxlel87rg2uTW9q0TERERI6KyR5ZVGRMHKqM32K076/+wTYrDxERkaNjskcWVWrURqPXs95+DR4unOGHiIjIVpjskcUcvfHU6PVX7cujWbm8NisPERERMdkjC2r3416j1x2rBNisLERERBSPyR5l2MlbISj82TqjfcdGNeY0K0RERHaAyR5lyMPnUWj9wx6jfZuH1OU0K0RERHaCyR5lSNUJxiNvR71RBsXzeNusPERERGSMyR6l26Yz95Lse6dWYZuUhYiIiEzjnBiULgOWHMW6k3eN9nHiZCIiIvvDmj1Ks+WHbjLRIyIiyiJYs0dpknjUrbDy/Zo2KQsRERGZx2SPUkWr1aHu1/8m2f9X/5qoXDC7TcpERERE5jHZI7Ni47ToNu8gbj2NMNq/9aPXUdTPy2blIiIiIvOY7JFZxUZsSLJvZMvSTPSIiIiyACZ7lERMnBbFTSR4CZtugwqx6ZaIiCgrYLJH0s0n4eg4ex/uPotEoZweyZ63ZejrKJabNXpERERZBZM9kupMeTX44vrj8CTHu9UohP8FFWCiR0RElMUw2XPgmryECV5KOBCDiIgo62Ky50BCI2NQ/+vtePwi2uy5nCSZiIhIGZjsOcC0KVodEBUbhzem7zab6P09sDbK5fexWvmIiIgoczHZU7D7oZGoPnFrqs9f8m51lC+QLVPLRERERNbFZE9hWk7fhTN3QlN17u99aiC4aM5MLxMRERHZDpO9LOzKwzAMWX4CJ26GyNdi7ruUEr2qhbPj13ery+fOajXUapXVykpERES2wWQvi9HpdNDp4p83+GaH0bEj158m+778vu6Y17MqXJ00mV1EIiIisiNq2InJkydDpVJh8ODBhn2RkZEYMGAAcubMCS8vL7Rv3x737983et+NGzfQsmVLeHh4IHfu3Bg2bBhiY2OhRPN3X0Xg8PUo8nn8Zs6CnlXlqFqx7fmsAXzcnK1STiIiIrIfdlGzd+jQIcyePRsVKlQw2j9kyBCsW7cOf/zxB7Jly4aBAweiXbt22LNnjzweFxcnEz1/f3/s3bsXd+/eRffu3eHs7IyJEyciq3r4PArLD99E2Xw+yOXlir+O3sL6U3dxPzTK7Hs5ZQoRERHZVbIXFhaGrl27Ys6cORg/frxh/7NnzzBv3jwsWbIEDRo0kPsWLFiA0qVLY//+/ahRowY2bdqEs2fP4v/s3Qd4U1UbB/B/9x6stpS9994bZIMMQUFRwAVuRMSBIlMEceFARVRABQEV8JMpe+8le5a9R2npHvme95SEpE2btrQZN//f81yS3NzcezhJm7fvWatWrUJoaChq166N8ePH45133sGYMWPg6ekJR9RgwqocHb/ng/bwdHeFv5fN304iIiKyMzaPDqSZVrJz7dq1Mwn2du/ejaSkJLVfr3LlyihZsiS2bt2qgj25rVGjhgr09Dp27IiXXnoJhw4dQp06dcxeMyEhQW16UVFpgxrkerLllv61D3KOnHq+eWkEeMpAC51Vr+tIdeRoWEeWsY6yxvqxjHWU+zqSvuPSsiab3HdmycnJcHd3V4kruc1L0rXNzc1NbXLfnOx+fm0a7M2dOxd79uxRzbjpXblyRWXmgoODTfZLYCfP6Y8xDvT0z+ufy8zEiRMxduzYDPslUyh9/x7UypUrc/3adZflDbU8iOLlKimoFKwDUk5i6dKTcDQPUkfOgnVkGesoa6wfy1hHOasjV1dX9b3s4+OTaQDibMLCwnD69Ol8ObcE07Gxsaq1MzU1NcPz8pxdB3vnz5/H66+/rj5E3t7eVr32iBEjMGzYMJPMXokSJdChQwcEBuZ+9QiJsOX/0759e9VvMKfO3IzB61PS+iNm5tNHa6ByqD8qhQXAET1oHTkD1pFlrKOssX4sYx3lvI4k2IiIiFCZpiJFiqh9zh7w6XQ6xMTEwM/PL8/rQs4t78H169fVANQyZcqoYNuYvmXSboM9aaa9du0a6tata9gnKeENGzbgm2++wYoVK5CYmIjIyEiT7J6MxpUoWsjtjh07TM6rH62rP8YcLy8vtaUnH9y8+KHPzXlu3k1AewuB3g/966FDtcz/X44kr+pay1hHlrGOssb6sYx1lP06khkyJAApVqxYnrSCaUFqaqoKyCTTmT4QyyvSynn27FlV9+k/q9n97Nps6pW2bdviwIED2Ldvn2GrX7++Gqyhvy//idWr7y/3dezYMTXVSpMmTdRjuZVzSNCoJ3+FSHauatWqcBRv/7kf9T5cZXHNWq0EekRE5LjyK6ih/Ktvm2X2AgICUL16dZN9kgaVOfX0+5977jnV3FqwYEEVwL322msqwJPBGUKaXSWo69+/PyZPnqz66Y0cOVIN+jCXubPX9Wvn77qQ6fP7R3eAl7srvD04GTIRERE54GjcrHzxxRcqopXJlGX0rIy0/fbbbw3PS7+BxYsXq9G3EgRKsDhw4ECMGzcOjuBiZByaTVqT6fMb326DIB82LxAREZFGgr1169aZPJaBG1OnTlVbZkqVKoWlSy2vJmGPsgr0Tk7oDHc3psqJiIi06syZM2rgxd69e9VcwfmF0YSNxCaaX9LthZZl1SoYDPSIiIjsw5gxY0yCMRlAWrhwYXz88cdmj5cFHmQqOHuZx5ERhY1UHbUiw74XW5XDG+0r2qQ8RERElP0Rsk899RRmzpyZ4TkZNSv79cu32gMGezbQ6pO1Gfb9NLA+3u1cmQMxiIjIYahJfxOTbbLlZPWOhIQEDBkyRM1XJ13EmjdvbljQQQKz9As4LFq0yDBvnjwvCzHs379f7ZNN9skg0uPHj6vVvIytX79eTbIsz8vULDKOoHjx4mrgqGQHly9fDqfus+cszt40nfFamm2JiIgcTVxSitmWKms4PK4jfD2zF8a8/fbb+OuvvzBr1izV119m8JBBnydPWl6Bqm/fvjh48KAK0latSpsmLSgoSM2t16BBA8yePVudS2/GjBlo2rSpWuJVBpp+9tlnmDZtmlrC9eeff0b37t3Vkq4VKlSAtTCzZ2WJyabLnawb3tpmZSEiItK6mJgYfPfdd/jkk0/QuXNnNWXb9OnTVbD2008/WXy9HOfv76/WvpUFG2STfeKZZ57B33//rdbGFdHR0fjzzz/x7LPPqseffvop3nnnHTz++OOoVKmS6uMn2b0pU6bAmpjZs6I528/hvYUHTPaVLuxns/IQERE9CB8PN5Vhs9W1s+PUqVNqoESzZs0M+6QvXcOGDXHkyBG19FtuPfHEE3jzzTcxf/58PP/885g3b56aMk6ygbKU2aVLl0yuK+SxNAlbE4M9K0of6I3tXs1mZSEiInpQ0n8tu02p9srV1TVD/7/sjqKVBR+kWVaahyXYkybcPn36qExgdtettQY249rQwKalbV0EIiIiTStXrpwaPbt582aTYE4GaEiTrmT2pPlVmnv1ZNlWY/L6lJQUs+eXVbw2bdqkFnnYsmWLGpihDwTDw8NNrivksbWXdHXscNyBbRvR1tZFICIi0jw/Pz+10tZbb72lll8tWbKkGqARGxurAjPJ6vn6+uK9995TI3a3b9+eYUqV0qVLIyIiQgWBMrJWlnzVL8sqgzHKly+vplqRQRnyWE+uOXr0aBVwSl89yfzJOWRQhzUxs2cjYUHeti4CERGRU5g0aZJaelWycHXr1lWjcFesWIECBQqoAPC3335Tq3HVqFEDv//+u5pE2Zi8tlOnTmjTpo3KBMoxxk3ZMlDj9u3bhoEZehI8Dhs2TPXrk3PLiN7//e9/Vh2JK5jZIyIiIk3z9vbGV199pTZzevbsqTZjgwYNMtyXLJ6Mss3Mu+++qzKD5voDSmZPNnMkY5iT+QJzi5k9IiIiIg1jsEdERESkYQz2bKBCiL+ti0BEREROgsGeDYK8N9pXtHVRiIiIyEkw2LMiL4+06vbxzN6s30REREQPisGeFSUlp4248XRjtRMREZF1MOqwoqSUVHXrwWCPiIiIrIRRhxWdvpG2FIuHm4uti0JEREROgsGelSQmp2X1xOU78TYtCxERETkPBntWkpx6P9gLDeRSaURERM7KxcUFixYtstr1GOxZeXCGqFEsyKZlISIiciatW7fG0KFDH/g8sr7tiy++aPa5X3/9VS2rduPGDdgbBntWkmSU2WOfPSIiIseSmJiI5557DnPnzkVcXFyG52fMmIHu3bujcOHCsDcM9qwkOSUts+fu6qLSt0RERA5PpwMSY2yzybWz4emnn8b69evx5Zdfqu9f2U6dOqUCtzJlysDHxweVKlVSz6d/Xc+ePTFhwgSEh4erY5566ikV6P31118mx0ZERGDdunXqnOK7775DuXLl4OnpqV4nWT9bcrfp1Z1w2hV3ZvWIiEgrkmKBj8Jtc+33LgGefhYP+/LLL3H8+HFUr14d48aNU/sKFCiA4sWL448//kChQoWwZcsWDB48GEWLFkWfPn0Mr129ejUCAwOxcuVK9Viydj169MDPP/+sAj+9WbNmqfN16NABCxcuxOuvv44pU6agXbt2WLx4MZ555hn1fJs2bWALDPasJPFesBefdL85l4iIiPJXUFCQyrD5+voiLCzMsH/s2LGG+5Lh27p1K+bPn28S7Pn5+eHHH39Ur9eT7F3nzp1VNq9UqVLQ6XT45ZdfMHDgQLi6uuLTTz9VWcGXX35ZHT9s2DBs27ZN7Wewp3H/7L9k6yIQERHlLQ/ftAybra79AKZOnaoydOfOnVNNs9Inr3bt2hkGZBgHeqJ9+/YqSyd99MaMGaOaiOUckr0TR44cUVlCY82aNcvQTGxNDPasZPvpW7YuAhERUd6SPujZaEq1N3PnzsXw4cPx2WefoUmTJggICMAnn3yC7du3mxwnmb30JHsnmTtpuh01ahRmz56tMnZly5aFveIADSvx8mBVExER2YKnpydSUlIMjzdv3oymTZuqptY6deqgfPnyatBGdkkW7/z581iwYAGWLFliyOqJKlWqqPMbk8dVq1aFrTCzZyXrjl23dRGIiIicUunSpVXW7syZM/D390eFChVUP7sVK1ao/noyWnbnzp3qfnbIcQ899JCac08CyV69ehmee+utt1S/PwkiZYDGP//8o4LCVatWwVaYbiIiIiJNGz58ONzc3FR2rUiRIujYsaMK0Pr27YtGjRrh5s2bhgEV2SUDNW7fvo1HH30U3t73V8aS6Vqkf54MyKhWrRqmTZum+vfJxM62wsweERERaVrFihXVaFtjEoDJZmzixImG+zNnzszynE888YQKFqOiojI899JLL6ktMzKC15qY2bOyrjWK2roIRERE5ESY2bOS8T2rY+3Ra5jwSHVbF4WIiIicCIM9K+nfuJTaiIiIiKyJzbhEREREGsZgj4iIiLLN2oMLnJ0uD+qbwR4RERFZ5OHhoW5jY2NtXRSnEnuvvvX1nxvss0dEREQWyTx1wcHBuHbtmnrs6+sLF1kuzYmlpqaqNXXj4+PVMmp5ndGTQE/qW+pd6j+3GOwRERFRtoSFhalbfcDn7HQ6HeLi4uDj45Nvga8Eevp6zy0Ge0RERJQtEtAULVoUISEhSEpKgrNLSkrChg0b0LJlywdqZs2MnPNBMnp6DPaIiIgoRyQAyYsgxNG5ubkhOTlZLZeWH8FeXuEADSIiIiINY7BHREREpGEM9oiIiIg0jH32jCYsjIqKeuCOmjJMWs5jz233tsQ6sox1ZBnrKGusH8tYR5axjuy/jvRxi6WJlxnsAYiOjla3JUqUsHVRiIiIiHIcxwQFBWX6vIuO656oSREvXbqEgICAB5onRyJsCRjPnz+PwMDAPC2jVrCOLGMdWcY6yhrrxzLWkWWsI/uvIwnhJNALDw/PclJnZvak46KrK4oXL55n55M3nD8YWWMdWcY6sox1lDXWj2WsI8tYR/ZdR1ll9PQ4QIOIiIhIwxjsEREREWkYg7085OXlhdGjR6tbMo91ZBnryDLWUdZYP5axjixjHWmnjjhAg4iIiEjDmNkjIiIi0jAGe0REREQaxmCPiIiISMMY7BERERFpmGaDvYkTJ6JBgwZqVYyQkBD07NkTx44dMznmhRdeQLly5eDj44MiRYqgR48eOHr0aJbnlfEso0aNQtGiRdXr2rVrhxMnTpgcc/z4cXWuwoULq0kWmzdvjrVr12Z53gULFqB+/foIDg6Gn58fateujV9//TXH19ZyHRmbO3euWu1EypzTa2u5jmbOnKnqxXjz9vbO8bUdpY727NmD9u3bq5+bQoUKYfDgwbh7967FMv/xxx+oXLmyqpsaNWpg6dKlOb62luvo0KFD6N27N0qXLq0+Q1OmTDF73NSpU9UxUo+NGjXCjh074Cx1NH36dLRo0QIFChRQm5w3/f8/Lz9HjlY/zvadtieXv4us9p2m06iOHTvqZsyYoTt48KBu3759ui5duuhKliypu3v3ruGYadOm6davX6+LiIjQ7d69W9etWzddiRIldMnJyZmed9KkSbqgoCDdokWLdPv379d1795dV6ZMGV1cXJzhmAoVKqjryfPHjx/XvfzyyzpfX1/d5cuXMz3v2rVrdQsWLNAdPnxYd/LkSd2UKVN0bm5uuuXLl+fo2lquIz0pS7FixXQtWrTQ9ejRI8fX1nIdSVkDAwPVMfrtypUrmqyjixcv6goUKKB78cUXdUePHtXt2LFD17RpU13v3r2zLO/mzZvVz9bkyZPVz9vIkSN1Hh4eugMHDrCO7pHjhg8frvv99991YWFhui+++CLDMXPnztV5enrqfv75Z92hQ4d0gwYN0gUHB+uuXr3qFHXUr18/3dSpU3V79+7VHTlyRPf000+r61y4cCHb19Zy/TjTd9rFXNaRNb/TNBvspXft2jWZYka9yZmRipRj5INpTmpqqvrF98knnxj2RUZG6ry8vNQvRXH9+nV1jg0bNhiOiYqKUvtWrlyZozLXqVNHfRFl99rOUEfyAyk/RD/++KNu4MCBJj8YrKO0YE9+MWRGS3Ukv7RDQkJ0KSkphmP+++8/dd4TJ05keu0+ffrounbtarKvUaNGuhdeeCHb19Z6HRkrVaqU2WCvYcOGuldeecXwWK4RHh6umzhxos7Z6kj/uykgIEA3a9asbF/bmepHy99p0x6gjqz1nabZZtz07ty5o24LFixo9vmYmBjMmDEDZcqUUYsamxMREYErV66oNKrxmnTSfLF161b1WNK3lSpVwi+//KLOmZycjGnTpqmUcr169Qyvk6aPMWPGmL2OBOGrV69W6eeWLVtm+9rOUEfjxo1Txz333HO5urYz1JE0HZQqVUpdX5oopEkuJ9d2lDpKSEiAp6enyeLf0swhNm3alGkdyeuNzys6duxoOC/ryLLExETs3r3b5NpyDXnsrHUUGxuLpKQkQ3nz+3PkSPWj9e+0hAeoI2t9pzlFsJeamoqhQ4eiWbNmqF69uslz3377Lfz9/dW2bNkyrFy5Ur1p5kili9DQUJP98lj/nLS5r1q1Cnv37lX9BqQvy+eff47ly5erfh160mdA+mKl/2BKOeT6Xbt2xddff636AGT32lqvI/mh+emnn1RfmdxeW+t1JAHizz//jL///hu//fabKnPTpk1x4cKFbF/bUerooYceUvc/+eQTFXzcvn0b7777rnru8uXLmdaRvCar87KOLLtx4wZSUlJYR0beeecdhIeHG76Y8/Nz5Cj14yzfaQ/lso6s+Z3mFMHeK6+8goMHD6oOkOk9+eST6gt1/fr1qFixIvr06YP4+PhcX0v+gpHrSaS+ceNG1WFXOlx269bN5E2Xv3JeffVVk9fKl/q+ffuwc+dOTJgwAcOGDcO6detgDfZeR9HR0ejfv7/6ocjJl5Iz1ZFo0qQJBgwYoDpDt2rVSnWSlk7IkhXUWh1Vq1YNs2bNwmeffQZfX1+EhYWpv9DlF6HxX9jmftZsiXWkvTqaNGmSKuvChQszDIhy5vpxlu+0armoI6t/p+k0TvqUFC9eXHf69GmLxyYkJKgO8HPmzDH7/KlTp1QbvHTINdayZUvdkCFD1P1Vq1bpXF1ddXfu3DE5pnz58jnuy/Lcc8/pOnTokO1ra7mO5HxyXungq99cXFzUJvelv4Wz11FmHn30Ud3jjz+e7Ws7Sh0Zk0Eo0dHRqiO21Nv8+fMzvbZ0xk7fB23UqFG6mjVr5uraWqwjS332pGzyc7dw4UKT/QMGDFAdyJ2pjqQ/lfST3blz5wNdW6v1Y0yr32m5qSNrf6dpNrMnmRGJoOUvrTVr1qgoOzuvkU3a382Rc0jELtG5XlRUFLZv364yKvp+G8I4mtc/lrRyTsjx+rJk59pariOZJuPAgQPqr0T91r17d7Rp00bdl/4Wzl5H5khTm9SbDNvP7rUdpY6MyV/Q0iQzb948lVnRNxWZI683Pq+QZhz9eVlHlkmTl/QdNb62fC7lsTPV0eTJkzF+/HjVvUKmGXmQa2uxfpzlOy03dWT17zSdRr300kvqr61169aZTEMRGxurnpeI+aOPPtLt2rVLd/bsWTUdgwzBLliwYJZTB8gwaJle4O+//1ajbWTkjPEwaBlFWahQIV2vXr3U0O9jx46pKQxkagd5rPfQQw/pvv76a8NjKcu///6ryiVD1T/99FOdu7u7bvr06dm+ttbrKL30I5dYRzrd2LFjdStWrFDlkmkFJKPn7e2tpsbQWh0J+b/L/1Pq55tvvtH5+PjovvzyS5PzpK8jub78bMnPmEyZMXr0aLNTrzhzHUm2Q7IJshUtWlR99uS+8chCmXpFRgXOnDlT/c4aPHiwKkv6qX60WkdyXpl65s8//zQpr2R1cnJtrdaPM32n5baOrPmdptlgT+JYc5tMTaGfF6dz585quLT8opeUr8ybJHPkZEWGQn/wwQe60NBQ9Yuubdu26s01Jul8SVXLB0iG4jdu3Fi3dOnSDE0j8iWj9/7776smOvlilvl6mjRpon6Z5vTaWq6j7PxgOHsdDR06VM0rJV9Ccm6ZZ2rPnj2araP+/fur+pH/rzTD/vLLLxnOY+5zJE0rFStWVK+rVq2absmSJawjozqSeb/MlbdVq1Ymr5MvLv3nTaZi2bZtm9PUkTw2V17jY/Lyc+Ro9eNs32n9c/m7yFrfaS7yT85ygURERETkKDTbZ4+IiIiIGOwRERERaRqDPSIiIiINY7BHREREpGEM9oiIiIg0jMEeERERkYYx2CMiIiLSMAZ7RERERBrGYI+IyMi6devg4uKCyMhIm1xf1sGsUqWKWtfYElmTtXbt2jled5uInAuDPSJyWq1bt8bQoUNN9jVt2hSXL19GUFCQTcr09ttvY+TIkXBzc7N4bKdOneDh4YHZs2dbpWxE5JgY7BERGfH09ERYWJjK7lnbpk2bcOrUKfTu3Tvbr3n66afx1Vdf5Wu5iMixMdgjIqckQdL69evx5ZdfqsBOtjNnzmRoxp05cyaCg4OxePFiVKpUCb6+vnj00UcRGxuLWbNmoXTp0ihQoACGDBli0vSakJCA4cOHo1ixYvDz80OjRo3UubMyd+5ctG/fHt7e3oZ9+/fvR5s2bRAQEIDAwEDUq1cPu3btMjzfrVs39ViCRCIic9zN7iUi0jgJ8o4fP47q1atj3Lhxal+RIkVUwJeeBHaSPZNgLDo6Gr169cIjjzyigsClS5fi9OnTKhvXrFkz9O3bV73m1VdfxeHDh9VrwsPDsXDhQtXseuDAAVSoUMFsmTZu3Ih+/fqZ7HvyySdRp04dfPfdd6ppd9++farpVq9kyZIIDQ1Vry1Xrlwe1xIRaQGDPSJyStInT5psJVMnzbZZSUpKUsGWPpiSzN6vv/6Kq1evwt/fH1WrVlXZt7Vr16pg79y5c5gxY4a6lUBPSJZPBlTI/o8++sjsdc6ePWs4Xk/O8dZbb6Fy5crqsblAUV4jryUiMofBHhGRBRIQGmfNJJMmzbcS6Bnvu3btmrov2Ttp0q1YsaLJeaRpt1ChQpleJy4uzqQJVwwbNgzPP/+8Ci7btWuHxx57LEMGz8fHR2UfiYjMYbBHRGSBcbOpkD595vbpp0C5e/euanLdvXt3hlG1xgFieoULF8bt27dN9o0ZM0Y17S5ZsgTLli3D6NGjVdOwNCPr3bp1SzVBExGZw2CPiJyWNONmZz67nJI+dnJeyfS1aNEiR6+Tfn7pSYZQtjfeeANPPPGEagrWB3vx8fFqcIa8lojIHI7GJSKnJU2x27dvV4Mybty4kWeTE0tgJgMrBgwYgAULFiAiIgI7duzAxIkTVYYuMx07dlTTrxg368pADxnFK33yNm/ejJ07d6pJl/W2bdsGLy8vNGnSJE/KTkTaw2CPiJyWDJqQZlYZYCHNoDIYIq9I9k2CvTfffFNN2dKzZ08VqMno2cxIgHjo0CEcO3ZMPZay3bx5U51HAsg+ffqgc+fOGDt2rOE1v//+u3qd9CskIjLHRafT6cw+Q0REVicjb6OiojBt2jSLx0o2UgJJmWevTJkyVikfETkeZvaIiOzI+++/j1KlSmWrSVman7/99lsGekSUJWb2iIiIiDSMmT0iIiIiDWOwR0RERKRhDPaIiIiINIzBHhEREZGGMdgjIiIi0jAGe0REREQaxmCPiIiISMMY7BERERFpGIM9IiIiIg1jsEdERESkYQz2iIiIiDSMwR4RERGRhjHYIyIiItIwBntEREREGsZgj4iIiEjDGOwRERERaRiDPSIiIiINY7BHREREpGEM9oiIiIg0jMEeERERkYYx2CMiIiLSMAZ7RERERBrGYI+IiIhIwxjsEREREWkYgz0iIiIiDWOwR0RERKRhDPaIiIiINIzBHhEREZGGMdgjIiIi0jAGe0REREQaxmCPiIiISMMY7BERERFpGIM9IiIiIg1jsEdERESkYQz2iIiIiDSMwR4RERGRhjHYIyIiItIwBntEREREGsZgj4iIiEjDGOwRERERaRiDPSIiIiINY7BHREREpGEM9oiIiIg0jMEeERERkYYx2CMiIiLSMAZ7RERERBrGYI+IiIhIwxjsEREREWkYgz0iIiIiDWOwR0RERKRhDPaIiIiINIzBHhE5vZkzZ8LFxcWweXt7o2LFinj11Vdx9epVWxePiOiBuD/Yy4mItGPcuHEoU6YM4uPjsWnTJnz33XdYunQpDh48CF9fX1sXj4goVxjsERHd07lzZ9SvX1/df/7551GoUCF8/vnn+Pvvv/HEE0/AHqWmpiIxMVFlI4mIzGEzLhFRJh566CF1GxERgeTkZIwfPx7lypWDl5cXSpcujffeew8JCQmG44cNG6YCRJ1OZ9j32muvqabhr776yrBPmoZln2QO9eQ8o0ePRvny5dX5S5Qogbffftvk/EJeJ83Ls2fPRrVq1dSxy5cvz+eaICJHxmCPiCgTp06dUrcSwEmmb9SoUahbty6++OILtGrVChMnTsTjjz9uOL5Fixa4desWDh06ZNi3ceNGuLq6qlvjfaJly5aG7Fz37t3x6aefolu3bvj666/Rs2dPdZ2+fftmKNeaNWvwxhtvqOe+/PJLFXgSEWWGzbhERPfcuXMHN27cUH32Nm/erPrw+fj4oHLlynjxxRdVwDd9+nR17Msvv4yQkBAVoK1duxZt2rRB8+bNDcFc9erV1fkOHDiA3r17Y8OGDYbryPMFCxZE1apV1eM5c+Zg1apVWL9+veEcQs4h192yZQuaNm1q2H/s2DF1Xv3riYiywsweEdE97dq1Q5EiRVQTqmTs/P39sXDhQhVs6Ztpjb355pvqdsmSJepWXiuBoT6wk4DRzc0Nb731lmq6PXHihCHYk6BOmmTFH3/8gSpVqqjXSrCp3/TNyBJMGpOsIgM9IsouZvaIiO6ZOnWqmnLF3d0doaGhqFSpkmqClYBPbqU/nbGwsDAEBwfj7NmzJk25MoJXH9TJgA/ZJJMnj+W8+/fvR79+/QyvkSDwyJEjKlg059q1ayaPZcQwEVF2MdgjIrqnYcOGhtG45ugzcVmRjJ009Z4+fVoFdxL8yetkvzwODw9XffRkv548rlGjhhr5a45kGo1J0zIRUXYx2CMisqBUqVIqIJMMnDS36knTbGRkpHpeTx/ErVy5Ejt37sS7775rGIwho28l2PPz80O9evUMr5ERvpLta9u2bbYCSiKinGCfPSIiC7p06aJup0yZYrJfn4nr2rWrSRNrsWLF1EjapKQkNGvWzBAEyujeP//8E40bN1ZNxXp9+vTBxYsXDYM/jMXFxSEmJibf/m9EpH3M7BERWVCrVi0MHDgQP/zwg8rkyQCJHTt2YNasWWqKFBmJa0wCu7lz56qm2QIFCqh9MmWLZPSOHz9u0l9P9O/fH/Pnz1cjb2UwhgSIKSkpOHr0qNq/YsWKLJuXiYiywmCPiCgbfvzxR5QtW1atoysDNmRwxogRI9REyOnpgz3jaVQkk9ekSRM1xYpxfz0hgz8WLVqksoG//PKLOr8szybXe/3119WgESKi3HLRGU/1TkRERESawj57RERERBrGYI+IiIhIwxjsEREREWkYgz0iIiIiDWOwR0RERKRhDPaIiIiINIzz7N1bl/LSpUsICAjgUkVERETkEGT2vOjoaLUMo8zXmRkGe4AK9NIvNE5ERETkCM6fP4/ixYtn+jyDPUBl9PSVFRgYmOvzyDqY//77Lzp06AAPD488LKF2sI4sYx1ZxjrKGuvHMtaRZawj+6+jqKgolazSxzGZYbAny4jca7qVQO9Bgz1Z4kjOwR8M81hHlrGOLGMdZY31YxnryDLWkePUkaUuaBygQURERKRhDPaIiIiINIzBHhEREZGGsc8eERGRA0tJSVF9x/KanNPd3R3x8fHqGmT9OpJ+gG5ubg98HgZ7REREDjrH2pUrVxAZGZlv5w8LC1MzVXAOWtvVUXBwsLrGg5yfwR4REZED0gd6ISEhakRoXgcbsuDA3bt34e/vn+WEvc4sNR/rSALJ2NhYXLt2TT0uWrRors/FYI+IiMjBSJOhPtArVKhQvgUyiYmJ8Pb2ZrBnozry8fFRtxLwyXud2yZdvntEREQORt9HTzJ6pG2+997jB+mXyWCPiIjIQbEvnfa55MF7zGZcIrJvOh2QEAXERQJ3zgPRV+B69yaqXVwLt/mzgdRkIP4OEH0ZSLwLtH4PaPyirUtNRGQ3GOwRkW2kpgLxkSp4U4Ga4fbe/ahLwN2raZsEdEak10p5uZPWb9nUoYUM9oiI7CXY27BhAz755BPs3r0bly9fxsKFC9GzZ0+LqcvJkyfjrbfeUvdLly6Ns2fPmjw/ceJEvPvuu/lceiIym4WLPJeWiZNbCdqS4oC7EshdBWJvAAl30wI4CeZSc9IHxQXwKQD4hyLVPwRXbt1FWIkycC1cEQgIBS7uBvb8ko//OSKyF2PGjMGiRYuwb98+k/3GMYH0datUqRJGjBiBxx57DM7MpsFeTEwMatWqhWeffRa9evXK8LwEgMaWLVuG5557Dr179zbZP27cOAwaNMjwOCAgIB9LTaRh0lSanJB2P+Z6WvOoZNUkaEuWoO0akBANxN4EbkUArm5AzI20IC72FqDLxaSibl6Ab0HAPwQIKJq2+YcCgeFp94OKAwVKAW6egFvaQuMpSUnYuXQpunTpAlf94uO+hRjsEZEhJoiKisJnn32Gvn37olixYmjatKmti6ZG7np6ejpXsNe5c2e1ZUYmETT2999/o02bNihbtqzJfgnu0h9L5BRSkoGkWECXmpZJS45Py5ilJN0Lzq6mHXNsaVpA5emfFrxJ0CavSUlMOyY5Me34dM2luebhB/gVTgvaJOsmj/2LpAVvvoUBr4C0YK5gGcA7KG+uSUQOIyEhQbXQzZ07VwVl9evXxxdffIEGDRpg5syZGDp0qMlk0ZLFe+SRR9Tcc/L82LFjTVoAZ8yYgaefftokJpBt6tSp+O233/DPP/+oYO/AgQN4/fXXsXXrVpX5k+TR559/rubJO3jwIGrWrImrV6+iSJEiuHXrFgoXLow+ffqocooPP/wQy5cvx6ZNm9Rjec2wYcOwbds2+Pn5oUOHDur/Ia8TrVu3RvXq1dUqG1KOGjVqYO3atVavb4fpsyeVv2TJEsyaNSvDc5MmTcL48eNRsmRJ9OvXD2+88Yaq2Kw+ZLLpyQdNP6z5QYY261+bH8vWaAXrKJOmT2nOvH0WSElAyu3zKBq5C6m7riIl9joQcw0u0hR6+wxcEmOgc/eEiwRrKQlwkexbfhTJxRVw9QD8igAePiqrppPMm4cvdBKsSUbPxU1l3HT+Yeo4XWAxwN07LcN2LwOXLbn4LJj7HLkkp6hfaKm6VJX5c2b8OdN+HUm5JfCRed5kE/I4LinvluxS50tMgVtCksURoT4ebjkaNSqB3l9//aWCtFKlSqkuXR07dsTx48cN/x/9rfF9uZUmWQnaVqxYgX///VftDwoKMqkH/X2Z+87Dw0N950dHR6trNG7cGNu3b1dz1w0ePBivvPKKKkeVKlXUnIUSjD366KNYv369eiy3+vOtW7cOrVq1Uo8lGG3Xrh2eeuopfPnll2rJNOlCJsHhqlWrDGWXuOXFF1/Exo0bM/y/skOOl/+TvOfp59nL7ufXYYI9qSyJ1tM39w4ZMgR169ZFwYIFsWXLFtU2L82/EqlnRvr06f8qMCYfmryYs2jlypUPfA6t03IduafEwTvpNnwSb8EtNQHeyXfglpIAr+RIuKUmqedkn2dSFFyRCq+kO3DT3f+BlTCpodyJMH/+rH6dJrt6I94jWG2pLm5IcfVEvHuwek1I1H6cK9QSSW5+SHbzVvuFOsazIHTyi8PNH0nufpb/k/rfVTfubbhzb7Pd5ygscg8aAbh9+zY2LV1q9bLYIy3/nDl7HUlCQzJXsnqDNA0KCcyafL7NJuXZOqwxfDzdst2F6/vvv1dZt2bNmql9n376qXovvv32W5UVk+BGn4gRcXFx6la/TwI4CS6N56CTTQIjCbrkOKmXb775Bnfu3EGjRo3w888/q/N8/fXXKgsnCSJJFj3xxBN4//331aTFTZo0UeWQDJ3cynO//vordu3ahTJlyqiMoASHcn6JMyRTN2rUKEM5p0yZojJ5e/bsQfny5ZGcnKxaI+X8esb/r+yQ/4eUW8Y5yPmMyQobmgr25E168skn1SzVxiR9qifpV2kLf+GFF1RA5+XlZfZcEhAav04qvkSJEurNDQwMzHUZ5YMmH4727durDyJppI4k8yYZtMRouEhT6V3JtMUAUZfhcuMYIPcT7sIl8iwQdwsu0jya20v5FITO3Rt3UrwRWKAQXCRjJhk1aQ6VplavAOik/5q7d1omTfq7BZdICwEl2+bqDvnUm//kA+Wg3c+Ry7G0ALlAgQKqL58zc8ifMytz9DqSgEbWY5XmR/33ontiHnXDyIWAwAD4emYvpDhz5oyqf8mKGX/nNmzYEBEREShevLgK5Iyf068kod8n3++S5Ur/nS2ZPBm8MWHCBFVHUj8TJ05U2cA333wTtWvXNll2TN5/CRAvXbqkgrO2bdti+vTp6rzSNCvNtjLgQ4I9Cbqk3PIaCTKPHj2qsnVSXnOtkZKIkqBcmqYfJLaQ/4f8/1u2bJkhBspu4OgQwZ5U5rFjxzBv3jyLx0r0LpGvfJhkFI458iExFwjKD3xe/NDn1Xm0zOZ1JP3YEqPTmk5lQIIEcXfOAXG3gbvXgaiL96cFyW1TqQRoqunT716/Nd/7j6U/mxqIEAJIk2mB0oBXIFzc3JGclIQN9wYf8HOUg8/RveYNVxeX+4M2nJzNf84cgKPWkSyXJgGRBDf6Zbr8vDxweFzHPLuGBEHRUdEqkLO0FFhOmnH15zIuu5DXyyYBkmT2jJ+T/6/xa/XXMlcuaSKW/nsS6IWGhhqONfea9GWRcQHSFezUqVM4fPiwCrCkaVmyapIhlL6Fcl59hvLhhx/GyJEjM6yNKwGl/vGDrpsrr5Wym/usZvez6xDB3k8//YR69eqpkbuWyDBsqRhJx5KTkv4QErTFXAPuXEgbNSpTf9w+k3ZfP5dbTkkfNenDpkaNhqX1ZZMBDzLwoWC5tAEHRSoBngGAX/6sVUnZzMQSOSHVrJnN7Fp2g71kTzd1zrxc97VcuXKqFW7z5s2qv56QjNnOnTvVwAwZHCH96ySYkuZWkX6KFXm9PgBMT5qBJUuXXpUqVdTgDuPzShnk/6ZPDkmzrLQOSEZPsoASqMkgi48//lh1EZH7epK5k36H0hwsXcnsef1gmwZ70tfg5MmThseSvpU3VCpNKk+fovzjjz/U8On0pO1cOllKJC79+eSxROTSWVLeLNI4GbQgc6tdPQxc2Q9Enk8biXo7IvujSmUQgsq2+QDBpYDAooB3cNp0H5KZk3ndgkoAwSUBd+sPl6cc4LJRRA5BAq2XXnpJZeD03/cyf670P5Pp1SSrJ82k7733nuqXL9/zEqSln09PHzNIM6rEAJl13dKTrmCjR4/GwIEDVVPv9evX8dprr6F///4qA6gPmCWbN3v2bAwfPtzQRUwGeKxevdqkC5j03ZMm3+eff151D5MgU2IaGbn7448/ZhhM4bTBnrSBS6Cmp69EeSP0b6xUmrzx0kkyPXlj5Xl50+SNkM6TEuwZvxmkATIJ77ltwPntwI5pac2f0uwqU45kRUaGSrAmfdskeJPMmzSXFiqftk/N3eYlOXJr/U+IiOjeLBqSOZRAS7J40jwqo2v1iRqZpkSCQQmmpB+dfM/LyFk9mTJlwYIFKoaQUbHGU69kxtfXV11Dpl6RfnTGU68Yk9G2MtWLPosnGTsJAGVGEP2AEhEeHq66mUlQ2KlTJxWHSKZS7ttbls9FJ5GUk5PsoQzblvb4Bx2gsZR9rR6sjqQJ9vJe4MQq4Mp/wKV9QNSFzE/o6g4UqQIUrgCE1wGCigHBpYEiFdPmcnNA/Bzlso6OLgHm9gOKNwSed8wRlnmFnyHt15F02pfMliQ50nfazysSjMn3o3wv2lvwYi9SrVBHWb3X2Y1fHKLPHmmcrMhwaFHal/WpNWr+uAxkEEPxBmlNrdKcWndg2n0Z+EBkwun/fiUiMsFgj2wnMQb4pQdwYWfG50JrACUaAEVrA6WbA4W0MmkI5R/22SMiMofBHtnOtJbAzZP3B0pU7gpU6gJU7Z42YIKIiIgeGIM9so075+8HeqWaAU8v4WhKIiKifMAel2QTLlcP3n8w8B8GepR3OOaMiMgEgz2yCddjS9LulGmZtswX0YPiHwxERGaxGZesJ/oKXPfOQcPTi+F6Z3faPu8gW5eKiIhI0xjsUf46uzVtOpUj/wDXj0ByePeXoAbQbKjtykZEROQEGOxR3rp5Cji2NC3IO702wyoXOr8QnPWuguKNesC9cpe05cmI8hT77BERGWOwRw8m9lZa5u7YsrTbuFsZj5GVLUo2URMhJxcoh/1Ll6JYnS6AA85aT/aMffaIiMxhsEfZF3MjbQmzKweBiA3A2S1AUkzG44JKpA28kMmQZd48n+D7zyUlWbXIRERE69atU+vo6oWEhKB58+b45JNPULZsWWgdgz1KkxgLnNkERF0E7l5Lu42PTLsfdxuIPJehSdbAww8o1QSo1Bmo0h3wD7F26YmIiCw6duwYAgICcOLECQwePBjdunXDf//9Bzc3N7tYrzm/1mnm1CuU5odWwJzHgMVDgXUfAXtmAYf/Bs5tBa4fvR/oeQakLWFW72mg90/A2xHA+5eAp/4CGjzPQI9sj/PskTN/9mUZyrzc5Hd/do7Lwc9d69at8dprr2Ho0KEoUKAAQkNDMX36dMTExOCZZ55RwVj58uWxbNkydXxKSgqee+45lClTBj4+PqhUqRK+/PJLw/ni4+NRrVo1FbzpnTp1Sp3n559/Nrm2ZPSKFi2Kli1bYtSoUTh8+DBOnkyb4P+7775DuXLl4Onpqa7x66+/Gl43fPhwPPzww4bHU6ZMgYuLC5YvX27YJ2X+8ccfDY/lfpUqVeDt7Y3KlSvj22+/NTx35swZ9fp58+ahVatW6pjZs2cjvzCzR2luHE+7DS6Zti5tYDjgH5rWBCu3voWAkCqATwHOZ0ZEZI8kMPsoPE+zQUadcLL23iXA0y/b5541axbefvtt7NixQwU8L730EhYuXIhHHnkE7733Hr744gv0798f586dU9mu4sWL448//kChQoWwZcsWFdhJ0NanTx9DoNSoUSN07dpVBWVPPfUU2rdvj2effTbTMkjgKBITE9W1X3/9dRXEtWvXDosXL1aBp1xXmn8lIJPgTQJPyQKuX78ehQsXVrdNmzbFxYsXVYApgayQ8kgw+c0336BOnTrYu3cvBg0aBD8/PwwcONBQhnfffRefffaZOkb+H/mFwR6ZGrQW8Cts61IQ5Rz/CCFyGLVq1cLIkSPV/REjRmDSpEkqeJKASEigJJk2aWJt3Lgxxo4da3itZPi2bt2K+fPnq2BP1K5dGx9++CGef/55PP744zh79qwK2DJz+fJlfPrppyhWrJjK4r344ot4+umn8fLLL6vnhw0bhm3btqljJNhr0aIFoqOjVdBWr149bNiwAW+99RYWLVqkyi99AuVckt0To0ePVkFcr169DGWWLOK0adNMgj3JbuqPyU8M9oiIiLTAwzctw5ZHUlNTERUdjcCAALi6ulq+dg7UrFnTcF8yZZKxq1GjhmGfNO2Ka9euqdupU6eqJlnJ9MXFxalsnAR4xt58800VfEk2TZqA5ZzpFS9eHDqdDrGxsSrg/Ouvv1Sz7ZEjR0yagUWzZs0MzcXBwcHqeAnq5HjZ5HgJ6u7evauCP8n+CWmOliyfND3rg1eRnJyMoCDThQTq168Pa2CwR+zjRESklex2DppSLUpNBTxS0s5pKdjLofQDEaT/mvE+eZxWhFTMnTtX9ZmTTFmTJk1UXzwZRbt9+3aTc0hgePz4cRU8ygCMTp06Zbjuxo0bERgYqPruyXlyQppoJdjz8vJSgV3BggVVnzzJAEqwJ8GmkOBPSD9EaVo2ln4giDTrWgODPUqHTWHk6PjHC5GWbN68WfWL0zexCsmcpSf98yQ7qM+oSd87CcaMSXOqZOnSk+PkOsZNrPK4atWqhscS4El20d3d3RBIyj7JDkqQqe+vJ1nJ8PBwnD59Gk8++STsAYM9ItII/qFCpEUVKlTAL7/8ghUrVqhgTUbJ7ty5U93Xk2Ze6ccnffxKlCiBJUuWqEBLsm6enp4WryH976T/nwyUkCDxn3/+wYIFC7Bq1SrDMTKCV/rtSV9A6WOoD/bkdTJYpGLFioZjpY/hkCFDVLOtBIYJCQnYtWsXbt++rfoDWhunXiE24xIRkd164YUX1CCGvn37qmbRmzdvmmT5jh49qoI1mdpEAj0h92/cuIEPPvggW9fo2bOn6p8nAzJkGhcZSDFjxgxDtk7INDGSOSxSpIiaSkUfAEpTs9wak4EiMnpXziGvkaBw5syZJgGqNTGzR6Y4opGIiPKR9HtLT+adS08GUuhJ0CSbsYkTJ6pbCbxkwIUxaaqVwRx6rVu3NjmfOTL9i2xZ2bdvn8lj6bd369Yt1Q8wvX79+qnNnNKlS1ssT15iZo+ItIWZaiIiEwz2iEgbmJUmIjKLwR5x9CIREZGGMdgjIiIi0jAGe0SkMcxUk/OwZid/ctz32KbBnsw43a1bNzX5oMyWLcucGJN16mS/8ZZ+RmwZBSNz6chIGBl9I5Mp6mevpmziLwvSBPbZI+ehX20i/ShU0p7Ye+9x+lVHHGbqFVk/Ttaak1mvM1sIWII74+HWskyJMQn0ZEHjlStXIikpCc8884xar27OnDn5Xn5NYid3IiK7J8tuSYJDv3asr6+vYYmxvCLzx8katPHx8ZbXxnVSqflYR/o1fOU9lvc6/VJrDhPsde7cWW1ZkeAuLCzM7HOycPHy5cvVTNr6xYS//vprdOnSRU2MKBlDsl+RsYlITE5FSKC3rYtCRORw9N+N+oAvr0mwERcXBx8fnzwPJLVCZ4U6kkAvszhIM5Mqy+SLsmCxzFz90EMP4cMPP0ShQoXUc7I0ilSCPtATssyJRNeyQPIjjzxi9pyybIlselFRUepWMoOy5Zb+tQ9yDptITYY+OZyUlAy451/59XUTl5CADl9uw7XoBGwY3hJFgxjwOfznyMZ15JKSrH6h6VJTkezkdcfPkPPUUeHChdX3Y3Jycp7335NzbtmyRa1LK+vBknXrSIJHOadk9OQ65mT382vX75404UrzriwvIosev/feeyoTKEGe/OevXLmiAkFjUjEyo7U8lxmZdVvWrUvv33//VanwByVNyo7ERZeM7vfuq+Zwd798v+bi5atxLTrt4/f89HV4pWpqvl/T0Tja58jWdVQk6j80BXAnKgrrly61abnsBT9DlrGOste/nuyzjrLbZ9Oug73HH3/ccF/WlqtZsybKlSunsn1t27bN9XlHjBhhshCxZPZkPb0OHTqYXfIkuyTCll8c7du3f6COlFaXkgTcWwGmfYcOgHdQvl1KX0eNm7cAdm1V+47fcUXnzh3ZTODonyMb15HLKW/gFBAUGKi6cjgzfoYsYx1Zxjqy/zrSt0w6dLCXXtmyZVXK+uTJkyrYkzbs9H0VJNUpI3Szat+WfoDpB3oIeaPy4s3Kq/NYjVGfUg9JQ1uh7CnpBoLvPBeFZuUL5/t1HYnDfY5sXUfuaZ2X5Y8G1lsafoYsYx1Zxjqy3zrK7jUdanjNhQsXcPPmTRQtWlQ9btKkCSIjI7F7927DMWvWrFGjYxo1amTDkjoYG0y9Ep9k2mz75I/brV4G0ipOJUREZDeZPZkPT7J0ehEREdi3b5/qcyeb9Kvr3bu3ytJJn723334b5cuXR8eOHdXxVapUUf36Bg0ahO+//16lU1999VXV/MuRuLlkpabUpBTTYM/fy6GSzGSX2A2AiMjuMnu7du1CnTp11CakH53cHzVqlBqA8d9//6F79+6oWLGimiy5Xr162Lhxo0kT7OzZs1G5cmXVrCv9dJo3b44ffvjBhv8ryo7kVNPsS7FgH5uVhYiISMtsmk5p3bp1lkPFV6xYYfEckgHkBMqO1+yVnGJ6zaRUjsYlIiLKDw7VZ4+0IyVdZi99sy5RrrHLHhGRCQZ7lI6V+uyly+QlJfMbmoiIKD8w2CObZvY83dM+gszs0QPjPI1ERGYx2CObTL2i77Pn45E2N1oigz0iIqJ8wWCPbJId0Y/G9fVMC/ai4/N+XUdyVvwcEREZY7BHNpF8L5MX7Otp2JeQzOweERFRXmOwRzbJhOj77AUYTabMfnv0YNhnj4jIHAZ7ZKPRuPf67N1rxlX70s29R0RERA+OwR7ZhD6z5+HmCnfXtACTmT0iIqK8x2CPbNpnTwI9/WCNRPbZo7zAgT5ERCYY7JFtpl65F+C5u91vNj5/O9bq5SAN4Tx7RERmMdgjm069om/CFbEJKVa5NhERkTNhsEc2oZ9U2d3NFf73RuS+t/CAjUtFRESkPQz2yCZTrxhn9tzuZfeuRSdwYmXKA/wMEREZY7BH6VinGfdiZJyhz97MZxoY9n+x6oRVrk9axD57RETmMNgjm9B31bsVk4g6JQsY9n+1+gSnYCEiIspDDPbIJu614qJkQT91+/UTdQzPbT55w1bFIiIi0hwGe2STqVdS710z0CdtcEbHamGG58b875DVy0Mawn6fREQmGOyRTaZe0X8fu967nqe7K0IDvdT9Mzdj2ZRLOcd59oiIzGKwRzahH3Vr/PU8/4UmhvsV3l9mg1IRERFpD4M9sslUFekze6JUIT+80qac4fE/+y9ZvVxERERaw2CP0nGx6gCN9C1vL7cub7j/2u97rVIW0hr22SMiMsZgj2xCd+8L2SVdtOfn5Y4ZT9+fd+/HjaetXjZyVOyzR0RkDoM9stFo3My/nttUDjHc/3DJEVyNirdewYiIiDSGwR7ZdICGfnLl9I6M62S43+ij1bhwO9ZaRSMiItIUBntk06lX0jfj6vl4uiE8yNvwuPnHa61SLtIAzrNHRGQ/wd6GDRvQrVs3hIeHqy/9RYsWGZ5LSkrCO++8gxo1asDPz08dM2DAAFy6ZDpCs3Tp0uq1xtukSZNs8L+h3EyqnFlmTyx4uZnJ4/cWHsjvYhEREWmOTYO9mJgY1KpVC1OnTs3wXGxsLPbs2YMPPvhA3S5YsADHjh1D9+7dMxw7btw4XL582bC99tprVvofaIUNpl7JRiYxLMgb3/S7v4zanO3n8O26k/lfOHJMnFSZiMistLWqbKRz585qMycoKAgrV6402ffNN9+gYcOGOHfuHEqWLGnYHxAQgLCw+8tt0YOw9goaWR/3cM1wXItKwLjFh9XjycuP4cTVu/iib20rlJKIiMjx2TTYy6k7d+6oZtrg4GCT/dJsO378eBUA9uvXD2+88Qbc3TP/ryUkJKhNLyoqytB0LFtu6V/7IOewiaRkeBjuJgG6/Av49HWTkpq2HFpqSqrF+urfqDjqlghEz++2qccL917EB10qIsBbX2ptcdjPkY3ryCU5Wf1C0+lSkezkdcfPkGWsI8tYR/ZfR9m9rotOPyzSxiSIW7hwIXr27Gn2+fj4eDRr1gyVK1fG7NmzDfs///xz1K1bFwULFsSWLVswYsQIPPPMM2p/ZsaMGYOxY8dm2D9nzhz4+vrC2bgnx6DrgZfU/f/V+hk61/z/G2D6UVccvO2KvmVT0DQ0ex/B17ealuvdWsko6nxvF2WiUPRRND/5EaK9imJN1Y9tXRwionwnXd4kySXJsMDAQMcO9iRy7d27Ny5cuIB169Zl+R/6+eef8cILL+Du3bvw8vLKdmavRIkSuHHjRpbntkTKKU3P7du3h4eHA2Wd4u/A47O0ZcqS3r0EuHnm26X0dbTwRijWnbiJj3pWxWP1imfrtdJ82+WbLSb79rz/EAK8HSpBrd3PkY3ryOXcFrj/2h26QhWQ/OJWODN+hixjHVnGOrL/OpL4pXDhwhaDPXdHqMg+ffrg7NmzWLNmjcVgrFGjRkhOTsaZM2dQqVIls8dIEGguEJQ3Ki/erLw6j9Uk3/8YeHh4Am75X3bdvc707m7u2a6rqsUL4L8xHVBzzL+GfXUnrMHBsR3h72X3H2Xtf45sXUdu7oY/HFlvafgZsox1ZBnryH7rKLvXdHWEQO/EiRNYtWoVChUqZPE1+/btg6urK0JC7q/CQHYok7VxLQn09sBPA+ub7Ks+egWi49mnhO6xj8YKIiK7YdN0iDS1njx5fyqNiIgIFaxJ/7uiRYvi0UcfVdOuLF68GCkpKbhy5Yo6Tp739PTE1q1bsX37drRp00aNyJXHMjjjqaeeQoECBWz4P3M0tlguzfzauNnRtkoodrzfFg0nrDbsqzHmX/zxYhM0KF0wT8tJRETk6Gya2du1axfq1KmjNjFs2DB1f9SoUbh48SL+97//qX56tWvXVsGffpOBGEKaYufOnYtWrVqhWrVqmDBhggr2fvjhB1v+txyclaZeQfamXslMSIA3jo6/v6SaeOz7rZi07OiDF44cE+fZIyKyv8xe69atDWukmmNp7IiMwt22LW06DnIs9zN7uT+Ht4cbDo3tiGqjVxj2fb/+FAa3LIuCfvk3yISIiMiR2HWfPdJwHyfDpMoPlo3x83LHmUldTfbVHb8Sqanst+W8+N4TERljsEc2sftcZJ6e79F007eUfW8pAz4iIiIGe2Srfk8VQvzVbUxCSp6c7+2OGafZkYDPTqaRJKtgnz0iInMY7JFN6O41tRUN9s6T84UEeqvm3BMTTNdaLjNiaZ6cn4iIyFEx2CObuLc07gP32UvPw80VH/asbrKv0UerEJeYNxlEIiIiR8Ngj2wz9cq95lW3fGg2blvFdELtq1EJqDJqOZt0nQXfZyIiEwz2yCb0YydyO89eVooG+WBqv7pomG6C5e/Xn877i5H94Dx7RERmMdgjm2RCHmQFjezoWrMo5r/YxGTfx8uPYvWRq/lyPSIiIk0Fe+fOncPGjRuxYsUKtZxZQkJC3peMNE0f7OVHZs9Y/8alTB4/N2sXDl+Kyt+LEhEROWKwd+bMGbzzzjsoVaoUypQpo5Yo69y5M+rXr4+goCC0b98ef/zxB1L1Pe/JMVmpKczQjJvP0d4b7SvihVZlTfZ1+WojM3yaxj57REQ5DvaGDBmCWrVqISIiAh9++CEOHz6MO3fuIDExEVeuXMHSpUvRvHlztaZtzZo1sXPnzuycluyGTrOZPVk2bUTnKjj9UZcMGb6DF+/k78XJythnj4go12vj+vn54fTp0yhUqFCG50JCQvDQQw+pbfTo0Vi+fDnOnz+PBg0aZOfUBGcfoGGdL2jJII7rUQ2j/j5k2Pfw15swfUB9tK8aapUyEBER2W1mb+LEiWYDPXM6deqEXr16PWi5SOvNuPeiPWsFe6JP/RIof2/lDr1Bv+zCteh4q5WBiIjIbvvsSdZuw4YNqumWKO+aca0X7Hl7uGHVsFY4PK6jyf6GE1bj7M0Yq5WD8hnn2SMiyl2w98svv6B169YIDg5G27ZtVd+9zZs3Izk5ObunIHtlgy9H/SVtMTWar6c7yhb2M9nX6pN1XGWDiIicO9iTwRnSb2/q1KkoXrw4fvzxR7Ro0QIFChRQTbcff/wxduzYkb+lJc2wRWbP2M9PZ+xTKqtskAPjpMpERA8+z17p0qXxzDPPYNasWWoqllOnTuHLL79UgzQ++ugjNG3aNCenIyd2f+oV21y/dGE/nJjQOcP+icuOICGZGT4iItKOXH/Vnj17VvXhW79+vbpNSkpCy5Yt87Z0pPmpV/Jjbdzs8nBzRcTELnipdTnDvmnrT6PSSK6j69j43hER5XjqFf2qGevWrcPatWvV7Y0bN1QmTyZXHjRoEBo2bAhPT8/sno6cXH4vl5Zdcv13OlVGAV8PfLT0qGF/mRFL0bd+CXz8aE2blo+IiMhqwZ404ZYsWRIvvfSS2urVqwc3N7cHLgDZExcbzLMHuzCoRVm4wAUTlh4x7Ju367zazkzqatOyUXbZyYeJiMhRm3H79Omj1sCVgRgyEnfKlClqXVw2d5EjDtAwl+Eb1LIs5jzfKMNz83eet0mZiIiIrBrszZ07F5cvX8aWLVvUmrgy8rZLly5qNO7DDz+MTz75hMukOSobTr1iL8GeXtPyhTNk8t7+6z888cM23LibYLNyUQ7wD1AiogcboFG5cmXVjDtv3jy1Lq4Ef7Vr11bZviZNmuT0dGRPrBh4pdxrx7WzWM/ghVZlTR5vPX0T9T9cZbPyEBER5XufPWNXr15VgzT0AzaOHz8OLy8vNe8eUXYSL/pgz81eOu2lM6JzFcQnpmDW1rMm+4f8vhcjH66CkABvm5WNMmGvfzkQETlKZm/+/Pl4+eWXUbVqVYSHh2PgwIE4ePCg6su3evVqREZGqsCPHJF1m71SdEDyvWDPzzNXf29Yxdge1XH6oy4m+/63/5JaXu3DxYdtVi4iIqKcyPY37VNPPYX69evjkUceQZs2bdCsWTP4+PiYHCNLp7m72++XN9kH/Uhc4eZm39kYV1cX/P1KM/T6boshGyl+3BSB/k1KoVQh02XXyB6wzx4RUa4yezNmzFD98yZMmIB27dqZDfQky5cTMhlzt27dVKZQRkMuWrTI5HkZ6Ttq1CgULVpUXU+ue+LECZNjbt26hSeffBKBgYFq3d7nnnsOd+/ezVE5SM/F6l/FtpxUObtqlQjGyQmd8UTDEhnW0y397hIsPXDZZmUjIiLKs2Dv+eefx8qVK80+l5KSogK9rVu3IidiYmJQq1Yttd6uOZMnT8ZXX32F77//Htu3b4efnx86duyI+Ph4wzES6B06dEiVbfHixSqAHDx4cI7KQbbL7DlArKfIHyMTe9XE0fGdMjz38uw9+HlThE3KRcYc5MNERGSvwd6kSZPQq1cvFXQZS01NVYHe5s2bsWpVzkYryhQuMopXmobTk6yezOU3cuRI9OjRAzVr1sQvv/yCS5cuGTKAR44cwfLly/Hjjz+iUaNGaN68Ob7++ms1TYwcR/Y5VYVJM66dDtDIjLeHG/73arMM+8ctPoz2n6+3SZmIiIiyku0Odq+//rpqMpW59SR7Vq1aNZXR69u3LzZu3Ig1a9aofXklIiJCTe0iTbd6QUFBKqiTDOLjjz+ubqXpVvoS6snxrq6uKig1F0QKmRxaNr2oqCh1K+v7ypZb+tc+yDlsIjkJHhLzubggOZ/LLnVjHFqmJicjKdWxAr4qoX6Y81wD9PvJdF7JE9fuqmbd7/vVRtsqIc73ObIic3XkIn2G7/2hmN+fY3vHz5BlrCPLWEf2X0fZvW6ORlOMHTtWBXwdOnRQI28l67Z+/Xo1Grd69erISxLoidDQUJP98lj/nNyGhJh+qcoAkYIFCxqOMWfixInq/5Lev//+C19f3wcue2bN3fbKO+k2OsqXZKoOS5cutWpmb9myZQ7TlJve69WALw9l/BF6cc4+dC+ZgrbFdE71ObIF4zoKjjmFVgDi4uKw0gqfY0fAz5BlrCPLWEf2W0exsbHZOi7HQ2elmfT27duqr52/v78K9KSJ1ZGMGDECw4YNM8nslShRQgWxMtDjQSJsecPbt28PDw/JlTmIqMvAQcDF1UVlbvOT1NGfS9J+KKQFt2vX/L1efut2MxbLD13FpytNBw7975wbWjasgR61ijrP58iKzNWRy6U9wHGowVz5/Tm2d/wMWcY6sox1ZP91pG+ZzLNgzzg4kiXSpKlEVs6YOXOmyXGff/458kJYWJhhAmcZjasnj+W6+mOuXbuWYVSwZB/1rzdHJoCWLT15o/Lizcqr81iNR9rHQBJs1ii3voug9NdzqHoyo3xYEF4NC8KgVuWw91wkHv9hm+G54X8eUNt/Yzog0NtD+58jGzCpo3vTPrnA8T9XeYWfIctYR5axjuy3jrJ7zWwP0Ni7d69hk8mUZWk0CayM9+/btw95pUyZMipgk8yhcQQrffH0y7LJrUzmvHv3bsMx0ndQBo1I3z7KKeu0p6bqr+ao7bdmeLm7oXHZQmpOvvRqjvkXqcZt10RERFaU7cxefqyOIfPhnTx50mRQhgSM0ueuZMmSGDp0qBqtW6FCBRX8ffDBB2pOvp49e6rjq1Spgk6dOmHQoEFqehZJp7766qtq8IYcR/Y9GtcR5tjLzZx85pR9bym2vPsQwoNN56ek/MDAmogoV5m9/LBr1y7UqVNHbfqmYrkvEymLt99+G6+99pqaN69BgwYqOJSpVry9769LOnv2bFSuXBlt27ZV/XRk+pUffvjBZv8nyn5s6WCzrmSbTMA8rkfGkelNJ63BR0uP2KRMzkGjHygiImtk9mSOvSFDhmRrpKo0s964cQNdu3a1eGzr1q1V37/MSDPfuHHj1JYZyQLOmTPH4rUoG1ys24wrS5FpkbubK55qVAqj/j6U4bkfNpzG+mPXMaRtBXStmfPBG0RERPmS2Tt8+DBKlSqFl19+WU2Vcf36dcNz0m/vv//+w7fffoumTZuqefcCAgJyXBByHsYDNLRKAtmIiV3UJpk+Y8euRuOVOXtQ/8OVWf6xQ0REZLVgT1aukNUxpE9cv3791MAJT09PFdTJqFZpev35558xYMAAHD16FC1btsyTwpG1WLnP3r1bVw322UufmZZNMn3HPzQN+MSNu4koM4LzweU5BtBERLkboCHz6k2fPh3Tpk1TmbyzZ8+qyUsLFy6spkKRW6Kc9dnTdrBnzNPdFXs+aI+64zNOvCmrbnzyaE08Vr+ETcpGRETaluNJlWUpMgnu9HPdkZZYJ/jS513cbDo8yPoK+nli24i2aDzx/nRCem/9+R+OXYnG+12r2KRsmuBEfzwQEeWEk33dkj00e8UluzhdZk8vLMgbC19uava5HzdFqClaUjgnHxER5SEGe2RVC/ZexDeH3dT9y3fi4YzqlCyAM5O6YkCTUmbj7sqjV2Ly/rQ6otxgsExEZIzBHt1nhUzbOwsyTkfirMb1qI6SBc1PZ3Qx1gUVR/2L+KQUq5eLiIi0hcEeWU36wKVD1VA4uwmPVMcLLcvi2Ied0D5dfags3wfLkZSiH79MWXO+bgFERHke7MnUK+7u7mptXNIS6zR7fbvulOH+e50rYVr/enB2LSoUwYguVdTautMH1MdHj9TIcEyF95dh2Ly8W3eaiIicS46CPQ8PD7VmbUoKm5Yo575afcJw/5mmpdQcdGSqX6OS6FmrqNm+jjJFy4zNETYpl0PhPHtERA/WjPv+++/jvffew61bt3L6UrJ7+Rd87T8fabj/Ts3kfLuOFkzuXR2hPuYDlrH/HMb20zfZtEtERPk3z94333yDkydPIjw8XC2h5ufnZ/L8nj17cnpKcoJMSI+pm9VtyYI+CPeLzvfrOTLJeD5fKQXuxavjycZlcPlOHFp9ss7wfN8ftqk5+3a811atzkH3MFNMRJQ3wV7Pnj1z+hJycgcv3jHc71I9DEhisGdJiA/QpVFJeLi7olQhPxXc3YpJNDwv97t+tQkLXm4KP68c/xgTEZETyfG3xOjRo/OnJKTZzMiGE9cN999sXwFLl97vu0fZs+v9dmrCZWPHrkaj2ugVmNqvLrrWzNjPz3mxzx4RkbFctQFFRkbixx9/xIgRIwx996T59uLFi7k5HWnc5OXH1G2vusVsXRSH5erqgoiJXTCkbYUMz70yZw96Tt2M20aZPyIiolwHe//99x8qVqyIjz/+GJ9++qkK/MSCBQtU8EeOKP8yIf/sv2S436laWL5dx1n68r3Yqix61g6Ha7ok7L7zkagzfiXO3YyF82KfPSKiPAn2hg0bhqeffhonTpyAt7e3YX+XLl2wYcOGnJ6ONO613/ca7jevUNimZdECX093THm8Dk5P7KqWXCtT2HSAVMtP1mJHBEfKExHRAwR7O3fuxAsvvJBhf7FixXDlypWcno40nBm5E5dkuF+3ZLAKVChvrR3eOsO+PtO24lq0c647rHCePSKiBwv2vLy8EBUVlWH/8ePHUaRIkZyejjT85fjx8qOG+99ztYx8s/HtNhn2NZywGrvO3IKOgQ8RkdPLcbDXvXt3jBs3Ti2dpu9HdO7cObzzzjvo3bt3fpSRHJAEGXO2n1P3K4b6IyTgfpM/5a0SBX2x4/22GfY/+v1WlBmxVM3T5xQ4zx4RUd4Ee5999hnu3r2LkJAQxMXFoVWrVihfvjwCAgIwYcKEnJ6ONPplefDi/ezvF31r59l5yTwJpnvXLW72uSYT1+BSpJMEfERElEGOO1EFBQVh5cqV2LRpkxqZK4Ff3bp10a5du5yeyvkkxQPJdvilm5CxWf5Bdftmk+F+tfCgPD8/ZfTJozUxqltVBPl44NMVx/DN2pOG55pOWoNvn6yLDlVDtb/qhi4FiLsNp5aUDI/kGCAuEkhmX1mzWEeWsY4evI48/QE3D9hajt+9+Ph4NQq3efPmaqNsunIQ+LkjkHgXWmfcTyz9aFHK37n4JNATwztWMgn2xMuz05YylFG8mhZzHfi4NJyZfAq6yJ0Dti6J/WIdWcY6yoM6emYZUKopbC3Hf+IHBwejZcuW+OCDD7BmzRrVlEvZcGmv/Qd6FTvlyWk2nrhhuP/jwPp5ck7KO/XGr4QmFSwHFCpv61IQEdmdHGf2Vq1apebTW7duHb744gskJyejfv36qu9e69at0b59+/wpqVZU6AA8/jvsklvepOkH/LzDcL9MIWb2bGXG0w0wcdkRPNOsDEYsuP9n582YRCz577L2lljz9AVe3QWkpsDZJSUnYdmyZejcuTM83G3fhGSPWEeWsY7yoI5c3WAPcvztrm++fe+991SgJ/PuTZs2DZMnT8akSZOQksJftFlycc2zoMoerT9+fx3cjx6poZoWyTbaVA5Rm/hhw2lE3IgxWWLtlTnApnfaoHgBX2hqkJGGf76yLVUHnYsb4OrO+sgM68gy1pFm6ihXPbVlTr0ffvgBAwYMUNOt/PPPP3j44Yfx+eef53kBS5curaZ3Sb+98sor6nnJJqZ/7sUXX4T9cY75zgYaZfX6NSpp07LQfWvebIXxPaujcliAyf7mH69F6XeXID6Jf6QREWlVjsNQWSlD+ulJkCWbzK9Xs2ZNFWTlB8kcGmcLDx48qJqKH3vsMcO+QYMGqbn/9Hx9NZSpcCAxCcmG+6+0KWfTspAp+fns37gUnmpUEov2XVTNuvFJqYbnu361EavfzLgaBxEROb4cZ/ZklYzY2Fi1NJpsV69ezddBGnK9sLAww7Z48WKUK1dO9RE0Du6MjwkMDIT90m6z5pqj1wz3B7Uoa9OyUOZB3yN1iuPAmI4m+09dj0Hbz9bh5t0Em5WNiIjsJLO3b98+REZGqkEa69evV333Dh8+jNq1a6NNmzb5OrFyYmIifvvtNwwbNswkkzh79my1XwK9bt26qZHCWWX3EhIS1KanX/5NVgXRrwySG/rXmjuHS3KyquxUXSpSHuAa9mzG5gh1WzzYG34eLmbrIas6IuvW0dQnauGV3/ebBHz1PlyF55uXxjsdK8Ke8XOUNdaPZawjy1hH9l9H2b2ui+4BFs+8efOmGpX7999/4/fff0dqamq+DtCYP38++vXrp5ZnCw8PV/uk72CpUqXUY5nkWZqVGzZsiAULFmR6njFjxmDs2LEZ9s+ZMyffmoBL3liHOud/xuXAOthR7g1oza0EYOyetL8dHgpPRY9S95sIyT7JT/77u9wQk5wx2zy4cgqK++kQ5GmTohERUTZIS6vERXfu3MmyVTPHwZ4EURLgySYZvYIFC6rRudJ/T5pWa9WqhfzSsWNHeHp6qgEhmZG5/9q2bYuTJ0+q5t7sZvZKlCiBGzduPFATsETYsrqI9Cn08DAdgu2y91e4L30DqRU6IaXPb9CaDlM2IeJmrLq/9Z1WKOzvleM6ItvVUYUP/jW7f9d7bQwTNdsTfo6yxvqxjHVkGevI/utI4pfChQtbDPZy3IwrI11lUuXBgwer4K5GjRqwhrNnz6o5/rLK2IlGjRqp26yCPS8vL7WlJ29UXrxZZs9zb4kqV1dXuGrshyY1VWcI9ETRAv4WX5NXda1l1qyjGc80wJSVx7H/wh2T/fU/WotPH6uFR+uZX3fX1vg5yhrrxzLWkWWsI/uto+xeM8fB3rVr9zvhW9OMGTMQEhKCrl27WuxTKIoW1diEsXZs+sbThvu7R3KNZEfUplKI2mTqHOO5EsXwP/YjLjEZ/Zs49xJkRESOKlczAEq/vEWLFuHIkSPqcdWqVdGjRw+4ueXPTNHSF1CCvYEDB8Ld/X6RT506pfrZdenSBYUKFVJ99t544w2VeZTpYOyT9kbjTlx21HC/UCbNt+QYZj3bUGVq5+w4h5GLDhr2f/D3IbV90beWGs1LREQannpFmkerVKmiJlSWJlXZ+vfvj2rVqqngKz9I860Mynj22WdN9kv/PXmuQ4cOqFy5Mt58803DJM92J/fjYOzasSvRhvs/DuA6uFogq5481bgU9o3KuPThG/P2q0mYF+69gAcY20VERPac2RsyZIjqC7dt2zY1OEM/Kvepp55Szy1ZsiTPCynBnLkvFhlUIdO/kO1MWXXccL9d1VCbloXyVrCvJxqULoCdZ26bDfpkG96hIl5uXZ7L4hERaSmzJ8GVrIOrD/SENKHKurgMvJyLBOCbTt5Q9+21Az89mA971sDDNYvi2yfrmn3+03+Po+x7S/Ha73uRkspMHxGRJoI9GcUaHX2/6U7v7t27qlmVMnPvizCflpXLrbjEFPy67Swu38n5Kih7z0ciOj5tibS3OlbKh9KRrVUKC8A3/eqiS42iODOpK6b0rW32uH/2X0K595binT//w/XoBCQkc61dIiKHDfYefvhhNe3K9u3bVWZHNmnSlSlZunfvnj+lpHwzcdkRfLDoILp9vTnHr+317RbD/dBA7zwuGdmjnnWK4fRHXTB9QH20rRyS4fl5u86jwYRVqDRyOU5cjcahS3fYt4+IyNGCva+++kr12WvSpAm8vb3V1qxZM5QvXx5ffvll/pSS8s26Y2nTbNzI4ZqoO8/cyqcSkb2T/nntq4bip6cboHaJ4EyPa//FBnT9ahPKjFiK6RvuT89DRER2PkAjODhYLY924sQJNfWKrFEro3Ml2CPHk9tW5d+2nTXcXzWsZd4ViBzK10/UweQVx1T2bvF/lzM9bsLSI2owj0ztUrN4MDzdc/x3JhERWXOePVGhQgVDgCcBH1lgp01ZLrns5/f3vkvq/oRHqqN8SECel4scQ4mCvirgE+N6JOLszRg8YtS8bywmMQWPfr9V3W9RoTA+eqSGej0REeWvXP15/dNPP6F69eqGZly5/+OPP+Z96cguvb/wgOF+5+pcqYTSFPTzRPViQahXqoCasiUrG0/cQIvJa9Wcfedv3V9qj4iI7CCzN2rUKHz++ed47bXXVL89sXXrVrVyhUx8PG7cuHwopobYWRY0p1nZ2MRkLNh7Ud2vX6qA+oIn0vNwc8VfLzVV97eeuokT16JRq3gwhszdi7NG6ycbk6BPLHqlWZZ9AImIyErB3nfffYfp06fjiSeeMOyTUbiyPJkEgAz2MqONZtwfjDrav9G+Yp6Xh7SjSblCahPr32qDJ37Yhq2nb2Z6fM+paSPCC/h6YMf77VTgSEREDy7Hv02TkpJQv37GZbHq1auH5OS0OddIu6asOqFuZcGEpve+yImy48NHquOJhiXRu27WE3Dfjk1ChfeX4Y15+9ScfUREZOXMnqyDK9k9aco19sMPP+DJJ598wOI4A/tqxs2JLfdWyxAznmnIgTmUI+WK+GNirxrq/vie1TB/53mUC/HHuVuxeH/hwQzHL9x7UW3Fgn2w4OWmKOzvBTcuy0ZEZJ3RuDJA499//0Xjxo3VY5lgWfrrDRgwAMOGDTMclz4gdGp2Oho3J7Fnvx+3G+43Lnt/uTyinPL1dMfTzcoYHvepXwJfrz6Br9aczHDsxcg4NPpotbpft2Qw5r2Q1leYiIjyKdg7ePAg6tZNWyfz1KlT6rZw4cJqk+f0mPVxDG7ZfJ+2G/W1kqk2vNzd8rFU5Gykf96wDpXwcpvyGPvPIRQv4ItPVhzLcNyec5GqiffH/mnTvRARUT4Ee2vXpo2cI23ITrOYzKvX94dthseyegJRfvD2cMPEXjXV/cEty6LrVxtx/OrdDMc9/+te9evr9a3/qkm9OdcjEVHmONzN2uws45mYkprpc7IqwtS1J1Fl1HLDvj71i6svZCJrZPv+faMV/hvTAQHemf9d2u7zDWq+vk0nbnAdXiKivFxBg7Th9PUYs/tvxySizviVGfZPupd1IbKWQG8PHBjT0fD4+NVodPhiQ4bjnvoprU9ps/KFMKx9JdQpEazW8SUicnYM9sjEmRsxaP3pOrPP7Xy/Hb88yeYqhgbgxPgOaP/xCpy5m/HzuPnkTWw+mbZk29NNS2PUw1X5uSUip8Zgz1oMzUv29aUj34Gp94omTWHmjOxaBT3rFFNTXxDZizdqpMCrbH28OHtfpsfM3HJGbTIn5Me9a3ItXiJySuyz58T2nrttCPTMeaROMRwd3wnPtyjLQI/sUtvKITgxoTOm9K2d5XFbTt1Uy7L1mLpZdVEgInImDPacUHxSCl6fuxePfJvW1GXO4tea44u+tTkYgxxiIIdknk991MVkv6zR6+lu+itu//lI1Re10UercCc2ycolJSKyDTbjOtFo3OSUVExYegQzNp/J9JiFLzdFnZIFrFouovyYRkgGaBz/sDP2nY9Er283m2Sxr0YloNa4f9X9eYMbo1FZLv1HRNrFYM9qbDclhKwv2uzjNUhMzjjNypvtK+LU9btYtO+Sely7RLANSkiU9/SDMuQzfXpiV6Sm6tTgI1mezZh+DsnZzzdCs/KFbVJWIqL8xGBPwzKbokIsGdIc1cKDDI+nPM4VCUj7wd+64a1x+kYMDlyMxBvz9ps8/6TRcoBr3myFskX8bVBKIqK8x2DP6vK/Gff8rVjVGd2cpxqXxJhu1eDuxu6apD3uri5IzmLUkQR85UP81datZjg+/fc4Vh+5ipPX75osX/3QZ+vVbf/GpfB+1yrsu0pEDo3BnrXk88z+d+KS8Ma8fdhz7jYizXQ8f7VNebzRvmK2lkcjcgbyB8+7nSurTWw4fh0Dft5hcsyv286qTbzXpTIGNi3NdaGJyOEw2HNgCckp+HXrWXy45Eimx7zTqTIGtSjDTB6RBS0rFsHpj7rg8enbsCPiVobnP1p6VG3Cz9MNC15uhkphXJOXiOyfXQd7Y8aMwdixY032VapUCUePpv3CjY+Px5tvvom5c+ciISEBHTt2xLfffovQ0FBo1dZTN7Fw7wXM33Uh02N61A7Haw9VUE1VRM6kSblC2HjiBsICvXP1emnmnf9CE3U/JiEZz83aiW2nMwZ+MYkp6Djlfn/Y5UNboFJoAFzsbO1rIiK7D/ZEtWrVsGrVKsNjd/f7RX7jjTewZMkS/PHHHwgKCsKrr76KXr16YfPmzbA/95pxc/BlIF82/9t/Cf/sv6QmhbVkQJNSeLtTZfh72f3bSpQvZHLlX7aexaP1ij/wufy83DF3cFrgt+LQFbzz139mu0iITlM2Gu5/3qcWmpcvjJBcBpxERHnN7qMCCe7CwsIy7L9z5w5++uknzJkzBw899JDaN2PGDFSpUgXbtm1D48aN4Uh0Oh0OXLyD7t/kLFDtUDVU9TniyEEioJC/l+qbmtc6VgtTmzh7MwZj/ncIa49dN3vssPmmo3wHNpFBHlUzTPBMRGQtdh/snThxAuHh4fD29kaTJk0wceJElCxZErt370ZSUhLatWtnOLZy5crqua1bt2YZ7EmTr2x6UVFR6lbOJ1tu6V9r7hyuKSmQbt2pqalIMXr+5LW76PfTTtzOwWz+g1uUxpA25eBlNELwQcptTVnVEaVhHdl3HYUHeuKHp9KmKjp8OQozNp/Fov2XMz1+1tazatMLDfDCd0/WRo1i96c+ymv8DFnGOrKMdWT/dZTd67roJKVkp5YtW4a7d++qfnqXL19W/fcuXryIgwcP4p9//sEzzzxjErSJhg0bok2bNvj4449z1BdQSJbQ1zd/Fkove205alycgwsFGmOq7yv4/VT2RvR1KZGCuoV0KOKTL8Uiojwg85VLB424FOD9Xdn/GzrURwdPV6itdIAOHYqlwtvu/wQnInsRGxuLfv36qdbOwMDATI+z618rnTt3NtyvWbMmGjVqhFKlSmH+/Pnw8cl99DNixAgMGzbMJLNXokQJdOjQIcvKyk6EvXLlSrRv3x4eHh4mz7nuOAdcBDwDC+P3Y5kH/DABEQAAcfpJREFUet1qhqF6eCBaVSyCckX8oDVZ1RGlYR05dh092k2HMzdjEZ2QjGUHr+Cnzfezeuldjbvfh/dUtAtWXzJt6m1TqTCeblIK9UsVyFEzsD3Xj71gHVnGOrL/OtK3TFpi18FeesHBwahYsSJOnjypKjYxMRGRkZFqv97Vq1fN9vEz5uXlpbb05I3KizfL7Hlc3QxrcqYnHbrbVQ1FoLfz/DDlVV1rGevIceuoUrinuq1fpjA+6FbdsDb1+dtxKFPYT018LoM+/tx9AUevRGd6nrXHbqgtvUZlCqJnnWKoHBZgWOLQ3Ehge60fe8I6sox1ZL91lN1rOlSwJ026p06dQv/+/VGvXj31n1y9ejV69+6tnj927BjOnTun+vbZn7TW8lM3Ygx7fuhfD+2rhnK6BiInIHNdSqAnShT0xfMtyqpNetIkJKfi0KU7WHrgCn7aFGHxXNsjbqktM6MfrgzXhLSBX0REdh3sDR8+HN26dVNNt5cuXcLo0aPh5uaGJ554Qk218txzz6nm2IIFC6rm19dee00Feo4yErfDvdF9ROS85I89WY6tXqmCavvg4aq4GhWPPtO24uzNWDQsUxCF/DxVk3B2jV0sc5G6Y/SelepxYX8v3Lib1qogf2C+1LocQgK8EBLgbZgNyoMTrxNpll0HexcuXFCB3c2bN1GkSBE0b95cTasi98UXX3wBV1dXldkznlTZEbzVsZKti0BEdio00Bvr32pj9rmdZ27hr90XUCTAS02cPnn5MVyMjMvyfPpAT6w8fFVt5kzuXRO1SgSrSamDfNlsR6QVdh3sycoYWZHpWKZOnao2R6FTY/aAXnWL2booROSAGpQuqDa9HrWL4ebdBPy27RxWHrmCSqGBaFe5MPbs3o0iZati48mbalWR7Hj7r/8yfU6yi483LIGWFYqoQFDWCJbsoEw+TUT2jT+l1pKu70whv4wDRIiIcjuZ9OvtKqhNP0IwMUKHLk1LYXCr8qrv3rGr0fhxYwROXI1GYooORy5nbxSf3s2YRExde0ptWa0vXKdEMLrVCkfJgr6cSJrITjDYswFpfuEvQSKyZr/AymGB+PSxWoZ9SSmp2HXmNuqUDFZ9BkV8Ugr2novE1tM3seH4dew7H5mj68hrZPty9QmT/bJusGQFyxXxV4NUigX7qHWIicg6GOzZQMVQLm1GRLYlAzKalCtksk+CPtkn27D2FVVGUNbn/vfwVVyOjFPZuqj4ZKw5ei1H15Ks4th/Dpt9TqaP6V23OGqXDEbN4kGqeZiI8haDPavRGfrsefOXGRE5SEZQ+gTKZk5sYjJOX4/B9egE7D0fiVsxCThzIxZe7q5Ync2AUOYZnLD0iNnnigZ5451OldG2SgiSUnQo4OvBqaqIcoHBng2wCZeItMDX0x3V763x26ZySKbHXYuOR5OJa5CSmrN5/y7ficfQefsy7A/wdkeP2uEo6OuJHnWKqeZhyUIyECQyj8GeDbixrwoRORGZz+/UR11UJjAxORXBvp4qOJNgbuw/h7DikPmpYDITHZ+sRh+Lr9acNHlO5iVsXr4wAr3d0ahsIdU0Lb9ySxXS3vKTRNnFYM/Ko3HlXwZ7ROSsmUDftJXkVBYuPNgH0/rXNznm8p04zN1xXvUTzOmIYbEj4pbaMtO2cggeb1gSJYO9EJOU8/8DkSNisGcDbmxqICIyq2iQD95oX1FtxqQJODk1FS5wUZm6v/ddwpt/7M/x+aUv4f3+hO54b9e/6t4jdYrh4ZpFUaVooApCibSEwZ4NsF8JEVHOSIuIm+v9wW296xVXm56sIiKTPF+KjMMnK45h8X+Xc3T+hXsvqs24b7XMGfhKm/JoXLYQ+1qTQ2OwZzX6jsnyC8vGRSEi0hiZu09I37xv+tXFN/3uPycTSZ+6HoOQQC/4eLjh4MU7eOvPzFcLEdK3cHvELWyP2GHYJ8vTyf5zt2LNvqZcET/8/HQD9g8ku8NgzwbYZ4+IyHoqhAaoTU+aartUC8Hsv5ej3UOtsfl0JGoVD8Kb8/fjxLW7mZ7nZBbPCQkoW32yzvC4YemCaiLrkoV88+h/QpQ7DPZswJXNuERENiXNsqE+QIkCvujfOG36mBVDWyI6IRn+Xu4qg3fq+l08/PWmXF9jx5lbaPnJWsNjWRP90XrFUa9UAU4eTVbFYM/qo3GlGZfBHhGRvZEl3IJ8PNR9H083NYfg9vfaIiYhGYE+Hrgdk4j2X2xQz3esFqomfJbl32T6wIgbMRjy+14czmIE8YI9F9Wm90XfWni4Zjiu3IlXy2jql60jymsM9myAmT0iIscQGuhtuF/Y3wtnJnXNcIybS1p/vqWvtzCsMTxzyxlMWXVc/Z2fkJxq9txvzNuvNmPPNiuDV9qUQyF/rzz/v5DzYrBnA8zsERFpl2ToXmxVTm3iWlQ8rkYlYN+FSHyw6GCWr/15c4TaGpUpiOEdK1lcL3jLqRs4ezMWt2ISVfNzZGwSLkbGooCvJwa3LMugkRQGezbAYI+IyHmEBHqrrUbxIPRvXEqtHnLsajSmrT+NjSeu48bdxAyvkZHAj32/Vd2XIO6ngfUzXT4uM0sOXMa64a2RnKpT8xTKSGRpqhapqTrVP3HP2dtoVr4wp5bROAZ7VnOvz56OzbhERM4+12rlsEB80be2Yd/Nuwkq6Nt08gbGLz5scvzdhGT0/WFbjq9z4XYcyr+/LFvHyuCRz/vcLw9pC4M9G+A8e0REZEyaW2WrGOoPfy83lCzoh+/Xn8L649czfU1BP0+1BvCZm7EoXsBHBXfViwUiNRVZDhQxRwaOTO5dE+78gtIkBns2wOXSiIgos6xf3wYl1f0m5Qqp280nb+DJH7cbjnm7UyU837xslk2v645dw9Mzdubo2hIg1iwenOuyk/1isGeDqVf0fSaIiIgskT515kYBZ6V1pRBETOyiBm/4et4b4OECFPH3wrXoBDWAQ76KJJPX+pO1Kju4I+IWgz2NYr7WBpjZIyIia2QJSxf2MwwQCQnwVvtkOhnJCuqbbJ9sVErdfrjkiI1LTPmFwZ4NMLNHRET2om2VEMP93Wdv2bQslD8Y7FkNV9AgIiL7U7aIv+F+7+/SpnshbWGwZwNsxiUiInsytF0Fw33pu0fawmDPBtiMS0RE9uSVNuUN9/tM26rW+iXtYLBn9dG4aesoEhER2QsPN1dsefchw+M2n65DbGKyTctEThLsTZw4EQ0aNEBAQABCQkLQs2dPHDt2zOSY1q1bq9FFxtuLL74Ie8Y+e0REZG/Cg33w7ZN1DY9rjV+DhBSbFomcIdhbv349XnnlFWzbtg0rV65EUlISOnTogJgY0/TyoEGDcPnyZcM2efJk2DM24xIRkT3qUqOoWklD7+0d7oiMTbJpmUjjkyovX77c5PHMmTNVhm/37t1o2bKlYb+vry/CwsJg39KacQUHaBARkb3q06AEft95DnvPRarHDSauxco3WqJCaICti0ZaDPbSu3PnjrotWLCgyf7Zs2fjt99+UwFft27d8MEHH6gAMDMJCQlq04uKSltDUDKHsuWW/rXmzuGakgq3e1Ov6FJTH+g6jiyrOqI0rCPLWEdZY/1YxjrK2tznGuDhqVtw4lpaS1r7Lzbg4Ki28PK4txoH2cXnKLvXddHp7o0csHOpqano3r07IiMjsWnTJsP+H374AaVKlUJ4eDj+++8/vPPOO2jYsCEWLFiQ6bnGjBmDsWPHZtg/Z86cLIPEB1Hxyv9Q5fKf+D25DQ6WehYNQxyi2omIyIl9ccANZ+7eb436sgkHbdiT2NhY9OvXTyXDAgMDHT/Ye+mll7Bs2TIV6BUvXjzT49asWYO2bdvi5MmTKFeuXLYzeyVKlMCNGzeyrKzsRNjSt7B9+/bw8PAwec510+dwW/+RCvY8enyJHrXD4YyyqiNKwzqyjHWUNdaPZayj7NXRwqUr8f6u+42Avz1bH43KmLauObMkG3+OJH4pXLiwxWDPIZpxX331VSxevBgbNmzIMtATjRo1UrdZBXteXl5qS0/eqLx4s8yexzVtLIxE1v4+Xk7/yyWv6lrLWEeWsY6yxvqxjHWUNX8PqObb6uNWq8dP/bwLJyZ0VlO1kO0/R9m9pl2/W5J0lEBv4cKFKmNXpkwZi6/Zt2+fui1atCjslZe7XVc7ERGRgfTTm/zo/RG6Fd5fpr6fyXHYddQh067IwAvpSydz7V25ckVtcXFx6vlTp05h/PjxanTumTNn8L///Q8DBgxQI3Vr1rz/wbQ3nHqFiIgcSZ/6JTCgSSnD4zIjliI5JdWmZSKNBHvfffedaoeWiZMlU6ff5s2bp5739PTEqlWr1Nx7lStXxptvvonevXvjn3/+gf3R/xXkwqlXiIjI4YzrUR2eRs235d9fhsRkBnyOwK777FlKE8ugCpl42dHc675HRETkUA6O7YiKI5cZHlcfswKHx3aEO/vw2TW+OzbgysweERE5IE93VxwZ18nwWDJ7kuFLYpOuXWOwZy33spQyqTLXxiUiIkfl4+mG/aM7mOyTQRtrj16zWZkoawz2bICZPSIicmRBPh44PK6jyb5nZu5EaipH6dojBns2wMweERE5Ol9Pd0RM7ILKYffXzI1PTrFpmcg8BntWc/+vHY7GJSIiLXBxccHi15obHickse+ePWKwZ4OQj6NxiYhIK4xH4tYZv5KDNewQww4bYDMuERFplQzWuB2TaOtikBEGe9ZiNGcgB2gQEZGWSYbvhw2nbF0MuofBnpXJ1CsM9oiISEu8PTKGEx8tPYrS7y7B6et3bVImcpAVNLSKzbhERKQlM59piAMX7uD5FmVw7Go0Ok3ZaHjuoc/SVro6Or4TvD3cbFhK58XMng1wNC4REWlJ47KFMKhlWTU6t3JYIE5M6Iyyhf1Mjqn8wXK89vteJHMAh9Ux2LMSnS7V0IzLWI+IiLTMw80Va4a3xsa325js/2f/JbW82ryd52xWNmfEYM9KjCcV9/di6zkREWlfiYK+2D+qA0Z0rozqxQIN+9/564Dqz9f5y41cdcMKGOxZic5oNK67G1N7RETkHIJ8PfBCq3JY/FoLTOtfD8G+HobnjlyOQtn3lqLTlA24FhVv03JqGVNMVpJ6L9iTfzlAg4iInFHHamFq23PuNnp9u8Ww/+iVaDT8aLW6P7FXDfSqWwxe7hzMkVeY2bNBZo9TrxARkTOrW7IATk7ojHc7V87w3IgFB1Bp5HJ0/2YTTnHaljzBzJ6VGMV6zOwREZHTk2XWXmxVTm2XIuPQdNIak+f/u3AHbe9N2+Lj4YYVQ1uiZCFfG5XWsTHYs3IzruDUK0RERPeFB/vgzKSualqW79efwoK9F3H6eozh+bikFLT8ZK26L8Fhx2qhqFU8GK5MnmQLgz0r0aneevdW0OCHk4iIyGy279WHKqgtKj4J3607pTZjEgzKJgK83PFY/RJ4rH5xVCl6f7QvmWKwZyX6xB7jPCIiIssCvT3wTqfKapORulPXnsSsrWdNjolOSMbPmyPUJl5oWRaP1iuO0oX91Fx/lIbBnpWbcV3AaI+IiCgnQgK9MbZHdbVFxyfh/K04fLf+lJqk2di0DafVJjzdXFG8oA+61wpHuSL+eLhmUbXChzNisGft0bhO+kEjIiLKCwHeHqga7oGvn6ijtsTkVMzbdR7bT9/E7rO3cflO2nx9iSmpqt/flFUn1GNZqk0U9PPEU41LoXWlIqjtJP3+GOxZCZtxiYiI8p6nuyv6Ny6lNiF9/VYcvILNJ29g55nbuBgZZ3L8rZhEfLX6hNr0ZF6/MoX8UCHUH+VD/FHY3wvBvp7QCgZ7Vl4bl624RERE+dvXL23QRgnDvmtR8fj032M4ce0u9p6LRIC3O6Ljkw3PL9hzMctztqlUBDWKB6NyWADqlyqgmpUdCYM9KzG04jLaIyIisqqQQG9MfrSWyb67CcnYduomDl+OUk3Ba49dUyt5pJhZq3ftsetqM8fP3Q0rovejUdnCakRw8QI+KjMoGUd7wWDPSnQpierWWTuHEhER2RN/L3e0qxqqNjG8YyV1K3P97Tp7GysPX8X649dx8lrWq3jEJLtg6cGrajMmWcBFrzSDt4ftl31jsGclhfZPU7eM9YiIiOx7rr/GZQup7YN0Ay0l6ffzpggs3HsRlcICkJCUjMNnrsA/MEhNAyOteOduxarjJUt4/Go0ahYPhq1pJtibOnUqPvnkE1y5cgW1atXC119/jYYNG8JeJHkEQJcYi92ogsdtXRgiIiLKEWmZc3MBBrUsqzaRlJSEpUsvokuXxvDw8FD7pBm49th/VfAnzcP2wH4alB/AvHnzMGzYMIwePRp79uxRwV7Hjh1x7do12Iu3i89GjYSfsN6tqa2LQkRERPnEzdUF9UoXQMVQf7uZ2FkTmb3PP/8cgwYNwjPPPKMef//991iyZAl+/vlnvPvuuxmOT0hIUJteVFSUIUKXLbf0rzV3joVHpM3fE1XCAh7oGo4uqzqiNKwjy1hHWWP9WMY6sox1lPs6mv5UnQzH5IfsnttFZ5jt1zElJibC19cXf/75J3r27GnYP3DgQERGRuLvv//O8JoxY8Zg7NixGfbPmTNHnSs/DN/mhiSdC96plYzw/LkEEREROZHY2Fj069cPd+7cQWBgoHYzezdu3EBKSgpCQ9NG0+jJ46NHj5p9zYgRI1Szr3Fmr0SJEujQoUOWlZWdCHvlypVo3769oe1e72rwWYQHeaNjNdNyOpus6ojSsI4sYx1ljfVjGevIMtaR/deRvmXSEocP9nLDy8tLbenJG5UXb5a58wxuVf6Bz6sleVXXWsY6sox1lDXWj2WsI8tYR/ZbR9m9pn30HHwAhQsXhpubG65eNZ3fRh6HhYXZrFxERERE9sDhgz1PT0/Uq1cPq1evNuxLTU1Vj5s0aWLTshERERHZmiaacaX/nQzIqF+/vppbb8qUKYiJiTGMziUiIiJyVpoI9vr27Yvr169j1KhRalLl2rVrY/ny5RkGbRARERE5G00Ee+LVV19VGxERERFpqM8eEREREWWOwR4RERGRhjHYIyIiItIwBntEREREGsZgj4iIiEjDNDMa90HodLocrTGX1Rp5siixnIdLy5jHOrKMdWQZ6yhrrB/LWEeWsY7sv470cYs+jskMgz0A0dHR6rZEiRK2LgoRERFRjuOYoKCgTJ930VkKB52ALK926dIlBAQEwMXF5YEibAkYz58/j8DAwDwto1awjixjHVnGOsoa68cy1pFlrCP7ryMJ4STQCw8Ph6tr5j3zmNmTjouurihevHienU/ecP5gZI11ZBnryDLWUdZYP5axjixjHdl3HWWV0dPjAA0iIiIiDWOwR0RERKRhDPbykJeXF0aPHq1uyTzWkWWsI8tYR1lj/VjGOrKMdaSdOuIADSIiIiINY2aPiIiISMMY7BERERFpGIM9IiIiIg1jsEdERESkYZoN9iZOnIgGDRqoVTFCQkLQs2dPHDt2zOSYF154AeXKlYOPjw+KFCmCHj164OjRo1meV8azjBo1CkWLFlWva9euHU6cOGFyzPHjx9W5ChcurCZZbN68OdauXZvleRcsWID69esjODgYfn5+qF27Nn799dccX1vLdWRs7ty5arUTKXNOr63lOpo5c6aqF+PN29s7x9d2lDras2cP2rdvr35uChUqhMGDB+Pu3bsWy/zHH3+gcuXKqm5q1KiBpUuX5vjaWq6jQ4cOoXfv3ihdurT6DE2ZMsXscVOnTlXHSD02atQIO3bsgLPU0fTp09GiRQsUKFBAbXLe9P//vPwcOVr9ONt32p5c/i6y2neaTqM6duyomzFjhu7gwYO6ffv26bp06aIrWbKk7u7du4Zjpk2bplu/fr0uIiJCt3v3bl23bt10JUqU0CUnJ2d63kmTJumCgoJ0ixYt0u3fv1/XvXt3XZkyZXRxcXGGYypUqKCuJ88fP35c9/LLL+t8fX11ly9fzvS8a9eu1S1YsEB3+PBh3cmTJ3VTpkzRubm56ZYvX56ja2u5jvSkLMWKFdO1aNFC16NHjxxfW8t1JGUNDAxUx+i3K1euaLKOLl68qCtQoIDuxRdf1B09elS3Y8cOXdOmTXW9e/fOsrybN29WP1uTJ09WP28jR47UeXh46A4cOMA6ukeOGz58uO7333/XhYWF6b744osMx8ydO1fn6emp+/nnn3WHDh3SDRo0SBccHKy7evWqU9RRv379dFOnTtXt3btXd+TIEd3TTz+trnPhwoVsX1vL9eNM32kXc1lH1vxO02ywl961a9dkihn1JmdGKlKOkQ+mOampqeoX3yeffGLYFxkZqfPy8lK/FMX169fVOTZs2GA4JioqSu1buXJljspcp04d9UWU3Ws7Qx3JD6T8EP3444+6gQMHmvxgsI7Sgj35xZAZLdWR/NIOCQnRpaSkGI7577//1HlPnDiR6bX79Omj69q1q8m+Ro0a6V544YVsX1vrdWSsVKlSZoO9hg0b6l555RXDY7lGeHi4buLEiTpnqyP976aAgADdrFmzsn1tZ6ofLX+nTXuAOrLWd5pmm3HTu3PnjrotWLCg2edjYmIwY8YMlClTRi1qbE5ERASuXLmi0qjGa9JJ88XWrVvVY0nfVqpUCb/88os6Z3JyMqZNm6ZSyvXq1TO8Tpo+xowZY/Y6EoSvXr1apZ9btmyZ7Ws7Qx2NGzdOHffcc8/l6trOUEfSdFCqVCl1fWmikCa5nFzbUeooISEBnp6eJot/SzOH2LRpU6Z1JK83Pq/o2LGj4bysI8sSExOxe/duk2vLNeSxs9ZRbGwskpKSDOXN78+RI9WP1r/TEh6gjqz1neYUwV5qaiqGDh2KZs2aoXr16ibPffvtt/D391fbsmXLsHLlSvWmmSOVLkJDQ032y2P9c9LmvmrVKuzdu1f1G5C+LJ9//jmWL1+u+nXoSZ8B6YuV/oMp5ZDrd+3aFV9//bXqA5Dda2u9juSH5qefflJ9ZXJ7ba3XkQSIP//8M/7++2/89ttvqsxNmzbFhQsXsn1tR6mjhx56SN3/5JNPVPBx+/ZtvPvuu+q5y5cvZ1pH8pqszss6suzGjRtISUlhHRl55513EB4ebvhizs/PkaPUj7N8pz2Uyzqy5neaUwR7r7zyCg4ePKg6QKb35JNPqi/U9evXo2LFiujTpw/i4+NzfS35C0auJ5H6xo0bVYdd6XDZrVs3kzdd/sp59dVXTV4rX+r79u3Dzp07MWHCBAwbNgzr1q2DNdh7HUVHR6N///7qhyInX0rOVEeiSZMmGDBggOoM3apVK9VJWjohS1ZQa3VUrVo1zJo1C5999hl8fX0RFham/kKXX4TGf2Gb+1mzJdaR9upo0qRJqqwLFy7MMCDKmevHWb7TquWijqz+nabTOOlTUrx4cd3p06ctHpuQkKA6wM+ZM8fs86dOnVJt8NIh11jLli11Q4YMUfdXrVqlc3V11d25c8fkmPLly+e4L8tzzz2n69ChQ7avreU6kvPJeaWDr35zcXFRm9yX/hbOXkeZefTRR3WPP/54tq/tKHVkTAahREdHq47YUm/z58/P9NrSGTt9H7RRo0bpatasmatra7GOLPXZk7LJz93ChQtN9g8YMEB1IHemOpL+VNJPdufOnQ90ba3WjzN8p+Wmjqz9nabZzJ5kRiSClr+01qxZo6Ls7LxGNml/N0fOIRG7ROd6UVFR2L59u8qo6PttCONoXv9Y0so5Icfry5Kda2u5jmSajAMHDqi/EvVb9+7d0aZNG3Vf+ls4ex2ZI01tUm8ybD+713aUOjImf0FLk8y8efNUZkXfVGSOvN74vEKacfTnZR1ZJk1e0nfU+NryuZTHzlRHkydPxvjx41X3Cplm5EGurcX6cZbvtNzUkdW/03Qa9dJLL6m/ttatW2cyDUVsbKx6XiLmjz76SLdr1y7d2bNn1XQMMgS7YMGCWU4dIMOgZXqBv//+W422kZEzxsOgZRRloUKFdL169VJDv48dO6amMJCpHeSx3kMPPaT7+uuvDY+lLP/++68qlwxV//TTT3Xu7u666dOnZ/vaWq+j9NKPXGId6XRjx47VrVixQpVLphWQjJ63t7eaGkNrdSTk/y7/T6mfb775Rufj46P78ssvTc6Tvo7k+vKzJT9jMmXG6NGjzU694sx1JNkOySbIVrRoUfXZk/vGIwtl6hUZFThz5kz1O2vw4MGqLOmn+tFqHcl5ZeqZP//806S8ktXJybW1Wj/O9J2W2zqy5neaZoM9iWPNbTI1hX5enM6dO6vh0vKLXlK+Mm+SzJGTFRkK/cEHH+hCQ0PVL7q2bduqN9eYpPMlVS0fIBmK37hxY93SpUszNI3Il4ze+++/r5ro5ItZ5utp0qSJ+mWa02truY6y84Ph7HU0dOhQNa+UfAnJuWWeqT179mi2jvr376/qR/6/0gz7yy+/ZDiPuc+RNK1UrFhRva5atWq6JUuWsI6M6kjm/TJX3latWpm8Tr649J83mYpl27ZtTlNH8thceY2PycvPkaPVj7N9p/XP5e8ia32nucg/OcsFEhEREZGj0GyfPSIiIiJisEdERESkaQz2iIiIiDSMwR4RERGRhjHYIyIiItIwBntEREREGsZgj4iIiEjDGOwRERERaRiDPSIiI+vWrYOLiwsiIyNtcn1ZB7NKlSpqXWNLZE3W2rVr53jdbSJyLgz2iMhptW7dGkOHDjXZ17RpU1y+fBlBQUE2KdPbb7+NkSNHws3NzeKxnTp1goeHB2bPnm2VshGRY2KwR0RkxNPTE2FhYSq7Z22bNm3CqVOn0Lt372y/5umnn8ZXX32Vr+UiIsfGYI+InJIESevXr8eXX36pAjvZzpw5k6EZd+bMmQgODsbixYtRqVIl+Pr64tFHH0VsbCxmzZqF0qVLo0CBAhgyZIhJ02tCQgKGDx+OYsWKwc/PD40aNVLnzsrcuXPRvn17eHt7G/bt378fbdq0QUBAAAIDA1GvXj3s2rXL8Hy3bt3UYwkSiYjMcTe7l4hI4yTIO378OKpXr45x48apfUWKFFEBX3oS2En2TIKx6Oho9OrVC4888ogKApcuXYrTp0+rbFyzZs3Qt29f9ZpXX30Vhw8fVq8JDw/HwoULVbPrgQMHUKFCBbNl2rhxI/r162ey78knn0SdOnXw3Xffqabdffv2qaZbvZIlSyI0NFS9tly5cnlcS0SkBQz2iMgpSZ88abKVTJ0022YlKSlJBVv6YEoye7/++iuuXr0Kf39/VK1aVWXf1q5dq4K9c+fOYcaMGepWAj0hWT4ZUCH7P/roI7PXOXv2rOF4PTnHW2+9hcqVK6vH5gJFeY28lojIHAZ7REQWSEBonDWTTJo030qgZ7zv2rVr6r5k76RJt2LFiibnkabdQoUKZXqduLg4kyZcMWzYMDz//PMquGzXrh0ee+yxDBk8Hx8flX0kIjKHwR4RkQXGzaZC+vSZ26efAuXu3buqyXX37t0ZRtUaB4jpFS5cGLdv3zbZN2bMGNW0u2TJEixbtgyjR49WTcPSjKx369Yt1QRNRGQOgz0iclrSjJud+exySvrYyXkl09eiRYscvU76+aUnGULZ3njjDTzxxBOqKVgf7MXHx6vBGfJaIiJzOBqXiJyWNMVu375dDcq4ceNGnk1OLIGZDKwYMGAAFixYgIiICOzYsQMTJ05UGbrMdOzYUU2/YtysKwM9ZBSv9MnbvHkzdu7cqSZd1tu2bRu8vLzQpEmTPCk7EWkPgz0icloyaEKaWWWAhTSDymCIvCLZNwn23nzzTTVlS8+ePVWgJqNnMyMB4qFDh3Ds2DH1WMp28+ZNdR4JIPv06YPOnTtj7Nixhtf8/vvv6nXSr5CIyBwXnU6nM/sMERFZnYy8jYqKwrRp0yweK9lICSRlnr0yZcpYpXxE5HiY2SMisiPvv/8+SpUqla0mZWl+/vbbbxnoEVGWmNkjIiIi0jBm9oiIiIg0jMEeERERkYYx2CMiIiLSMAZ7RERERBrGYI+IiIhIwxjsEREREWkYgz0iIiIiDWOwR0RERKRhDPaIiIiINIzBHhEREZGGMdgjIiIi0jAGe0REREQaxmCPiIiISMMY7BERERFpGIM9IiIiIg1jsEdERESkYQz2iIiIiDSMwR4RERGRhjHYIyIiItIwBntEREREGsZgj4iIiEjDGOwRERERaRiDPSIiIiINY7BHREREpGEM9oiIiIg0jMEeERERkYYx2CMiIiLSMAZ7RERERBrGYI+IiIhIwxjsEREREWkYgz0iIiIiDWOwR0RERKRhDPaIiIiINIzBHhEREZGGMdgjIiIi0jAGe0REREQaxmCPiIiISMMY7BERERFpGIM9IiIiIg1jsEdERESkYQz2iIiIiDSMwR4RERGRhjHYIyIiItIwBntEREREGsZgj4iIiEjDGOwRERERaRiDPSIiIiINY7BHREREpGEM9oiIiIg0jMEeERERkYYx2CMiIiLSMAZ7REQWHDhwAI8++ihKlSoFb29vFCtWDO3bt8fXX39tclxSUhK++uorNGjQAAEBAfD391f3ZZ88Z05KSgpmzJiB1q1bo2DBgvDy8kLp0qXxzDPPYNeuXVb6HxKRlrnodDqdrQtBRGSvtmzZgjZt2qBkyZIYOHAgwsLCcP78eWzbtg2nTp3CyZMn1XExMTHo2rUr1q9fj4cffhidOnWCq6srli9fjv/9739o1aoVlixZAj8/P8O54+Li0KtXL3VMy5Yt0a1bNxXwnTlzBvPnz8fx48dx7tw5FC9e3IY1QESOjsEeEVEWJIDbuXOnCryCg4NNnrt27RpCQkLU/RdeeAE//PCDyva9+uqrJsdNnTpV7XvxxRfx3XffGfbLPnnuiy++wNChQzNk/GT/448/zmCPiB4Igz0ioixUrlwZRYsWxdq1azM95sKFC6rpVbJ3q1evNnvMQw89hI0bNyIiIkIFb/KaMmXKqKzhv//+m4//AyJyduyzR0SUBemnt3v3bhw8eDDTY5YtW6YycQMGDMj0GHkuOTlZNdnqXyOP+/fvny/lJiLSY7BHRJSF4cOHIzY2FrVr10bTpk3xzjvvqEyc8YCLw4cPq9tatWpleh79c0eOHDG5rVGjRj7/D4jI2THYIyLKgoy63bp1K7p37479+/dj8uTJ6NixoxqRKwMvRHR0tLqVEbiZ0T8XFRVlcpvVa4iI8gKDPSIiC2T6lAULFuD27dvYsWMHRowYoQI8mY5Fsnr6gE0f9JmTPiAMDAy0+BoiorzAYI+IKJs8PT1V4PfRRx+pUbXSlPvHH3+gSpUq6vn//vsv09fqn6tataph4Id+Dj8iovzEYI+IKBfq16+vbi9fvozOnTvDzc0Nv/76a6bH//LLL3B3d1fz7wn9a3777TerlZmInBODPSKiLMiUK+ZmqFq6dKm6rVSpEkqUKKFWvFi1apXJPHp633//PdasWYPnnnvOMGeevGbQoEFqsEf6lThEamoqPvvsMzVFCxHRg+A8e0REWahevboajfvII4+optfExES1qsa8efNUwLZ371412fLdu3dVtm7Tpk1qMIc+g7dixQr8/fffZlfQkPP27NkTK1euVMulycobBQoUUKtmSPPw0aNH1X0ZDEJElFsM9oiIsiDz4kngJQGeZNkk2JOl0ySwGzlypGEFDSHPffvtt6ppVgI1+fUqAaLMsffyyy/Dw8Mjw/llfr6ZM2di1qxZql+fBIDh4eFqEuYhQ4aoKV+IiB4Egz0iIiIiDWOfPSIiIiINY7BHREREpGEM9oiIiIg0jMEeERERkYYx2CMiIiLSMAZ7RERERBrmbusC2AOZqf7SpUtqgXIXFxdbF4eIiIjIIpk9Lzo6Ws3N6eqaef6OwR6gAj2ZCZ+IiIjI0Zw/f96wFKM5DPYAldHTV1ZgYGCuz5OUlKTWuezQoYPZmfKJdZQdrCPLWEdZY/1YxjqyjHVk/3UUFRWlklX6OCYzDPZkGZF7TbcS6D1osOfr66vOwR8M81hHlrGOLGMdZY31YxnryDLWkePUkaUuaBygQURERKRhDPaIiIiINIzBHhEREZGGsc9eDqZnSUxMtNh27+7ujvj4eKSkpMCZSd8FNzc3WxeDiIjI6THYywYJ8iIiIlTAZ2m+m7CwMDWql/P1AcHBwao+WBdERES2w2DPAgngLl++rLJUMrw5q0kLJRi8e/cu/P39szzOGeosNjYW165dU4+LFi1q6yIRERE5LZsGexs2bMAnn3yC3bt3q4Bq4cKF6Nmzp0nQMHr0aEyfPh2RkZFo1qwZvvvuO1SoUMFwzK1bt/Daa6/hn3/+UQFW79698eWXX6qAKy8kJyerwEVmp5bh1dlp6vX29nbqYE/4+PioWwn4QkJC2KRLRERkIzaNSGJiYlCrVi1MnTrV7POTJ0/GV199he+//x7bt2+Hn58fOnbsqPrE6T355JM4dOgQVq5cicWLF6sAcvDgwXlWRn3fO09Pzzw7p7PQB8fSl5GIiIicMLPXuXNntZkjWb0pU6Zg5MiR6NGjh9r3yy+/IDQ0FIsWLcLjjz+OI0eOYPny5di5cyfq16+vjvn666/RpUsXfPrppyobl1fY7yznWGdERES2Z7d99mRAxJUrV9CuXTvDvqCgIDRq1Ahbt25VwZ7cyiAAfaAn5HhpQpVM4COPPGL23AkJCWozXm5En4FKn4WSxxJ4ShNtdgZo6G8tHesMpA6kLqQO9c24+vplti9zrCPLWEdZY/1YxjqyjHWU+zoaMGMXLtyOw2eP1kCdksHIL9l9b+w22JNAT0gmz5g81j8nt9IfzJhMfVKwYEHDMeZMnDgRY8eOzbBf1rdL3y9PzicjSmXghaWpV/Sio6OzdZzWSX3FxcWppnXp+2hMmt0pa6wjy1hHWWP9WMY6sox1lPM6OnHRDTcSXLBl6xZcPoh8I2MKHDrYy08jRozAsGHDMiwkLAsZp18bV/oHylQqMuBDBl5kRbJYEujJgsT20IR5/fp1NcBl6dKluHr1KgoUKICaNWvigw8+UINdxJYtWzBhwgRs27ZNBWYy+OXpp5/GkCFDMgyqWLt2rWoe37Fjhzq2dOnS6NSpE9544w0UK1Ysw/Wl7mSgRsuWLQ11J3+FyA9F+/btudZiJlhHlrGOssb6sYx1ZBnrKPd1NOHgemlGRJuWzVG1qGlckZf0LZMOG+xJNk1IkGI8dYc8rl27tuEY/fQeepJBkhG6+teb4+Xlpbb05I1K/4GWARoSuEnTsKURtvqmW/3xtvbYY4+p7NqsWbNQtmxZVXerV6/G7du3Vflk9HOfPn3wzDPPqGynNImvWrUKb7/9tgr+5s+fbwhap02bhpdffhkDBw7EX3/9pQK9c+fOqX6UX3zxBT7//PMM15dryOvN1au5fWSKdWQZ6yhrrB/nriNJQKw5eg3Xou93W8oJ+f47cNUF0fuvckaFHNZRVHxa82qAj1e+fr6ye267DfbKlCmjAjYJTvTBnUSw0hfvpZdeUo+bNGmipmSRqVvq1aun9q1Zs0YFXdK3z5lJvWzcuBHr1q1Dq1at1L5SpUqhYcOGhpHQgwYNQvfu3fHDDz8YXvf888+rpnLZL8Fe3759ceHCBZXpk00COz0J+CRrJ9ciIiL7svX0TTw3a9cDnsUN804fzqMSaZVbpnXk720fYZZNSyH94E6ePGkyKGPfvn2qz13JkiUxdOhQfPjhh6ppUYI/aX6UEbb6ufiqVKmimhElaJHpWSSd+uqrr6rBG3k5Ejf9X0pxSeaXQpMgMy4xBe6JyfmS2fPxcMt287A0O8smI5cbN26cIZMp/RNv3ryJ4cOHZ3htt27dULFiRfz+++8q2Pvjjz9UhlAyfuZIRpCIiOzLpci0acoK+3uidokCOX69TpeqWoQkAeDiYvvWKnuky6KOZGBGSEDW3b+cItjbtWsX2rRpY3is70cnTYUzZ85UwYVkoGTePMkeNW/eXE21Ytx3bvbs2SrAa9u2rWFSZZmbL79IoFd11ArYwuFxHeHrmb23TAaWSB3qA+G6deuqDJ8EwtJv7/jx44aA2ZzKlSsbjjlx4oTqy8iVMIiIHIc+MVG/VEF83z+t9SsnJIEifb67dKmj2abuB+UodWTTYK9169aG6UrMkSzWuHHj1JYZyQLOmTMnn0ro2CTw7dq1q2rOlT54y5YtUxNV//jjj4Zjsqp/42PsYcAJERFldCsmEf1/2o4rd+4vOGAc7Pl4sr+ds7OPxmQHIk2pkmHLrBk3OioaAYEB+daMm1OSBZVRQrJJM7j0yZMRujJhtZCJqZs2bZrhdbK/atWq6r406d65c0ctacfsHhGRfdkRcROHLmU+KrNaeP6NBiXHwGAvhyTDlVlTqgR7yZ5u6nl7GI1rjgRw0o9PppmRrOhnn32WIdj73//+p5pux48frx4/+uijePfdd1VW0HiAhp40sbPfHhGRbegzeHVLBmNS75omz3m7u6FkoazXdSftY7CnUTL4QqZeefbZZ1UfPZn7T/pISsAmy8/JOsMynYr04ZM+kdLvUfrlyejnt956SwV4Mi2LkDkIJciTY2RE9IABA9RIXBmlK1OvyEAQCRqJiCh/HL8ajY0nbph9bu+52+q2kL8XKoYGWLlk5AgY7GmUBGAy/YwEaadOnVKdSCVokwEb7733njpGAjqZKFkmVW7RooWaBFlGPr///vtqJLRxPz2ZY0+ac2VSZVmGTj+p8sMPP2wyQTUREeW9wb/swpmbWa+WEORjvwMEyLYY7GmUTLUiEyXLlhUJ8mSEc3bIusPGaxUTEZF1XI1Kmxi5fdVQ+JoZcOHl7opBLcraoGTkCBjsERER2THj+V0/eqQGigRkXAGKKCsM9oiIiGzg9PW76P/TDtyMyXo5M+MZsjiNCuUGgz0iIiIb2HLqJi5GxmX7+EqhAfDNxRRcRAz2iIiIbCD+XtOs9MMb9XDavKZZCQvyhqsrJ7innGOwl03ZWWmCTLHOiMgZpabq8NeeC7gaZbqiRXrbTt8yrF1boiDnwqP8w2DPAje3tJR5YmIifHx8bF0chxIbmzZNgD2vF0hElNe2nr6Jt/78L9vHB/l45mt5iBjsWeDu7g5fX19cv35dBS1ZrYwhK2hIUCjz1dnrChrWyuhJoHft2jW1soY+YCYicgbXo9MGXIQFeqNN5SJZHisrLg1sWspKJSNnxWDPAplYWNaDjYiIwNmzZy0Pj4+LUxlA4wmJnZUEemFhYbYuBhGRVbunxCam9cWrUTwIE3uZLl9GZAsM9rLB09NTrSwhWbusyCoVGzZsQMuWLZ2+6VL+/8zoEZGWxCQko9vXm3D6Rky2jvfhyFmyEwz2skmaZb29vbM8RoKb5ORkdZyzB3tERFpz7Gp0tgM9adxpVLZgvpeJKDsY7BEREWVD/L3m2bJF/PDni02zPNbDzQUB3vyjn+wDgz0iInJ6u87cwpqj17I85uyttBkGArzcUdCPI2jJcTDYIyIipzfk9724dCfrefH0CjDQIwfDYI+IiJzejZi0AXh965eAn1fmX43ubi54tF5xK5aM6MEx2CMiIk1LSdUZlibTS0pKRkJK2ghbtxQgMTlV7X+nc2U20ZLmMNgjIiLNiopPQscvNuCy2SZad7y9Y43JHl9PTpdC2uO8yzwQEZHmHb8SnUmgl1GTsoXg5c6vRdIeZvaIiEiz4u4131YKDcDfrzYzmQR/+fIV6NSpo2FeVAn0uPoRaRGDPSIicli/7ziHY1eiM33+/L3pUny93OBttKKFG1IhLbayz4MrXZDG2X2wFx0djQ8++AALFy7EtWvXUKdOHXz55Zdo0KCBYZ3C0aNHY/r06YiMjESzZs3w3XffqeXNiIhIuySQG7HgQLaOLeTnle/lIbJXdh/sPf/88zh48CB+/fVXhIeH47fffkO7du1w+PBhFCtWDJMnT8ZXX32FWbNmoUyZMiow7Nixo3re0vJmRETkuG7dmy4lwNsdA5uUzvQ4N1cXPFKnmBVLRmRf7DrYi4uLw19//YW///4bLVu2VPvGjBmDf/75R2Xvxo8fjylTpmDkyJHo0aOHev6XX35BaGgoFi1ahMcff9zG/wMiIsqt5JRU3I5NyvT5K1FpAy9CArwwvGMlK5aMyLHYdbCXnJyMlJSUDBk6Hx8fbNq0CREREbhy5YrK9OkFBQWhUaNG2Lp1a6bBXkJCgtr0oqKiDB12Zcst/Wsf5BxaxzqyjHVkGetI+/Ujgd7DU7fi1PUYi8d6e7jm+P+qhTrKb6wj+6+j7F7XRSed3uxY06ZN4enpiTlz5qiM3e+//46BAweifPnymDFjhuqjd+nSJRQtWtTwmj59+qgRVfPmzTN7TskOjh07NsN+uYavr2++/n+IiMiyO4nAqN1p+QgXZP41JYNnOxdPRYfidv1VRpQvYmNj0a9fP9y5cweBgYGOmdkT0lfv2WefVf3z3NzcULduXTzxxBPYvXt3rs85YsQIDBs2zCSzV6JECXTo0CHLyspOhL1y5Uq0b9/eMJSfTLGOLGMdWcY60n79nL0ZC+zeBD8vN+wb2TbPz6+FOspvrCP7ryN9y6Qldh/slStXDuvXr0dMTIz6T0kGr2/fvihbtizCwsLUMVevXjXJ7Mnj2rVrZ3pOLy8vtaUnb1RevFl5dR4tYx1ZxjqyjHXkuPVz9mYMftoUkWEZM707cWnNUz4e7vn6f7DnOrIXrCP7raPsXtPugz09Pz8/td2+fRsrVqxQo3Bl9K0EfKtXrzYEdxIQbt++HS+99JKti0xERJn4YcNpzN5+zuJxMviCiB6M3Qd7EthJt8JKlSrh5MmTeOutt1C5cmU888wzql/e0KFD8eGHH6p59fRTr8gULT179rR10YmIKBOR9zJ37aqEom6pYLPHuMAFbauEWLlkRNpj98GedDqUPnYXLlxAwYIF0bt3b0yYMMGQunz77bdVE+/gwYPVpMrNmzfH8uXLOcceEZENJaWk4nJk5mvS3r43R177qiHo26CkFUtG5HzsPtiTkbWyZUaye+PGjVMbERHZnrTGdPt6E45msYyZnvESZkTkpMEeERE5lvikVEOg5+vpBpdMjgsN9EajMoWsWjYiZ8Rgj4iI8lSc0QjbA2M6quXKiMh2GOwREVGOTd9wGvsvRJp9Tj+diqe7KwM9IjvAYI+IiHLkalQ8Jiw9YvG4sEAOlCOyBwz2iIgoR6Ljkwz98d7uWCnT45qVL2zFUhFRZhjsERFRBrGJyWlLlplx4tpddRvk44Gnm5WxcsmIKKcY7BERkYmUVB06fLEBF27HZXmcD6dNIXIIDPaIiMhEVFySIdAr7G9+uTIZd/F4wxJWLhkR5QaDPSIiMjt1iqebK3aNbGfr4hDRA2KwR0TkhP49dAXzd52HTpfxudjEtGDPx5PNtERawGCPiMgJTV5xDCfvDbTITNEgTp1CpAUM9oiInHj6lNfbVkCxYJ+MB7gATcpyKTMiLWCwR0SkQak64ODFKKS6uJp9PiYhram2e+1wlCvib+XSEZE1MdgjItKgRWddsX7bNovHycTIRKRtDPaIiDToyr35kAv7e8LPy/yv+jolgrmkGZETYLBHRKRBiaku6vbDntXRqXpRWxeHiGyIwR4RkYMuZ/beggO4EhWf4TmdTodLMWn3vbnKBZHTY7BHROSANp24gUX7LmVxRFpmr3gBX6uViYjsE4M9IiIHdDchWd1WDgvAK23KmzyXkpKCvXv3ottDTVE+hCNtiZwdgz0iIjsVn5SCPeduIzU143MyrYooWdAX3WqFmzyXlJQEl/M61CoeZK2iEpEdY7BHRGSn3pi3D8sOXsnyGE6dQkSWMNgjIrJTp6/HGLJ35oI6Lw839G1Q0gYlIyJHwmCPiMhOxSWlrXLxRd/aqFeqgK2LQ0QOisEeEZGNxCWm4M0/9uHi7Tizz1++k7bfh9OnENEDML9oop2QEWUffPABypQpAx8fH5QrVw7jx49Xc0jpyf1Ro0ahaNGi6ph27drhxIkTNi03EVF27DhzC0sPXMH+C3fMbkkpOni6uaJoEFe5ICKNZvY+/vhjfPfdd5g1axaqVauGXbt24ZlnnkFQUBCGDBmijpk8eTK++uordYwEhRIcduzYEYcPH4a3N39BEpH9irk3fUrFUH+806my2WPKFvFHAT9PK5eMiLTEroO9LVu2oEePHujatat6XLp0afz+++/YsWOHIas3ZcoUjBw5Uh0nfvnlF4SGhmLRokV4/PHHbVp+IiKZPmXb6ZtITM44f8quM7fVbViQD9pWCbVB6YjIGdh1sNe0aVP88MMPOH78OCpWrIj9+/dj06ZN+Pzzz9XzERERuHLlimq61ZOsX6NGjbB169ZMg72EhAS16UVFRRnmppItt/SvfZBzaB3ryDLWkbbqaNLSo5i59VyWx/i4u+Tp/8WR6sdWWEeWsY7sv46ye10XnXEHODuTmpqK9957TzXVurm5qT58EyZMwIgRIwyZv2bNmuHSpUuqz55enz594OLignnz5pk975gxYzB27NgM++fMmQNfXy4tRER5Z/pRVxy87YrCXjr4e2R83t1Vh07FdagQZLe/ionITsXGxqJfv364c+cOAgMDHTOzN3/+fMyePVsFYdJnb9++fRg6dCjCw8MxcODAXJ9XgsVhw4aZZPZKlCiBDh06ZFlZ2YmwV65cifbt28PDw8xvdWIdZQPrSFt1NO/qLuD2LYzoVhPda93/ozQ/OVL92ArryDLWkf3Xkb5l0hK7DvbeeustvPvuu4bm2Bo1auDs2bOYOHGiCvbCwsLU/qtXr5pk9uRx7dq1Mz2vl5eX2tKTNyov3qy8Oo+WsY4sYx05Th39ve8ivlt3CimpGbNz52/Hqlt/H0+rl9Ve6seesY4sYx3Zbx1l95ru9p6edHU1nR1GmnOleVfI6FsJ+FavXm0I7iTK3b59O1566SWblJmInM/MLWdw9Ep0lseULexntfIQETlMsNetWzfVR69kyZKqGXfv3r1qcMazzz6rnpd+edKs++GHH6JChQqGqVekmbdnz562Lj4ROdHkyOL9LlVQvVhQhudlnrzSDPaIyEbsOtj7+uuvVfD28ssv49q1ayqIe+GFF9Qkynpvv/02YmJiMHjwYERGRqJ58+ZYvnw559gjojx1OyYRG0/eQKqZptobdxPVbd1SwahXqqANSkdE5KDBXkBAgJpHT7bMSHZv3LhxaiMiyi/v/PUf/j18Nctj/L3Yr4mI7I9dB3tERPbiwr31a6uFB6KAb8YVLSqE+quVMIiI7A2DPSKibK6EIUZ3q4aGZdhUS0SOg8EeEdE9645dw7jFh5GQlHFpsytR8erWx8PNBiUjIso9BntERPcs3HsRp6/HZPq8BHolCvpYtUxERA+KwR4RUbopVF5uXQ4dq6VN2m6seAEfBJvpr0dEZM8Y7BGR09l55hZOXL2bYX/EjRjDYItaJYJtUDIiorzHYI+InMq1qHj0nbYVZqbLMwjgFCpEpCEM9ojIqVyNSlCBnreHK1pUKJLh+bBAbzSvUNgmZSMiyg8M9ojIqcTdm0IlPMgH0wfUt3VxiIjyHYM9ItKkTSduqFUv9MGdXlJy2rQq3pxChYicBIM9ItKkJQcu42Jk2qoX5lQvFmjV8hAR2QqDPSLS9IoXL7Qsi0frFTd5ztXVBWUK+dmoZERE1sVgj4gc2tWoeCw/eAVJKaarXhy5HKVuixf0RYXQABuVjojI9hjsEZFDG7/4MBb/dznT5wO9+WuOiJwbfwsSkUO7FpWgbhuWLojwYG+T5wr7e6FdlVAblYyIyD4w2CMih6YfbftS63JoUznE1sUhIrI7DPaIyCEs2HMBo/93CIn3pk5JTXHD2ztXIYFTqRARZYnBHhE5BBmEER2fbLTHBbgX6AV4uav1bImIKCMGe0TkUM21HzxcFW0rFcLatWvRpk0buLu7o6CfJ3w9+euMiMgc/nYkIruSnJKKv/ZcMAy80Dt9PUbdFgv2RrFgHxT0kvs+8PDwsFFJiYgcA4M9IrIrG0/KMmcHMn0+yMfTquUhInJ0DPaIyK5cj07L6EnWrmXFIibPSVavYZmCSE0x7rtHRERZYbBHRDaVmqozeRybkBbI1S4RjIm9aph/TVr3PSIiygYGe0RkM6P/PohZW8+afY5TqRAR5Q1X2LnSpUvDxcUlw/bKK6+o5+Pj49X9QoUKwd/fH71798bVq1dtXWwiyoblh66Y3e/qAjQqW9Dq5SEi0iK7z+zt3LkTKSn322wOHjyI9u3b47HHHlOP33jjDSxZsgR//PEHgoKC8Oqrr6JXr17YvHmzDUtNRNkRl5j2s73g5aYoXcjPsN/DzQUB3hxlS0TkFMFekSKmHbQnTZqEcuXKoVWrVrhz5w5++uknzJkzBw899JB6fsaMGahSpQq2bduGxo0b26jURGTsbkIyftt2Fnfikkz2x9wL9sICvdVceURE5ITBnrHExET89ttvGDZsmGrK3b17N5KSktCuXTvDMZUrV0bJkiWxdevWTIO9hIQEtelFRUWpWzmXbLmlf+2DnEPrWEfOWUd/7jqPScuOmn3u/+3dB3hUVdrA8Te9dyAhEkIvIgiCdESQsoiIgorCirDYwRXQtTwWyq4CuoqI4CqfFF0VxFVYBURAQGFB6dKk997SK8n9nnNChgwpk4RJZubO//c8l5m5d+bew5uZzJtTvTw9xN+rbP9fM8bInoiPbcTINmLk/DEq7XU9DMOwHgrnxL766isZOHCgHD16VGJjY3WN3tChQ60SN6V169Z6Zv1JkyYVeZ6xY8fKuHHjCu1X5wsMDKyw8gPuaskxD/nhuJfEBhrSIMz6V06tEENaRLnMryEAcBppaWk6L1ItnaGhofat2Tt06JD88ssvcuTIEX0h1dTaokULadeunfj7+0tFUU22vXr10one9Xj55Zd17WDBmr24uDjp0aNHicEqTYa9bNky3aeQWf2LRozcI0apmZelYPq2ZcV+keNH5U/Na8nLvRpe9/nNEKOKRHxsI0a2ESPnj1F+y6QtZUr2Pv/8c5kyZYps3LhRoqOjddIVEBAgFy9elAMHDuhEb9CgQfLiiy9KfHy82JNKLJcvXy7ffPONZV9MTIxu2k1ISJDw8HDLfjUaVx0rjp+fn96upX5Q9vhh2es8ZkaMzBujF77eJl9tPF7ksWB/+/6fXDVGlYX42EaMbCNGzhuj0l6z1FOvqJq7999/X4YMGaITr1OnTuk+c2vWrJFdu3bp7HLhwoWSm5srrVq10qNj7UkNvKhWrZr07t3bsq9ly5b6P7pixQrLvj179uhmXlXLCKDyrd57rsj9AT5e0rZOVKWXBwDcXalr9tQo2J49exZ7XNWU3X777Xp744035PDhw/Yqo04gVbL3yCOPiLf31SKrqVaGDRumm2QjIyN1E+wzzzyjEz1G4gKOnU5lybOdpHaVq9OpeHt6iLeX00/tCQDum+yVlOhdS01wrDZ7Uc23qrbuL3/5S6FjkydPFk9PTz2Zshqooco5ffp0u10bQPEWbj0hm49cKnI6lbAAH1bBAAAzTL2iJjRetWqVnvi4Q4cOOumyNzVworhBw6qf4LRp0/QGoPKoOfNGzdsq1yxta5kUOTSAPj4A4PLJ3muvvaYHTKh+dCoZU6tZqMRv6tSp9ishAKeUlJ6tEz2V2D3Zua7VsRY1wyXYz6Wm8QQA0yrTb2M1ClcNvsg3b9482bZtmx6Rq6jBG6rPHskeYD6Zl3OsVsA4mZCub1VS91yP659OBQDgBMnek08+KR07dpQ333xTTz5cp04deeedd/Q6tWoKlA8//FAaNGhQQUUF4CgJaVnS9Z3VcjE1q8hRtgAA51WmoXG//vqrVK9eXW655Rb57rvvZObMmbJlyxZp3769dOrUSY4fP65XoQBgLvvOplgSPU+Pq5ta6uzOptUdXTwAgL1q9ry8vPSEyaom76mnnpKgoCD54IMPrntFCwCuMZ1K4+qhekoVAIDrKFcPatV8u3TpUvnss8/ktttu0wMzhg8fbv/SAah0arDVRz8flANnUyz7Tibm9c8L8GGePAAwdbKnliVT/fV2794tN998s7z00kty5513ynPPPacnMZ4xY4Y0bdq04koLoFKabCcu+aPIY1VDCi8zCAAwUbKnVrBQCd9DDz2klyhTTbmqdm/27Nn68YABA6RPnz4yadKkiisxgAqVkJY34jYqyFeGdapttQJG72Z02QAAUyd7P/30kx6QUa9ePXnsscf0bb477rhDNm/eLOPHj6+IcgKoINk5uZZpVJQjF1L1bUyYvzx9+9XPOADADZK9+vXry8cffyyPPvqoLFu2TOLj4wutZqGaeQG4Tv+8vh+slV2nkgodY6kzADCHMvW2VlOtqNq9Fi1a6ClW1Lx6AFxXdo5hSfSCfL30BMlqU+va9m1Oky0AuF3NXvPmzfUqGgDMIT07b0oVZcvrPcTXm9G2AOC2yZ5q7vHw8KjY0gCoUNuOJcistYckWy1qq5ZAy87Vt2pyZLXGLQDAfEr9Z3yTJk1k7ty5elm0kuzbt0+P0p04caI9ygfAjqb+tF8WbD0pi34/pbflu8/o/TGh/vwxBwDuXrM3depUvXrG008/Ld27d5dWrVrplTPUoIxLly7Jrl27ZM2aNbJz504ZMWKETvgAOJekjLxpVQa0ipMbY0Mt+9vVjXJgqQAATpHsqalVVH89ldDNmzdPPv/8czly5Iikp6dLlSpV9KCNwYMHy6BBgyQiIqJCCw2gdNR6tqcTMyyPE9LyauZ73hQtXRtFO7BkAACnXS6tY8eOegPg3FSSd9vbKyXrcl6/vIKYVgUA3Ee51sYF4PwOnkvRiZ4afBEZ5GvZHx8ZKM3jwh1aNgBA5SHZA0wqLStvWpWbbgiThcM7OLo4AAAHIdkDTGLj4Yvy8c8H5fKVaVXOJuf11QvwYe48AHBnJHuASfxr9QFZvvtsof2x4QEOKQ8AwDmQ7AEmkZRxWd8+3DZemtYI0/fVRMldGlZzcMkAAI5UpvadkydPyvPPPy9JSYUXTU9MTJS//e1vcuZM3iStACpWcka2bDpy0bKpaVaULo2qygOt4vR2b4saEh54dXAGAMD9lCnZe/fdd3WiFxp6dTLWfGFhYZKcnKyfY08nTpyQP//5zxIVFSUBAQHStGlTq/V51TJur7/+ulSvXl0f79atm17FAzAz9b7vM3WN9P9wnWXbfzZFH2NaFQBAuZO9H374QU+cXBx17Pvvvxd7UStzdOjQQXx8fGTJkiV6lY533nnHatLmt956S95//33517/+Jb/++qsEBQVJz549JSPj6kSygNlk5xhy+EKavl8zMlBqReVtnepXkVtqMqk5AKCcffYOHTokNWvWLPZ4jRo15PDhw2IvkyZNkri4OJk1a5ZlX+3ata1qN9577z159dVXpW/fvnrfp59+KtHR0bJgwQJ58MEH7VYWwJmkZ+dNq6IsH91ZfL0ZcQsAsEOyp5pJVTJXXMKnjqnn2Mt///tfXUt3//33y+rVq+WGG27Qa/M+9thjluTz9OnTuum2YHNymzZtZN26dcUme5mZmXrLl98HMTs7W2/llf/a6zmH2RGj8sUoN9eQcYt2y4FzqXnHcvKmV1ETJkvuZcnO9hB3wvuoZMTHNmJkGzFy/hiV9roehqoeK6XevXtLbGyszJgxo8jjjz76qB7EsXjxYrEHf39/fTt69Gid8G3YsEGeffZZ3WT7yCOPyP/+9z/dzKuuqfrs5XvggQfEw8NDr+FblLFjx8q4ceMK7f/iiy8kMDDQLmUH7OlEqshbvxf+26yKvyGvtbhaywcAcB9paWkycOBAPUi2qPEU5arZUyNxu3fvrmvP1Mhb1VyqqBG4qu/c7Nmz5ccffxR7yc3NlVatWsmbb76pH7do0UJ27NhhSfbK6+WXX9YJZMGaPdVc3KNHjxKDVZoMe9myZTpGqp8hCiNG5YvRhsOXRH7fINVC/OSVXg0tz21RM1yqh+X9UeROeB+VjPjYRoxsI0bOH6OiZke57mSvS5cuMm3aNF27NnnyZJ0YqRo0lVGq/+TUqVOla9euYi+qtu7GG2+02te4cWP5z3/+o+/HxMRYks2CNXvqcfPmzYs9r5+fn96upf4P9vhh2es8ZkaMbDt8KVMupOU1224/kfeBrhLsJ31viXNwyZwH76OSER/biJFtxMh5Y1Taa5Z5UuUnnnhC7rrrLvnqq69k//79epBEgwYN5L777tMDNOxJNdHu2bPHat/evXslPj7eMlhDJXwrVqywJHcqy1Wjcp966im7lgWoTMdTRZ6d+r9C+wN9mVYFAFAJK2iogRKjRo2Siqau0b59e92Mq/rh/fbbb/Lxxx/rTVG1iiNHjpR//OMfUr9+fZ38vfbaa7pf4T333FPh5QMqytl0D0tyFx8VpO97e3rI0A5XR6MDAFBhyd78+fPlyy+/1LVsiqrZUx0EVe2ePd16663y7bff6j5248eP18mcmmpl0KBBlue88MILkpqaKo8//rgkJCRIx44d9XyA+YM7AFeUlZt326Z2pMwa2trRxQEAuEuypwZMPPTQQzrZUwleo0aN9P6dO3fKgAED9IhZlQSqGjd7UU3GaiuOupZKBNUGuKrE9Gx57qttcjY5Q3eNOH4ub968AJptAQCVmexNmTJFli9frue/uzYBU/uGDh2qn6OaVgGU3tr952X57oLrSuf9wZTfhAsAQHmVadp9tZLF22+/XWRN2913362nX5k5c2a5CwO4q5TMy/q2WY0w+fjPLeTxRjkyZ0hLGdWtgaOLBgBwp5q9ffv2Wa1WcS11bMSIEfYoF2B6O08myvFL6fr+lqMJ+rZGRIB0aVhV0g8Y0r5ulPiwDBoAoLKXS1ODIIpbLk1Ne8LACMC2g+dSpPf7awrtD/Qt15gpAACKVaZqg3bt2smHH35Y7HE14bJ6DoCSHb2YZplapWV8hN461IuSQW2K/kMKAIDyKlM1wiuvvCK33367XLhwQS+dpkbjqpGDu3fvlnfeeUcWLlwoK1euLHdhAHeRnpW3nm2T2FCZ/2R7q2MsOg4AcFiypyY4njdvnp7TLn/JsnwRERF62hW16gUAazm5hjzz5WbZeyZFP07OyEvo/H2YWgUAULHK3EHo3nvvlZ49e8rSpUv1gA1FzbnXo0cPCQwMrIgyAi5vz+lkWbz9dKH9dasGO6Q8AAD3Ua7e4CqpU0kfgNJJz86bWiUm1F/eezBvHWcfL0+5uUaYg0sGADC7Mg3QWLdunXz//fdW+z799FO9jFm1atV0825mZqa9ywi4pGMX02TBlhN6W7H7rN4XHugjbetE6U0NyvD2YmoVAIAT1eypJcnUAI38SZW3b98uw4YNkyFDhkjjxo31hMuxsbEyduzYiiov4DIemrHeMo9evmA/plYBAFSuMn3zbN26Vf7+979bHs+dO1fatGkjM2bM0I/j4uJkzJgxJHtwe3p92yuJXpvakeLr7SmeHh4ytEMtRxcNAOBmypTsXbp0SaKjoy2PV69eLb169bI8vvXWW+XYsWP2LSHggjIv51rufzLkVmr0AAAOU6ZvIJXoHTp0SNfgZWVlyebNm2XcuHGW48nJyeLj41MR5QSc3i/7zsmY/+6UzOxcyTUMy35/ljwDALhKsnfnnXfKSy+9JJMmTZIFCxboUbmdOnWyHP/999+lbt26FVFOwOkt2HJSDp5LtdpXr1owgzAAAK6T7Kn+ev369ZPOnTtLcHCwzJkzR3x9fS3HZ86cqefbA9xRRnbeqhhPdK4jvZtWtyR7AAC4TLJXpUoV+fnnnyUxMVEne15e1rP/z58/X+8H3GUQxso9Z+VUYoZ+fOBcimWi5GY1wh1cOgAA8pSr13hYWNETwUZGRpbndIBL2nz0kvxl9sZC+0MYjAEAcCJ8KwHldDIhr0YvMshXWsVH6PvRof7SuWFVB5cMAICrSPaAckrPyuuj1zwuXD4e3MrRxQEAoEgke0AZTFzyh8zbcFTfz8jOm0svwMe67yoAAM6EZA8oA5XoXUrLttp30w1F92EFAMAZkOwBZZB2pen280fbSHSon/h5e0lcZKCjiwUAQLFI9gAbc+f9d9tJSUrPFrUoRv4yaI1iQiQq2M/RxQMAwCanntp/7Nix4uHhYbU1atTIcjwjI0OGDx8uUVFRen6//v37y5kzZxxaZpjLt1tOyAtf/y7/WLRb3li8W+/z8vSQIKZXAQC4CKf/xmrSpIksX77c8tjb+2qRR40aJYsWLdKTOau5/0aMGKFX+Fi7dq2DSguzOZOUN71KnSpBcnNc3kTJ7epEiT+DMgAALsLpkz2V3MXExBTar1bx+OSTT+SLL76Qrl276n2zZs2Sxo0by/r166Vt27YOKC3MJv3KEmhdG1WTV++60dHFAQDAfMnevn37JDY2Vvz9/aVdu3YyYcIEqVmzpmzatEmys7OlW7dulueqJl51bN26dSUme5mZmXrLl5SUpG/V+dRWXvmvvZ5zmJ0rxGjiD3vk378eE0NELufk9dHz8/aotDK7QowcjRiVjPjYRoxsI0bOH6PSXtfDUAt8OqklS5ZISkqKNGzYUE6dOiXjxo2TEydOyI4dO+S7776ToUOHWiVtSuvWraVLly4yadKkEvsCqnNdS9USBgYystLdvb7RSxKzPSyPPcSQRxvlyk0RTvtRAQC4obS0NBk4cKBu7QwNDXXNZO9aCQkJEh8fL++++64EBASUO9krqmYvLi5Ozp8/X2KwSpNhL1u2TLp37y4+Pj7lPo+ZuUKMWr7xkyRlXJZ//6WVxEUESKCvt4QHVl5ZXSFGjkaMSkZ8bCNGthEj54+Ryl+qVKliM9lz+mbcgsLDw6VBgwayf/9+HdisrCydAKr9+dRo3KL6+BXk5+ent2upH5Q9flj2Oo+ZOVuMfvrjjOw4kdecn3plLr061UIlNjzAYWVythg5I2JUMuJjGzGyjRg5b4xKe02nnnrlWqpJ98CBA1K9enVp2bKl/k+uWLHCcnzPnj1y9OhR3bcPKK2EtCx57NNN8u6yvXrLyTXE00MkxN+l/hYCAKBITv1t9vzzz0ufPn100+3JkydlzJgx4uXlJQ899JCeamXYsGEyevRoiYyM1NWXzzzzjE70GImLsriYmqUTPF8vT7mvVQ29r1V8hIT485csAMD1OXWyd/z4cZ3YXbhwQapWrSodO3bU06qo+8rkyZPF09NTT6as+uD17NlTpk+f7uhiw0Wo7qq5hkhqZl6zbUSQj7x5b1NHFwsAAPdJ9ubOnVvicTUdy7Rp0/QGlMWl1Cy5a+oaOZGQbtkXwETJAAATcqk+e4C97DiZaJXoKe3rVXFYeQAAcMuaPaCipF8ZcdusRpjMGdpaPD08JKwSp1cBAKCykOzBbWRk58hn647IhdQs2X82Re9TI24jgnwdXTQAACoMyR7cxo+7zsgbi3db7YsIJNEDAJgbyR7cxoWUvFVT6lQNkq4Nq4mPt6c80CrO0cUCAKBCkezB9LIu50rm5RxJTM9bMLplzQh59a4bHV0sAAAqBckeTO3AuRS5Z9paSc64bNkX6MsUKwAA98HUKzC1rUcTrBI9X29P6cAUKwAAN0LNHkwtPTtvipVujaNl2qAW4uXhId5e/I0DAHAfJHswndxcQz5Zc0iOXUqT3aeS9L5gPy/x86b5FgDgfkj2YDpbjiUUmmKlSrCfw8oDAIAjkezBdBLSsvRtdKifDGgVJ/6+XnJ/S6ZYAQC4J5I9mMblnFy5mJolp5My9ONaUUEyukdDRxcLAACHItmDKeTkGvKnKb9YlkFTAphiBQAAkj2Yw4XUTEui5+XpIT5eHtKzSYyjiwUAgMOR7MEUMrJyLRMm7xr/J0cXBwAAp0GyB5e2/uAF+WbzcUlIy1sKLcCHplsAAAoi2YNLG//dLtl1ZS49pWoIU6wAAFAQyR5cWmJ6Xo3eI+3ipXp4gHRtVM3RRQIAwKmQ7MElnU3KkLSsHEnNylv3dmCbeGkYE+LoYgEA4HRI9uBy/rPpuDw3f5vVPn8f1rsFAKAoJHtwOb8fT9C3vl6e4uftKc3iwqRGRKCjiwUAgFMi2YPLUc23ysju9eXp2+s5ujgAADg1kj24hNxcQ97+cY8cvZAmW45e0vuYZgUAANtcqqPTxIkTxcPDQ0aOHGnZl5GRIcOHD5eoqCgJDg6W/v37y5kzZxxaTtjf7ycS5cNVB2TR9lNyMjFv7duYUH9HFwsAAKfnMsnehg0b5KOPPpJmzZpZ7R81apR89913Mn/+fFm9erWcPHlS+vXr57ByomIkXZlipXqYv4zv20Q+GNhCut8Y7ehiAQDg9Fwi2UtJSZFBgwbJjBkzJCIiwrI/MTFRPvnkE3n33Xela9eu0rJlS5k1a5b873//k/Xr1zu0zLCP7Jxc2XUySfaeSdaPY8MDZHC7WnJXs1jx9nKJty8AAA7lEn32VDNt7969pVu3bvKPf/zDsn/Tpk2SnZ2t9+dr1KiR1KxZU9atWydt27Yt8nyZmZl6y5eUlLcCgzqX2sor/7XXcw6zK2uMHvtss6zae97y2M/bw/Tx5X1kGzEqGfGxjRjZRoycP0alva7TJ3tz586VzZs362bca50+fVp8fX0lPDzcan90dLQ+VpwJEybIuHHjCu3/8ccfJTDw+qfwWLZs2XWfw+xKG6Oth9UgDA8J8jbE11Okrsc5Wbx4sbgD3ke2EaOSER/biJFtxMh5Y5SWlub6yd6xY8fk2Wef1UH097dfZ/yXX35ZRo8ebVWzFxcXJz169JDQ0NDryrBVWbt37y4+Pj52Kq25lDVGr2/9SUQuy3+e7ih1qwaJO+B9ZBsxKhnxsY0Y2UaMnD9G+S2TLp3sqWbas2fPyi233GLZl5OTIz///LN88MEHsnTpUsnKypKEhASr2j01GjcmJqbY8/r5+entWuoHZY8flr3OY2YlxehSapb8fdEufZuckbccWkign9vFlPeRbcSoZMTHNmJkGzFy3hiV9ppOnezdcccdsn37dqt9Q4cO1f3yXnzxRV0bp/6jK1as0FOuKHv27JGjR49Ku3btHFRqXK+lO0/LN5tPWB6r+fQiAvlFAwBAeTh1shcSEiI33XST1b6goCA9p17+/mHDhukm2cjISN0E+8wzz+hEr7jBGXB++bV5LeMj5MFb4+TG2FAJ9HXqtyoAAE7L5b9BJ0+eLJ6enrpmT42w7dmzp0yfPt3RxUI5ZGTnyM6TiXLgXIp+3CA6RO5vFefoYgEA4NJcLtlbtWqV1WM1cGPatGl6g2t78t+bZNWec5bHgb4shwYAgNslezCvfWdSLKtkRAT6Sp+bYx1dJAAAXB7JHpxGenaOvp3zl9a6CRcAAFw/kj04VE6uIa8u2CEHz6VIQlqWZfQtAACwD5I9OJRa9/bL345aHqtELzLI16FlAgDATEj24FApmZct/fRe7X2jNIwJkSA/3pYAANgL36pwiNxcQzYfvSSbjlzUj6uG+EnvZtUdXSwAAEyHZA8OMX/zCXl14S7LY/rpAQBQMUj24BCHzqfq2yrBvhIbHiBDO9RydJEAADAlkj04REZ2rr4d2CZeRndv4OjiAABgWiR7qDT/Xn9E5m04KgkJXpJinNb7WCUDAICKRbKHSjNt5X45lZghIh4ikq331YoKdHSxAAAwNZI9VJrUK9OsDKiTI906tJIqIQHSPC7c0cUCAMDUSPZQYTIv58i6Axcs/fPSsvKWQ7sx3JDbG1QVHx8fB5cQAADzI9lDhXl/xT6ZtvJAof100wMAoPKQ7KHCHL2Yrm9rRgZKtRA/ff/WWuESmLXPwSUDAMB9kOyhwqRn5fXRe/r2uvJg65r6fnZ2tixeTLIHAEBlIdmD3azdf17eWLRb99VTTiaokbciAbTbAgDgMCR7sJuvNh6TXaeSCu2vUyXYIeUBAAAke7Cj/NG2j3WqLXc0jtb3VV+9OlVJ9gAAcBSSPZTbhsMX5filNMvjYxfz7t8YGypt60Q5sGQAACAfyR7KZd+ZZLn/X+uKPBbsx/x5AAA4C5I9lMvxS3nTqoT4eUvzmldXwYgJ9ZeO9ao4sGQAAKAgkj2US3p2Xv+8xtVD5bNhbRxdHAAAUAySPdj0y75zMmbhTsm4kuApaVfu+zOtCgAATs1TnNiHH34ozZo1k9DQUL21a9dOlixZYjmekZEhw4cPl6ioKAkODpb+/fvLmTNnHFpmM1qw5aQcPJ8qJxMzLFtCWrY+1igmxNHFAwAArlqzV6NGDZk4caLUr19fDMOQOXPmSN++fWXLli3SpEkTGTVqlCxatEjmz58vYWFhMmLECOnXr5+sXbvW0UU3lfTsvJUwnuhcR+5qGmvZ7+PtIQ2qkewBAODMnDrZ69Onj9XjN954Q9f2rV+/XieCn3zyiXzxxRfStWtXfXzWrFnSuHFjfbxt27YOKrXrys7JlR93npFLaVlW+w+eS9W3dasGS9MaYQ4qHQAAMF2yV1BOTo6uwUtNTdXNuZs2bdLrrHbr1s3ynEaNGknNmjVl3bp1JSZ7mZmZesuXlJS36oM6n9rKK/+113MOR/rvtlPy3Nfbiz0e6O1x3f83V49RZSBGthGjkhEf24iRbcTI+WNU2ut6GKp91Ilt375dJ3eqf57ql6dq8u688059O3ToUKukTWndurV06dJFJk2aVOw5x44dK+PGjSu0X50zMDBQ3NXyEx7y3VEvifQzpEaQ9dsizEekT3yu+DEeAwAAp5CWliYDBw6UxMREPbbBZWv2GjZsKFu3btX/ka+//loeeeQRWb169XWd8+WXX5bRo0db1ezFxcVJjx49SgxWaTLsZcuWSffu3cXHx/UmFt63Yr/I0YPS6+aaMrZP4wq5hqvHqDIQI9uIUcmIj23EyDZi5Pwxym+ZtMXpkz1fX1+pV6+evt+yZUvZsGGDTJkyRQYMGCBZWVmSkJAg4eFXJ/VVo3FjYmJKPKefn5/erqV+UPb4YdnrPBVl+a4z8uqCHZJx+epUKkr6lbVtg/wrvvzOHiNnQIxsI0YlIz62ESPbiJHzxqi013TqqVeKkpubq5tuVeKn/pMrVqywHNuzZ48cPXpUN/uieIu3n5LTSXnTpxTcMi/n6uM33cAgDAAAzMKpa/ZUc2uvXr30oIvk5GTdp27VqlWydOlSPdXKsGHDdHNsZGSkbn595plndKLHSNzSrX7x1zvqy903X51KRQn285aYMH8HlQwAALhVsnf27FkZPHiwnDp1Sid3aoJlleiptnFl8uTJ4unpqSdTVrV9PXv2lOnTpzu62E5BjbtZsuO0nLiyhm1B+86m6NuakYFSr1qwA0oHAAAqi1Mne2oevZL4+/vLtGnT9AZr208kytOfby7xOaH+Tv3jBwAAdsC3vUmdScqbkiYyyFdub1C10PGqoX5yWxH7AQCAuZDsmbxfXsPoEHl3QHNHFwcAADgIyZ4LO3Q+VQbNWC/nU62XN1Nyc/MmRQ7wZRZkAADcGcmeC1t/8IKcTMwo8Tkt4yMqrTwAAMD5kOy5sPxJkLs1jpbxfZsUOu7j5SlVQwpPHg0AANwHyZ6T+/XgBVl/8GKRxzYcztsfFeQrseEBlVwyAADgCkj2nHyuvEfnbJTkzMslPi88kGVsAABA0Uj2nFhGdq4l0Xvw1jjx8vQo9JxAXy8Z0qGWA0oHAABcAcmeE9Te5VwZOXut5Mxsy/037m1aZLIHAABQEpI9B8rOyZV7pq2VnSeTSnyer7cniR4AACgXz/K9DPag1q21legpHepGVUp5AACA+VCz5wSrXKjRtCue61zs88ICGIABAADKh2SvkkxetlcyLucld/nOXVm/NtDPS8IDfR1UMgAAYGYke5Vk1tpDkpRR9BQqkUFMfAwAACoGyV4leaR9Lcm40mxbkKeHh9zVLNYhZQIAAOZHsldJnuvR0NFFAAAAbojRuAAAACZGsgcAAGBiJHsAAAAmRrIHAABgYiR7AAAAJkayBwAAYGIkewAAACZGsgcAAGBiJHsAAAAmRrIHAABgYiyXJiKGYejbpKSk6zpPdna2pKWl6fP4+PjYqXTmQoxsI0a2EaOSER/biJFtxMj5Y5Sft+TnMcUh2ROR5ORkfRsXF+foogAAAJQ5jwkLCyv2uIdhKx10A7m5uXLy5EkJCQkRDw+P68qwVcJ47NgxCQ0NtWsZzYIY2UaMbCNGJSM+thEj24iR88dIpXAq0YuNjRVPz+J75lGzpzouenpKjRo17HY+9QPng1EyYmQbMbKNGJWM+NhGjGwjRs4do5Jq9PIxQAMAAMDESPYAAABMjGTPjvz8/GTMmDH6FkUjRrYRI9uIUcmIj23EyDZiZJ4YMUADAADAxKjZAwAAMDGSPQAAABMj2QMAADAxkj0AAAATM22yN2HCBLn11lv1qhjVqlWTe+65R/bs2WP1nCeeeELq1q0rAQEBUrVqVenbt6/88ccfJZ5XjWd5/fXXpXr16vp13bp1k3379lk9Z+/evfpcVapU0ZMsduzYUVauXFnieb/55htp1aqVhIeHS1BQkDRv3lw+++yzMl/bzDEqaO7cuXq1E1Xmsl7bzDGaPXu2jkvBzd/fv8zXdpUYbd68Wbp3764/N1FRUfL4449LSkqKzTLPnz9fGjVqpGPTtGlTWbx4cZmvbeYY7dy5U/r37y+1atXS76H33nuvyOdNmzZNP0fFsU2bNvLbb7+Ju8RoxowZ0qlTJ4mIiNCbOu+1/397vo9cLT7u9p22uZy/iyrtO80wqZ49exqzZs0yduzYYWzdutW48847jZo1axopKSmW53z00UfG6tWrjUOHDhmbNm0y+vTpY8TFxRmXL18u9rwTJ040wsLCjAULFhjbtm0z7r77bqN27dpGenq65Tn169fX11PH9+7dazz99NNGYGCgcerUqWLPu3LlSuObb74xdu3aZezfv9947733DC8vL+OHH34o07XNHKN8qiw33HCD0alTJ6Nv375lvraZY6TKGhoaqp+Tv50+fdqUMTpx4oQRERFhPPnkk8Yff/xh/Pbbb0b79u2N/v37l1jetWvX6s/WW2+9pT9vr776quHj42Ns376dGF2hnvf8888bX375pRETE2NMnjy50HPmzp1r+Pr6GjNnzjR27txpPPbYY0Z4eLhx5swZt4jRwIEDjWnTphlbtmwxdu/ebQwZMkRf5/jx46W+tpnj407faSfKGaPK/E4zbbJ3rbNnz6opZvQPuTgqkOo56o1ZlNzcXP2L7+2337bsS0hIMPz8/PQvReXcuXP6HD///LPlOUlJSXrfsmXLylTmFi1a6C+i0l7bHWKkPpDqQ/R///d/xiOPPGL1wSBGecme+sVQHDPFSP3SrlatmpGTk2N5zu+//67Pu2/fvmKv/cADDxi9e/e22temTRvjiSeeKPW1zR6jguLj44tM9lq3bm0MHz7c8lhdIzY21pgwYYLhbjHK/90UEhJizJkzp9TXdqf4mPk77aPriFFlfaeZthn3WomJifo2MjKyyOOpqakya9YsqV27tl7UuCiHDh2S06dP62rUgmvSqeaLdevW6ceq+rZhw4by6aef6nNevnxZPvroI12l3LJlS8vrVNPH2LFji7yOSsJXrFihq59vu+22Ul/bHWI0fvx4/bxhw4aV69ruECPVdBAfH6+vr5ooVJNcWa7tKjHKzMwUX19fq8W/VTOHsmbNmmJjpF5f8LxKz549LeclRrZlZWXJpk2brK6trqEeu2uM0tLSJDs721Lein4fuVJ8zP6dlnkdMaqs7zS3SPZyc3Nl5MiR0qFDB7npppusjk2fPl2Cg4P1tmTJElm2bJn+oRVFBV2Jjo622q8e5x9Tbe7Lly+XLVu26H4Dqi/Lu+++Kz/88IPu15FP9RlQfbGufWOqcqjr9+7dW6ZOnar7AJT22maPkfrQfPLJJ7qvTHmvbfYYqQRx5syZsnDhQvn3v/+ty9y+fXs5fvx4qa/tKjHq2rWrvv/222/r5OPSpUvy0ksv6WOnTp0qNkbqNSWdlxjZdv78ecnJySFGBbz44osSGxtr+WKuyPeRq8THXb7TupYzRpX5neYWyd7w4cNlx44dugPktQYNGqS/UFevXi0NGjSQBx54QDIyMsp9LfUXjLqeytR/+eUX3WFXdbjs06eP1Q9d/ZUzYsQIq9eqL/WtW7fKhg0b5I033pDRo0fLqlWrpDI4e4ySk5Pl4Ycf1h+KsnwpuVOMlHbt2sngwYN1Z+jOnTvrTtKqE7KqFTRbjJo0aSJz5syRd955RwIDAyUmJkb/ha5+ERb8C7uoz5ojESPzxWjixIm6rN9++22hAVHuHB93+U5rUo4YVfp3mmFyqk9JjRo1jIMHD9p8bmZmpu4A/8UXXxR5/MCBA7oNXnXILei2224z/vrXv+r7y5cvNzw9PY3ExESr59SrV6/MfVmGDRtm9OjRo9TXNnOM1PnUeVUH3/zNw8NDb+q+6m/h7jEqzn333Wc8+OCDpb62q8SoIDUIJTk5WXfEVnH76quvir226ox9bR+0119/3WjWrFm5rm3GGNnqs6fKpj533377rdX+wYMH6w7k7hQj1Z9K9ZPdsGHDdV3brPFxh++08sSosr/TTFuzp2pGVAat/tL66aefdJZdmteoTbW/F0WdQ2XsKjvPl5SUJL/++quuUcnvt6EUzObzH6tq5bJQz88vS2mubeYYqWkytm/frv9KzN/uvvtu6dKli76v+lu4e4yKopraVNzUsP3SXttVYlSQ+gtaNcnMmzdP16zkNxUVRb2+4HkV1YyTf15iZJtq8lJ9RwteW70v1WN3itFbb70lf//733X3CjXNyPVc24zxcZfvtPLEqNK/0wyTeuqpp/RfW6tWrbKahiItLU0fVxnzm2++aWzcuNE4cuSIno5BDcGOjIwsceoANQxaTS+wcOFCPdpGjZwpOAxajaKMiooy+vXrp4d+79mzR09hoKZ2UI/zde3a1Zg6darlsSrLjz/+qMulhqr/85//NLy9vY0ZM2aU+tpmj9G1rh25RIwMY9y4ccbSpUt1udS0AqpGz9/fX0+NYbYYKer/rv6fKj4ffPCBERAQYEyZMsXqPNfGSF1ffbbUZ0xNmTFmzJgip15x5xip2g5Vm6C26tWr6/eeul9wZKGaekWNCpw9e7b+nfX444/rslw71Y9ZY6TOq6ae+frrr63Kq2p1ynJts8bHnb7TyhujyvxOM22yp/LYojY1NUX+vDi9evXSw6XVL3pV5avmTVJz5JREDYV+7bXXjOjoaP2L7o477tA/3IJUdb6qqlZvIDUUv23btsbixYsLNY2oL5l8r7zyim6iU1/Mar6edu3a6V+mZb22mWNUmg+Gu8do5MiRel4p9SWkzq3mmdq8ebNpY/Twww/r+Kj/r2qG/fTTTwudp6j3kWpaadCggX5dkyZNjEWLFhGjAjFS834VVd7OnTtbvU59ceW/39RULOvXr3ebGKnHRZW34HPs+T5ytfi423faw+X8XVRZ32ke6p+y1QUCAADAVZi2zx4AAABI9gAAAEyNZA8AAMDESPYAAABMjGQPAADAxEj2AAAATIxkDwAAwMRI9gAAAEyMZA8ACli1apV4eHhIQkKCQ66v1sFs3LixXtfYFrUma/Pmzcu87jYA90KyB8Bt3X777TJy5Eirfe3bt5dTp05JWFiYQ8r0wgsvyKuvvipeXl42n/unP/1JfHx85PPPP6+UsgFwTSR7AFCAr6+vxMTE6Nq9yrZmzRo5cOCA9O/fv9SvGTJkiLz//vsVWi4Aro1kD4BbUknS6tWrZcqUKTqxU9vhw4cLNePOnj1bwsPD5fvvv5eGDRtKYGCg3HfffZKWliZz5syRWrVqSUREhPz1r3+1anrNzMyU559/Xm644QYJCgqSNm3a6HOXZO7cudK9e3fx9/e37Nu2bZt06dJFQkJCJDQ0VFq2bCkbN260HO/Tp49+rJJEACiKd5F7AcDkVJK3d+9euemmm2T8+PF6X9WqVXXCdy2V2KnaM5WMJScnS79+/eTee+/VSeDixYvl4MGDujauQ4cOMmDAAP2aESNGyK5du/RrYmNj5dtvv9XNrtu3b5f69esXWaZffvlFBg4caLVv0KBB0qJFC/nwww910+7WrVt1022+mjVrSnR0tH5t3bp17RwlAGZAsgfALak+earJVtXUqWbbkmRnZ+tkKz+ZUjV7n332mZw5c0aCg4Plxhtv1LVvK1eu1Mne0aNHZdasWfpWJXqKquVTAyrU/jfffLPI6xw5csTy/HzqHH/729+kUaNG+nFRiaJ6jXotABSFZA8AbFAJYcFaM1WTpppvVaJXcN/Zs2f1fVV7p5p0GzRoYHUe1bQbFRVV7HXS09OtmnCV0aNHy6OPPqqTy27dusn9999fqAYvICBA1z4CQFFI9gDAhoLNporq01fUvvwpUFJSUnST66ZNmwqNqi2YIF6rSpUqcunSJat9Y8eO1U27ixYtkiVLlsiYMWN007BqRs538eJF3QQNAEUh2QPgtlQzbmnmsysr1cdOnVfV9HXq1KlMr1P9/K6lagjVNmrUKHnooYd0U3B+speRkaEHZ6jXAkBRGI0LwG2ppthff/1VD8o4f/683SYnVomZGlgxePBg+eabb+TQoUPy22+/yYQJE3QNXXF69uypp18p2KyrBnqoUbyqT97atWtlw4YNetLlfOvXrxc/Pz9p166dXcoOwHxI9gC4LTVoQjWzqgEWqhlUDYawF1X7ppK95557Tk/Zcs899+hETY2eLY5KEHfu3Cl79uzRj1XZLly4oM+jEsgHHnhAevXqJePGjbO85ssvv9SvU/0KAaAoHoZhGEUeAQBUOjXyNikpST766CObz1W1kSqRVPPs1a5du1LKB8D1ULMHAE7klVdekfj4+FI1Kavm5+nTp5PoASgRNXsAAAAmRs0eAACAiZHsAQAAmBjJHgAAgImR7AEAAJgYyR4AAICJkewBAACYGMkeAACAiZHsAQAAmBjJHgAAgJjX/wNQuIUdFCSMUQAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 700x1680 with 4 Axes>"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["combined_fig = curve_all_data(df_charging_data)\n", "combined_fig.figure"]}, {"cell_type": "code", "execution_count": null, "id": "f67317f0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}