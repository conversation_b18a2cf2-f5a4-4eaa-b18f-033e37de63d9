#############################################################################
# Copyright (C) 2023 AUTEL Netherlands B.V. - All Rights Reserved
# Unauthorized copying of this file, via any medium is strictly prohibited
#
# Proprietary and confidential
#############################################################################
import os
import re
import sys
import pickle
import json
import numpy as np
from abc import ABC, abstractmethod
from sklearn.preprocessing import MinMaxScaler, LabelEncoder
from sklearn.metrics import confusion_matrix
from sklearn.metrics import recall_score, precision_score, f1_score, accuracy_score
from sklearn.neighbors import KDTree
from sklearn.tree import export_text
sys.path.append("../")
from application.algorithm.vehicle_identification.utils import NpEncoder, analyze_label_distribution, caculate_fpr_and_fnr, test_dataset_split, predict_error_analysis

class Classifier(ABC):
    def __init__(self):
        self._model = None
        self._encoder = None
        self._coms = None
        self._save_dir = None
        self._config = None
        self._scaler = None

    @abstractmethod
    def _algorithm(self):
        pass

    def group_similar_records(self, features_ori, features, labels):
        tree = KDTree(features)
        groups = []
        mapping, mapping_single, mapping_multi = {}, {}, {}
        for feature, label in zip(features, labels):
            idx = tree.query_radius([feature], 0.0001) # 0.04
            group = ','.join(np.unique(labels[idx[0]]))
            groups.append(group)
            if group not in mapping:
                mapping[group] = {}
            number = int(feature[0]) # MAC
            if number not in mapping[group]:
                mapping[group][number] = idx[0]
            else:
                mapping[group][number] = np.unique(np.append(mapping[group][number], idx[0]))

            if len(group.split(',')) == 1:
                if group not in mapping_single.keys():
                    mapping_single[group] = set()
                for item in features_ori[idx[0]]:
                    mapping_single[group].add(tuple(item))
            else:
                if group not in mapping_multi.keys():
                    mapping_multi[group] = set()
                for item in features_ori[idx[0]]:
                    mapping_multi[group].add(tuple(item))

        analyze_label_distribution(self._config, self._save_dir, mapping_single, mapping_multi)
        return features, groups, mapping

    def feature_transform(self, features):
        values = features[:, 1:5].astype(np.float32)
        normed = self._scaler.transform(values)
        header = np.array([[int(addr[0][:6].upper(), 16)] for addr in features[:, 0:1]], dtype=np.float32)
        features = np.append(header, normed, axis=1)
        return features

    def group_label_mapping(self, labels):
        mapping = {}
        for c in self._encoder.classes_:
            for v in c.split(','):
                mapping[v] = c if v not in mapping.keys() else (';').join([mapping[v], c])
        return np.array([mapping[x] for x in labels])

    def compute_center_of_mass(self, features, mapping):
        coms = [None] * len(mapping)
        for g, ins in mapping.items():
            coms[self._encoder.transform([g])[0]] = {
                k: features[v][:, 1:5] for k, v in ins.items()
            }       
        return coms

    def train(self, features_ori, labels, save_dir, config):
        self._save_dir = save_dir
        self._config = config
        
        features = features_ori.copy()
        self._scaler = MinMaxScaler(feature_range=(0, 1)).fit(features[:, 1:5])
        features = self.feature_transform(features)

        features, labels, mapping = self.group_similar_records(features_ori, features, labels)
        self._encoder = LabelEncoder().fit(labels)
        labels = self._encoder.transform(labels)
        self._coms = self.compute_center_of_mass(features, mapping)
        
        self._model = self._algorithm()
        self._model.fit(features, labels)
        print(f"feature importance: {self._model.feature_importances_}")

        return self._model.score(features, labels)

    def predict(self, features):
        features = self.feature_transform(features)
        predictions = self._model.predict(features)
        checks = []
        for feature, prediction in zip(features, predictions):
            items = self._coms[prediction].items()   
            ok = any([k == feature[0] and any(np.linalg.norm(f-feature[1:5], axis=1) <= 0.06) for k, f in items]) # 0.15
            checks.append(ok)
        result = [name if ok else f"Unknown({name})" for name, ok in zip(self._encoder.inverse_transform(predictions), checks)]
        return result
        
    def test(self, features, labels, label_analysis_file, save_dir, epoch=0):
        features_copy = features.copy()
        features = self.feature_transform(features_copy)
        pred_model = self._model.predict(features)
        pred_post = [self._encoder.transform([name])[0] if "Unknown" not in name else name for name in self.predict(features_copy)]

        result = ""
        feats_error, target_error, training_error, pred_error = [], [], [], []
        feats_unknow, target_unknow, training_unknow, pred_unknow = [], [], [], []
        for pred, prefix in zip([pred_model, pred_post], ["model", "post"]):
            ground_truth = []
            for feat, ry, iy, py in zip(features_copy, labels, self.group_label_mapping(labels), pred):
                if prefix == "post" and isinstance(py, str): 
                    feats_unknow.append(feat)
                    target_unknow.append(ry)
                    training_unknow.append(iy)
                    match = re.search(r"Unknown\((.*)\)", py)
                    pred_unknow.append(match.group(1))
                    continue
                py = self._encoder.inverse_transform([py])[0]
                if ry in py:
                    ground_truth.append(py)
                else:
                    ground_truth.append(iy.split(';')[0])
                    if prefix == "post":
                        feats_error.append(feat)
                        target_error.append(ry)
                        training_error.append(iy)
                        pred_error.append(py)
            pred_filtered = list(filter(lambda item:  not isinstance(item, str), pred))
            pred_splited, gt_splited = test_dataset_split(pred_filtered, ground_truth, label_analysis_file)
            result += self.get_metric_result(f"{epoch}_{prefix}", pred_splited, gt_splited)

        error_list = [feats_error, target_error, training_error, pred_error]
        unknow_list = [feats_unknow, target_unknow, training_unknow, pred_unknow]
        save_path = os.path.join(save_dir, f"{self._config['error_analysis_file']}_epoch{epoch}.txt")
        predict_error_analysis(label_analysis_file, unknow_list, error_list, save_path)
        return result
    
    def get_metric_result(self, prefix, pred_splited, gt_splited):
        titles = [f"epoch{prefix}_ALL", f"epoch{prefix}_EU+US", f"epoch{prefix}_EU", f"epoch{prefix}_US"]
        string = "\n"
        for pred, gt, title in zip(pred_splited, gt_splited, titles): 
            string += f"-----------------------{title}-----------------------\n"     
            gt = self._encoder.transform(gt)           
            cm = confusion_matrix(gt, pred, labels=self._encoder.transform(self._encoder.classes_))
            # disp = ConfusionMatrixDisplay(confusion_matrix=cm, display_labels=self._encoder.classes_)
            # fig, ax = plt.subplots(figsize=(22, 8))
            # ax.get_xaxis().set_visible(False)
            # disp.plot(ax=ax)
            # plt.savefig('{}\\{}_{}.png'.format(self._save_dir, self.__class__.__name__, title))
            
            fpr, fnr = caculate_fpr_and_fnr(cm)
            string += '{:.4f}\t '.format(accuracy_score(gt, pred))
            string += '{:.4f}\t '.format(precision_score(gt, pred, average='weighted', zero_division=0))
            string += '{:.4f}\t '.format(recall_score(gt, pred, average='weighted', zero_division=0))
            string += '{:.4f}\t '.format(f1_score(gt, pred, average='weighted', zero_division=0))
            string += '{:.4f}\t '.format(fpr) # 误检率
            string += '{:.4f}\n '.format(fnr) # 漏检率
        
        return string 

    def save_dtree_model_data(self, epoch):
        tree_data_txt = export_text(self._model, max_depth=999, decimals=8)
        output_dict = dict()
        model_list = list()
        data_str_list = tree_data_txt.split('\n')
        for data in data_str_list:
            # get layer num
            layer_num = data.count('|')
            invalid_data = False
            # get the feature index and value
            feature_idx = -1
            feature_val = -1
            idx = data.find('feature_')
            if idx != -1:
                feature_idx = data[idx+len('feature_')]
                idx_1 = data.find('<= ')
                if idx_1 == -1:
                    idx_1 = data.find('>  ')
                feature_val = data[idx_1+3:]
            # leaf node has class info
            else:
                idx_1 = data.find('class: ')
                if idx_1 != -1:
                    feature_val = data[(idx_1+len('class: ')):]
                else:
                    invalid_data = True

            if not invalid_data:
                model_list.append([float(layer_num), float(feature_idx), float(feature_val)])
        output_dict['mode_data'] = model_list
        # labels string list
        output_dict['label_list'] = self._encoder.classes_
        # test predict data preprocessing needed
        output_dict['data_max'] = self._scaler.data_max_
        output_dict['data_min'] = self._scaler.data_min_
        output_dict['coms'] = self._coms
        with open(os.path.join(self._save_dir,'dtree_model_data_epoch{}.json'.format(epoch)), 'w') as f:
            dict_str = json.dumps(output_dict, cls=NpEncoder)
            f.write(dict_str)

    def save(self, epoch):
        save_path = os.path.join(self._save_dir, '{}_epoch{}.bin'.format(self.__class__.__name__, epoch))
        with open(save_path, 'wb') as f:
            pickle.dump(self, f)
            print(f'info: model saved to {save_path}')
        if self.__class__.__name__ == 'DecisionTreeClassifier':
            self.save_dtree_model_data(epoch)
