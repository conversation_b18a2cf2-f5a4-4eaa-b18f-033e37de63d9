#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据生成脚本
根据用户提供的 site_no 和 piles_info 动态生成测试数据插入SQL语句
"""
import os
import random
import json
from datetime import datetime
from typing import List, Dict


def generate_site_data(site_no: str) -> Dict:
    """
    生成站点基础信息数据
    
    Args:
        site_no: 站点编号
        
    Returns:
        Dict: 站点数据字典
    """
    # 预设的城市和经纬度信息
    cities = [
        {"name": "Shanghai", "region": "China/Shanghai", "lat_lng": "31.2304,121.4737"},
        {"name": "Beijing", "region": "China/Beijing", "lat_lng": "39.9042,116.4074"},
        {"name": "Shenzhen", "region": "China/Guangdong", "lat_lng": "22.3193,114.1694"},
        {"name": "Hangzhou", "region": "China/Zhejiang", "lat_lng": "30.2741,120.1551"},
        {"name": "Nanjing", "region": "China/Jiangsu", "lat_lng": "32.0603,118.7969"},
    ]
    
    city = random.choice(cities)
    
    # 电力公司名称
    grid_companies = [
        "国家电网上海公司", "国家电网北京公司", "国家电网深圳公司",
        "国家电网杭州公司", "国家电网南京公司", "南方电网广东公司"
    ]
    
    return {
        "site_no": site_no,
        "demand_limit": random.randint(200, 800),  # 月最优demand_limit值 (kW)
        "es_total_energy": random.randint(300, 1500),  # 储能电池容量 (kWh)
        "region": city["region"],
        "lat_and_lng": city["lat_lng"],
        "site_grid_limit": random.randint(400, 1200),  # 场站电网功率限制 (kW)
        "grid_reverse": random.choice([0, 1]),  # 电网能否逆流 (1:可以, 0:不可以)
        "grid_name": random.choice(grid_companies),
        "pv_can_control": random.choice([0, 1]),  # 光伏是否可调 (1:可调, 0:不可调)
        "is_ai_active": 1,  # 是否开启AI能力 (1:start, 0:stop)
        "purchase_price_type": random.choice([1, 2, 3]),  # 购买电价类型 (1:固定电价, 2:分时电价, 3:动态电价)
    }


def generate_pile_data(pile_info: Dict, site_no: str) -> Dict:
    """
    根据充电桩信息生成完整的充电桩数据
    
    Args:
        pile_info: 充电桩信息 {"pile_sn": "pile_01", "gun_num": 4}
        site_no: 站点编号
        
    Returns:
        Dict: 充电桩数据字典
    """
    pile_sn = pile_info["pile_sn"]
    gun_num = pile_info["gun_num"]
    
    # 根据充电枪数量确定充电桩类型和额定功率
    if gun_num >= 4:
        # 大功率直流快充桩
        pile_type = "DC_FAST"
        # 每个充电枪约30kW，但总功率不超过120kW
        rated_power = min(gun_num * 30, 120)
    elif gun_num >= 2:
        # 中等功率直流快充桩
        pile_type = "DC_FAST"
        rated_power = gun_num * 20
    else:
        # 交流慢充桩
        pile_type = "AC_SLOW"
        rated_power = gun_num * 3.5
    
    return {
        "pile_sn": pile_sn,
        "gun_num": gun_num,
        "pile_type": pile_type,
        "rated_power": rated_power,
        "site_no": site_no,
    }


def generate_insert_sql(site_no: str, piles_info: List[Dict]) -> str:
    """
    生成完整的INSERT SQL语句
    
    Args:
        site_no: 站点编号
        piles_info: 充电桩信息列表 [{"pile_sn": "pile_01", "gun_num": 4}, ...]
        
    Returns:
        str: 完整的INSERT SQL语句
    """
    # 生成站点数据
    site_data = generate_site_data(site_no)
    
    # 生成SQL语句
    sql_parts = []
    
    # 1. 插入站点信息
    site_sql = f"""-- 插入站点基础信息 (SiteDB)
INSERT INTO `Site` (
    `site_no`, 
    `demand_limit`, 
    `es_total_energy`, 
    `region`, 
    `lat_and_lng`, 
    `site_grid_limit`, 
    `grid_reverse`, 
    `grid_name`, 
    `pv_can_control`, 
    `is_ai_active`, 
    `purchase_price_type`, 
    `created_at`, 
    `updated_at`
) VALUES (
    '{site_data['site_no']}',                    -- site_no: 站点编号
    {site_data['demand_limit']},                 -- demand_limit: 月最优demand_limit值 (kW)
    {site_data['es_total_energy']},              -- es_total_energy: 储能电池容量 (kWh)
    '{site_data['region']}',                     -- region: 场站所在国家/地区
    '{site_data['lat_and_lng']}',                -- lat_and_lng: 场站的经纬度信息
    {site_data['site_grid_limit']},              -- site_grid_limit: 场站电网功率限制 (kW)
    {site_data['grid_reverse']},                 -- grid_reverse: 电网能否逆流 (1:可以, 0:不可以)
    '{site_data['grid_name']}',                  -- grid_name: 电力公司名称
    {site_data['pv_can_control']},               -- pv_can_control: 光伏是否可调 (1:可调, 0:不可调)
    {site_data['is_ai_active']},                 -- is_ai_active: 是否开启AI能力 (1:start, 0:stop)
    {site_data['purchase_price_type']},          -- purchase_price_type: 购买电价类型 (1:固定电价, 2:分时电价, 3:动态电价)
    NOW(),                                       -- created_at: 创建时间
    NOW()                                        -- updated_at: 更新时间
);"""
    
    sql_parts.append(site_sql)
    
    # 2. 插入充电桩信息
    if piles_info:
        pile_sql = """-- 插入充电桩信息 (PileDB)
INSERT INTO `Pile` (
    `pile_sn`, 
    `gun_num`, 
    `pile_type`, 
    `rated_power`, 
    `site_no`, 
    `created_at`, 
    `updated_at`
) VALUES"""
        
        pile_values = []
        for pile_info in piles_info:
            pile_data = generate_pile_data(pile_info, site_no)
            pile_value = f"""(
    '{pile_data['pile_sn']}',                     -- pile_sn: 充电桩序列号
    {pile_data['gun_num']},                       -- gun_num: 充电枪数量
    '{pile_data['pile_type']}',                   -- pile_type: 充电桩类型
    {pile_data['rated_power']:.2f},               -- rated_power: 额定功率 (kW)
    '{pile_data['site_no']}',                     -- site_no: 所属站点编号
    NOW(),                                        -- created_at: 创建时间
    NOW()                                         -- updated_at: 更新时间
)"""
            pile_values.append(pile_value)
        
        pile_sql += "\n" + ",\n".join(pile_values) + ";"
        sql_parts.append(pile_sql)
    
    # 3. 添加验证查询
    verify_sql = f"""
-- 验证插入的数据
-- 验证站点信息
SELECT 'SiteDB 数据验证:' as info;
SELECT 
    site_no,
    demand_limit,
    es_total_energy,
    region,
    lat_and_lng,
    site_grid_limit,
    grid_reverse,
    grid_name,
    pv_can_control,
    is_ai_active,
    purchase_price_type
FROM `Site` WHERE site_no = '{site_no}';

-- 验证充电桩信息
SELECT 'PileDB 数据验证:' as info;
SELECT 
    pile_sn,
    gun_num,
    pile_type,
    rated_power,
    site_no
FROM `Pile` WHERE site_no = '{site_no}';

-- 验证站点和充电桩关联关系
SELECT '站点充电桩统计:' as info;
SELECT 
    s.site_no,
    COUNT(p.pile_sn) as pile_count,
    SUM(p.gun_num) as total_guns,
    MAX(p.rated_power) as max_power
FROM `Site` s
LEFT JOIN `Pile` p ON s.site_no = p.site_no
WHERE s.site_no = '{site_no}'
GROUP BY s.site_no;"""
    
    sql_parts.append(verify_sql)
    
    return "\n\n".join(sql_parts)


def main(site_no: str):
    """
    主函数 - 示例用法
    """
    # 示例参数
    piles_info = [
        {"pile_sn": "pile_01", "gun_num": 4},
        {"pile_sn": "pile_02", "gun_num": 2},
        {"pile_sn": "pile_03", "gun_num": 7},
        {"pile_sn": "pile_04", "gun_num": 2},
        {"pile_sn": "pile_05", "gun_num": 6},
        {"pile_sn": "pile_06", "gun_num": 2},
    ]
    
    print("=" * 60)
    print("测试数据生成脚本")
    print("=" * 60)
    print(f"站点编号: {site_no}")
    print(f"充电桩信息: {json.dumps(piles_info, ensure_ascii=False, indent=2)}")
    print("\n" + "=" * 60)
    
    # 生成SQL语句
    sql = generate_insert_sql(site_no, piles_info)
    
    # 输出SQL语句
    print(sql)

    cur_dir = os.path.dirname(os.path.abspath(__file__))
    # 保存到文件
    filename = os.path.join(cur_dir, f"long_term_load_forecast_0.sql")
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(sql)
    
    print(f"\n" + "=" * 60)
    print(f"SQL语句已保存到文件: {filename}")
    print("=" * 60)


if __name__ == "__main__":
    main("SITE001")
