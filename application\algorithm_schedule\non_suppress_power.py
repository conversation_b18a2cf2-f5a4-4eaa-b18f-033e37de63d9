from datetime import datetime, timedelta
from datetime import timezone
from typing import List, Dict, Optional

import numpy as np
from scipy.interpolate import interp1d

from application.db_operate.db_operate import DBOperate
from application.db_operate.models import EVModelDB
from application.utils.logger import setup_logger
from application.algorithm.power_predict.predict import predict

logger = setup_logger("non_suppress_power", direction="algorithm_schedule")


class NonSuppressPowerPrediction:
    """
    非压制功率预测类 - 基于曲线拟合的场站功率叠加算法
    
    核心功能：
    将多个车辆的个体充电功率曲线叠加为场站的总功率需求曲线
    
    算法架构：
    1. 单车功率曲线建模：基于SOC和车型参数生成功率衰减曲线
    2. 多车并行处理：获取所有充电车辆数据并生成预测
    3. 曲线拟合叠加：使用线性插值将离散数据转换为连续函数进行叠加
    
    技术特性：
    - 支持车辆不同开始时间的充电场景
    - 使用SciPy线性插值进行曲线拟合
    - 60秒时间分辨率，最大预测8小时
    - 完善的异常处理和数据验证
    
    数学模型：
    P_total(t) = Σ f_i(t - t_start_i) × I_i(t)
    其中f_i为车辆i的拟合函数，t_start_i为开始时间，I_i为充电状态
    """

    def __init__(self, site_no: str):
        """
        初始化非压制功率预测器
        
        Args:
            site_no: 场站编号
        """
        self.site_no = site_no
        self.db_operate = DBOperate()
        logger.info(f"Initialized NonSuppressPowerPrediction for site: {site_no}")

    def predict_power_for_vehicle(self, local_id: str, charging_records: List, ev_model: EVModelDB, start_soc: float, non_suppress_rated_power: float, max_curr: float) -> Optional[Dict]:
        """
        为单个车辆预测从当前时刻到充电结束的非压制功率曲线
        
        Args:
            local_id: 充电会话ID
            charging_records: 充电记录列表
            ev_model: 车型信息
            start_soc: 起始SOC
            non_suppress_rated_power: 非压制下的总额定功率, 单位：kW
            max_curr: 枪线最大电流, 单位：A

        Returns:
            Dict: 包含预测功率曲线的字典，格式：
            {
                "curve_start_time": datetime,  # 预测开始时间
                "curve_end_time": datetime,    # 预计充电结束时间
                "time_list": List[int],        # 相对时间（秒）
                "power_list": List[float]      # 功率值（kW）
            }
        """
        try:
            if not charging_records:
                logger.warning(f"No charging records found for local_id: {local_id}")
                return None

            # 获取当前时间作为预测起始时间
            from datetime import timezone
            current_time = datetime.now(timezone.utc)

            # 获取最新的充电数据
            latest_record = charging_records[-1]                                                              # 记录已按时间升序排列
            current_power = float(latest_record.vol_output) * float(latest_record.curr_output) / 1000         # 单位：kW
            current_voltage = float(latest_record.vol_output)                                                 # 单位：V
            curr_list_of_1min = list()                                                                        # 单位：A
            for charging_record in charging_records:
                curr_list_of_1min.append(float(charging_record.curr_demand))

            # 调用非压制功率预测算法进行预测
            # 记录predict函数调用前的入参
            logger.info(f"Calling predict function for local_id {local_id} with parameters:")
            logger.info(f"  - vehicle_label: {ev_model.vehicle_label}")
            logger.info(f"  - start_soc: {int(start_soc)}")
            logger.info(f"  - current_power: {current_power}")
            logger.info(f"  - current_voltage: {current_voltage}")
            logger.info(f"  - curr_list_of_1min: {curr_list_of_1min}")
            logger.info(f"  - non_suppress_rated_power: {non_suppress_rated_power}")
            logger.info(f"  - max_curr: {max_curr}")
            
            time_power_predict = predict(ev_model.vehicle_label, int(start_soc), current_power, current_voltage, curr_list_of_1min, non_suppress_rated_power, max_curr)
            
            if not time_power_predict:
                logger.warning(f"Failed to predict power for local_id: {local_id}")
                return None
            
            # 记录predict函数调用后的返回值
            if time_power_predict:
                logger.info(f"predict function returned successfully for local_id {local_id} :")
                logger.info(f"  - result type: {type(time_power_predict)}")
                logger.info(f"  - result length: {len(time_power_predict)}")
                logger.info(f"  - first few data points: {time_power_predict[:3] if len(time_power_predict) >= 3 else time_power_predict}")
                if len(time_power_predict) > 3:
                    logger.info(f"  - last few data points: {time_power_predict[-3:]}")
            
            # 初始化列表
            time_list = []
            power_list = []

            # 分离预测结果
            for time, power in time_power_predict:
                time_list.append(int(time))
                power_list.append(power)

            curve_end_time = current_time + timedelta(seconds=time_list[-1] if time_list else 0)

            return {
                "curve_start_time": current_time,
                "curve_end_time": curve_end_time,
                "time_list": time_list,
                "power_list": power_list
            }

        except Exception as e:
            logger.error(f"Error predicting power for local_id {local_id}: {e}")
            return None

    def run_prediction(self) -> Dict:
        """
        运行非压制功率预测主流程
        
        执行步骤：
        1. 查询场站充电会话
        2. 为每个车辆生成功率预测曲线  
        3. 使用曲线拟合算法叠加为场站总需求
        4. 保存预测结果
        
        Returns:
            Dict: 预测结果统计
        """
        try:
            logger.info(f"Starting non-suppress power prediction for site: {self.site_no}")

            # 1. 获取所有正在充电的会话
            charging_sessions = self.db_operate.get_charging_sessions_by_site(self.site_no)

            if not charging_sessions:
                logger.info(f"No charging sessions found in site {self.site_no}")
                return {
                    "site_no": self.site_no,
                    "total_sessions": 0,
                    "successful_predictions": 0,
                    "failed_predictions": 0
                }

            # 2. 为每个充电会话进行预测，同时缓存预测结果到内存
            successful_count = 0
            failed_count = 0
            vehicle_predictions_cache = []  # 内存缓存，存储所有成功的预测结果

            for session in charging_sessions:
                try:
                    local_id = session.local_id

                    logger.info(f"Start to predict non suppress power for local_id {local_id} in site {self.site_no}")

                    # 获取充电记录（充电时间不足60秒，不进行预测）
                    charging_records = self.db_operate.get_last_n_seconds_charging_records(local_id, seconds=60)
                    if not charging_records:
                        logger.warning(f"No charging records found for local_id: {local_id}")
                        failed_count += 1
                        continue

                    # 获取车型信息
                    ev_model = self.db_operate.get_ev_model_by_local_id(local_id)
                    if not ev_model:
                        logger.warning(f"No EV model found for local_id: {local_id}")
                        failed_count += 1
                        continue

                    # 获取起始soc
                    start_soc = self.db_operate.get_start_soc_by_local_id(local_id)
                    if not start_soc:
                        logger.warning(f"No start SOC found for local_id: {local_id}")
                        failed_count += 1
                        continue

                    # 获取非压制下的总额定功率
                    non_suppress_rated_power = self.db_operate.get_non_suppress_rated_power_of_pile(local_id)
                    if not non_suppress_rated_power:
                        logger.warning(f"No non-suppress rated power found for local_id: {local_id}")
                        failed_count += 1
                        continue

                    # 获取枪线最大电流
                    max_curr = self.db_operate.get_max_curr_of_charger(local_id)
                    if not max_curr:
                        logger.warning(f"No max curr found for local_id: {local_id}")
                        failed_count += 1
                        continue

                    # 预测功率曲线
                    prediction_data = self.predict_power_for_vehicle(local_id, charging_records, ev_model, start_soc, non_suppress_rated_power, max_curr)
                    if not prediction_data:
                        logger.warning(f"Failed to predict power for local_id: {local_id}")
                        failed_count += 1
                        continue

                    # 保存到数据库
                    save_success = self.db_operate.save_non_suppress_power_prediction(local_id, prediction_data)
                    if save_success:
                        # 缓存到内存中，用于后续叠加
                        vehicle_predictions_cache.append({
                            "local_id": local_id,
                            "curve_start_time": prediction_data["curve_start_time"],
                            "curve_end_time": prediction_data["curve_end_time"],
                            "time_list": prediction_data["time_list"],
                            "power_list": prediction_data["power_list"]
                        })
                        successful_count += 1
                        logger.info(f"Successfully processed non-suppress power prediction for local_id: {local_id}")
                    else:
                        failed_count += 1

                except Exception as e:
                    logger.error(f"Error processing session {session.local_id}: {e}")
                    failed_count += 1

            # 场站功率需求曲线叠加
            site_demand_success = False
            if successful_count > 0 and vehicle_predictions_cache:
                logger.info(f"Starting site demand curve aggregation with {successful_count} predictions")
                
                aggregated_data = self.aggregate_site_demand_curve_from_cache(vehicle_predictions_cache)
                
                if aggregated_data:
                    site_demand_success = self.db_operate.save_site_demand_prediction(self.site_no, aggregated_data)
                    if site_demand_success:
                        logger.info(f"Site demand prediction saved successfully for site {self.site_no}")
                    else:
                        logger.warning(f"Failed to save site demand prediction for site {self.site_no}")
                else:
                    logger.warning(f"Failed to aggregate site demand curve for site {self.site_no}")

            result = {
                "site_no": self.site_no,
                "total_sessions": len(charging_sessions),
                "successful_predictions": successful_count,
                "failed_predictions": failed_count,
                "site_demand_aggregated": site_demand_success
            }

            logger.info(f"Non-suppress power prediction completed for site {self.site_no}: {result}")
            return result

        except Exception as e:
            logger.error(f"Error in non_suppress_power prediction for site {self.site_no}: {e}")
            return {
                "site_no": self.site_no,
                "total_sessions": 0,
                "successful_predictions": 0,
                "failed_predictions": 0,
                "error": str(e)
            }

    def aggregate_site_demand_curve_from_cache(self, vehicle_predictions_cache: List[Dict]) -> Optional[Dict]:
        """
        基于曲线拟合的场站功率需求叠加算法
        
        将多个车辆的离散功率数据转换为连续拟合函数，在统一时间轴上叠加
        
        算法步骤：
        1. 分析车辆时间范围，创建统一60秒时间轴
        2. 使用SciPy线性插值为每车辆创建拟合函数  
        3. 在时间轴上逐点计算总功率并叠加
        
        Args:
            vehicle_predictions_cache: 车辆预测数据缓存列表
            
        Returns:
            Dict: 场站总需求功率曲线数据，包含时间和功率列表
        """
        try:
            logger.info(f"Starting site demand curve aggregation for site: {self.site_no}")

            if not vehicle_predictions_cache:
                logger.warning(f"No vehicle prediction cache data for site {self.site_no}")
                return None

            logger.info(f"Processing {len(vehicle_predictions_cache)} vehicle predictions")

            # 步骤1: 确定统一时间轴
            current_time = datetime.now(timezone.utc)
            latest_end_time = current_time
            time_interval = 60  # 60秒间隔

            # 找到最晚的充电结束时间
            for cache_item in vehicle_predictions_cache:
                if cache_item["curve_end_time"] > latest_end_time:
                    latest_end_time = cache_item["curve_end_time"]

            # 创建统一时间轴
            total_duration_seconds = int((latest_end_time - current_time).total_seconds())
            unified_time_list = [i * time_interval for i in
                                 range(0, (total_duration_seconds // time_interval) + 1)]

            logger.info(f"Time axis: {total_duration_seconds / 3600:.1f} hours, {len(unified_time_list)} points")

            # 步骤2: 创建车辆功率曲线拟合函数
            vehicle_fitting_functions = []
            successful_fitting_count = 0
            
            for i, cache_item in enumerate(vehicle_predictions_cache):
                try:
                    vehicle_time_list = cache_item["time_list"]
                    vehicle_power_list = cache_item["power_list"]
                    vehicle_id = cache_item.get("local_id", f"vehicle_{i}")
                    
                    # 数据验证：确保有足够数据点
                    if len(vehicle_time_list) < 2 or len(vehicle_power_list) < 2:
                        logger.warning(f"Vehicle {vehicle_id}: insufficient data points, skipping")
                        continue
                    
                    # 数据对齐
                    if len(vehicle_time_list) != len(vehicle_power_list):
                        min_len = min(len(vehicle_time_list), len(vehicle_power_list))
                        vehicle_time_list = vehicle_time_list[:min_len]
                        vehicle_power_list = vehicle_power_list[:min_len]
                    
                    # 转换为NumPy数组
                    time_array = np.array(vehicle_time_list, dtype=float)
                    power_array = np.array(vehicle_power_list, dtype=float)
                    
                    # 创建线性插值拟合函数
                    fitting_func = interp1d(
                        time_array, power_array, 
                        kind='linear', bounds_error=False, fill_value=0.0
                    )
                    
                    vehicle_fitting_functions.append({
                        "local_id": vehicle_id,
                        "fitting_func": fitting_func,
                        "curve_start_time": cache_item["curve_start_time"],
                        "curve_end_time": cache_item["curve_end_time"],
                        "time_range": (min(time_array), max(time_array))
                    })
                    
                    successful_fitting_count += 1
                    logger.debug(f"Vehicle {vehicle_id}: fitting successful")
                    
                except Exception as e:
                    logger.error(f"Vehicle {vehicle_id} fitting failed: {e}")
                    continue

            if not vehicle_fitting_functions:
                logger.warning(f"No valid vehicle curves for site {self.site_no}")
                return None

            logger.info(f"Created {successful_fitting_count} vehicle fitting functions")

            # 步骤3: 功率叠加计算
            aggregated_power_list = []
            
            for time_index, time_offset in enumerate(unified_time_list):
                current_moment = current_time + timedelta(seconds=time_offset)
                total_power = 0.0
                
                # 计算每个车辆在当前时间点的功率贡献
                for vehicle_fit in vehicle_fitting_functions:
                    if vehicle_fit["curve_start_time"] <= current_moment <= vehicle_fit["curve_end_time"]:
                        vehicle_time_offset = (current_moment - vehicle_fit["curve_start_time"]).total_seconds()
                        
                        try:
                            vehicle_power = float(vehicle_fit["fitting_func"](vehicle_time_offset))
                            vehicle_power = max(0.0, vehicle_power)  # 确保非负
                            total_power += vehicle_power
                        except Exception as e:
                            logger.debug(f"Fitting function error for {vehicle_fit['local_id']}: {e}")
                            continue
                
                aggregated_power_list.append(round(total_power, 2))
            
            # 计算统计信息
            max_power = max(aggregated_power_list) if aggregated_power_list else 0.0
            avg_power = sum(aggregated_power_list) / len(aggregated_power_list) if aggregated_power_list else 0.0
            
            logger.info(f"Power aggregation completed: peak={max_power:.1f}kW, avg={avg_power:.1f}kW")

            # 构建结果
            result = {
                "curve_start_time": current_time,
                "curve_end_time": latest_end_time,
                "time_list": unified_time_list,
                "power_list": aggregated_power_list
            }

            logger.info(f"Site demand curve aggregation completed for site {self.site_no}")
            logger.info(f"Vehicles: {len(vehicle_fitting_functions)}/{len(vehicle_predictions_cache)}, "
                       f"Duration: {total_duration_seconds / 3600:.1f}h, Points: {len(unified_time_list)}")

            return result

        except Exception as e:
            logger.error(f"Error in site demand curve aggregation from cache for site {self.site_no}: {e}")
            return None


def non_suppress_power(site_no: str, data: dict) -> Optional[Dict]:
    """
    非压制功率预测算法调度入口
    
    Args:
        site_no: 场站编号
        data: 相关输入数据
        
    Returns:
        Dict: 预测结果统计
    """
    try:
        logger.info(f"[non_suppress_power] Starting prediction for site_no={site_no}")

        # 创建预测实例并运行
        predictor = NonSuppressPowerPrediction(site_no)
        result = predictor.run_prediction()

        logger.info(f"[non_suppress_power] Completed prediction for site_no={site_no}, result={result}")
        return result

    except Exception as e:
        logger.error(f"[non_suppress_power] Error for site_no={site_no}: {e}")
        return {
            "site_no": site_no,
            "total_sessions": 0,
            "successful_predictions": 0,
            "failed_predictions": 0,
            "error": str(e)
        }
