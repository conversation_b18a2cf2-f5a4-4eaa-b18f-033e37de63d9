import unittest
import json
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone
from decimal import Decimal

from application.algorithm_schedule.storage_dispatch import storage_dispatch
from application.db_operate.models import ESRealtimeDataDB, SiteDB, PileDB, HybridLoadPredictionDB


class TestStorageDispatch(unittest.TestCase):
    """储能调度方法的单元测试"""

    def setUp(self):
        """测试前的准备工作"""
        self.site_no = 'TEST_SITE_001'
        self.test_data = {'test': 'data'}
        self.trigger = 'test_trigger'

        # 模拟储能实时数据
        self.mock_es_realtime = Mock(spec=ESRealtimeDataDB)
        self.mock_es_realtime.es_soc = 50
        self.mock_es_realtime.es_min_soc = 20
        self.mock_es_realtime.es_max_soc = 90

        # 模拟场站数据
        self.mock_site = Mock(spec=SiteDB)
        self.mock_site.es_total_energy = 100
        self.mock_site.site_grid_limit = 1000

        # 模拟充电桩数据
        self.mock_pile = Mock(spec=PileDB)
        self.mock_pile.rated_power = Decimal('50.00')

        # 模拟融合负载预测数据
        self.mock_hybrid_load = Mock(spec=HybridLoadPredictionDB)
        self.mock_hybrid_load.power_list = json.dumps([100, 120, 110, 130] * 24)  # 96个点
        self.mock_hybrid_load.curve_start_time = datetime(2024, 1, 1, 0, 0, 0, tzinfo=timezone.utc)

        # 模拟电价数据（24个点）
        self.mock_price_list = [0.5, 0.6, 0.7, 0.8] * 6

        # 模拟需量数据
        self.mock_demand_data = [{
            "start_time": "2024-01-01 00:00:00",
            "end_time": "2024-01-01 23:59:59",
            "price": 10.0,
            "unit": "yuan",
            "total_demand_target": 500,
            "target_demand_warning_ratio": 0.9
        }]

        # 模拟光伏预测数据（96个点）
        self.mock_pv_predicted_list = [0] * 48 + [10, 20, 30, 40] * 12

        # 模拟售电价格数据（24个点）
        self.mock_electricity_sale_price_list = [0.4, 0.5, 0.6, 0.7] * 6

        # 模拟算法返回结果
        self.mock_algorithm_result = {
            'scheduling_time': 1640995200,
            'es_scheduling_strategy': [10.5, -5.2, 0.0] * 32  # 96个点
        }

    @patch('application.algorithm_schedule.storage_dispatch.DBOperate')
    @patch('application.algorithm_schedule.storage_dispatch.energy_storage_optimization_runner')
    def test_successful_dispatch(self, mock_algorithm, mock_db_class):
        """测试成功的储能调度"""
        # 设置数据库操作模拟
        mock_db = Mock()
        mock_db_class.return_value = mock_db
        
        mock_db.get_es_realtime_data.return_value = self.mock_es_realtime
        mock_db.get_site.return_value = self.mock_site
        mock_db.get_pile.return_value = self.mock_pile
        mock_db.get_hybrid_load_predicted.return_value = self.mock_hybrid_load
        mock_db.get_pv_predicted_list.return_value = self.mock_pv_predicted_list
        mock_db.get_price_by_site_no_and_date.return_value = self.mock_price_list
        mock_db.get_site_demand_data.return_value = self.mock_demand_data
        mock_db.get_future_24h_sell_price_from_db.return_value = self.mock_electricity_sale_price_list

        # 设置算法模拟
        mock_algorithm.return_value = self.mock_algorithm_result

        # 执行测试
        result = storage_dispatch(self.site_no, self.test_data, self.trigger)

        # 验证结果
        self.assertEqual(result, self.mock_algorithm_result)
        
        # 验证数据库调用
        mock_db.get_es_realtime_data.assert_called_once_with(site_no=self.site_no)
        mock_db.get_site.assert_called_once_with(site_no=self.site_no)
        mock_db.get_pile.assert_called_once_with(site_no=self.site_no)
        mock_db.get_hybrid_load_predicted.assert_called_once_with(site_no=self.site_no)
        mock_db.get_pv_predicted_list.assert_called_once_with(site_no=self.site_no)
        mock_db.get_price_by_site_no_and_date.assert_called_once_with(
            site_no=self.site_no, 
            date='2024-01-01'
        )
        mock_db.get_site_demand_data.assert_called_once_with(site_no=self.site_no)
        mock_db.get_future_24h_sell_price_from_db.assert_called_once_with(self.site_no)
        mock_db.save_energy_storage_task.assert_called_once_with(
            site_no=self.site_no, 
            result=self.mock_algorithm_result
        )

        # 验证算法调用
        mock_algorithm.assert_called_once()
        call_args = mock_algorithm.call_args[0][0]
        self.assertEqual(call_args['start_time'], '2024-01-01 00:00:00')
        self.assertEqual(call_args['pv_predicted_list'], self.mock_pv_predicted_list)
        self.assertEqual(call_args['hybrid_load_predicted_list'], json.loads(self.mock_hybrid_load.power_list))
        self.assertEqual(call_args['electricity_purchase_price_list'], self.mock_price_list)
        self.assertEqual(call_args['es_soc'], 50)
        self.assertEqual(call_args['es_total_energy'], 100)
        self.assertEqual(call_args['es_rated_power'], 50.0)
        self.assertEqual(call_args['site_grid_limit'], 1000)
        self.assertEqual(call_args['demand_data'], self.mock_demand_data)
        self.assertEqual(call_args['min_soc'], 20)
        self.assertEqual(call_args['max_soc'], 90)
        self.assertEqual(call_args['electricity_sale_price_list'], self.mock_electricity_sale_price_list)

    @patch('application.algorithm_schedule.storage_dispatch.DBOperate')
    def test_no_es_realtime_data(self, mock_db_class):
        """测试无储能实时数据的情况"""
        mock_db = Mock()
        mock_db_class.return_value = mock_db
        mock_db.get_es_realtime_data.return_value = None

        result = storage_dispatch(self.site_no, self.test_data)

        self.assertIsNone(result)
        mock_db.get_es_realtime_data.assert_called_once_with(site_no=self.site_no)
        # 确保没有调用其他方法
        mock_db.get_site.assert_not_called()

    @patch('application.algorithm_schedule.storage_dispatch.DBOperate')
    def test_no_site_data(self, mock_db_class):
        """测试无场站数据的情况"""
        mock_db = Mock()
        mock_db_class.return_value = mock_db
        mock_db.get_es_realtime_data.return_value = self.mock_es_realtime
        mock_db.get_site.return_value = None

        result = storage_dispatch(self.site_no, self.test_data)

        self.assertIsNone(result)
        mock_db.get_site.assert_called_once_with(site_no=self.site_no)
        # 确保没有调用后续方法
        mock_db.get_pile.assert_not_called()

    @patch('application.algorithm_schedule.storage_dispatch.DBOperate')
    def test_no_pile_data(self, mock_db_class):
        """测试无充电桩数据的情况"""
        mock_db = Mock()
        mock_db_class.return_value = mock_db
        mock_db.get_es_realtime_data.return_value = self.mock_es_realtime
        mock_db.get_site.return_value = self.mock_site
        mock_db.get_pile.return_value = None

        result = storage_dispatch(self.site_no, self.test_data)

        self.assertIsNone(result)
        mock_db.get_pile.assert_called_once_with(site_no=self.site_no)

    @patch('application.algorithm_schedule.storage_dispatch.DBOperate')
    def test_no_hybrid_load_data(self, mock_db_class):
        """测试无融合负载预测数据的情况"""
        mock_db = Mock()
        mock_db_class.return_value = mock_db
        mock_db.get_es_realtime_data.return_value = self.mock_es_realtime
        mock_db.get_site.return_value = self.mock_site
        mock_db.get_pile.return_value = self.mock_pile
        mock_db.get_hybrid_load_predicted.return_value = None

        result = storage_dispatch(self.site_no, self.test_data)

        self.assertIsNone(result)
        mock_db.get_hybrid_load_predicted.assert_called_once_with(site_no=self.site_no)

    @patch('application.algorithm_schedule.storage_dispatch.DBOperate')
    def test_invalid_price_list_length(self, mock_db_class):
        """测试电价列表长度不正确的情况"""
        mock_db = Mock()
        mock_db_class.return_value = mock_db
        mock_db.get_es_realtime_data.return_value = self.mock_es_realtime
        mock_db.get_site.return_value = self.mock_site
        mock_db.get_pile.return_value = self.mock_pile
        mock_db.get_hybrid_load_predicted.return_value = self.mock_hybrid_load
        mock_db.get_pv_predicted_list.return_value = self.mock_pv_predicted_list
        # 返回长度不为24的电价列表
        mock_db.get_price_by_site_no_and_date.return_value = [0.5, 0.6, 0.7]

        result = storage_dispatch(self.site_no, self.test_data)

        self.assertIsNone(result)

    @patch('application.algorithm_schedule.storage_dispatch.DBOperate')
    def test_empty_price_list(self, mock_db_class):
        """测试空电价列表的情况"""
        mock_db = Mock()
        mock_db_class.return_value = mock_db
        mock_db.get_es_realtime_data.return_value = self.mock_es_realtime
        mock_db.get_site.return_value = self.mock_site
        mock_db.get_pile.return_value = self.mock_pile
        mock_db.get_hybrid_load_predicted.return_value = self.mock_hybrid_load
        mock_db.get_pv_predicted_list.return_value = self.mock_pv_predicted_list
        # 返回空电价列表
        mock_db.get_price_by_site_no_and_date.return_value = []

        result = storage_dispatch(self.site_no, self.test_data)

        self.assertIsNone(result)


    @patch('application.algorithm_schedule.storage_dispatch.DBOperate')
    def test_invalid_json_in_hybrid_load(self, mock_db_class):
        """测试融合负载预测数据中JSON格式错误的情况"""
        mock_db = Mock()
        mock_db_class.return_value = mock_db
        mock_db.get_es_realtime_data.return_value = self.mock_es_realtime
        mock_db.get_site.return_value = self.mock_site
        mock_db.get_pile.return_value = self.mock_pile

        # 设置无效的JSON数据
        invalid_hybrid_load = Mock(spec=HybridLoadPredictionDB)
        invalid_hybrid_load.power_list = "invalid json data"
        invalid_hybrid_load.curve_start_time = datetime(2024, 1, 1, 0, 0, 0, tzinfo=timezone.utc)
        mock_db.get_hybrid_load_predicted.return_value = invalid_hybrid_load

        # 应该抛出JSON解析异常
        with self.assertRaises(json.JSONDecodeError):
            storage_dispatch(self.site_no, self.test_data)

    @patch('application.algorithm_schedule.storage_dispatch.DBOperate')
    def test_invalid_pile_rated_power(self, mock_db_class):
        """测试充电桩额定功率无法转换为float的情况"""
        mock_db = Mock()
        mock_db_class.return_value = mock_db
        mock_db.get_es_realtime_data.return_value = self.mock_es_realtime
        mock_db.get_site.return_value = self.mock_site

        # 设置无法转换为float的rated_power
        invalid_pile = Mock(spec=PileDB)
        invalid_pile.rated_power = "invalid_number"
        mock_db.get_pile.return_value = invalid_pile
        mock_db.get_hybrid_load_predicted.return_value = self.mock_hybrid_load
        mock_db.get_pv_predicted_list.return_value = self.mock_pv_predicted_list
        mock_db.get_price_by_site_no_and_date.return_value = self.mock_price_list
        mock_db.get_site_demand_data.return_value = self.mock_demand_data
        mock_db.get_future_24h_sell_price_from_db.return_value = self.mock_electricity_sale_price_list

        # 应该抛出类型转换异常
        with self.assertRaises((ValueError, TypeError)):
            storage_dispatch(self.site_no, self.test_data)

    @patch('application.algorithm_schedule.storage_dispatch.DBOperate')
    @patch('application.algorithm_schedule.storage_dispatch.energy_storage_optimization_runner')
    def test_algorithm_exception(self, mock_algorithm, mock_db_class):
        """测试算法调用异常的情况"""
        # 设置数据库操作模拟
        mock_db = Mock()
        mock_db_class.return_value = mock_db
        mock_db.get_es_realtime_data.return_value = self.mock_es_realtime
        mock_db.get_site.return_value = self.mock_site
        mock_db.get_pile.return_value = self.mock_pile
        mock_db.get_hybrid_load_predicted.return_value = self.mock_hybrid_load
        mock_db.get_pv_predicted_list.return_value = self.mock_pv_predicted_list
        mock_db.get_price_by_site_no_and_date.return_value = self.mock_price_list
        mock_db.get_site_demand_data.return_value = self.mock_demand_data
        mock_db.get_future_24h_sell_price_from_db.return_value = self.mock_electricity_sale_price_list

        # 设置算法抛出异常
        mock_algorithm.side_effect = Exception("Algorithm failed")

        result = storage_dispatch(self.site_no, self.test_data)

        # 应该返回空字典
        self.assertEqual(result, {})
        # 确保没有调用保存方法
        mock_db.save_energy_storage_task.assert_not_called()

    @patch('application.algorithm_schedule.storage_dispatch.DBOperate')
    @patch('application.algorithm_schedule.storage_dispatch.energy_storage_optimization_runner')
    def test_save_task_exception(self, mock_algorithm, mock_db_class):
        """测试保存任务异常的情况"""
        # 设置数据库操作模拟
        mock_db = Mock()
        mock_db_class.return_value = mock_db
        mock_db.get_es_realtime_data.return_value = self.mock_es_realtime
        mock_db.get_site.return_value = self.mock_site
        mock_db.get_pile.return_value = self.mock_pile
        mock_db.get_hybrid_load_predicted.return_value = self.mock_hybrid_load
        mock_db.get_pv_predicted_list.return_value = self.mock_pv_predicted_list
        mock_db.get_price_by_site_no_and_date.return_value = self.mock_price_list
        mock_db.get_site_demand_data.return_value = self.mock_demand_data
        mock_db.get_future_24h_sell_price_from_db.return_value = self.mock_electricity_sale_price_list

        # 设置算法正常返回
        mock_algorithm.return_value = self.mock_algorithm_result

        # 设置保存任务抛出异常
        mock_db.save_energy_storage_task.side_effect = Exception("Save failed")

        result = storage_dispatch(self.site_no, self.test_data)

        # 应该仍然返回算法结果
        self.assertEqual(result, self.mock_algorithm_result)

    @patch('application.algorithm_schedule.storage_dispatch.DBOperate')
    @patch('application.algorithm_schedule.storage_dispatch.energy_storage_optimization_runner')
    def test_missing_optional_data(self, mock_algorithm, mock_db_class):
        """测试可选数据缺失的情况"""
        # 设置数据库操作模拟
        mock_db = Mock()
        mock_db_class.return_value = mock_db
        mock_db.get_es_realtime_data.return_value = self.mock_es_realtime
        mock_db.get_site.return_value = self.mock_site
        mock_db.get_pile.return_value = self.mock_pile
        mock_db.get_hybrid_load_predicted.return_value = self.mock_hybrid_load
        mock_db.get_pv_predicted_list.return_value = []  # 空的光伏预测数据
        mock_db.get_price_by_site_no_and_date.return_value = self.mock_price_list
        mock_db.get_site_demand_data.return_value = []  # 空的需量数据
        mock_db.get_future_24h_sell_price_from_db.return_value = []  # 空的售电价格数据

        # 设置算法正常返回
        mock_algorithm.return_value = self.mock_algorithm_result

        result = storage_dispatch(self.site_no, self.test_data)

        # 应该正常返回结果
        self.assertEqual(result, self.mock_algorithm_result)

        # 验证算法调用时的参数
        call_args = mock_algorithm.call_args[0][0]
        self.assertEqual(call_args['pv_predicted_list'], [])
        self.assertEqual(call_args['demand_data'], [])
        self.assertEqual(call_args['electricity_sale_price_list'], [])

    @patch('application.algorithm_schedule.storage_dispatch.DBOperate')
    @patch('application.algorithm_schedule.storage_dispatch.energy_storage_optimization_runner')
    def test_edge_case_soc_values(self, mock_algorithm, mock_db_class):
        """测试SOC边界值的情况"""
        # 设置极端SOC值
        extreme_es_realtime = Mock(spec=ESRealtimeDataDB)
        extreme_es_realtime.es_soc = 0  # 最低SOC
        extreme_es_realtime.es_min_soc = 0
        extreme_es_realtime.es_max_soc = 100  # 最高SOC

        mock_db = Mock()
        mock_db_class.return_value = mock_db
        mock_db.get_es_realtime_data.return_value = extreme_es_realtime
        mock_db.get_site.return_value = self.mock_site
        mock_db.get_pile.return_value = self.mock_pile
        mock_db.get_hybrid_load_predicted.return_value = self.mock_hybrid_load
        mock_db.get_pv_predicted_list.return_value = self.mock_pv_predicted_list
        mock_db.get_price_by_site_no_and_date.return_value = self.mock_price_list
        mock_db.get_site_demand_data.return_value = self.mock_demand_data
        mock_db.get_future_24h_sell_price_from_db.return_value = self.mock_electricity_sale_price_list

        mock_algorithm.return_value = self.mock_algorithm_result

        result = storage_dispatch(self.site_no, self.test_data)

        # 验证算法调用时的SOC参数
        call_args = mock_algorithm.call_args[0][0]
        self.assertEqual(call_args['es_soc'], 0)
        self.assertEqual(call_args['min_soc'], 0)
        self.assertEqual(call_args['max_soc'], 100)

    @patch('application.algorithm_schedule.storage_dispatch.DBOperate')
    @patch('application.algorithm_schedule.storage_dispatch.energy_storage_optimization_runner')
    def test_different_trigger_values(self, mock_algorithm, mock_db_class):
        """测试不同触发器值的情况"""
        mock_db = Mock()
        mock_db_class.return_value = mock_db
        mock_db.get_es_realtime_data.return_value = self.mock_es_realtime
        mock_db.get_site.return_value = self.mock_site
        mock_db.get_pile.return_value = self.mock_pile
        mock_db.get_hybrid_load_predicted.return_value = self.mock_hybrid_load
        mock_db.get_pv_predicted_list.return_value = self.mock_pv_predicted_list
        mock_db.get_price_by_site_no_and_date.return_value = self.mock_price_list
        mock_db.get_site_demand_data.return_value = self.mock_demand_data
        mock_db.get_future_24h_sell_price_from_db.return_value = self.mock_electricity_sale_price_list

        mock_algorithm.return_value = self.mock_algorithm_result

        # 测试不同的触发器值
        test_triggers = [None, '', 'manual', 'scheduled', 'emergency']

        for trigger in test_triggers:
            with self.subTest(trigger=trigger):
                result = storage_dispatch(self.site_no, self.test_data, trigger)
                self.assertEqual(result, self.mock_algorithm_result)

    @patch('application.algorithm_schedule.storage_dispatch.DBOperate')
    def test_database_connection_failure(self, mock_db_class):
        """测试数据库连接失败的情况"""
        # 设置数据库操作抛出异常
        mock_db_class.side_effect = Exception("Database connection failed")

        # 应该抛出数据库连接异常
        with self.assertRaises(Exception):
            storage_dispatch(self.site_no, self.test_data)


if __name__ == '__main__':
    unittest.main()
