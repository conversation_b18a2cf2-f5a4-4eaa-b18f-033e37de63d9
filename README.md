# EMS (Energy Management System) 项目

## 项目简介
这是一个能源管理系统(EMS)项目，用于管理和优化光储充场站的能源使用。系统支持通过Kafka进行消息传递，并提供REST API接口进行控制和管理。

## 主要功能
1. 光储充场站AI能力控制
   - 开启/关闭场站AI能力
   - 更新场站基本信息
   - 支持多充电桩管理
   - 支持储能电池容量管理
   - 支持电网限制管理

2. 消息队列集成
   - NOC Kafka消费者
   - EMS Kafka消费者
   - Kafka生产者
   - 控制结果消息发送（支持储能、充电桩、光伏三种控制类型）
   - 场站独立心跳机制（每个开启AI能力的场站都有独立的5秒心跳）

3. 数据库操作
   - 支持数据持久化存储
   - 支持时间段管理的需求限制功能（SiteDemandDataDB）

4. 算法调度系统
   - 储能调度（storage_dispatch）：支持96个功率点的定时发送（15分钟间隔）
   - 充电功率调度（charge_power_dispatch）：支持多充电桩功率分配
   - 光伏控制（pv_control）：支持光伏逆变器功率限制

## API接口说明

### 控制EMS AI能力接口
- 接口路径: `/api/v1/control_ems_ai`
- 请求方式: POST
- 请求参数:
  ```json
  {
    "site_no": "xxxx",
    "status": "start/stop" 
  }
  ```

## Kafka消息格式说明

### 控制结果消息格式 (ai-ems-msg)
发送到Kafka的控制结果消息遵循统一格式：

```json
{
  "ts": "1748327973000",           // 调度策略下发时间（毫秒时间戳）
  "biz_seq": "uuid-string",        // 幂等串
  "site_no": "场站编号",           // 场站编号
  "es_control": {                  // 储能信息（可为null）
    "power": [100, 80, 60, 40, 20, 0, -20, -40, -60, -80, "...96个点"]  // 功率数组（kW），96个15分钟间隔的功率点
  },
  "charger_control": [             // 充电信息数组（可为null）
    {
      "pile_sn": "充电桩SN",
      "connect_id": "连接ID",
      "power": 60                  // 功率值（kW）
    }
  ],
  "pv_inverter_control": {         // 逆变器信息（可为null）
    "power_limit": 50              // 功率限制（kW）
  }
}
```

### 控制类型说明

1. **储能调度 (storage_dispatch)**
   - 接收96个功率点的调度策略
   - 一次性下发包含96个功率点的完整数组
   - 实时发送（非定时任务）
   - 每个功率点代表15分钟间隔的调度策略（共24小时）
   - 消息格式：`es_control.power` 为包含96个整数的数组

2. **充电功率调度 (charge_power_dispatch)**
   - 解析充电桩功率分配信息
   - 支持多个充电桩同时控制
   - 实时发送（非定时任务）

3. **光伏控制 (pv_control)**
   - 发送光伏逆变器功率限制
   - 实时发送（非定时任务）

### 场站心跳机制说明

系统为每个开启AI能力的场站维护独立的心跳定时器：

1. **心跳间隔**：每个场站每5秒发送一次心跳消息
2. **智能心跳**：当场站有其他控制消息发送时，会自动重置心跳计时器，避免重复发送
3. **自动管理**：
   - 场站开启AI时自动启动心跳
   - 场站关闭AI时自动停止心跳
   - 服务启动时自动恢复所有已开启AI场站的心跳

#### 心跳消息格式
```json
{
  "ts": "1691234567890",
  "biz_seq": "xxxx",
  "site_no": "SITE001",
  "es_control": null,
  "charger_control": null,
  "pv_inverter_control": null
}
```

## 技术栈
- FastAPI: Web框架
- Kafka: 消息队列
- SQLAlchemy: 数据库ORM
- Uvicorn: ASGI服务器

## 项目部署运行

### 自动初始化

项目启动时会自动进行以下初始化操作：

1. **数据库初始化**
   - 检查EMS数据库是否存在
   - 如不存在，自动创建数据库和所有表结构

2. **Kafka Topics初始化**
   - 自动创建必需的Kafka topics：
     - `real-data` - NOC消费者订阅（3个分区，支持并发处理）
     - `ems-ai-msg` - EMS消费者订阅（1个分区，保证消息顺序）
     - `ai-ems-msg` - 生产者发送（1个分区，保证消息顺序）
   - 副本因子动态确定：单节点集群使用1，多节点集群使用min(3, broker数量)
   - 如果topic已存在则跳过创建

### Win环境运行测试

```powershell
# cd 到项目根目录
cd ems

# 1.create python venv
python -m venv .venv

# 2.install packages
pip install --no-cache-dir --default-timeout=1000 -r requirements.txt

# 3.set环境变量
$env:PYTHONPATH="D:\\code\\python\\ems"
$env:CONFIG_ENCRYPTION_KEY="xxxxx"

# 4.查看环境变量是否成功设置
echo $env:PYTHONPATH
echo $env:CONFIG_ENCRYPTION_KEY

# 5.运行项目
uvicorn application.server:app --host 127.0.0.1 --port 8000
```

#### 算法调度问题解决方案

如果遇到算法调度相关错误，以下是常见问题的解决方案：

**问题1：比较差异时出错**
```
ERROR - 比较差异时出错: unsupported operand type(s) for -: 'dict' and 'dict'
ERROR - 比较差异时出错: unsupported operand type(s) for -: 'tuple' and 'tuple'
```

**原因分析：**
- 原始的`_is_diff_large`函数无法正确处理复杂数据类型（dict和tuple）
- 光伏预测结果是dict格式：`{'predicted_time': ..., 'pv_predicted_list': [...]}`
- 融合负载预测结果是tuple列表格式：`[(datetime, float), ...]`

**解决方案：**
已在v1.2中最终优化，将`_is_diff_large()`函数极大简化为专门的曲线比较函数：

- **专注核心功能：** 专门用于比较预测曲线的功率差异，代码从109行简化到50行
- **自动数据提取：** 智能提取不同格式数据的功率数组
  - 光伏预测：`{'pv_predicted_list': [...]}`提取功率数组
  - 融合负载：`[(datetime, power), ...]`提取功率值
- **灵活长度比较：** 支持不同长度的数组，从头到尾逐点比较
- **简洁高效：** 去除复杂的类型判断，直接处理预测曲线的核心需求

这个优化后的函数专门针对EMS系统的两个预测曲线比较场景，代码更简洁、逻辑更清晰、性能更高效。

#### 日志级别控制

项目支持通过环境变量动态控制日志级别，方便调试和问题排查。

**支持的日志级别：**
- `DEBUG`: 详细调试信息（包括消息处理成功日志）
- `INFO`: 一般信息（默认级别）
- `WARNING`: 警告信息
- `ERROR`: 错误信息
- `CRITICAL`: 严重错误

**使用方法：**

1. **临时设置日志级别**
```powershell
# 设置DEBUG级别查看详细日志
$env:LOG_LEVEL = "DEBUG"
python -m application.server
```

2. **使用调试脚本启动**
```powershell
# 运行调试脚本（自动设置DEBUG级别）
.\start_debug_mode.ps1
```

3. **生产环境设置**
```powershell
# 设置WARNING级别减少日志量
$env:LOG_LEVEL = "WARNING"
python -m application.server
```

**DEBUG模式特点：**
- 显示Kafka消息处理成功的详细信息
- 显示算法调度的详细执行过程
- 显示数据库操作的详细日志
- 显示缓存操作的详细信息

**示例：** `ems_kafka_message_handler.py`中的第21行使用了`logger.debug`，只有在DEBUG级别时才会输出：
```python
logger.debug(f"Processing message success, message: {message_data}")
```

**日志文件位置：**
- 控制台输出：实时显示
- 文件日志：`logs/` 目录下按模块分类存储
- 日志文件自动轮转和压缩

**常见调试场景：**
- 查看Kafka消息处理详情：设置`LOG_LEVEL=DEBUG`
- 排查算法调度问题：设置`LOG_LEVEL=DEBUG`查看详细执行过程
- 生产环境监控：设置`LOG_LEVEL=WARNING`或`LOG_LEVEL=ERROR`

#### 交流桩支持

项目现已支持交流桩的特殊处理，能够自动识别和管理交流桩。

**交流桩特点：**
- 桩号以`AL`开头，如：`AL7019A2GR7C00010X`
- 桩号第5、6位表示功率（如19kW）
- 功率不可变，不参与功率分配
- 只有一把枪，不需要模块信息
- 不需要订阅运维数据

**功能特性：**
- **自动识别**：根据桩号前缀自动识别交流桩
- **功率解析**：自动从桩号中提取额定功率
- **分离处理**：在订阅和功率分配中自动分离交流桩和直流桩
- **数据保存**：直接保存交流桩基础信息，跳过复杂的模块获取流程

**使用示例：**
```python
from application.utils.pile_utils import PileUtils

# 识别交流桩
is_ac = PileUtils.is_ac_pile("AL7019A2GR7C00010X")  # True
power = PileUtils.extract_ac_pile_power("AL7019A2GR7C00010X")  # 19.0

# 分离桩类型
pile_list = ["AL7019A2GR7C00010X", "DE7480B2GRCC00008B"]
ac_piles, dc_piles = PileUtils.separate_pile_types(pile_list)
# ac_piles: ["AL7019A2GR7C00010X"]
# dc_piles: ["DE7480B2GRCC00008B"]
```

**处理流程：**
1. 场站订阅时自动分离交流桩和直流桩
2. 交流桩直接保存基础信息到数据库
3. 直流桩执行完整的订阅和模块信息获取流程
4. 功率分配算法自动排除交流桩
5. 日志记录详细的处理过程

**功率分配特殊处理：**
- **demand预测排除**：交流桩不参与车辆demand信息的预测和获取
- **功率扣减**：在功率分配前，先从场站总功率中扣减在充电的交流桩额定功率
- **直流桩分配**：剩余功率仅分配给直流桩
- **自动调整**：`site_grid_limit`和`site_demand_limit`会自动扣减交流桩占用的功率

**功率分配示例：**
```
场站总功率: 100kW
在充电的交流桩: AL7019A2GR7C00010X (19kW) + AL7022A2GR7C00020X (22kW)
交流桩功率总和: 41kW
剩余可分配功率: 59kW (仅分配给直流桩)
```

**测试验证：**
运行测试脚本验证交流桩功能：
```powershell
python test_ac_pile_support.py
```


# >>>>>>>> ems算法训练数据处理记录
## S3数据同步
```bash
s3://storageandchargingproject/sandboxs3/energy_management/

aws s3 ls s3://storageandchargingproject/sandboxs3/ --profile s3

aws s3 sync s3://storageandchargingproject/sandboxs3/energy_management/ . --profile s3

aws s3 sync s3://storageandchargingproject/sandboxs3/RELBENCH/ ./RELBENCH --profile s3

aws s3 sync s3://storageandchargingproject/sandboxs3/TimesExperiments/ ./TimesExperiments --profile s3

aws s3 sync s3://storageandchargingproject/sandboxs3/ods_energy_pile_base_op_location/ ./ods_energy_pile_base_op_location --profile s3

aws s3 sync s3://storageandchargingproject/sandboxs3/ods_energy_pile_base_op_location_evse/ ./ods_energy_pile_base_op_location_evse --profile s3

aws s3 sync s3://storageandchargingproject/sandboxs3/ods_es_op_evse_meter_upload_index/2023-12-01/ ./ods_es_op_evse_meter_upload_index/2023-12-01/ --profile s3

aws s3 sync s3://storageandchargingproject/sandboxs3/ods_es_op_evse_meter_upload_index/2023-12-02/ ./ods_es_op_evse_meter_upload_index/2023-12-02/ --profile s3

aws s3 sync s3://storageandchargingproject/sandboxs3/ods_es_op_evse_meter_upload_index/2025-02-24/ ./ods_es_op_evse_meter_upload_index/2025-02-24/ --profile s3

aws s3 sync s3://storageandchargingproject/sandboxs3/ods_es_op_evse_meter_upload_index/2025-03-02/ ./ods_es_op_evse_meter_upload_index/2025-03-02/ --profile s3
```

## 数据库模型更新

### SiteDemandDataDB 支持
原本的 `SiteDB.demand_limit` 字段已被替换为 `SiteDemandDataDB` 表，支持基于时间段的需求限制管理。

**数据结构：**
```json
{
  "site_no": "场站编号",
  "start_time": "2025-01-01 00:00:00",
  "end_time": "2025-01-31 23:59:59", 
  "price": 6.0,
  "unit": "euro",
  "total_demand_target": 120,
  "target_demand_warning_ratio": 90.0
}
```

**主要特性：**
- **时间段管理**：支持不同时间段的需求限制配置
- **时区自动识别**：根据场站的region自动获取对应时区进行时间匹配
- **动态获取**：根据当前时间自动获取对应的需求限制
- **安全控制**：如果没有找到对应时间段的数据，不进行功率分配，确保安全
- **日志记录**：详细记录需求限制的获取过程

**时区支持：**
系统支持根据场站的region字段自动识别时区，支持的格式包括：
- **ISO国家代码**：CN（中国）、NL（荷兰）、DE（德国）、FR（法国）等
- **英文国家名**：China、Netherlands、Germany、France等
- **中文国家名**：中国、荷兰、德国、法国等

常见的region与时区映射示例：
```python
"CN" -> "Asia/Shanghai"      # 中国
"NL" -> "Europe/Amsterdam"   # 荷兰
"DE" -> "Europe/Berlin"      # 德国
"FR" -> "Europe/Paris"       # 法国
"GB" -> "Europe/London"      # 英国
"US" -> "America/New_York"   # 美国
```

**API方法：**
- `get_current_demand_limit(site_no, current_time=None, site_info=None)`：获取当前时间对应的需求限制，未找到返回None
- `get_site_demand_data(site_no)`：获取场站的所有需求数据

**使用示例：**
```python
# 获取当前时间的需求限制（自动根据场站region识别时区）
current_limit = db_operate.get_current_demand_limit("SITE001")

# 获取指定时间的需求限制
specific_time = datetime(2025, 6, 15, 10, 0, 0, tzinfo=timezone.utc)
specific_limit = db_operate.get_current_demand_limit("SITE001", specific_time)

# 如果已经有site_info，可以传递避免重复查询
with db_operate.get_db() as db:
    site_info = db.scalar(select(SiteDB).where(SiteDB.site_no == "SITE001"))
    current_limit = db_operate.get_current_demand_limit("SITE001", None, site_info)

# 获取所有需求数据
all_demand_data = db_operate.get_site_demand_data("SITE001")
```

**时区处理流程：**
1. 根据场站的region字段查找对应的时区
2. 将输入的UTC时间转换为场站当地时间
3. 使用当地时间查询SiteDemandDataDB中的时间段数据
4. 返回匹配时间段的total_demand_target值，未找到返回None
5. 如果返回None，功率分配算法不会执行，确保系统安全

## 数据变化检测功能

### 功能说明

为了避免Kafka高频发送时产生的重复数据插入，系统实现了基于哈希值的数据变化检测机制。该功能自动检测 `demand_data`、`sell_price`、`purchase_price_data` 这三种电价数据的内容变化，只有当数据真正发生变化时才会执行数据库插入操作。

### 核心特性

1. **智能变化检测**
   - 使用SHA256哈希算法计算数据指纹
   - 精确识别数据内容的变化（包括数值、时间、单位等任何字段的变化）
   - 支持多种数据结构（List、Dict等可序列化类型）
   - **数组排序标准化**：自动对数组数据进行排序，确保相同内容但不同顺序的数据产生相同哈希值，避免因数据顺序变化导致的误判

2. **高性能内存缓存**
   - TTL缓存机制：自动过期清理，避免内存无限增长
   - 快速响应：毫秒级的变化检测性能
   - 轻量级：无需额外数据库存储开销

### 支持的数据类型

- **demand_data**：需求电价数据
- **sell_price**：卖电电价数据  
- **purchase_price_data**：固定电价和分时电价数据

### 工作流程

1. **接收Kafka消息**：EMS消息处理器接收来自Kafka的电价数据
2. **数据标准化**：对数组类型的数据进行排序标准化，确保相同内容产生一致的哈希值
3. **计算数据哈希**：为标准化后的数据内容计算SHA256哈希值
4. **检测变化**：
   - 检查内存缓存中的上次哈希值
   - 比较当前哈希与历史哈希，判断数据是否变化
5. **处理结果**：
   - **数据有变化**：执行数据库插入，更新内存缓存
   - **数据无变化**：跳过插入，记录调试日志

### 性能优化效果

- **减少数据库写入**：避免重复数据插入，降低数据库负载
- **提高处理效率**：跳过无变化数据的处理，提升系统吞吐量
- **节省存储空间**：防止大量重复数据堆积，优化存储成本
- **降低CPU使用**：哈希计算开销极小，变化检测速度极快

### 日志记录

系统会记录详细的变化检测日志：

```
INFO - 场站 SITE001 需求电价数据发生变化，已插入 3 条记录
DEBUG - 场站 SITE001 卖电电价数据无变化，跳过插入
INFO - 首次处理数据: site_no=SITE001, data_type=purchase_price_data
INFO - 数据发生变化: site_no=SITE001, data_type=demand_data, old_hash=abcd1234..., new_hash=efgh5678...
DEBUG - 数据无变化，跳过插入: site_no=SITE001, data_type=demand_data  // 相同内容不同顺序的数组
```

### 数组排序标准化示例

对于相同内容但不同顺序的数组数据，系统会产生相同的哈希值：

```python
# 这两个demand_data数组内容相同，仅顺序不同
data1 = [["686888", "6.0000"], ["686893", "6.0000"], ["686892", "0.0000"]]
data2 = [["686892", "0.0000"], ["686888", "6.0000"], ["686893", "6.0000"]]

# 系统会自动排序标准化，两者产生相同hash值
# 结果：data1和data2只会插入一次，第二次会跳过插入
```

### 配置参数

```python
# 数据变化检测器配置
data_change_detector = DataChangeDetector(
    cache_ttl_hours=2,     # 内存缓存TTL时间（小时），缓存过期后重新检测
    cache_maxsize=1000     # 内存缓存最大条目数，防止内存占用过多
)
```

### 内存管理

- **缓存过期**：TTL机制自动清理过期缓存，服务重启或缓存过期后首次接收到数据会重新插入
- **内存控制**：LRU算法自动淘汰最少使用的缓存项，保持内存使用在合理范围
- **轻量设计**：每个哈希值仅占64字节，1000个条目约占64KB内存

### 测试验证

系统包含完整的单元测试，覆盖以下场景：
- 哈希值计算准确性
- 首次数据处理
- 相同数据检测
- 变化数据检测
- 内存缓存操作
- 消息处理器集成

运行测试：
```bash
python -m pytest tests/test_data_change_detection.py -v
```

### 使用场景

此功能特别适用于以下情况：
- **高频数据传输**：Kafka每秒发送大量消息，但实际数据内容变化不频繁
- **电价数据管理**：电价通常在特定时间点更新，其他时间保持不变
- **系统性能优化**：减少不必要的数据库写入，提升整体系统性能

## Bug修复记录

### Decimal JSON序列化问题修复 (2025-07-15)

**问题描述：**
线性规划功率分配算法在日志输出时出现JSON序列化错误：
```
TypeError: Object of type Decimal is not JSON serializable
```

**问题根因：**
- 数据库字段使用Decimal类型保证数值精度
- Python标准json模块无法直接序列化Decimal对象
- 错误发生在`application/algorithm/power_distribution/linear_programming.py`第226行

**解决方案：**
1. 添加自定义JSON编码器`DecimalEncoder`
2. 自动将Decimal类型转换为float进行序列化
3. 保持数值精度的同时解决序列化问题

**修复代码：**
```python
class DecimalEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理Decimal类型"""
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        return super(DecimalEncoder, self).default(obj)

# 使用自定义编码器
json.dumps(self.input_data, ensure_ascii=False, cls=DecimalEncoder)
```

**验证结果：**
- ✅ 所有Decimal类型正确转换为float
- ✅ 复杂嵌套数据结构正常序列化
- ✅ 功率分配算法日志输出恢复正常
- ✅ 不影响数据精度和算法计算结果

### 数据变化检测逻辑修复 (2025-07-16)

**问题描述：**
数据变化检测器对相同的业务数据重复插入数据库，导致冗余数据和性能问题：

```log
2025-07-16 01:56:55,500 - data_change_detector - INFO - 数据发生变化: site_no=1743948000001, data_type=demand_data, old_hash=9a04ab37..., new_hash=fae22091...
2025-07-16 01:56:55,514 - ems_kafka_consumer - INFO - 场站 1743948000001 需求电价数据发生变化，已插入 9 条记录
```

**问题根因：**
- 数据适配器（`SiteDemandDataDBAdapter`, `SiteSellPriceDataDBAdapter`）在处理业务数据时会添加动态`version`字段
- 每次处理时`version`字段包含当前时间戳：`datetime.now(pytz.UTC).strftime("%Y%m%d_%H%M%S")`  
- 数据变化检测是对适配后的数据进行哈希计算，导致相同业务数据产生不同哈希值
- 结果：即使业务数据完全相同，也被误判为数据变化

**技术细节：**
```python
# 问题代码（修复前）
for demand_data in message_data["demand_data"]:
    demand_price_data = SiteDemandDataDBAdapter.adapt(demand_data)  # 添加version字段
    demand_data_list.append(demand_price_data)

is_changed, data_hash = data_change_detector.is_data_changed(
    data=demand_data_list  # 对包含version的数据检测变化
)
```

**解决方案：**
将数据变化检测提前到数据适配之前，基于原始业务数据进行变化检测：

```python
# 修复后代码
original_demand_data = message_data["demand_data"]
is_changed, data_hash = data_change_detector.is_data_changed(
    data=original_demand_data  # 对原始业务数据检测变化
)

if is_changed:
    # 只有数据真正变化时才进行适配和插入
    for demand_data in original_demand_data:
        demand_price_data = SiteDemandDataDBAdapter.adapt(demand_data)
        demand_data_list.append(demand_price_data)
```

**修复范围：**
- ✅ `demand_data` - 需求电价数据
- ✅ `sell_price` - 卖电电价数据  
- ℹ️ `purchase_price_data` - 购买电价数据（无动态字段，无需修复）

**验证结果：**
- ✅ 相同业务数据哈希值一致（修复后）
- ✅ 不同version字段不影响变化检测
- ✅ 只有业务数据真正变化时才插入数据库
- ✅ 显著减少冗余数据写入，提升系统性能
- ✅ 保持存储元数据（version字段）的正常功能

**测试验证：**
```bash
# 运行验证脚本
python test_data_change_fix.py

# 结果：
✅ 修复前哈希相同: False（有问题）
✅ 修复后哈希相同: True（已修复）
🎉 修复成功！数据变化检测现在只基于业务数据内容
```

# <<<<<<<< ems算法训练数据处理记录































