
import pandas as pd

# ================================ EVSE DATA PROCESSING ============================================================================
class evse_sn_data_processing:
    def __init__(self, data: pd.DataFrame, time_intervals: str = '30s'):
        """ 
        For charger data preprocessing
        ====================================
            data: raw EVCS data must have columns [evse_sn, create_time, power]
            time_intervals: the time in seconds for which to resample the data for (granularity): smaller is better
        ====================================
        This function processes the {charger data} by uniformly filling the power usage for every determined "time_interval" 30s interval
        """
        self.data = data
        self.time_intervals = time_intervals
        self.time_intervals_int = int(''.join(filter(str.isdigit, self.time_intervals)))
        self.chargers = list(self.data.evse_sn.unique())
        self.chargers_data = None
        self.power_consumption()

    def power_consumption(self,):
        self.chargers_data = pd.DataFrame()
        for charger in self.chargers:
            # print(f" HERE WE GO FOR CHARGER {charger} \n")
            df = self.data[self.data['evse_sn']==charger].sort_values(by='create_time')
            df = df.drop_duplicates(subset='create_time') # *this has potential issues, two events occure at the same time.
            df = df.set_index('create_time')
            df = df.resample(self.time_intervals).ffill()
            df.dropna(inplace=True)
            df.reset_index(inplace=True)

            df['energy_consumed_KWh'] = (df['power'] / 1000) * (self.time_intervals_int / (60*60) )

            # chargerframes.append[df.copy(deep=True)]
            self.chargers_data = pd.concat([self.chargers_data, df.copy(deep=True)], ignore_index=True)
            # print(f"CURRENT DATA {self.chargers_data.head(3)}")

# ================================ EVCS DATA PROCESSING ============================================================================

class evcs_data_processing:
    def __init__(self, data, time_granularity: str = '15min', time_zone:str = 'Asia/Shanghai'):
        """
        Processes charging station data using all charger data in the station
        ===============================
            data: preprocesses charging station data with charger charging information for a uniform time interval
            time_granularity: The time interval for which we want to compute energy consumption for a charger
            time_zone: the time zone of the charger or charging station

            data must have the following columns `Timestamp, location_id (charging station id), energy_consumed_KWh`
        ===============================
        """
        self.data = data
        self.time_granularity = time_granularity

        #----- CREATE TIMESTAMP FOR PREDICTION INTERVAL ------
        self.start_time = data.create_time.min().floor(self.time_granularity)
        self.end_time = data.create_time.max()
        # self.time_zone = time_zone
        self.time_range = pd.date_range(start=self.start_time, end=self.end_time, freq=self.time_granularity)

        # --- FIX TIME ZONING ---------------------------------------
        # self.time_range = self.time_range.tz_localize('UTC')
        # self.time_range = self.time_range.tz_convert(self.time_zone)
        # self.time_range = self.time_range.tz_localize(None)
        self.df = pd.DataFrame(self.time_range, columns=['Timestamp'])

        #----- SET TIME INTERVAL START AND END ---------------------
        self.df['Interval_start'] = self.df['Timestamp'].shift(1)
        self.df = self.df.dropna().reset_index(drop=True)  # Drop the first row where Interval_Start is NaN
        self.df['Year'] = self.df['Timestamp'].dt.year
        self.df['Month'] = self.df['Timestamp'].dt.month
        self.df['DayOfWeek'] = self.df['Timestamp'].dt.dayofweek
        self.df['MinuteOfDay'] = self.df['Timestamp'].dt.hour * 60 + self.df['Timestamp'].dt.minute
        self.df['Day'] = self.df['Timestamp'].dt.day
        self.df['Hour'] = self.df['Timestamp'].dt.hour
        self.df['total_power_demand_KWh'] = 0.0                  # create targer variable column

        #---- EXTRACT NUMBER OF CHARGING STATIONS ------------------
        self.charging_stations = list(self.data.location_id.unique())
        self.charging_stations_data = {station: self.df.copy(deep=True) for station in self.charging_stations }

        self.prep_data()


    def prep_data(self):
        for station in self.charging_stations:
            source_station_data = self.data[self.data['location_id']==station]
            for i, row in self.charging_stations_data[station].iterrows():
                interval_start = row['Interval_start']
                interval_end = row['Timestamp']
                sum_value = source_station_data[(source_station_data['create_time'] > interval_start) & (source_station_data['create_time'] <= interval_end)]['energy_consumed_KWh'].sum()
                self.charging_stations_data[station].at[i, 'total_power_demand_KWh'] = float(sum_value)
                self.charging_stations_data[station]['station'] = station
                
        self.processed_data = pd.concat([self.charging_stations_data[station] for station in self.charging_stations], ignore_index=True)


    def train_test_data(self, split_date = None, split_ratio = 0.7):
        """ 
        splits data into training and testing set by year and month. test set is data after provided year and month
        ================================== 
            split_date: str or datetime  max date to keep in the training set
            split_ratio: float  the ratio of data that should be allocated to train set. The rest is allocated to test set
        ===================================
        after running this fuction, you can get a training or set dataset split by time

        """
        if self.processed_data != None:
            self.prep_data()
        
        if split_date != None:
            self.train_data = self.processed_data[self.processed_data['Timestamp'] <= split_date].copy()
            self.test_data = self.processed_data[self.processed_data['Timestamp'] > split_date].copy()
            
        else:
            print(f" SPLITTING DATA BY RATIO {split_ratio} : {1-split_ratio} FOR TRAIN AND TEST RESPECTIVELY")
            self.processed_data = self.processed_data.sort_values(by='Timestamp')
            self.processed_data = self.processed_data.reset_index(drop=True)
            split_index = int(len(self.processed_data) * split_ratio)
            # Split the dataset
            self.train_data = self.processed_data.iloc[:split_index].copy()
            self.test_data = self.processed_data[self.processed_data['Timestamp'] > self.train_data.Timestamp.max()].copy()

        self.train_data = self.train_data.reset_index(drop=True)
        self.test_data = self.test_data.reset_index(drop=True)

# ======================= FEATURE EXTRACTION ================================================================================

class feature_engineering:
    def __init__(self, 
                 df: pd.DataFrame,
                 target_feature: str, 
                 horizon_features: dict = {}, 
                 window_features: dict = {},
                 forecast_features: dict = {},
                 forecast_horizon: int = 96,
                 time_granularity: int = 15,
                 inference: bool = False):
        
        self.df = df
        self.target_feature = target_feature
        self.horizon_features = horizon_features
        self.window_features = window_features
        self.forecast_features = forecast_features
        self.forecast_horizon = forecast_horizon
        self.time_granularity =  time_granularity
        self.inference = inference

        # Generate features
        self.generate_window_features()
        self.generate_horizon_features()
        self.df.dropna(inplace=True)
        self.df = self.df.sort_values(by=['Timestamp', 'horizon'], ascending=[True, True])
        self.df = self.df.reset_index(drop=True)
        

    def generate_horizon_features(self):

        rows = []
        for h in range(1, self.forecast_horizon + 1):
            # Copy the base data (features that remain the same for the base time t)
            temp = self.df.copy(deep=True)
            # Create the target column as the power demand at time t+h.
            if not self.inference:
                temp['target'] = temp[self.target_feature].shift(-h)
            # Add a column indicating the forecast horizon (i.e. which future step)
            temp['horizon'] = h
            # Optionally, compute a future timestamp: this assumes constant 15-min intervals.
            temp['future_Timestamp'] = temp['Timestamp'] + pd.Timedelta(minutes=self.time_granularity * h)
            temp['future_MinuteOfDay'] = temp['future_Timestamp'].dt.hour * 60 + temp['future_Timestamp'].dt.minute
            temp['future_DayOfWeek'] = temp['future_Timestamp'].dt.dayofweek
            temp['future_Hour'] = temp['future_Timestamp'].dt.hour

            if len(self.horizon_features) > 0:
                for feature in self.horizon_features:
                    # reflect the future time step. Here we create new columns with a 'future_' prefix.
                    temp['future_' + feature] = temp[feature].shift(-h)
            
            # Drop rows with NaN values (which occur at the end due to shifting)
            temp = temp.dropna()
            
            # Append to the list of rows
            rows.append(temp)

        self.df = pd.concat(rows)


    def generate_window_features(self,):

        if len(self.window_features) > 0:
            for feature in self.window_features:
                lags = self.window_features[feature]
                for lag in range(1, lags + 1):
                    self.df[f'{feature}_{lag}'] = self.df[feature].shift(lag)

#====================== TRAIN TEST SPLIT ===============================================

def train_test_window(df: pd.DataFrame, training_window: int ,  testing_window: int ):
    """ 
    Takes a time series dataset and returns the train and test set based on set time window in days
        df: a pandas dataframe
        training_window: length of the historical data in days to consider
        testing_window: length of window in days of historical data use for testing (less than training_window)
    
        returns train and test dataset
    """

    training_window_date = df.Timestamp.max() - pd.DateOffset(days=training_window)
    testing_window_date = df.Timestamp.max() - pd.DateOffset(days=testing_window)

    train_set = df[(df['Timestamp'] >= training_window_date) & (df['Timestamp'] <= testing_window_date)].copy()
    test_set = df[df['Timestamp'] > testing_window_date].copy()

    train_set = train_set.sort_values('Timestamp')
    test_set = test_set.sort_values('Timestamp')

    return train_set, test_set
