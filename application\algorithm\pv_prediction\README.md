# 🌞 Solar PV Power Forecasting

This project provides a machine learning pipeline to **train** and **predict** 24-hour ahead solar PV power generation in 15-minute intervals using weather data and PV system metadata.

---

## 📦 Contents

- `pv_training.py`: Trains a multi-output regression model on historical solar generation and weather data.
- `prediction_model.py`: Uses the trained model to predict solar power for a specific PV system given forecast data.

---

## 🧾 Input Datasets

| File | Description |
|------|-------------|
| `solar_pv_attributes.csv` | PV system attributes (capacity, tilt, azimuth, installation date) |
| `solar_station_metadata.csv` | Station metadata (geolocation, elevation) |
| `solar_station_obs.csv` | Historical weather observations |
| `Solar_generation.csv` | Historical PV generation data |
| `Weather_and_forecast.csv` | Weather forecast for inference |

---

## 🏋️ Model Training (`pv_training.py`)

### Features Used

#### Weather Observations
- `GHI`, `DNI`, `DHI`
- `air_temperature`
- `cloud_cover`
- `wind_speed`
- `relative_humidity`

#### System Metadata
- `max_capacity`
- `azimuth`, `tilt`
- `latitude`, `longitude`, `elevation`

#### Time Features
- `hour`, `minute`, `dayofyear`

### Target
- `generated_power`: 96 values representing 24 hours (15-min intervals)


The input dataset columns meaning and datatypes are as follows 

| 列名                   | English (short)               | 中文 (简短)        | Type          |
|-----------------------|-------------------------------|-------------------|---------------|
| `site_name`           | Site name                     | 站点名称           | `str`         |
| `site_id`             | Site ID                       | 站点ID             | `str`         |
| `current_time`        | time of prediction            |  预测时间         | `timestamp`     |
| `max_capacity`        | Max capacity (kW)             | 最大容量（千瓦）   | `float`       |
| `azimuth`             | Azimuth angle (°)             | 方位角（度）       | `float`       |
| `tilt`                | Tilt angle (°)                | 倾斜角（度）       | `float`       |
| `latitude`            | Latitude                      | 纬度               | `float`       |
| `longitude`           | Longitude                     | 经度               | `float`       |
| `elevation`           | Elevation (m)                 | 海拔（米）         | `float`       |
| `GHI`                 | Global horizontal irradiance  | 全球水平辐照度     | `float`       |
| `DNI`                 | Direct normal irradiance      | 法线直射辐照度     | `float`       |
| `DHI`                 | Diffuse horizontal irradiance | 散射水平辐照度     | `float`       |
| `air_temperature`     | Air temperature (°C)          | 气温（℃）         | `float`       |
| `cloud_cover`         | Cloud cover (%)               | 云量（%）          | `float`       |
| `wind_speed`          | Wind speed (m/s)              | 风速（m/s）        | `float`       |
| `relative_humidity`   | Relative humidity (%)         | 相对湿度（%）      | `float`       |
| `hour`                | Hour of day                   | 小时               | `int`         |
| `minute`              | Minute                        | 分钟               | `int`         |
| `dayofyear`           | Day of year                   | 年内天数           | `int`         |
| `generated_power`     | Generated power (kW)          | 发电功率（千瓦）   | `float`       |


The input format of the data is as follows 

```python
input_data = {
    "site_name": "Site Name",
    "site_id": "Site ID",
    "current_time": "2019-11-02 23:30:00",
    "system_metadata": {
        "max_capacity": 1000.0,
        "azimuth": 180.0,
        "tilt": 30.0,
        "latitude": 40.1234,
        "longitude": -74.5678,
        "elevation": 50.0,
        "fields": [
            "Timestamp","GHI","DNI","DHI","air_temperature","cloud_cover",
            "wind_speed","relative_humidity","hour","minute",
            "dayofyear","generated_power"
        ],
        "values": [
            ["2019-11-01 00:00:00", 0.0,0.0,0.0,15.2,0.8,3.5,65.0,0,0,183,0.0],
            ["2019-11-01 00:00:15", 5.2,0.5,4.7,15.5,0.7,3.6,64.8,0,15,183,0.0],
            ["2019-11-01 00:00:30", 10.1,2.3,7.8,20.5,0.3,4.2,60.1,23,45,183,450.0]
            # … up to 96 rows for 1 day and 96*x for x days…
        ]
    }
}
```
__NOTE:__ current_time value must be in values

### Output Artifacts
- `pv_model.pkl`: Trained pv forecast model
- `pv_scaler.pkl`: Scaler used on input features

---

## 🔮 Prediction (`prediction_model.py`)

This script uses weather forecast and system metadata to predict 96 values of power generation.

### Function
```python
predpredict_from_inputict(input_data: int, start_time: str) 
```

this return the 24hour energy generation in 15min intervals that looks as follows 

```python
output = [egen_1, egen_2, ... , egen_96]
```
