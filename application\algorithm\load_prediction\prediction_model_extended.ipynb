{"cells": [{"cell_type": "code", "execution_count": 2, "id": "8e78a980", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.11803868, -0.3100816, -0.30614358, 0.58142835, 0.53823376, 0.53823376, 0.16177462, 0.13438407, 0.13438407, 0.18138038, 0.18138038, 0.17123047, 0.17123047, 0.21250874, 0.2586374, 0.2586374, 0.40960044, 0.5833224, 1.1730767, 1.0115259, 1.0115259, 0.8007238, 1.1903964, 1.4272432, 1.8393675, 1.4362613, 1.0416061, 1.0416061, 0.656055, 0.656055, 0.8462574, 0.98080224, 0.5949327, 0.430209, 0.26599926, 0.16088364, 0.04342046, 0.04342046, 0.34292528, 0.34292528, 0.34292528, 0.33590674, 0.33590674, 0.3153941, 0.3153941, 0.3015113, 0.3015113, 0.22607462, 0.3066473, 0.3066473, 0.3066473, 0.30943346, 0.23752989, 0.19929251, 0.1280028, 0.061062314, 0.067414515, 0.10983207, 0.055141296, 0.020454979, 0.020454979, 0.020454979, -0.04053007, -0.04053007, -0.04053007, -0.04053007, -0.04053007, -0.04053007, -0.04053007, -0.08154221, -0.08154221, -0.08154221, -0.08154221, -0.08154221, -0.08154221, -0.08154221, -0.08154221, -0.08154221, -0.08154221, -0.08154221, -0.061619397, -0.057709012, -0.057709012, -0.057709012, -0.057709012, -0.057709012, -0.057709012, -0.057709012, -0.057709012, -0.057709012, -0.057709012, -0.057709012, -0.057709012, -0.057709012, -0.00978616, 0.6058009]\n"]}], "source": ["import pandas as pd\n", "import helper\n", "import pickle\n", "import json\n", "\n", "# ================= LOAD INPUT DATA =========================\n", "with open(\"input_data.json\", \"r\") as f:\n", "    input_data = json.load(f)\n", "\n", "# ===================== Load model from file ======================\n", "with open('load_xgb_model.pkl', 'rb') as f:\n", "    model = pickle.load(f)\n", "\n", "# ====================== INPUT DATA PROCESSING ===============\n", "window_features = {\"total_power_demand_KWh\": 96}\n", "horizon_features = {}\n", "forecast_features = {}\n", "target_feature = \"total_power_demand_KWh\"\n", "forecast_horizon = 96\n", "time_granularity = 15 # in minutes\n", "time_granularity_str = str(time_granularity) + 'min'\n", "\n", "# ===============================================================\n", "def prepare_features_from_input(input_data: dict) -> pd.DataFrame:\n", "    current_time = input_data[\"current_time\"]\n", "    fields = input_data[\"fields\"]\n", "    values = input_data[\"time_series\"]\n", "    df_inf = pd.DataFrame(values, columns=fields)\n", "\n", "    val_columns = ['Timestamp', 'DayOfWeek', 'Hour', 'total_power_demand_KWh']\n", "    df_inf.rename(columns={'charging_consumption': 'total_power_demand_KWh'}, inplace=True)\n", "\n", "    try:\n", "        df_inf['Timestamp'] = pd.to_datetime(df_inf['Timestamp'], unit='ms')\n", "    except:\n", "        df_inf['Timestamp'] = pd.to_datetime(df_inf['Timestamp'])\n", "    df_inf = df_inf.set_index('Timestamp')\n", "    df_inf = df_inf[~df_inf.index.duplicated(keep='first')]\n", "    df_15min = df_inf.resample('15min').ffill()\n", "    df_inf = df_15min.reset_index()\n", "\n", "    df_inf['Hour'] = df_inf['Timestamp'].dt.hour\n", "    df_inf['DayOfWeek'] = df_inf['Timestamp'].dt.dayofweek\n", "    df_inf = df_inf[val_columns]\n", "\n", "    prediction_feature_generation  = helper.feature_engineering(df=df_inf, \n", "                                            target_feature=target_feature, \n", "                                            horizon_features=horizon_features, \n", "                                            window_features=window_features,\n", "                                            forecast_features=forecast_features, \n", "                                            forecast_horizon=forecast_horizon,\n", "                                            time_granularity=time_granularity,\n", "                                            inference=True)\n", "\n", "    prediction_engineered_data_inf = prediction_feature_generation.df\n", "    feature_columns = [col for col in prediction_engineered_data_inf.columns if 'time' not in col.lower()]\n", "\n", "    prediction_engineered_data_inf_point = prediction_engineered_data_inf[prediction_engineered_data_inf['Timestamp'] == current_time]\n", "    prediction_engineered_data_inf_point = prediction_engineered_data_inf_point.sort_values(by='future_Timestamp').reset_index(drop=True)\n", "    prediction_engineered_data_inf_point = prediction_engineered_data_inf_point[feature_columns]\n", "\n", "    return prediction_engineered_data_inf_point\n", "\n", "def predict_from_input(input_data: dict) -> list:\n", "    X = prepare_features_from_input(input_data)\n", "    preds = model.predict(X)\n", "    # times = [start_time + timedelta(minutes=i*INTERVAL_MINUTES) for i in range(len(preds))]\n", "    # preds_df = pd.DataFrame({\"timestamp\": times, \"predicted_power\": preds})\n", "    return list(preds)\n", "\n", "if __name__ == \"__main__\":\n", "    load_prediction_values = predict_from_input(input_data)\n", "    print(load_prediction_values)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}