## 数学建模

### 变量&已知量

设一个场站总共有 N 个充电桩，每个充电桩有 M 个枪头 (如果场站有多种类型的充电桩，比如同时存在2枪头充电桩和4枪头充电桩，$M=4$, 即M取最大值)：

**变量：**

- $pgun_{i,j}$ 表示第 $i$ 个充电桩的第 $j$ 个枪头分配到的最高功率（限制功率）



**已知量：**

- $DGun_{i,j}$ 表示第 $i$ 个充电桩的第 $j$ 个枪头的需求功率，是已知常量
- $SiteDemandLimit$ 表示场站功率限制, $PVPower, ESPower$ 分别表示光伏和储能的功率限制
- $PilePower_{i}$ 表示第 $i$ 个充电桩的总功率限制, 是已知常量
- $PileModulePower_{i, k}$ 表示第 $i$ 个充电桩的第 $k$ 个模组的功率限制，是已知量
- $Delta_{i,j}$ 表示第 $i$ 个充电桩的第 $j$ 个枪头功率分配与需求功率的可配置冗余偏差量，可进行配置，默认全为`0`；








### 目标函数

**Object function**:
$$
\min \sum_{i=1}^{N}\sum_{j=1}^{M} \lvert DGun_{i,j} - pgun_{i,j} - Delta_{i,j} \rvert
$$

目标函数意义：
- 目标函数是所有枪头功率分配与需求功率的差值的绝对值之和，结果越小表示功率分配越合理；
- 其中 $Delta_{i,j}$ 表示第 $i$ 个充电桩的第 $j$ 个枪头功率分配与需求功率的可配置冗余偏差量，可进行配置，默认全为0；







### 约束条件

**Constraints($\mathrm{s.t.}$)**:

1. 功率限制
   $$
   pgun_{i,j} \geq 0
   $$
2. 场站功率限制 + 储能和光伏功率限制
   $$
   \sum_{i=1}^{N} \sum_{j=1}^{M} pgun_{i, j} \leq \text{PVPower} + \text{ESPower} + \text{SiteDemandLimit}
   $$
3. 桩功率限制
   $$
   \sum_{j=1}^{M} pgun_{i,j} \leq PilePower_{i}, i = 1, 2, ..., N
   $$








### 模组约束条件

#### 普通桩(非N1桩)

$$
pgun_{i,j} \leq PileModulePower_{i,j} + \sum_{\substack{k=1 \\ k \neq j \\ DGun_{i,k} = 0}}^{M} PileModulePower_{i,k}
$$

对于第 $i$ 个充电桩的第 $j$ 个枪头，其分配的功率 $pgun_{i,j}$ 不应超过其自身的模组功率限制 $PileModulePower_{i,j}$，加上其他枪头（在同一个充电桩上）如果其需求功率 $DGun_{i,k}$ 为零时的模组功率限制之和。

**关键代码**

```python
# 定义变量
pgun = [[solver.NumVar(0, solver.infinity(), f'pgun_{i}_{j}') for j in range(M)] for i in range(N)]

# 假设 PileModulePower 和 DGun 是已知的
PileModulePower = [[10, 20, 30] for _ in range(N)]  # 示例数据
DGun = [[5, 0, 10] for _ in range(N)]  # 示例数据

# 添加约束
for i in range(N):
    for j in range(M):
        # 计算 sum([PileModulePower[i][k] for k in range(M) if k != j and DGun[i][k] == 0])
        additional_power = sum(PileModulePower[i][k] for k in range(M) if k != j and DGun[i][k] == 0)
        solver.Add(pgun[i][j] <= PileModulePower[i][j] + additional_power)
```








#### N1桩

需要调用 N1桩最佳功率分配算法，获取每个枪头分配的功率，然后进行约束条件检查。
例如：
<img src="./assets/N1桩.png" width="450">

```python
"""
存在多个最优解：
[
    (0.10069, [[2], [4, 1, 3], [], [8, 5, 6, 7]]), 
    (0.10069, [[2], [4, 1, 5], [], [8, 3, 6, 7]])
]
"""
gun_demands = [75, 135, 0, 220]
modules_powers = [40, 80, 40, 80, 40, 80, 40, 80]

n1_pile = N1PileBestPowerDistribution(gun_demands, modules_powers, directed_connections)
min_diff, best_distribution = n1_pile.get_best_power_distribution()
print(min_diff, best_distribution)
# 输出：0.10069, [[2], [4, 1, 3], [], [8, 5, 6, 7]]
```
容易得出如下结果
```python
"""
得出功率限制为 [80, 160, 0, 240]
pgun[i][0] <= 80
pgun[i][1] <= 160
pgun[i][2] <= 0
pgun[i][3] <= 240
"""
```
所以有如下约束条件：
$$
pgun_{i, j} <= Process(BestGunDistribution)[j], i = 1, 2, ..., N, Pile_i为N1桩
$$

- $Process()$ 表示对 $BestGunDistribution$ 进行处理，获取每个枪头分配的功率上限



### 线性规划求解实现

#### 求解器

具体线性规划求解代码详见 `application/algorithm/power_distribution/linear_programming.py`
```python
from linear_programming import LinearProgrammingPowerDistributionSolver


solver = LinearProgrammingPowerDistributionSolver(input_data)
ret_values, pgun_slover_values = solver.get_solve_result()
```





#### 多解求解器

**新增线性规划多解求解器`PowerDistributionMultipleSolutionsSolver`**

具体线性规划求解代码详见 `application/algorithm/power_distribution/linear_programming.py`

```python
from linear_programming import PowerDistributionMultipleSolutionsSolver


solver = PowerDistributionMultipleSolutionsSolver(t_input_data)
solutions = solver.get_multiple_solutions()
```

**核心实现逻辑：**

1. **求解初始最优解：**使用 OR-Tools 找到最优目标函数值和一个初始解。
2. **固定最优目标值：**添加约束将目标函数固定为最优值，将问题转化为探索所有满足该值的解。
3. **枚举顶点解：**通过迭代求解，随机化目标函数以探索不同顶点, 为每个变量设置随机系数来探索不同的顶点解，添加切割约束排除已找到的解，逐步找到所有顶点解。
4. **排除已找到解**：使用线性约束，所有变量的加权和不能等于当前解的加权和,  这样可以有效排除当前解，同时保持线性规划的特性，然后继续寻找新解。



**后期可优化方向：**

1. **描述边界解**：如果最优解集是一个高维面，使用线性代数方法（例如求解约束系统的解空间）来参数化描述。
2. **可选：使用多面体计算库**：对于高维问题，结合专门的多面体分析工具（如 pycddlib）来直接计算最优解集的顶点和面。





## N1桩最佳功率分配算法
### 分体桩简介
分体桩由主机柜和终端构成，功率模块集中在主机柜里，各个终端共享主机柜的充电模块。

以DS480为例，主机柜的功率模块结构如下，其有8个模组，其中4个模组为40kw，4个模组为80kw。该主机柜可以配多个枪，枪的数量从4到8不等，下图例子是配置了4个枪（枪A、枪B、枪C、枪D）的情况。每个枪均有直连模组，比如枪A的直连模组是第二组，枪B的直连模组是第四组。直连模组和其他模组在调度分配上的优先级是不一样的，当某个枪（设为枪A）插枪时，其直连模组必须分配给枪A，即使该直连模组被其他枪所用，也需要从其他枪中减投，然后分配到枪A中。

<img src="./assets/N1桩.png" width="450">





### 模组的拓扑约束关系

以枪A为例的拓扑约束关系如下：

<img src="./assets/N1桩拓扑关系.png" width="450">





### 动态规划求解(回溯)

#### 求解流程
1. 建立图模型以及相应的数据结构
2. 通过动态规划或者回溯算法求解出所有可行的分配方案
3. 确立优化目标函数
4. 通过计算优化目标函数的值，挑选出最优的分配方案（可能同时存在多个）


#### 目标函数
$$
\argmin Obj = \frac{1}{UsedGunsCnt} * \sum_{i=1}^{K} \lvert \frac{DGun_{i}}{PLimit_{i}} - 1 \rvert * \alpha
$$

**其中：**
- $\alpha$ 是一个加权系数，当不存在多余模组可利用时，$\alpha = 1$，否则按如下公式计算
  $$
  \alpha =
  \begin{cases}
  1 & \sum_{i=1}^{K} DGun_{i} \geq PartModulePower \\\\
  10^{GunNum - \sum_{i=1}^{K} Bolean(Dgun_i <= PLimit_i)} & \sum_{i=1}^{K} DGun_{i} < PartModulePower
  \end{cases}
  $$
- $PartModulePower$ 表示所有模组功率之和减去最小模组功率
  $$
  PartModulePower = \sum_{j=1}^{J} ModulePower_{j} - min(ModulePower_{j} | j \in 1...J)
  $$

具体实现详见代码 `application/algorithm/power_distribution/n1_pile_distribution_dp.py`


求解案例：
```python
"""
存在多个最优解：
[
    (0.125, [[2, 1, 7], [4, 3, 5], [6], [8]]), 
    (0.125, [[2, 3, 7], [4, 1, 5], [6], [8]])
]
"""
gun_demands = [140, 180, 90, 70]
modules_powers = [40, 80, 40, 80, 40, 80, 40, 80]

n1_pile = N1PileBestPowerDistribution(gun_demands, modules_powers)
min_diff, best_distribution = n1_pile.get_best_power_distribution()
print(min_diff, best_distribution)
# 输出：0.125, [[2, 1, 7], [4, 3, 5], [6], [8]]
```
求解图例如下
<img src="./assets/N1桩求解图例.png">







## 数据样例
### 输入数据
整体输入数据样例：
```json
{
    "pv_power": 20.00,  # 光伏发电功率
    "es_power": 300.50,  # 储能电池充放电功率, 正表示放电，负表示充电
    "site_grid_limit": 1200.00,  # 场站的电网功率限制
    "site_demand_limit": 800.00,  # 场站demand limit值
    # 场站内部所有桩号
    "site_pile_info": ["DE0120B1GN7C00009J", "DL7480B2GR6C00017B"],
    # 所有桩的模块信息
    "module_info": module_info,
    # 所有工作枪的非压制功率曲线
    "demand_info": {
        "DE0120B1GN7C00009J_1": {
            "predicted_time": 1748327973,
            "vehicel_non_suppressed_power_list": [75.50, 70.20, 0.0],
            "vehicle_vol": 400
        },
        "DE0120B1GN7C00009J_2": {
            "predicted_time": 1748327973,
            "vehicel_non_suppressed_power_list": [75.50, 70.20, 135.00],
            "vehicle_vol": 400
        },
        "DL7480B2GR6C00017B_1": {
            "predicted_time": 1748327973,
            "vehicel_non_suppressed_power_list": [100.00, 120.25, 140.00],
            "vehicle_vol": 400
        },
        "DL7480B2GR6C00017B_2": {
            "predicted_time": 1748327973,
            "vehicel_non_suppressed_power_list": [80.00, 150.25, 180.00],
            "vehicle_vol": 400
        },
        "DL7480B2GR6C00017B_3": {
            "predicted_time": 1748327973,
            "vehicel_non_suppressed_power_list": [80.00, 90.25, 0.00],
            "vehicle_vol": 400
        },
        "DL7480B2GR6C00017B_4": {
            "predicted_time": 1748327973,
            "vehicel_non_suppressed_power_list": [80.00, 100.25, 70.00],
            "vehicle_vol": 400
        }
    }
}
```


`module_info` 样例：
```json
[
        {
            "sn": "DE0120B1GN7C00009J",
            "chargeType": 2,
            "gunNum": 2,
            "ratedPower": 120.0,
            "moduleList": [
                {
                    "moduleNo": 1,
                    "unitNum": 3,
                    "unitPower": 20.0
                },
                {
                    "moduleNo": 2,
                    "unitNum": 3,
                    "unitPower": 20.0
                }
            ],
            "gunBusList": [
                {
                    "sn": null,
                    "busNo": 1,
                    "gunNo": 1,
                    "bomIndex": 1
                },
                {
                    "sn": null,
                    "busNo": 2,
                    "gunNo": 2,
                    "bomIndex": 2
                },
                {
                    "sn": null,
                    "busNo": 3,
                    "gunNo": 3,
                    "bomIndex": 3
                },
                {
                    "sn": null,
                    "busNo": 4,
                    "gunNo": 4,
                    "bomIndex": 4
                }
            ],
            "gunMaxCurr": [
                {
                    "gunNo": 1,
                    "maxCurr": 300.0
                },
                {
                    "gunNo": 2,
                    "maxCurr": 200.0
                }
            ]
        },
        {
            "sn": "DL7480B2GR6C00017B",
            "chargeType": 5,
            "gunNum": 4,
            "ratedPower": 480.0,
            "moduleList": [
                {"moduleNo": 1, "unitNum": 1, "unitPower": 40.0},
                {"moduleNo": 2, "unitNum": 2, "unitPower": 40.0},
                {"moduleNo": 3, "unitNum": 1, "unitPower": 40.0},
                {"moduleNo": 4, "unitNum": 2, "unitPower": 40.0},
                {"moduleNo": 5, "unitNum": 1, "unitPower": 40.0},
                {"moduleNo": 6, "unitNum": 2, "unitPower": 40.0},
                {"moduleNo": 7, "unitNum": 1, "unitPower": 40.0},
                {"moduleNo": 8, "unitNum": 2, "unitPower": 40.0}
            ],
            "gunBusList": [
                {
                    "sn": null,
                    "busNo": 1,
                    "gunNo": 1,
                    "bomIndex": 1
                },
                {
                    "sn": null,
                    "busNo": 2,
                    "gunNo": 2,
                    "bomIndex": 2
                },
                {
                    "sn": null,
                    "busNo": 3,
                    "gunNo": 3,
                    "bomIndex": 3
                },
                {
                    "sn": null,
                    "busNo": 4,
                    "gunNo": 4,
                    "bomIndex": 4
                }
            ],
            "gunMaxCurr": []
}]
```






### 输出数据

```json
{
    "predicted_time": 1748327973,
    "site_charger_power_allocation_list": {
        "DE0040B1GP5C00046K_1": 0.00,
        "DE0040B1GP5C00046K_2": 120.00,
        "DE7480B2GRCC000025_1": 140.00,
        "DE7480B2GRCC000025_2": 180.00,
        "DE7480B2GRCC000025_3": 0.00,
        "DE7480B2GRCC000025_4": 70.00
    }
}
```





## 储能调度算法

### 数学模型
**目标函数：**

$$
arg \max_{PowerBattery(x)}
(\int_{t0}^{t0+24}PayloadActual(x) * PriceCharging(x)dx - \\
\int_{t0}^{t0+24}(PayloadActual(x) + PowerBattery(x) - PV(x)) * PriceGrid(x) dx)
$$

其中：

- $PayloadActual(x)$, $PriceCharging(x)$, $PriceGrid(x)$, $PV(x)$ 表示已知量, 都是关于时间$x$的函数
- $Grid(x) = PayloadActual(x) + PowerBattery(x) - PV(x)$ 表示电网功率



### 求解方法

求解方法：**离散化 + 线性规划**
连续时间域上的优化问题，可以离散化处理，将时间域离散为多个时间点，然后使用线性规划求解。

- 将24小时时间区间离散化为等间隔的时间点（如每15分钟一个点）
- 在每个时间点 $x$ 处，$PowerBattery(x)$ 作为决策变量
- 目标函数转化为离散求和形式
- 使用线性规划求解器（如 CPLEX、Gurobi）求解
- 优点：实现简单，求解速度快
- 缺点：精度受离散化间隔影响

