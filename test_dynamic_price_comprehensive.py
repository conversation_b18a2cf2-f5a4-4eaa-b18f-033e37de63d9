#!/usr/bin/env python3
"""
动态电价获取功能的综合测试用例
覆盖各种正常和异常场景
"""

import sys
import os
import logging
import time
import threading
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
import pytest

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('comprehensive_test.log', mode='w', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

class TestDynamicPriceScheduler:
    """动态电价调度器测试类"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.logger = setup_logging()
        
    def test_scheduler_initialization_success(self):
        """测试调度器正常初始化"""
        self.logger.info("测试调度器正常初始化")
        
        try:
            from application.algorithm_schedule.dynamic_price_scheduler import DynamicPriceScheduler
            
            scheduler = DynamicPriceScheduler()
            
            # 验证基本属性
            assert scheduler.site_no == 'ALL'
            assert scheduler.running == False
            assert scheduler.first_run == True
            assert scheduler.last_dynamic_price_biz_seq is None
            
            self.logger.info("✅ 调度器初始化测试通过")
            
        except Exception as e:
            self.logger.error(f"❌ 调度器初始化测试失败: {e}")
            raise
    
    def test_scheduler_timing_calculation(self):
        """测试调度器时间计算"""
        self.logger.info("测试调度器时间计算")
        
        try:
            from application.algorithm_schedule.dynamic_price_scheduler import DynamicPriceScheduler
            import pytz
            
            scheduler = DynamicPriceScheduler()
            
            # 测试不同时间点的下次执行时间计算
            cet = pytz.timezone('CET')
            
            # 测试场景1: 当前时间在12:10之前
            test_time_before = datetime(2024, 1, 15, 10, 0, 0, tzinfo=cet)
            with patch('datetime.datetime') as mock_datetime:
                mock_datetime.now.return_value = test_time_before
                mock_datetime.side_effect = lambda *args, **kw: datetime(*args, **kw)
                
                next_exec_seconds = scheduler._calculate_next_execution_time()
                expected_seconds = (2 * 60 + 10) * 60  # 2小时10分钟
                
                assert abs(next_exec_seconds - expected_seconds) < 60, f"时间计算错误: {next_exec_seconds} vs {expected_seconds}"
            
            # 测试场景2: 当前时间在12:10之后
            test_time_after = datetime(2024, 1, 15, 14, 0, 0, tzinfo=cet)
            with patch('datetime.datetime') as mock_datetime:
                mock_datetime.now.return_value = test_time_after
                mock_datetime.side_effect = lambda *args, **kw: datetime(*args, **kw)
                
                next_exec_seconds = scheduler._calculate_next_execution_time()
                expected_seconds = (22 * 60 + 10) * 60  # 22小时10分钟（明天的12:10）
                
                assert abs(next_exec_seconds - expected_seconds) < 60, f"时间计算错误: {next_exec_seconds} vs {expected_seconds}"
            
            self.logger.info("✅ 调度器时间计算测试通过")
            
        except Exception as e:
            self.logger.error(f"❌ 调度器时间计算测试失败: {e}")
            raise
    
    def test_scheduler_stop_mechanism(self):
        """测试调度器停止机制"""
        self.logger.info("测试调度器停止机制")
        
        try:
            from application.algorithm_schedule.dynamic_price_scheduler import DynamicPriceScheduler
            
            scheduler = DynamicPriceScheduler()
            
            # 测试停止机制
            scheduler.stop()
            assert scheduler.running == False
            assert scheduler._stop_event.is_set() == True
            
            self.logger.info("✅ 调度器停止机制测试通过")
            
        except Exception as e:
            self.logger.error(f"❌ 调度器停止机制测试失败: {e}")
            raise

class TestDynamicPriceFetch:
    """动态电价获取功能测试类"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.logger = setup_logging()
        
    def test_normal_price_fetch_success(self):
        """测试正常电价获取成功场景"""
        self.logger.info("测试正常电价获取成功场景")
        
        try:
            from application.algorithm_schedule.dynamic_price_fetch import dynamic_price_fetch
            
            # 模拟正常的API响应
            mock_price_data = {
                'prices': [
                    {
                        'price': 100 + i,
                        'deliveryStart': f'2024-01-01T{i:02d}:00:00Z',
                        'deliveryEnd': f'2024-01-01T{(i+1) % 24:02d}:00:00Z' if i < 23 else '2024-01-02T00:00:00Z'
                    } for i in range(24)
                ],
                'marketMainCurrency': 'EUR'
            }
            
            mock_db = Mock()
            mock_db.save_site_electricity_price = Mock()
            
            with patch('application.algorithm_schedule.dynamic_price_fetch.get_dynamic_electricity_price', return_value=mock_price_data), \
                 patch('application.algorithm_schedule.dynamic_price_fetch.DBOperate', return_value=mock_db), \
                 patch('application.algorithm_schedule.dynamic_price_fetch.send_electricity_price_with_retry', return_value=True):
                
                result = dynamic_price_fetch('ALL', datetime.now().date())
                
                # 验证结果
                assert result is not None
                assert 'biz_seq' in result
                assert 'electricity_price' in result
                assert len(result['electricity_price']) > 0
                
                self.logger.info("✅ 正常电价获取测试通过")
                
        except Exception as e:
            self.logger.error(f"❌ 正常电价获取测试失败: {e}")
            raise
    
    def test_api_timeout_scenario(self):
        """测试API超时场景"""
        self.logger.info("测试API超时场景")
        
        try:
            from application.algorithm_schedule.dynamic_price_fetch import get_region_prices
            import requests
            
            # 模拟API超时
            def mock_timeout_api(*args, **kwargs):
                raise requests.exceptions.Timeout("API请求超时")
            
            mock_db = Mock()
            
            with patch('application.algorithm_schedule.dynamic_price_fetch.get_dynamic_electricity_price', side_effect=mock_timeout_api), \
                 patch('application.algorithm_schedule.dynamic_price_fetch.DBOperate', return_value=mock_db), \
                 patch('application.algorithm_schedule.dynamic_price_fetch.time.sleep'):  # 加速测试
                
                result = get_region_prices(['DE'], datetime.now().date())
                
                # 验证超时后的处理
                assert isinstance(result, list)
                assert len(result) == 0  # 超时应该返回空列表
                
                self.logger.info("✅ API超时场景测试通过")
                
        except Exception as e:
            self.logger.error(f"❌ API超时场景测试失败: {e}")
            raise
    
    def test_api_connection_error(self):
        """测试API连接错误场景"""
        self.logger.info("测试API连接错误场景")
        
        try:
            from application.algorithm_schedule.dynamic_price_fetch import get_region_prices
            import requests
            
            # 模拟连接错误
            def mock_connection_error(*args, **kwargs):
                raise requests.exceptions.ConnectionError("无法连接到服务器")
            
            mock_db = Mock()
            
            with patch('application.algorithm_schedule.dynamic_price_fetch.get_dynamic_electricity_price', side_effect=mock_connection_error), \
                 patch('application.algorithm_schedule.dynamic_price_fetch.DBOperate', return_value=mock_db), \
                 patch('application.algorithm_schedule.dynamic_price_fetch.time.sleep'):
                
                result = get_region_prices(['DE'], datetime.now().date())
                
                assert isinstance(result, list)
                assert len(result) == 0
                
                self.logger.info("✅ API连接错误场景测试通过")
                
        except Exception as e:
            self.logger.error(f"❌ API连接错误场景测试失败: {e}")
            raise
    
    def test_invalid_api_response_format(self):
        """测试API返回无效格式数据"""
        self.logger.info("测试API返回无效格式数据")
        
        try:
            from application.algorithm_schedule.dynamic_price_fetch import get_region_prices
            
            # 测试各种无效格式
            invalid_responses = [
                None,  # 空响应
                {},    # 空字典
                {'prices': []},  # 空价格数组
                {'prices': [{'price': 100}] * 12},  # 数据不完整
                {'prices': [{'invalid': 'data'}] * 24},  # 缺少必要字段
                {'invalid_structure': 'data'},  # 完全错误的结构
                "invalid_json_string",  # 非字典类型
                {'prices': None},  # prices为None
            ]
            
            mock_db = Mock()
            
            for i, invalid_response in enumerate(invalid_responses):
                self.logger.info(f"测试无效响应 {i+1}: {type(invalid_response)}")
                
                with patch('application.algorithm_schedule.dynamic_price_fetch.get_dynamic_electricity_price', return_value=invalid_response), \
                     patch('application.algorithm_schedule.dynamic_price_fetch.DBOperate', return_value=mock_db), \
                     patch('application.algorithm_schedule.dynamic_price_fetch.time.sleep'):
                    
                    result = get_region_prices(['DE'], datetime.now().date())
                    
                    # 所有无效响应都应该返回空列表
                    assert isinstance(result, list)
                    assert len(result) == 0
            
            self.logger.info("✅ 无效API响应格式测试通过")
            
        except Exception as e:
            self.logger.error(f"❌ 无效API响应格式测试失败: {e}")
            raise
    
    def test_database_operation_failure(self):
        """测试数据库操作失败场景"""
        self.logger.info("测试数据库操作失败场景")
        
        try:
            from application.algorithm_schedule.dynamic_price_fetch import get_region_prices
            
            # 模拟正常的API响应
            mock_price_data = {
                'prices': [
                    {
                        'price': 100 + i,
                        'deliveryStart': f'2024-01-01T{i:02d}:00:00Z',
                        'deliveryEnd': f'2024-01-01T{(i+1) % 24:02d}:00:00Z' if i < 23 else '2024-01-02T00:00:00Z'
                    } for i in range(24)
                ],
                'marketMainCurrency': 'EUR'
            }
            
            # 模拟数据库操作失败
            mock_db = Mock()
            mock_db.save_site_electricity_price.side_effect = Exception("数据库连接失败")
            
            with patch('application.algorithm_schedule.dynamic_price_fetch.get_dynamic_electricity_price', return_value=mock_price_data), \
                 patch('application.algorithm_schedule.dynamic_price_fetch.DBOperate', return_value=mock_db), \
                 patch('application.algorithm_schedule.dynamic_price_fetch.time.sleep'):
                
                # 即使数据库操作失败，也应该能返回电价数据（因为数据库操作是异步的）
                result = get_region_prices(['DE'], datetime.now().date())
                
                # 验证即使数据库失败，电价数据仍然可用
                assert isinstance(result, list)
                # 由于数据库操作是异步的，主流程应该仍然成功
                
                self.logger.info("✅ 数据库操作失败场景测试通过")
                
        except Exception as e:
            self.logger.error(f"❌ 数据库操作失败场景测试失败: {e}")
            raise

    def test_partial_region_failure(self):
        """测试部分区域获取失败场景"""
        self.logger.info("测试部分区域获取失败场景")

        try:
            from application.algorithm_schedule.dynamic_price_fetch import get_region_prices

            # 模拟部分区域成功，部分失败
            def mock_partial_success_api(areas, date, currency="EUR"):
                if areas in ['DE', 'FR']:
                    return {
                        'prices': [
                            {
                                'price': 100 + i,
                                'deliveryStart': f'2024-01-01T{i:02d}:00:00Z',
                                'deliveryEnd': f'2024-01-01T{(i+1) % 24:02d}:00:00Z' if i < 23 else '2024-01-02T00:00:00Z'
                            } for i in range(24)
                        ],
                        'marketMainCurrency': 'EUR'
                    }
                else:
                    return None  # 其他区域失败

            mock_db = Mock()

            with patch('application.algorithm_schedule.dynamic_price_fetch.get_dynamic_electricity_price', side_effect=mock_partial_success_api), \
                 patch('application.algorithm_schedule.dynamic_price_fetch.DBOperate', return_value=mock_db), \
                 patch('application.algorithm_schedule.dynamic_price_fetch.time.sleep'):

                regions = ['AT', 'BE', 'DE', 'FR', 'NL', 'PL']
                result = get_region_prices(regions, datetime.now().date())

                # 验证只有成功的区域被返回
                assert isinstance(result, list)
                assert len(result) == 2  # 只有DE和FR成功

                success_regions = [item['region'] for item in result]
                assert 'DE' in success_regions
                assert 'FR' in success_regions
                assert 'AT' not in success_regions

                self.logger.info("✅ 部分区域获取失败场景测试通过")

        except Exception as e:
            self.logger.error(f"❌ 部分区域获取失败场景测试失败: {e}")
            raise

    def test_retry_mechanism_success(self):
        """测试重试机制成功场景"""
        self.logger.info("测试重试机制成功场景")

        try:
            from application.algorithm_schedule.dynamic_price_fetch import get_region_prices

            call_count = {}

            def mock_retry_success_api(areas, date, currency="EUR"):
                if areas not in call_count:
                    call_count[areas] = 0
                call_count[areas] += 1

                # DE第一次就成功
                if areas == 'DE':
                    return {
                        'prices': [{'price': 100 + i, 'deliveryStart': f'2024-01-01T{i:02d}:00:00Z', 'deliveryEnd': f'2024-01-01T{(i+1) % 24:02d}:00:00Z' if i < 23 else '2024-01-02T00:00:00Z'} for i in range(24)],
                        'marketMainCurrency': 'EUR'
                    }
                # FR第二次成功
                elif areas == 'FR' and call_count[areas] >= 2:
                    return {
                        'prices': [{'price': 200 + i, 'deliveryStart': f'2024-01-01T{i:02d}:00:00Z', 'deliveryEnd': f'2024-01-01T{(i+1) % 24:02d}:00:00Z' if i < 23 else '2024-01-02T00:00:00Z'} for i in range(24)],
                        'marketMainCurrency': 'EUR'
                    }
                else:
                    return None

            mock_db = Mock()

            with patch('application.algorithm_schedule.dynamic_price_fetch.get_dynamic_electricity_price', side_effect=mock_retry_success_api), \
                 patch('application.algorithm_schedule.dynamic_price_fetch.DBOperate', return_value=mock_db), \
                 patch('application.algorithm_schedule.dynamic_price_fetch.time.sleep'):

                result = get_region_prices(['DE', 'FR'], datetime.now().date())

                # 等待后台重试完成
                time.sleep(0.5)

                # 验证重试机制
                assert call_count['DE'] == 1  # DE第一次就成功
                assert call_count.get('FR', 0) >= 1  # FR至少被调用一次

                # 验证第一轮返回的结果
                assert isinstance(result, list)
                assert len(result) >= 1  # 至少DE成功

                self.logger.info("✅ 重试机制成功场景测试通过")

        except Exception as e:
            self.logger.error(f"❌ 重试机制成功场景测试失败: {e}")
            raise

    def test_retry_mechanism_max_attempts(self):
        """测试重试机制达到最大次数"""
        self.logger.info("测试重试机制达到最大次数")

        try:
            from application.algorithm_schedule.dynamic_price_fetch import get_region_prices

            call_count = {}

            def mock_always_fail_api(areas, date, currency="EUR"):
                if areas not in call_count:
                    call_count[areas] = 0
                call_count[areas] += 1
                return None  # 总是失败

            mock_db = Mock()

            with patch('application.algorithm_schedule.dynamic_price_fetch.get_dynamic_electricity_price', side_effect=mock_always_fail_api), \
                 patch('application.algorithm_schedule.dynamic_price_fetch.DBOperate', return_value=mock_db), \
                 patch('application.algorithm_schedule.dynamic_price_fetch.time.sleep'):

                result = get_region_prices(['DE'], datetime.now().date())

                # 等待后台重试完成
                time.sleep(0.5)

                # 验证达到最大重试次数
                assert call_count['DE'] >= 1  # 至少被调用一次（第一轮）

                # 验证返回空结果
                assert isinstance(result, list)
                assert len(result) == 0

                self.logger.info("✅ 重试机制最大次数测试通过")

        except Exception as e:
            self.logger.error(f"❌ 重试机制最大次数测试失败: {e}")
            raise

class TestEdgeCasesAndExceptions:
    """边界情况和异常场景测试类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.logger = setup_logging()

    def test_memory_leak_prevention(self):
        """测试内存泄漏预防"""
        self.logger.info("测试内存泄漏预防")

        try:
            from application.algorithm_schedule.dynamic_price_fetch import get_region_prices
            import gc

            # 记录初始内存状态
            gc.collect()
            initial_objects = len(gc.get_objects())

            mock_price_data = {
                'prices': [{'price': 100 + i, 'deliveryStart': f'2024-01-01T{i:02d}:00:00Z', 'deliveryEnd': f'2024-01-01T{(i+1) % 24:02d}:00:00Z' if i < 23 else '2024-01-02T00:00:00Z'} for i in range(24)],
                'marketMainCurrency': 'EUR'
            }

            mock_db = Mock()

            # 执行多次获取操作
            with patch('application.algorithm_schedule.dynamic_price_fetch.get_dynamic_electricity_price', return_value=mock_price_data), \
                 patch('application.algorithm_schedule.dynamic_price_fetch.DBOperate', return_value=mock_db), \
                 patch('application.algorithm_schedule.dynamic_price_fetch.time.sleep'):

                for i in range(10):
                    result = get_region_prices(['DE'], datetime.now().date())
                    del result  # 显式删除结果

            # 等待后台线程完成
            time.sleep(1.0)

            # 检查内存状态
            gc.collect()
            final_objects = len(gc.get_objects())

            # 验证没有严重的内存泄漏（允许一些合理的增长）
            object_increase = final_objects - initial_objects
            assert object_increase < 1000, f"可能存在内存泄漏，对象增加了 {object_increase} 个"

            self.logger.info("✅ 内存泄漏预防测试通过")

        except Exception as e:
            self.logger.error(f"❌ 内存泄漏预防测试失败: {e}")
            raise

    def test_concurrent_access(self):
        """测试并发访问场景"""
        self.logger.info("测试并发访问场景")

        try:
            from application.algorithm_schedule.dynamic_price_fetch import get_region_prices

            mock_price_data = {
                'prices': [{'price': 100 + i, 'deliveryStart': f'2024-01-01T{i:02d}:00:00Z', 'deliveryEnd': f'2024-01-01T{(i+1) % 24:02d}:00:00Z' if i < 23 else '2024-01-02T00:00:00Z'} for i in range(24)],
                'marketMainCurrency': 'EUR'
            }

            mock_db = Mock()
            results = []
            exceptions = []

            def fetch_worker():
                try:
                    result = get_region_prices(['DE'], datetime.now().date())
                    results.append(result)
                except Exception as e:
                    exceptions.append(e)

            with patch('application.algorithm_schedule.dynamic_price_fetch.get_dynamic_electricity_price', return_value=mock_price_data), \
                 patch('application.algorithm_schedule.dynamic_price_fetch.DBOperate', return_value=mock_db), \
                 patch('application.algorithm_schedule.dynamic_price_fetch.time.sleep'):

                # 创建多个并发线程
                threads = []
                for i in range(5):
                    thread = threading.Thread(target=fetch_worker)
                    threads.append(thread)
                    thread.start()

                # 等待所有线程完成
                for thread in threads:
                    thread.join(timeout=5.0)

                # 验证并发访问结果
                assert len(exceptions) == 0, f"并发访问出现异常: {exceptions}"
                assert len(results) == 5, f"期望5个结果，实际得到 {len(results)} 个"

                # 验证所有结果都是有效的
                for result in results:
                    assert isinstance(result, list)

            self.logger.info("✅ 并发访问场景测试通过")

        except Exception as e:
            self.logger.error(f"❌ 并发访问场景测试失败: {e}")
            raise

    def test_extreme_data_sizes(self):
        """测试极端数据大小场景"""
        self.logger.info("测试极端数据大小场景")

        try:
            from application.algorithm_schedule.dynamic_price_fetch import get_region_prices

            # 测试超大数据
            large_price_data = {
                'prices': [{'price': 100 + i, 'deliveryStart': f'2024-01-01T{i:02d}:00:00Z', 'deliveryEnd': f'2024-01-01T{(i+1) % 24:02d}:00:00Z' if i < 23 else '2024-01-02T00:00:00Z'} for i in range(24)],
                'marketMainCurrency': 'EUR',
                'extra_large_field': 'x' * 10000  # 10KB的额外数据
            }

            # 测试空数据
            empty_price_data = {'prices': []}

            # 测试超长区域列表
            many_regions = [f'R{i:03d}' for i in range(100)]

            mock_db = Mock()

            test_cases = [
                (large_price_data, ['DE'], "超大数据"),
                (empty_price_data, ['DE'], "空数据"),
                (large_price_data, many_regions, "超长区域列表"),
            ]

            for price_data, regions, description in test_cases:
                self.logger.info(f"测试 {description}")

                with patch('application.algorithm_schedule.dynamic_price_fetch.get_dynamic_electricity_price', return_value=price_data), \
                     patch('application.algorithm_schedule.dynamic_price_fetch.DBOperate', return_value=mock_db), \
                     patch('application.algorithm_schedule.dynamic_price_fetch.time.sleep'):

                    result = get_region_prices(regions[:5], datetime.now().date())  # 限制区域数量

                    # 验证函数能正常处理极端情况
                    assert isinstance(result, list)

            self.logger.info("✅ 极端数据大小场景测试通过")

        except Exception as e:
            self.logger.error(f"❌ 极端数据大小场景测试失败: {e}")
            raise

    def test_system_resource_exhaustion(self):
        """测试系统资源耗尽场景"""
        self.logger.info("测试系统资源耗尽场景")

        try:
            from application.algorithm_schedule.dynamic_price_fetch import get_region_prices

            # 模拟内存不足
            def mock_memory_error(*args, **kwargs):
                raise MemoryError("系统内存不足")

            # 模拟磁盘空间不足
            def mock_disk_full_error(*args, **kwargs):
                raise OSError("磁盘空间不足")

            mock_db = Mock()

            # 测试内存不足场景
            with patch('application.algorithm_schedule.dynamic_price_fetch.get_dynamic_electricity_price', side_effect=mock_memory_error), \
                 patch('application.algorithm_schedule.dynamic_price_fetch.DBOperate', return_value=mock_db), \
                 patch('application.algorithm_schedule.dynamic_price_fetch.time.sleep'):

                result = get_region_prices(['DE'], datetime.now().date())

                # 验证系统资源不足时的处理
                assert isinstance(result, list)
                assert len(result) == 0

            # 测试磁盘空间不足场景
            mock_db.save_site_electricity_price.side_effect = mock_disk_full_error

            mock_price_data = {
                'prices': [{'price': 100 + i, 'deliveryStart': f'2024-01-01T{i:02d}:00:00Z', 'deliveryEnd': f'2024-01-01T{(i+1) % 24:02d}:00:00Z' if i < 23 else '2024-01-02T00:00:00Z'} for i in range(24)],
                'marketMainCurrency': 'EUR'
            }

            with patch('application.algorithm_schedule.dynamic_price_fetch.get_dynamic_electricity_price', return_value=mock_price_data), \
                 patch('application.algorithm_schedule.dynamic_price_fetch.DBOperate', return_value=mock_db), \
                 patch('application.algorithm_schedule.dynamic_price_fetch.time.sleep'):

                result = get_region_prices(['DE'], datetime.now().date())

                # 验证磁盘空间不足时仍能返回电价数据（因为数据库操作是异步的）
                assert isinstance(result, list)

            self.logger.info("✅ 系统资源耗尽场景测试通过")

        except Exception as e:
            self.logger.error(f"❌ 系统资源耗尽场景测试失败: {e}")
            raise

class TestDataIntegrityAndValidation:
    """数据完整性和验证测试类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.logger = setup_logging()

    def test_price_data_validation(self):
        """测试电价数据验证"""
        self.logger.info("测试电价数据验证")

        try:
            from application.algorithm_schedule.dynamic_price_fetch import _fetch_single_region_price

            # 测试各种数据验证场景
            test_cases = [
                # (数据, 期望结果, 描述)
                (None, False, "空数据"),
                ({}, False, "空字典"),
                ({'prices': []}, False, "空价格数组"),
                ({'prices': [{'price': 100}] * 12}, False, "数据不完整（12小时）"),
                ({'prices': [{'price': 100}] * 25}, False, "数据过多（25小时）"),
                ({'prices': [{'price': 100, 'deliveryStart': f'2024-01-01T{i:02d}:00:00Z', 'deliveryEnd': f'2024-01-01T{(i+1) % 24:02d}:00:00Z' if i < 23 else '2024-01-02T00:00:00Z'} for i in range(24)]}, True, "正确的24小时数据"),
                ({'prices': [{'price': None}] * 24}, False, "价格为None"),
                ({'prices': [{'price': 'invalid'}] * 24}, False, "价格为非数字"),
                ({'prices': [{'price': float('inf')}] * 24}, False, "价格为无穷大"),
                ({'prices': [{'price': float('nan')}] * 24}, False, "价格为NaN"),
            ]

            mock_db = Mock()
            today = datetime.now().date()
            electricity_prices = []

            for price_data, expected, description in test_cases:
                self.logger.info(f"测试数据验证: {description}")

                with patch('application.algorithm_schedule.dynamic_price_fetch.get_dynamic_electricity_price', return_value=price_data):

                    result = _fetch_single_region_price('DE', today, mock_db, today, electricity_prices)

                    assert result == expected, f"数据验证失败: {description}, 期望 {expected}, 实际 {result}"

            self.logger.info("✅ 电价数据验证测试通过")

        except Exception as e:
            self.logger.error(f"❌ 电价数据验证测试失败: {e}")
            raise

    def test_timezone_handling(self):
        """测试时区处理"""
        self.logger.info("测试时区处理")

        try:
            from application.algorithm_schedule.dynamic_price_fetch import dynamic_price_fetch
            import pytz

            # 测试不同时区的日期处理
            timezones = [
                pytz.timezone('CET'),
                pytz.timezone('UTC'),
                pytz.timezone('US/Eastern'),
                pytz.timezone('Asia/Shanghai'),
            ]

            mock_price_data = {
                'prices': [{'price': 100 + i, 'deliveryStart': f'2024-01-01T{i:02d}:00:00Z', 'deliveryEnd': f'2024-01-01T{(i+1) % 24:02d}:00:00Z' if i < 23 else '2024-01-02T00:00:00Z'} for i in range(24)],
                'marketMainCurrency': 'EUR'
            }

            mock_db = Mock()

            for tz in timezones:
                self.logger.info(f"测试时区: {tz}")

                # 模拟不同时区的当前时间
                test_time = datetime(2024, 1, 15, 12, 0, 0, tzinfo=tz)

                with patch('application.algorithm_schedule.dynamic_price_fetch.get_dynamic_electricity_price', return_value=mock_price_data), \
                     patch('application.algorithm_schedule.dynamic_price_fetch.DBOperate', return_value=mock_db), \
                     patch('application.algorithm_schedule.dynamic_price_fetch.send_electricity_price_with_retry', return_value=True), \
                     patch('datetime.datetime') as mock_datetime:

                    mock_datetime.now.return_value = test_time
                    mock_datetime.side_effect = lambda *args, **kw: datetime(*args, **kw)

                    result = dynamic_price_fetch('ALL', test_time.date())

                    # 验证时区处理正确
                    assert result is not None
                    assert 'electricity_price' in result

            self.logger.info("✅ 时区处理测试通过")

        except Exception as e:
            self.logger.error(f"❌ 时区处理测试失败: {e}")
            raise

    def test_date_boundary_conditions(self):
        """测试日期边界条件"""
        self.logger.info("测试日期边界条件")

        try:
            from application.algorithm_schedule.dynamic_price_fetch import dynamic_price_fetch

            # 测试各种边界日期
            boundary_dates = [
                datetime(2024, 1, 1).date(),    # 年初
                datetime(2024, 12, 31).date(),  # 年末
                datetime(2024, 2, 29).date(),   # 闰年2月29日
                datetime(2024, 3, 1).date(),    # 闰年后的3月1日
                datetime(2023, 2, 28).date(),   # 非闰年2月28日
                datetime(2024, 6, 21).date(),   # 夏至
                datetime(2024, 12, 21).date(),  # 冬至
            ]

            mock_price_data = {
                'prices': [{'price': 100 + i, 'deliveryStart': f'2024-01-01T{i:02d}:00:00Z', 'deliveryEnd': f'2024-01-01T{(i+1) % 24:02d}:00:00Z' if i < 23 else '2024-01-02T00:00:00Z'} for i in range(24)],
                'marketMainCurrency': 'EUR'
            }

            mock_db = Mock()

            for test_date in boundary_dates:
                self.logger.info(f"测试边界日期: {test_date}")

                with patch('application.algorithm_schedule.dynamic_price_fetch.get_dynamic_electricity_price', return_value=mock_price_data), \
                     patch('application.algorithm_schedule.dynamic_price_fetch.DBOperate', return_value=mock_db), \
                     patch('application.algorithm_schedule.dynamic_price_fetch.send_electricity_price_with_retry', return_value=True):

                    result = dynamic_price_fetch('ALL', test_date)

                    # 验证边界日期处理正确
                    assert result is not None
                    assert 'electricity_price' in result

            self.logger.info("✅ 日期边界条件测试通过")

        except Exception as e:
            self.logger.error(f"❌ 日期边界条件测试失败: {e}")
            raise

def run_comprehensive_tests():
    """运行所有综合测试"""
    logger = setup_logging()

    logger.info("=" * 80)
    logger.info("开始运行动态电价获取功能的综合测试")
    logger.info("=" * 80)

    test_classes = [
        TestDynamicPriceScheduler,
        TestDynamicPriceFetch,
        TestEdgeCasesAndExceptions,
        TestDataIntegrityAndValidation,
    ]

    total_tests = 0
    passed_tests = 0
    failed_tests = []

    for test_class in test_classes:
        logger.info(f"\n{'='*60}")
        logger.info(f"运行测试类: {test_class.__name__}")
        logger.info(f"{'='*60}")

        # 获取测试类中的所有测试方法
        test_methods = [method for method in dir(test_class) if method.startswith('test_')]

        for method_name in test_methods:
            total_tests += 1
            logger.info(f"\n运行测试: {method_name}")

            try:
                # 创建测试实例并运行测试
                test_instance = test_class()
                test_instance.setup_method()
                test_method = getattr(test_instance, method_name)
                test_method()

                passed_tests += 1
                logger.info(f"✅ {method_name} 通过")

            except Exception as e:
                failed_tests.append((test_class.__name__, method_name, str(e)))
                logger.error(f"❌ {method_name} 失败: {e}")

    # 输出测试总结
    logger.info(f"\n{'='*80}")
    logger.info("测试总结")
    logger.info(f"{'='*80}")
    logger.info(f"总测试数: {total_tests}")
    logger.info(f"通过测试: {passed_tests}")
    logger.info(f"失败测试: {len(failed_tests)}")
    logger.info(f"成功率: {(passed_tests/total_tests)*100:.1f}%")

    if failed_tests:
        logger.info(f"\n失败的测试:")
        for class_name, method_name, error in failed_tests:
            logger.info(f"  - {class_name}.{method_name}: {error}")

    if passed_tests == total_tests:
        logger.info("\n🎉 所有测试都通过了！")
        logger.info("动态电价获取功能测试验证成功")
        return True
    else:
        logger.error(f"\n❌ {len(failed_tests)} 个测试失败")
        return False

if __name__ == '__main__':
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)
