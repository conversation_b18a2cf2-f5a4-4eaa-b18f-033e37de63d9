#!/usr/bin/env python3
"""
运行储能调度方法的单元测试脚本
"""

import sys
import os
import unittest
from io import StringIO

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def run_tests():
    """运行所有储能调度相关的测试"""
    print("=" * 60)
    print("运行储能调度方法单元测试")
    print("=" * 60)
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    try:
        # 加载测试模块
        from tests.test_storage_dispatch import TestStorageDispatch
        suite.addTests(loader.loadTestsFromTestCase(TestStorageDispatch))
        
        # 运行测试
        runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
        result = runner.run(suite)
        
        # 输出测试结果摘要
        print("\n" + "=" * 60)
        print("测试结果摘要:")
        print(f"总测试数: {result.testsRun}")
        print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
        print(f"失败: {len(result.failures)}")
        print(f"错误: {len(result.errors)}")
        
        if result.failures:
            print("\n失败的测试:")
            for test, traceback in result.failures:
                print(f"- {test}: {traceback}")
        
        if result.errors:
            print("\n错误的测试:")
            for test, traceback in result.errors:
                print(f"- {test}: {traceback}")
        
        print("=" * 60)
        
        # 返回是否所有测试都通过
        return len(result.failures) == 0 and len(result.errors) == 0
        
    except ImportError as e:
        print(f"导入测试模块失败: {e}")
        print("请确保项目路径正确，并且所有依赖都已安装")
        return False
    except Exception as e:
        print(f"运行测试时发生错误: {e}")
        return False

def main():
    """主函数"""
    success = run_tests()
    
    if success:
        print("✅ 所有测试通过!")
        sys.exit(0)
    else:
        print("❌ 部分测试失败!")
        sys.exit(1)

if __name__ == '__main__':
    main()
