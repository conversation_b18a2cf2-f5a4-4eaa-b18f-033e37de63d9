import hashlib
import json
from typing import Dict, Any, Optional, Tuple

from cachetools import TTLCache

from application.utils.logger import setup_logger

logger = setup_logger("data_change_detector", direction="utils")


class DataChangeDetector:
    """
    数据变化检测器
    
    通过计算和比较数据哈希值来检测数据是否发生变化
    使用内存缓存避免重复数据插入
    """

    # 支持的数据类型
    SUPPORTED_DATA_TYPES = {
        'station_data',
        'demand_data',
        'sell_price',
        'purchase_price_data'
    }

    def __init__(self, cache_ttl_hours: int = 2, cache_maxsize: int = 1000):
        """
        初始化数据变化检测器
        
        Args:
            cache_ttl_hours: 内存缓存TTL时间（小时）
            cache_maxsize: 内存缓存最大条目数
        """
        # 内存缓存：key为"site_no:data_type"，value为哈希值
        self.hash_cache = TTLCache(maxsize=cache_maxsize, ttl=cache_ttl_hours * 3600)
        self.logger = logger

    def _calculate_data_hash(self, data: Any) -> str:
        """
        计算数据的SHA256哈希值
        
        Args:
            data: 要计算哈希的数据（支持List、Dict等可序列化类型）
            
        Returns:
            str: SHA256哈希值
        """
        try:
            # 对数据进行排序标准化，确保相同内容但不同顺序产生相同哈希
            normalized_data = self._normalize_data_for_hash(data)
            
            # 将数据转换为规范化的JSON字符串
            json_string = json.dumps(normalized_data, sort_keys=True, ensure_ascii=False, separators=(',', ':'))
            return hashlib.sha256(json_string.encode('utf-8')).hexdigest()
        except Exception as e:
            self.logger.error(f"计算数据哈希失败: {str(e)}")
            raise

    def _normalize_data_for_hash(self, data: Any) -> Any:
        """
        对数据进行标准化处理，确保相同内容但不同顺序的数据产生相同哈希
        
        Args:
            data: 要标准化的数据
            
        Returns:
            Any: 标准化后的数据
        """
        if isinstance(data, list):
            # 对列表进行排序
            # 先递归标准化每个元素，然后排序
            normalized_items = [self._normalize_data_for_hash(item) for item in data]
            try:
                # 尝试直接排序（适用于基本类型）
                return sorted(normalized_items)
            except TypeError:
                # 如果直接排序失败（复杂对象），转换为字符串再排序
                return sorted(normalized_items, key=lambda x: json.dumps(x, sort_keys=True, ensure_ascii=False))
        elif isinstance(data, dict):
            # 对字典的值递归标准化
            return {key: self._normalize_data_for_hash(value) for key, value in data.items()}
        else:
            # 基本类型直接返回
            return data

    def _get_cache_key(self, site_no: str, data_type: str) -> str:
        """生成缓存键"""
        return f"{site_no}:{data_type}"

    def _get_cached_hash(self, site_no: str, data_type: str) -> Optional[str]:
        """
        从内存缓存获取哈希值
        
        Args:
            site_no: 场站编号
            data_type: 数据类型
            
        Returns:
            Optional[str]: 缓存的哈希值，没有则返回None
        """
        cache_key = self._get_cache_key(site_no, data_type)
        return self.hash_cache.get(cache_key)

    def _set_cached_hash(self, site_no: str, data_type: str, data_hash: str) -> None:
        """
        设置内存缓存中的哈希值
        
        Args:
            site_no: 场站编号
            data_type: 数据类型
            data_hash: 哈希值
        """
        cache_key = self._get_cache_key(site_no, data_type)
        self.hash_cache[cache_key] = data_hash

    def is_data_changed(self, site_no: str, data_type: str, data: Any) -> Tuple[bool, str]:
        """
        检测数据是否发生变化
        
        Args:
            site_no: 场站编号
            data_type: 数据类型
            data: 要检测的数据
            
        Returns:
            Tuple[bool, str]: (是否发生变化, 当前数据的哈希值)
        """
        if data_type not in self.SUPPORTED_DATA_TYPES:
            raise ValueError(f"不支持的数据类型: {data_type}")

        # 计算当前数据的哈希值
        current_hash = self._calculate_data_hash(data)

        # 从内存缓存获取上次的哈希值
        cached_hash = self._get_cached_hash(site_no, data_type)

        # 比较哈希值
        if cached_hash is None:
            # 第一次处理该数据（服务启动后首次或缓存过期）
            self.logger.info(f"首次处理数据: site_no={site_no}, data_type={data_type}")
            return True, current_hash

        if current_hash != cached_hash:
            self.logger.info(f"数据发生变化: site_no={site_no}, data_type={data_type}, "
                             f"old_hash={cached_hash[:8]}..., new_hash={current_hash[:8]}...")
            return True, current_hash
        else:
            self.logger.debug(f"数据无变化，跳过插入: site_no={site_no}, data_type={data_type}")
            return False, current_hash

    def update_data_hash(self, site_no: str, data_type: str, data_hash: str) -> bool:
        """
        更新数据哈希值（在数据成功插入后调用）
        
        Args:
            site_no: 场站编号
            data_type: 数据类型
            data_hash: 新的哈希值
            
        Returns:
            bool: 更新是否成功
        """
        # 更新内存缓存
        self._set_cached_hash(site_no, data_type, data_hash)
        self.logger.debug(f"内存缓存哈希值更新成功: site_no={site_no}, data_type={data_type}")
        return True

    def clear_cache(self, site_no: Optional[str] = None, data_type: Optional[str] = None) -> None:
        """
        清空缓存
        
        Args:
            site_no: 指定场站编号（可选）
            data_type: 指定数据类型（可选）
        """
        if site_no and data_type:
            # 清空指定的缓存项
            cache_key = self._get_cache_key(site_no, data_type)
            self.hash_cache.pop(cache_key, None)
            self.logger.info(f"清空指定缓存: site_no={site_no}, data_type={data_type}")
        else:
            # 清空所有缓存
            self.hash_cache.clear()
            self.logger.info("清空所有内存缓存")

    def get_cache_info(self) -> Dict[str, Any]:
        """
        获取缓存信息
        
        Returns:
            Dict[str, Any]: 缓存统计信息
        """
        return {
            "cache_size": len(self.hash_cache),
            "maxsize": self.hash_cache.maxsize,
            "ttl": self.hash_cache.ttl,
            "currsize": self.hash_cache.currsize,
            "supported_data_types": list(self.SUPPORTED_DATA_TYPES)
        }


# 全局单例实例
data_change_detector = DataChangeDetector()
