import numpy as np
import matplotlib.pyplot as plt
from matplotlib.gridspec import GridSpec


def curve(x, y_dict, title='curve', xlabel='x', ylabel='y', 
          figsize=(10, 6), colors=None, styles=None, markers=None, markevery=None,
          grid=True, legend_loc='best', save_path=None, dpi=100):
    """
    绘制多条曲线图
    
    参数:
        x: array-like，所有曲线共用的x轴数据
        y_dict: dict，格式为 {曲线名称: y值数组}
        title: str，图表标题
        xlabel: str，x轴标签
        ylabel: str，y轴标签
        figsize: tuple，图表尺寸，默认(10, 6)
        colors: list，曲线颜色列表，若为None则使用默认颜色
        styles: list，线型列表，如['-', '--', '-.', ':']，若为None则使用默认实线
        markers: list，标记列表，如['o', 's', '^']，若为None则不使用标记
        grid: bool，是否显示网格
        legend_loc: str，图例位置，默认'best'
        save_path: str，保存图片路径，如为None则不保存
        dpi: int，保存图片的DPI
    
    返回:
        fig, ax: 图表对象和坐标轴对象
    """
    fig, ax = plt.subplots(figsize=figsize)
    
    # 默认颜色、线型和标记
    if colors is None:
        colors = plt.cm.tab10.colors  # 使用matplotlib的Tab10色板
    if styles is None:
        styles = ['-'] * len(y_dict)  # 默认使用实线
    if markers is None:
        markers = [''] * len(y_dict)  # 默认不使用标记
    
    # 确保列表长度足够
    colors = colors * (len(y_dict) // len(colors) + 1)
    styles = styles * (len(y_dict) // len(styles) + 1)
    markers = markers * (len(y_dict) // len(markers) + 1)
    
    # 绘制每条曲线
    for i, (name, y_data) in enumerate(y_dict.items()):
        ax.plot(x, y_data, label=name, color=colors[i], linestyle=styles[i], marker=markers[i], markevery=markevery)
    
    # 设置图表属性
    ax.set_title(title, fontsize=14)
    ax.set_xlabel(xlabel, fontsize=12)
    ax.set_ylabel(ylabel, fontsize=12)
    
    if grid:
        ax.grid(True, linestyle='--', alpha=0.7)
    
    ax.legend(loc=legend_loc)
    
    plt.tight_layout()
    
    # 保存图表
    if save_path:
        plt.savefig(save_path, dpi=dpi, bbox_inches='tight')
    
    return fig, ax


def concat_curves_vertical(figure_list, titles=None, figsize=(10, None), 
                           spacing=0.3, save_path=None, dpi=100):
    """
    将多个图表在垂直方向连接为一张图
    
    参数:
        figure_list: list，包含多个(fig, ax)元组，由curve函数返回的图表对象
        titles: list，每个子图的标题，如果为None则使用原标题
        figsize: tuple，(宽度, 高度)，如果高度为None则自动计算
        spacing: float，子图之间的间距，默认0.3
        save_path: str，保存图片的路径，如果为None则不保存
        dpi: int，保存图片的DPI
    
    返回:
        combined_fig: 合并后的图表对象
    """
    n_figures = len(figure_list)
    
    if n_figures == 0:
        raise ValueError("图表列表为空")
    
    # 自动计算高度
    if figsize[1] is None:
        # 根据子图数量计算总高度
        height = n_figures * figsize[0] * 0.6
        figsize = (figsize[0], height)
    
    # 创建新的大图
    combined_fig = plt.figure(figsize=figsize)
    gs = GridSpec(n_figures, 1, figure=combined_fig, hspace=spacing)
    
    # 逐个添加子图
    for i, (fig, ax) in enumerate(figure_list):
        # 提取原图的数据和属性
        lines = ax.get_lines()
        x_label = ax.get_xlabel()
        y_label = ax.get_ylabel()
        original_title = ax.get_title()
        legend_handles, legend_labels = ax.get_legend_handles_labels()
        x_lim = ax.get_xlim()
        y_lim = ax.get_ylim()
        grid_visible = ax.grid()
        
        # 确定标题
        if titles is not None and i < len(titles):
            title = titles[i]
        else:
            title = original_title
        
        # 创建新的子图
        new_ax = combined_fig.add_subplot(gs[i, 0])
        
        # 复制线条数据
        for line in lines:
            x_data = line.get_xdata()
            y_data = line.get_ydata()
            label = line.get_label()
            color = line.get_color()
            linestyle = line.get_linestyle()
            marker = line.get_marker()
            markevery = line.get_markevery()
            
            new_ax.plot(x_data, y_data, label=label, color=color, 
                        linestyle=linestyle, marker=marker, markevery=markevery)
        
        # 设置坐标轴标签、标题和网格
        new_ax.set_xlabel(x_label)
        new_ax.set_ylabel(y_label)
        new_ax.set_title(title)
        new_ax.grid(grid_visible)
        
        # 设置坐标轴范围
        new_ax.set_xlim(x_lim)
        new_ax.set_ylim(y_lim)
        
        # 添加图例
        if legend_labels:
            new_ax.legend(handles=legend_handles, labels=legend_labels)
    
    combined_fig.subplots_adjust(hspace=spacing, 
                                 left=0.1, 
                                 right=0.9,
                                 top=0.95,
                                 bottom=0.05)
    
    # 保存图片
    if save_path:
        combined_fig.savefig(save_path, dpi=dpi, bbox_inches='tight')
    
    plt.close('all')  # 关闭所有原始图表
    
    return combined_fig
