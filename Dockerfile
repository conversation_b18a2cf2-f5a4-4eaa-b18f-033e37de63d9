# 使用Python官方镜像作为基础镜像
FROM python:3.12-slim

# 设置工作目录
WORKDIR /app

# 创建日志目录
RUN mkdir -p /app/logs

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONPATH=/app \
    ENVIRONMENT=dev

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 先复制依赖文件
COPY requirements.txt .

# 安装Python依赖（这层会被缓存）, 
RUN pip install --no-cache-dir --default-timeout=1000 -r requirements.txt

# 安装cpu版本的torch，有gpu环境需要修改dockerfile
RUN pip install torch==2.5.1 torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu

# 复制应用代码（这层会经常变化）
COPY . .

# 声明需要使用的密钥（在构建时安全注入环境变量）
RUN --mount=type=secret,id=config-encryption-key,target=/tmp/config-encryption-key \
    if [ -f "/tmp/config-encryption-key" ]; then \
      mkdir -p /app/config && \
      cat /tmp/config-encryption-key > /app/config/encryption_key && \
      chmod 400 /app/config/encryption_key && \
      echo "加密密钥已安全存储"; \
    else \
      echo "警告：未提供加密密钥"; \
    fi

# 创建启动脚本，在容器启动时设置环境变量
RUN echo '#!/bin/bash\n\
# 从K8s环境变量获取密钥（如果已经设置）\n\
if [ -z "$CONFIG_ENCRYPTION_KEY" ]; then\n\
  # 环境变量未设置，尝试从文件读取\n\
  if [ -f "/app/config/encryption_key" ]; then\n\
    export CONFIG_ENCRYPTION_KEY=$(cat /app/config/encryption_key)\n\
    echo "从/app/config/encryption_key成功读取密钥并设置环境变量"\n\
  else\n\
    echo "警告：未找到加密密钥文件"\n\
  fi\n\
else\n\
  echo "使用K8s提供的环境变量密钥"\n\
fi\n\
\n\
# 打印环境变量以验证（仅调试用）\n\
echo "CONFIG_ENCRYPTION_KEY环境变量状态: ${CONFIG_ENCRYPTION_KEY:+已设置}${CONFIG_ENCRYPTION_KEY:-未设置}"\n\
\n\
# 执行传入的命令\n\
exec "$@"' > /app/entrypoint.sh && chmod +x /app/entrypoint.sh

# 暴露端口
EXPOSE 8000

# 使用新创建的entrypoint脚本
ENTRYPOINT ["/app/entrypoint.sh"]

# 设置默认shell，使容器保持运行
# CMD ["bash", "-c", "tail -f /dev/null"]

# 启动命令
CMD ["uvicorn", "application.server:app", "--host", "0.0.0.0", "--port", "8000"]
