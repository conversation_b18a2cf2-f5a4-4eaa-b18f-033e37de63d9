import unittest
from unittest.mock import patch, MagicMock

from application.api_manage.site_noc_manager import SiteNOCManager
from application.utils.constants import ResponseCode


class TestSiteNOCManager(unittest.TestCase):
    def setUp(self):
        """测试前的准备工作"""
        self.site_noc_manager = SiteNOCManager()
        self.site_no = "TEST_SITE_001"
        self.pile_list = ["PILE_001", "PILE_002"]

        # 模拟设备数据
        self.device_data = {
            "sn": "PILE_001",
            "chargeType": 2,
            "gunNum": 2,
            "ratedPower": 120,
            "moduleList": [
                {
                    "type": "power",
                    "moduleNo": 1,
                    "unitPower": 30,
                    "unitNum": 2
                },
                {
                    "type": "power",
                    "moduleNo": 2,
                    "unitPower": 30,
                    "unitNum": 2
                }
            ],
            "gunBusList": [
                {
                    "gunNo": 1,
                    "bomIndex": "BUS_001"
                },
                {
                    "gunNo": 2,
                    "bomIndex": "BUS_002"
                }
            ],
            "gunMaxCurr": [
                {
                    "gunNo": 1,
                    "maxCurr": 200
                },
                {
                    "gunNo": 2,
                    "maxCurr": 200
                }
            ]
        }

    @patch('application.api_manage.site_noc_manager.requests.post')
    @patch('application.db_operate.db_operate.DBOperate.handle_site_ai_status')
    @patch('application.db_operate.db_operate.DBOperate.save_pile_data')
    @patch('application.db_operate.db_operate.DBOperate.update_or_create_charger')
    @patch('application.db_operate.db_operate.DBOperate.delete_pile_module_info')
    @patch('application.db_operate.db_operate.DBOperate.insert_pile_module_info')
    def test_subscribe_site_success(self, mock_insert_module, mock_delete_module,
                                    mock_update_charger, mock_save_pile, mock_handle_ai, mock_post):
        """测试成功订阅场站的情况"""
        # 模拟AI状态未激活
        mock_handle_ai.return_value = False

        # 模拟API调用成功
        mock_response = MagicMock()
        mock_response.status_code = ResponseCode.SUCCESS
        mock_response.json.return_value = {
            "code": ResponseCode.SUCCESS,
            "data": [self.device_data]
        }
        mock_post.return_value = mock_response

        # 模拟数据库操作成功
        mock_save_pile.return_value = True
        mock_update_charger.return_value = True
        mock_delete_module.return_value = True
        mock_insert_module.return_value = True

        # 执行测试
        result = self.site_noc_manager.subscribe_site(self.site_no, self.pile_list)

        # 验证结果
        self.assertTrue(result)
        mock_handle_ai.assert_called_once_with(self.site_no)
        mock_post.assert_called()
        mock_save_pile.assert_called()
        mock_update_charger.assert_called()
        mock_delete_module.assert_called()
        mock_insert_module.assert_called()

    @patch('application.api_manage.site_noc_manager.requests.post')
    @patch('application.db_operate.db_operate.DBOperate.handle_site_ai_status')
    def test_subscribe_site_ai_already_active(self, mock_handle_ai, mock_post):
        """测试AI已经激活的情况"""
        # 模拟AI状态已激活
        mock_handle_ai.return_value = True

        # 执行测试
        result = self.site_noc_manager.subscribe_site(self.site_no, self.pile_list)

        # 验证结果
        self.assertTrue(result)
        mock_handle_ai.assert_called_once_with(self.site_no)
        mock_post.assert_not_called()

    @patch('application.api_manage.site_noc_manager.requests.post')
    @patch('application.db_operate.db_operate.DBOperate.handle_site_ai_status')
    def test_subscribe_site_api_failure(self, mock_handle_ai, mock_post):
        """测试API调用失败的情况"""
        # 模拟AI状态未激活
        mock_handle_ai.return_value = False

        # 模拟API调用失败
        mock_response = MagicMock()
        mock_response.status_code = ResponseCode.SUCCESS
        mock_response.json.return_value = {
            "code": ResponseCode.ERROR,
            "message": "API调用失败"
        }
        mock_post.return_value = mock_response

        # 执行测试
        result = self.site_noc_manager.subscribe_site(self.site_no, self.pile_list)

        # 验证结果
        self.assertFalse(result)
        mock_handle_ai.assert_called_once_with(self.site_no)
        mock_post.assert_called()

    def test_subscribe_site_invalid_input(self):
        """测试无效输入的情况"""
        # 测试空场站编号
        with self.assertRaises(ValueError):
            self.site_noc_manager.subscribe_site("", self.pile_list)

        # 测试空充电桩列表
        with self.assertRaises(ValueError):
            self.site_noc_manager.subscribe_site(self.site_no, [])


if __name__ == '__main__':
    unittest.main()
