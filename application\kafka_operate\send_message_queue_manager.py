import json
import queue
import threading
import time

from application.utils.idempotency import generate_idempotency_key
from application.utils.logger import setup_logger

logger = setup_logger("kafka_producer", direction="kafka")

def send_control_result_to_queue(control_result: dict, control_type: str, site_no: str):
    """
    将调度结果发送到Kafka队列
    
    Args:
        control_result: 控制结果字典
        control_type: 控制类型 ('storage_dispatch', 'charge_power_dispatch', 'pv_control')
        site_no: 场站编号
    """
    try:
        if control_type == 'storage_dispatch':
            _handle_storage_dispatch(control_result, site_no)
        elif control_type == 'charge_power_dispatch':
            _handle_charge_power_dispatch(control_result, site_no)
        elif control_type == 'pv_control':
            _handle_pv_control(control_result, site_no)
        else:
            logger.error(f"场站 {site_no} 未知的控制类型: {control_type}")
            
    except Exception as e:
        logger.error(f"场站 {site_no} 发送控制结果失败: {str(e)}")


def _handle_storage_dispatch(control_result: dict, site_no: str):
    """处理储能调度结果 - 一次性下发完整96点数组"""
    scheduling_time = control_result.get('scheduling_time')
    es_scheduling_strategy = control_result.get('es_scheduling_strategy', [])
    
    if not es_scheduling_strategy:
        logger.warning(f"场站 {site_no} 储能调度策略为空")
        return
    
    # 一次性发送完整的96点储能调度数组
    timestamp_ms = scheduling_time * 1000  # 转换为毫秒时间戳
    message = {
        "ts": str(timestamp_ms),
        "biz_seq": generate_idempotency_key({"timestamp_ms": timestamp_ms, "site_no": site_no}),
        "site_no": site_no,
        "es_control": {
            "power": es_scheduling_strategy
        },
        "charger_control": None,
        "pv_inverter_control": None
    }
    
    _send_message_to_queue(message, site_no, "storage_dispatch")
    logger.info(f"场站 {site_no} 发送储能调度数组消息，包含 {len(es_scheduling_strategy)} 个功率点")


def _handle_charge_power_dispatch(control_result: dict, site_no: str):
    """处理充电功率调度结果"""
    scheduling_time = control_result.get('scheduling_time')
    site_charger_power_allocations = control_result.get('site_charger_power_allocations', {})
    
    # 解析充电桩功率分配
    charger_control = []
    for pile_connect_id, power in site_charger_power_allocations.items():
        # pile_connect_id格式为: pile_sn_connect_id
        parts = pile_connect_id.rsplit('_', 1)
        if len(parts) == 2:
            pile_sn, connect_id = parts
            charger_control.append({
                "pile_sn": pile_sn,
                "connect_id": connect_id,
                "power": power
            })
        else:
            logger.warning(f"场站 {site_no} 无法解析充电桩连接ID: {pile_connect_id}")
    
    message = {
        "ts": str(scheduling_time * 1000),
        "biz_seq": generate_idempotency_key({"scheduling_time": scheduling_time}),
        "site_no": site_no,
        "es_control": None,
        "charger_control": charger_control,
        "pv_inverter_control": None
    }
    
    _send_message_to_queue(message, site_no, "charger_control")


def _handle_pv_control(control_result: dict, site_no: str):
    """处理光伏控制结果"""
    scheduling_time = control_result.get('scheduling_time')
    pv_power_limit = control_result.get('pv_power_limit')
    
    message = {
        "ts": str(scheduling_time * 1000),
        "biz_seq": generate_idempotency_key({"scheduling_time": scheduling_time}),
        "site_no": site_no,
        "es_control": None,
        "charger_control": None,
        "pv_inverter_control": {
            "power_limit": pv_power_limit
        }
    }
    
    _send_message_to_queue(message, site_no, "pv_control")


def _send_message_to_queue(message: dict, site_no: str = None, control_type: str = "unknown"):
    """将消息发送到队列"""
    message_json = json.dumps(message, ensure_ascii=False)
    message_bytes = message_json.encode('utf-8')

    # 从消息中获取site_no，如果参数没有提供的话
    if site_no is None:
        site_no = message.get('site_no', 'unknown')

    send_message_queue_manager.put({
        'topic': "ai-ems-msg",
        'value': message_bytes,
        'callback': None,
        'site_no': site_no,  # 添加site_no用于日志
        'control_type': control_type  # 添加control_type用于日志
    })

    # 更新场站心跳活动时间（非心跳消息才更新）
    if control_type != 'heartbeat' and site_no != 'unknown':
        site_heartbeat_manager.update_site_activity(site_no)

    logger.info(f"场站 {site_no} {control_type} 消息已加入队列")


class SendMessageQueueManager:
    _instance = None
    _queue = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(SendMessageQueueManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if self._queue is None:
            self._queue = queue.Queue()

    @property
    def queue(self):
        return self._queue

    def put(self, message_data):
        """将消息放入队列"""
        try:
            self._queue.put(message_data)
        except Exception as e:
            logger.error(f"消息入队失败: {str(e)}")

    def get(self, timeout=1.0):
        """从队列获取消息"""
        try:
            return self._queue.get(timeout=timeout)
        except queue.Empty:
            return None
        except Exception as e:
            logger.error(f"消息出队失败: {str(e)}")
            return None


# 创建全局单例实例
send_message_queue_manager = SendMessageQueueManager()


class SiteHeartbeatManager:
    """场站心跳管理器"""
    
    def __init__(self):
        self._site_heartbeats = {}  # site_no -> heartbeat_info
        self._lock = threading.Lock()
        self._heartbeat_interval = 5.0  # 5秒心跳间隔
    
    def start_site_heartbeat(self, site_no: str):
        """为指定场站启动心跳"""
        with self._lock:
            if site_no in self._site_heartbeats:
                logger.info(f"场站 {site_no} 心跳已存在，跳过创建")
                return
            
            # 创建心跳信息
            heartbeat_info = {
                'site_no': site_no,
                'thread': None,
                'stop_event': threading.Event(),
                'last_heartbeat_time': time.time()
            }
            
            # 启动心跳线程
            thread = threading.Thread(
                target=self._heartbeat_loop,
                args=(site_no, heartbeat_info),
                daemon=True,
                name=f"heartbeat_{site_no}"
            )
            thread.start()
            heartbeat_info['thread'] = thread
            
            self._site_heartbeats[site_no] = heartbeat_info
            logger.info(f"场站 {site_no} 心跳定时器启动成功")
    
    def stop_site_heartbeat(self, site_no: str):
        """停止指定场站的心跳"""
        with self._lock:
            if site_no not in self._site_heartbeats:
                logger.warning(f"场站 {site_no} 心跳不存在，无需停止")
                return
            
            heartbeat_info = self._site_heartbeats[site_no]
            heartbeat_info['stop_event'].set()
            
            if heartbeat_info['thread'] and heartbeat_info['thread'].is_alive():
                heartbeat_info['thread'].join(timeout=2)
            
            del self._site_heartbeats[site_no]
            logger.info(f"场站 {site_no} 心跳定时器停止成功")
    
    def update_site_activity(self, site_no: str):
        """更新场站活动时间（当有其他消息发送时调用）"""
        with self._lock:
            if site_no in self._site_heartbeats:
                self._site_heartbeats[site_no]['last_heartbeat_time'] = time.time()
    
    def _heartbeat_loop(self, site_no: str, heartbeat_info: dict):
        """场站心跳循环"""
        stop_event = heartbeat_info['stop_event']
        
        while not stop_event.is_set():
            try:
                current_time = time.time()
                last_heartbeat_time = heartbeat_info['last_heartbeat_time']
                
                # 检查是否需要发送心跳
                if current_time - last_heartbeat_time >= self._heartbeat_interval:
                    self._send_site_heartbeat(site_no)
                    heartbeat_info['last_heartbeat_time'] = current_time
                
                # 等待1秒后继续检查
                if stop_event.wait(timeout=1.0):
                    break
                    
            except Exception as e:
                logger.error(f"场站 {site_no} 心跳发送出错: {str(e)}")
                if stop_event.wait(timeout=10):
                    break
        
        logger.info(f"场站 {site_no} 心跳线程结束")
    
    def _send_site_heartbeat(self, site_no: str):
        """发送场站心跳消息"""
        ts = int(time.time() * 1000)
        heartbeat = {
            "ts": str(ts),
            "biz_seq": generate_idempotency_key({"timestamp_ms": ts, "site_no": site_no}),
            "site_no": site_no,
            "es_control": {"power": [12, 18, 25, 5, 8, 15, 22, 30, 35, 28, 40, 45, 52, 48, 55, 60, 65, 58, 70, 75, 68,
                                     72, 80, 85, 78, 82, 88, 90, 85, 80, 75, 70, 65, 60, 55, 50, 45, 40, 35, 30, 25, 20,
                                     15, 10, 5, 0, -5, -10, -15, -20, -25, -30, -35, -40, -45, -50, -55, -60, -65, -70,
                                     -75, -80, -85, -90, -85, -80, -75, -70, -65, -60, -55, -50, -45, -40, -35, -30,
                                     -25, -20, -15, -10, -5, 0, 5, 10, 15, 20, 25, 30, 28, 25, 20, 15, 10, 8, 5, -6]},
            "charger_control": [{"pile_sn": "DE7480D2GS6C000213", "connect_id": "1", "power": 40},
                                {"pile_sn": "DE7480D2GS6C000213", "connect_id": "2", "power": 60},
                                {"pile_sn": "DE7480B2GRCC000104", "connect_id": "1", "power": 60},
                                {"pile_sn": "DE7480B2GRCC000104", "connect_id": "2", "power": 80}],
            "pv_inverter_control": {"power_limit": 8}
        }
        
        message_json = json.dumps(heartbeat, ensure_ascii=False)
        message_bytes = message_json.encode('utf-8')
        
        send_message_queue_manager.put({
            'topic': "ai-ems-msg",
            'value': message_bytes,
            'callback': None,
            'site_no': site_no,
            'control_type': 'heartbeat'
        })
        
        logger.info(f"场站 {site_no} 心跳消息已发送")
    
    def get_active_sites(self):
        """获取当前有心跳的场站列表"""
        with self._lock:
            return list(self._site_heartbeats.keys())


# 全局场站心跳管理器实例
site_heartbeat_manager = SiteHeartbeatManager()


