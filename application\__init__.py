import os
import sys
from pathlib import Path

from application.utils.decrypt_config import decrypt_config
from application.utils.logger import setup_logger

logger = setup_logger("AI_EMS")

# Map environment names to configuration files
ENV_CONFIG_MAP = {
    'local': 'local.yaml.enc',
    'dev': 'dev.yaml.enc',
    'test': 'test.yaml.enc',
    'prod': 'prod.yaml.enc',
    'cn-northwest-1-test': 'cn-northwest-1-test-config.yaml.enc',
    'cn-northwest-1-prod': 'cn-northwest-1-prod-config.yaml.enc'
}

# Get project root directory
ROOT_DIR = Path(__file__).parent.parent

# Set environment variables
os.environ['PYTHONPATH'] = str(ROOT_DIR)
sys.path.append(str(ROOT_DIR))

# Determine which configuration file to use based on environment variables
# Priority:
# 1. Configuration file corresponding to ENVIRONMENT variable
# 2. Default to dev.yaml.enc

def set_project_root():
    # Get project root directory
    root_path = Path(__file__).parent.parent.resolve()

    # Set environment variables
    os.environ['PROJECT_ROOT'] = str(root_path)


def get_config_path() -> Path:
    """
    Determine which configuration file to use based on environment variables
    
    Priority:
    1. Configuration file corresponding to ENVIRONMENT variable
    2. Default to dev.yaml.enc
    """
    # Project root directory
    base_dir = Path(os.getenv('PROJECT_ROOT'))
    
    # Configuration file directory
    config_dir = base_dir / 'application' / "settings" / "encrypted"
    
    # Get environment name
    env = os.environ.get("ENVIRONMENT", "dev").lower()
    
    config_file = ENV_CONFIG_MAP.get(env, "dev.yaml.enc")
    return config_dir / config_file


def load_config():
    AVANTML_SDK_CONFIG_PATH = 'AVANTML_SDK_CONFIG_PATH'
    logger.info("execute init_config................")
    if AVANTML_SDK_CONFIG_PATH in os.environ and os.path.exists(os.environ[AVANTML_SDK_CONFIG_PATH]):
        logger.info("AVANTML_SDK_CONFIG_PATH is set, skip decrypt config")
        return
    
    pythonpath = os.getenv('PYTHONPATH')
    if not pythonpath:
        raise ValueError("PYTHONPATH is not set")
    
    # Ensure CONFIG_ENCRYPTION_KEY environment variable exists
    enc_key_value = os.getenv('CONFIG_ENCRYPTION_KEY')
    if not enc_key_value:
        raise ValueError("CONFIG_ENCRYPTION_KEY is not set")
    
    enc_config_path = get_config_path()
    config_data = decrypt_config(enc_config_path, enc_key_value)
    config_path = os.path.join(os.getenv('PROJECT_ROOT'), 'application', 'settings', 'config.yaml')
    with open(config_path, 'w') as f:
        f.write(config_data)
    
    # Set environment variable
    os.environ[AVANTML_SDK_CONFIG_PATH] = config_path
    logger.info(f"AVANTML_SDK_CONFIG_PATH: {os.environ[AVANTML_SDK_CONFIG_PATH]}")


def init_kafka_topics():
    """初始化Kafka topics"""
    try:
        import yaml
        from confluent_kafka.admin import AdminClient, NewTopic
        from confluent_kafka import KafkaException
        
        config_path = os.environ.get('AVANTML_SDK_CONFIG_PATH')
        if not config_path or not os.path.exists(config_path):
            logger.warning("配置文件不存在，跳过Kafka topics初始化")
            return
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 获取Kafka配置（使用producer的配置）
        kafka_config = config['artifact_instances']['ems_kafka_consumer']
        
        # 创建AdminClient
        admin_config = {
            'bootstrap.servers': kafka_config['bootstrap_servers']
        }
        admin_client = AdminClient(admin_config)
        
        # 获取集群信息以确定合适的副本因子
        metadata = admin_client.list_topics(timeout=10)
        broker_count = len(metadata.brokers)
        
        # 动态确定副本因子：单节点用1，多节点用min(3, broker_count)
        replication_factor = 1 if broker_count == 1 else min(3, broker_count)
        
        # 分区数根据预期负载设置：
        # - real-data: NOC数据，可能有多个站点，设置3个分区提高并发
        # - ems-ai-msg/ai-ems-msg: 控制消息，1个分区保证顺序
        topics_to_create = [
            NewTopic("real-data", num_partitions=3, replication_factor=replication_factor),
            NewTopic("ems-ai-msg", num_partitions=3, replication_factor=replication_factor),
            NewTopic("ai-ems-msg", num_partitions=3, replication_factor=replication_factor)
        ]
        
        logger.info(f"检测到 {broker_count} 个Kafka brokers, 使用副本因子: {replication_factor}")
        
        # 获取现有topics（重用之前的metadata）
        existing_topics = set(metadata.topics.keys())
        
        # 过滤出需要创建的topics
        new_topics = [topic for topic in topics_to_create if topic.topic not in existing_topics]
        
        if not new_topics:
            logger.info("所有必需的Kafka topics已存在")
            return
        
        # 创建topics
        logger.info(f"开始创建Kafka topics: {[t.topic for t in new_topics]}")
        futures = admin_client.create_topics(new_topics)
        
        # 等待创建完成
        for topic_name, future in futures.items():
            try:
                future.result()  # 等待操作完成
                logger.info(f"Kafka topic '{topic_name}' 创建成功")
            except KafkaException as e:
                if "already exists" in str(e).lower():
                    logger.info(f"Kafka topic '{topic_name}' 已存在")
                else:
                    logger.error(f"创建Kafka topic '{topic_name}' 失败: {str(e)}")
        
    except Exception as e:
        logger.error(f"Kafka topics初始化失败: {str(e)}")
        # 不抛出异常，允许应用继续启动


def init_database():
    """初始化数据库"""
    try:
        import yaml
        from sqlalchemy import create_engine, text
        
        config_path = os.environ.get('AVANTML_SDK_CONFIG_PATH')
        if config_path and os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            db_config = config['artifact_instances']['mysql']
            base_url = f"mysql+pymysql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}"
            
            # 先检查ems数据库是否存在
            engine = create_engine(base_url)
            with engine.connect() as conn:
                result = conn.execute(text("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = :database"), {"database": db_config['database']})
                if result.fetchone():
                    logger.info(f"数据库 '{db_config['database']}' 已存在，跳过初始化")
                    return
            
            # 数据库不存在，执行完整的建库建表SQL
            sql_file = os.path.join(os.path.dirname(__file__), 'db_operate', 'create_tables.sql')
            if os.path.exists(sql_file):
                with open(sql_file, 'r', encoding='utf-8') as f:
                    sql_content = f.read()
                
                # 连接到MySQL服务器（不指定数据库）
                with engine.connect() as conn:
                    for statement in sql_content.split(';'):
                        if statement.strip() and not statement.strip().startswith('--'):
                            conn.execute(text(statement))
                    conn.commit()
                
                logger.info("数据库创建和表结构初始化成功")
            else:
                logger.info("未找到create_tables.sql文件")
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")


set_project_root()
load_config()
init_database()
init_kafka_topics()
