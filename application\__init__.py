import os
import sys
import warnings
from pathlib import Path

# 过滤XGBoost版本兼容性警告
warnings.filterwarnings("ignore", message=".*If you are loading a serialized model.*", category=UserWarning)

from application.utils.decrypt_config import decrypt_config
from application.utils.logger import setup_logger

logger = setup_logger("AI_EMS")

# Map environment names to configuration files
ENV_CONFIG_MAP = {
    'local': 'local.yaml.enc',
    'dev': 'dev.yaml.enc',
    'test': 'test.yaml.enc',
    'prod': 'prod.yaml.enc',
    'cn-northwest-1-test': 'cn-northwest-1-test-config.yaml.enc',
    'cn-northwest-1-prod': 'cn-northwest-1-prod-config.yaml.enc'
}

# Get project root directory
ROOT_DIR = Path(__file__).parent.parent

# Set environment variables
os.environ['PYTHONPATH'] = str(ROOT_DIR)
sys.path.append(str(ROOT_DIR))

# Determine which configuration file to use based on environment variables
# Priority:
# 1. Configuration file corresponding to ENVIRONMENT variable
# 2. Default to dev.yaml.enc

def set_project_root():
    # Get project root directory
    root_path = Path(__file__).parent.parent.resolve()

    # Set environment variables
    os.environ['PROJECT_ROOT'] = str(root_path)


def get_config_path() -> Path:
    """
    Determine which configuration file to use based on environment variables
    
    Priority:
    1. Configuration file corresponding to ENVIRONMENT variable
    2. Default to dev.yaml.enc
    """
    # Project root directory
    base_dir = Path(os.getenv('PROJECT_ROOT'))
    
    # Configuration file directory
    config_dir = base_dir / 'application' / "settings" / "encrypted"
    
    # Get environment name
    env = os.environ.get("ENVIRONMENT", "dev").lower()
    
    config_file = ENV_CONFIG_MAP.get(env, "dev.yaml.enc")
    return config_dir / config_file


def load_config():
    AVANTML_SDK_CONFIG_PATH = 'AVANTML_SDK_CONFIG_PATH'
    logger.info("execute init_config................")
    if AVANTML_SDK_CONFIG_PATH in os.environ and os.path.exists(os.environ[AVANTML_SDK_CONFIG_PATH]):
        logger.info("AVANTML_SDK_CONFIG_PATH is set, skip decrypt config")
        return
    
    pythonpath = os.getenv('PYTHONPATH')
    if not pythonpath:
        raise ValueError("PYTHONPATH is not set")
    
    # Ensure CONFIG_ENCRYPTION_KEY environment variable exists
    enc_key_value = os.getenv('CONFIG_ENCRYPTION_KEY')
    if not enc_key_value:
        raise ValueError("CONFIG_ENCRYPTION_KEY is not set")
    
    enc_config_path = get_config_path()
    config_data = decrypt_config(enc_config_path, enc_key_value)
    config_path = os.path.join(os.getenv('PROJECT_ROOT'), 'application', 'settings', 'config.yaml')
    with open(config_path, 'w') as f:
        f.write(config_data)
    
    # Set environment variable
    os.environ[AVANTML_SDK_CONFIG_PATH] = config_path
    logger.info(f"AVANTML_SDK_CONFIG_PATH: {os.environ[AVANTML_SDK_CONFIG_PATH]}")


def init_kafka_topics():
    """初始化Kafka topics"""
    try:
        import yaml
        import time
        from confluent_kafka.admin import AdminClient, NewTopic
        from confluent_kafka import KafkaException
        
        config_path = os.environ.get('AVANTML_SDK_CONFIG_PATH')
        if not config_path or not os.path.exists(config_path):
            logger.warning("配置文件不存在，跳过Kafka topics初始化")
            return
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 获取Kafka配置（使用producer的配置）
        kafka_config = config['artifact_instances']['ems_kafka_consumer']
        
        # 创建AdminClient
        admin_config = {
            'bootstrap.servers': kafka_config['bootstrap_servers']
        }
        admin_client = AdminClient(admin_config)
        
        # 获取集群信息以确定合适的副本因子
        metadata = admin_client.list_topics(timeout=10)
        broker_count = len(metadata.brokers)
        
        # 动态确定副本因子：单节点用1，多节点用min(3, broker_count)
        replication_factor = 1 if broker_count == 1 else min(3, broker_count)
        
        # 分区数根据预期负载设置：
        # - real-data: NOC数据，可能有多个站点，设置1个分区保证时序性
        # - ems-ai-msg/ai-ems-msg: 控制消息，1个分区保证顺序
        topics_to_create = [
            NewTopic("real-data", num_partitions=1, replication_factor=replication_factor),
            NewTopic("ems-ai-msg", num_partitions=1, replication_factor=replication_factor),
            NewTopic("ai-ems-msg", num_partitions=1, replication_factor=replication_factor)
        ]
        
        logger.info(f"检测到 {broker_count} 个Kafka brokers, 使用副本因子: {replication_factor}")
        
        # 获取现有topics和详细信息
        metadata = admin_client.list_topics(timeout=10)
        existing_topics = set(metadata.topics.keys())
        topics_to_delete_recreate = []
        topics_need_creation = []
        
        # 检查每个要创建的topic
        for topic in topics_to_create:
            topic_name = topic.topic
            
            if topic_name in existing_topics:
                # Topic存在，检查分区数
                topic_metadata = metadata.topics[topic_name]
                current_partitions = len(topic_metadata.partitions)
                
                if current_partitions != topic.num_partitions:
                    logger.warning(f"Topic '{topic_name}' 存在但分区数不匹配: 当前{current_partitions}分区, 期望{topic.num_partitions}分区")
                    topics_to_delete_recreate.append(topic)
                else:
                    logger.info(f"Topic '{topic_name}' 已存在且配置正确 (分区数: {current_partitions})")
            else:
                # Topic不存在，需要创建
                topics_need_creation.append(topic)
        
        # 删除并重建分区数不匹配的topics
        for topic in topics_to_delete_recreate:
            topic_name = topic.topic
            logger.info(f"删除并重建Topic: {topic_name}")
            
            try:
                # 删除topic
                logger.info(f"删除Topic: {topic_name}")
                delete_futures = admin_client.delete_topics([topic_name])
                for name, future in delete_futures.items():
                    future.result(timeout=30)
                    logger.info(f"Topic '{name}' 删除成功")
                
                # 等待删除完成
                logger.info(f"等待Topic '{topic_name}' 删除完成...")
                for i in range(20):  # 最多等待20秒
                    time.sleep(1)
                    metadata = admin_client.list_topics(timeout=5)
                    if topic_name not in metadata.topics:
                        logger.info(f"Topic '{topic_name}' 已完全删除")
                        break
                
                # 重新创建topic
                logger.info(f"重新创建Topic: {topic_name} (分区数: {topic.num_partitions})")
                create_futures = admin_client.create_topics([topic])
                for name, future in create_futures.items():
                    future.result(timeout=30)
                    logger.info(f"Topic '{name}' 创建成功")
                
                # 验证结果
                time.sleep(2)
                metadata = admin_client.list_topics(timeout=10)
                if topic_name in metadata.topics:
                    actual_partitions = len(metadata.topics[topic_name].partitions)
                    if actual_partitions == topic.num_partitions:
                        logger.info(f"Topic '{topic_name}' 重建成功，分区数: {actual_partitions}")
                    else:
                        logger.warning(f"Topic '{topic_name}' 重建后分区数不正确: 期望{topic.num_partitions}, 实际{actual_partitions}")
                
            except Exception as e:
                logger.error(f"删除重建Topic '{topic_name}' 失败: {str(e)}")
        
        # 创建新的topics
        if topics_need_creation:
            logger.info(f"开始创建新的Kafka topics: {[t.topic for t in topics_need_creation]}")
            create_futures = admin_client.create_topics(topics_need_creation)
            
            # 等待创建完成
            for topic_name, future in create_futures.items():
                try:
                    future.result(timeout=30)  # 等待操作完成
                    logger.info(f"Kafka topic '{topic_name}' 创建成功")
                except KafkaException as e:
                    if "already exists" in str(e).lower():
                        logger.info(f"Kafka topic '{topic_name}' 已存在")
                    else:
                        logger.error(f"创建Kafka topic '{topic_name}' 失败: {str(e)}")
        
        if not topics_to_delete_recreate and not topics_need_creation:
            logger.info("所有必需的Kafka topics已存在且配置正确")
        
    except Exception as e:
        logger.error(f"Kafka topics初始化失败: {str(e)}")
        # 不抛出异常，允许应用继续启动


def init_database():
    """初始化数据库"""
    try:
        import yaml
        from sqlalchemy import create_engine, text
        
        config_path = os.environ.get('AVANTML_SDK_CONFIG_PATH')
        if config_path and os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            db_config = config['artifact_instances']['mysql']
            base_url = f"mysql+pymysql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}"
            
            # 先检查ems数据库是否存在
            engine = create_engine(base_url)
            with engine.connect() as conn:
                result = conn.execute(text("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = :database"), {"database": db_config['database']})
                if not result.fetchone():
                    logger.info(f"数据库 '{db_config['database']}' 不存在，开始创建...")
                    
                    # 数据库不存在，执行完整的建库建表SQL
                    sql_file = os.path.join(os.path.dirname(__file__), 'db_operate', 'create_tables.sql')
                    if os.path.exists(sql_file):
                        with open(sql_file, 'r', encoding='utf-8') as f:
                            sql_content = f.read()
                        
                        # 连接到MySQL服务器（不指定数据库）
                        for statement in sql_content.split(';'):
                            if statement.strip() and not statement.strip().startswith('--'):
                                conn.execute(text(statement))
                        conn.commit()
                        
                        logger.info("数据库创建和表结构初始化成功")
                    else:
                        logger.info("未找到create_tables.sql文件")
                else:
                    logger.info(f"数据库 '{db_config['database']}' 已存在，检查表结构...")
                    
                    # 数据库存在，检查并创建缺少的表
                    db_url = f"{base_url}/{db_config['database']}"
                    db_engine = create_engine(db_url)
                    
                    with db_engine.connect() as db_conn:
                        # 读取create_tables.sql文件
                        sql_file = os.path.join(os.path.dirname(__file__), 'db_operate', 'create_tables.sql')
                        if os.path.exists(sql_file):
                            with open(sql_file, 'r', encoding='utf-8') as f:
                                sql_content = f.read()
                            
                            # 解析SQL文件，提取CREATE TABLE语句
                            created_tables = []
                            statements = sql_content.split(';')
                            
                            for statement in statements:
                                statement = statement.strip()
                                if statement and not statement.startswith('--'):
                                    # 跳过CREATE DATABASE和USE语句
                                    if statement.upper().startswith('CREATE DATABASE') or statement.upper().startswith('USE '):
                                        continue
                                    
                                    # 处理CREATE TABLE语句
                                    if 'CREATE TABLE' in statement.upper():
                                        try:
                                            # 更精确地提取表名
                                            import re
                                            
                                            # 使用正则表达式提取表名
                                            match = re.search(r'CREATE\s+TABLE\s+`?(\w+)`?', statement, re.IGNORECASE)
                                            if match:
                                                table_name = match.group(1)
                                                
                                                # 检查表是否存在
                                                check_table_sql = """
                                                    SELECT TABLE_NAME 
                                                    FROM INFORMATION_SCHEMA.TABLES 
                                                    WHERE TABLE_SCHEMA = :database 
                                                    AND TABLE_NAME = :table_name
                                                """
                                                result = db_conn.execute(text(check_table_sql), {
                                                    "database": db_config['database'],
                                                    "table_name": table_name
                                                })
                                                
                                                if not result.fetchone():
                                                    # 表不存在，创建表
                                                    logger.info(f"表 '{table_name}' 不存在，开始创建...")
                                                    db_conn.execute(text(statement))
                                                    db_conn.commit()
                                                    created_tables.append(table_name)
                                                    logger.info(f"表 '{table_name}' 创建成功")
                                                else:
                                                    logger.debug(f"表 '{table_name}' 已存在，跳过创建")
                                            else:
                                                logger.warning(f"无法解析表名: {statement[:100]}...")
                                                
                                        except Exception as e:
                                            logger.error(f"创建表时出错: {str(e)}")
                                            logger.error(f"问题SQL语句: {statement[:100]}...")
                            
                            if created_tables:
                                logger.info(f"成功创建 {len(created_tables)} 个缺少的表: {', '.join(created_tables)}")
                            else:
                                logger.info("所有必要的表都已存在")
                        else:
                            logger.warning("未找到create_tables.sql文件，无法检查表结构")
                
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")


set_project_root()
load_config()
init_database()
init_kafka_topics()
