{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["某机床厂生产甲、乙两种机床，每台销售后的利润分别为4000 元与3000 元。生产甲机床需用 A、B机器加工，加工时间分别为每台 2 小时和 1 小时；生产乙机床需用 A、B、C三种机器加工，加工时间为每台各一小时。若每天可用于加工的机器时数分别为A 机器10 小时、B 机器8 小时和C 机器7 小时，问该厂应生产甲、乙机床各几台，才能使总利润最大？"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 数学建模"]}, {"cell_type": "markdown", "metadata": {}, "source": ["$$\n", "\\begin{aligned}\n", " & \\mathrm{max}z=4000x_{1}+3000x_{2} \\\\\n", " & \\mathrm{s.t.} \\\\\n", " & 2x_1+x_2\\leq10 \\\\\n", " & x_1+x_2\\leq8 \\\\\n", " & x_{2}\\leq7 \\\\\n", " & x_{1}\\geq0,x_{2}\\geq0\n", "\\end{aligned}\n", "$$"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## python代码求解"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["load d:\\WorkSpace\\ems\\.venv\\Lib\\site-packages\\ortools\\.libs\\zlib1.dll...\n", "load d:\\WorkSpace\\ems\\.venv\\Lib\\site-packages\\ortools\\.libs\\abseil_dll.dll...\n", "load d:\\WorkSpace\\ems\\.venv\\Lib\\site-packages\\ortools\\.libs\\utf8_validity.dll...\n", "load d:\\WorkSpace\\ems\\.venv\\Lib\\site-packages\\ortools\\.libs\\re2.dll...\n", "load d:\\WorkSpace\\ems\\.venv\\Lib\\site-packages\\ortools\\.libs\\libprotobuf.dll...\n", "load d:\\WorkSpace\\ems\\.venv\\Lib\\site-packages\\ortools\\.libs\\highs.dll...\n", "load d:\\WorkSpace\\ems\\.venv\\Lib\\site-packages\\ortools\\.libs\\ortools.dll...\n"]}], "source": ["from ortools.linear_solver import pywraplp"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Select a solver according to the type of your problem.\n", "solver = pywraplp.Solver(name='SolveSimpleSystem', problem_type=pywraplp.Solver.GLOP_LINEAR_PROGRAMMING)\n", "# Create the variables\n", "x1 = solver.NumVar(0, solver.infinity(), name='x1')\n", "x2 = solver.NumVar(0, 7, name='x2')\n", "# create the constraints\n", "constraint1 = solver.Constraint(-solver.infinity(), 10)\n", "constraint1.SetCoefficient(x1, 2)\n", "constraint1.SetCoefficient(x2, 1)\n", "\n", "constraint2 = solver.Constraint(-solver.infinity(), 8)\n", "constraint2.SetCoefficient(x1, 1)\n", "constraint2.SetCoefficient(x2, 1)\n", "\n", "# Create the objective function\n", "objective = solver.Objective()\n", "objective.SetCoefficient(x1, 4000)\n", "objective.SetCoefficient(x2, 3000)\n", "objective.SetMaximization()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of variables = 2\n", "Number of constraints = 2\n", "Solution:\n", "x1 =  1.9999999999999998\n", "x2 =  6.0\n", "Optimal objective value = 26.0\n"]}], "source": ["# Call the solver.\n", "solver.<PERSON><PERSON>()\n", "print('Number of variables =', solver.NumVariables())\n", "print('Number of constraints =', solver.NumConstraints())\n", "# The value of each variable in the solution.\n", "print('Solution:')\n", "print('x1 = ', x1.solution_value())\n", "print('x2 = ', x2.solution_value())\n", "# The objective value of the solution.\n", "opt_solution = 4 * x1.solution_value() + 3 * x2.solution_value()\n", "print('Optimal objective value =', opt_solution)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}