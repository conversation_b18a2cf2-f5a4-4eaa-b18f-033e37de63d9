import json
import re
import traceback
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Type


# 配置开关：控制是否获取实时数据
ENABLE_REALTIME_DATA = False  # 默认关闭，避免数据库问题

try:
    import holidays
except ImportError:
    holidays = None

from application.db_operate.db_operate import DBOperate
from application.db_operate.models import Base, PVAndMeterRealtimeDataDB, SiteDB, ChargerRealtimeDataDB, SiteDynamicPriceDataDB, SiteFixedAndTimeOfUsePriceDataDB, PileDB, ESRealtimeDataDB
from application.utils.logger import setup_logger
from application.utils.weather_cache import weather_cache_manager
from application.utils.timezone_mapping import get_timezone_with_fallback
from sqlalchemy import select, func

logger = setup_logger("get_input_data", direction="utils")


def execute_db_operation(operation_name: str, default_result, operation_func):
    """
    通用数据库操作辅助函数，减少重复的数据库连接和错误处理代码
    
    Args:
        operation_name: 操作名称，用于日志记录
        default_result: 操作失败时的默认返回值
        operation_func: 接收数据库会话作为参数的操作函数
    
    Returns:
        操作结果或默认值
    """
    try:
        db_operate = DBOperate()
        with db_operate.get_db() as db:
            return operation_func(db)
    except Exception as e:
        logger.error(f"{operation_name}失败: {e}")
        return default_result


def save_prediction_to_db(site_no: str, predicted_power: List[float], model_class):
    """
    保存预测结果到数据库

    Args:
        site_no: 场站编号
        predicted_power: 预测的功率列表
        model_class: 数据库模型类
    """
    def _save_prediction(db):
        # 计算预测时间范围
        start_time = datetime.now()
        end_time = start_time + timedelta(hours=24)
        
        # 使用差分存储减少数据库存储空间
        # 只存储起始时间和间隔，而不存储完整的时间列表
        time_list_compact = {
            "start_time": start_time.strftime("%Y-%m-%d %H:%M:%S"),
            "interval_minutes": 15,
            "count": len(predicted_power)
        }
        
        # 将预测结果转换为Python原生float类型，避免JSON序列化错误
        predicted_power_native = [float(power) for power in predicted_power]
        
        try:
            # 设置较短的事务超时时间，避免长时间锁定
            from sqlalchemy import text
            db.execute(text("SET innodb_lock_wait_timeout = 10"))
            
            # 先删除已存在的记录，避免使用merge导致的死锁
            existing_records = db.query(model_class).filter(
                model_class.site_no == site_no
            ).all()
            
            for record in existing_records:
                db.delete(record)
            
            # 插入新记录
            record = model_class(
                site_no=site_no,
                curve_start_time=start_time,
                curve_end_time=end_time,
                time_list=json.dumps(time_list_compact),
                power_list=json.dumps(predicted_power_native)
            )
            
            db.add(record)
            db.commit()
            
            logger.info(f"{model_class.__name__}预测结果已保存到数据库 - 场站: {site_no}")
            return True
            
        except Exception as e:
            db.rollback()
            logger.error(f"保存{model_class.__name__}预测结果时发生错误: {e}")
            raise
    
    execute_db_operation(f"保存{model_class.__name__}预测结果到数据库", False, _save_prediction)


def get_time_field(current_time: datetime) -> datetime:
    # 定义当前时间（预测起始点）
    current_time = datetime.now()
    the_part = 1 if current_time.minute % 15 >= 7.5 else 0
    the_minute = current_time.minute//15 * 15 + the_part * 15
    
    # 处理跨小时和跨天的情况，使用timedelta更安全
    if the_minute >= 60:
        # 跨小时：加1小时，分钟设为0
        current_time = current_time.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
    else:
        current_time = current_time.replace(minute=the_minute, second=0, microsecond=0)
    
    return current_time


def is_holiday(date_time: datetime, country_code: str = 'CN') -> bool:
    """
    检查指定日期是否为节假日
    
    Args:
        date_time: 要检查的日期时间
        country_code: 国家代码 ('CN'=中国, 'NL'=荷兰, 'FR'=法国, 'US'=美国等)
    
    Returns:
        bool: 是否为节假日
    """
    try:
        if holidays is None:
            logger.warning("holidays库未安装，无法检查节假日，返回False")
            return False
        
        # 根据国家代码获取节假日
        country_holidays = None
        if country_code == 'CN':
            country_holidays = holidays.China()
        elif country_code == 'NL':
            country_holidays = holidays.Netherlands()
        elif country_code == 'FR':
            country_holidays = holidays.France()
        elif country_code == 'US':
            country_holidays = holidays.US()
        elif country_code == 'DE':
            country_holidays = holidays.Germany()
        elif country_code == 'GB':
            country_holidays = holidays.UnitedKingdom()
        else:
            logger.warning(f"不支持的国家代码: {country_code}，返回False")
            return False
        
        # 检查是否为节假日
        date_obj = date_time.date()
        return date_obj in country_holidays
        
    except Exception as e:
        logger.error(f"检查节假日失败: {e}")
        return False



def get_country_code_from_coordinates(latitude: float, longitude: float) -> str:
    """
    根据经纬度坐标推断国家代码
    
    Args:
        latitude: 纬度
        longitude: 经度
    
    Returns:
        str: 国家代码
    """
    try:
        # 简单的地理区域映射
        if 18 <= latitude <= 54 and 73 <= longitude <= 135:
            return 'CN'  # 中国
        elif 50 <= latitude <= 54 and 3 <= longitude <= 7:
            return 'NL'  # 荷兰
        elif 42 <= latitude <= 51 and -5 <= longitude <= 8:
            return 'FR'  # 法国
        elif 48 <= latitude <= 55 and 5 <= longitude <= 15:
            return 'DE'  # 德国
        elif 50 <= latitude <= 59 and -8 <= longitude <= 2:
            return 'GB'  # 英国
        elif 24 <= latitude <= 49 and -125 <= longitude <= -66:
            return 'US'  # 美国
        else:
            logger.warning(f"无法根据坐标({latitude}, {longitude})确定国家，默认使用中国")
            return 'CN'
            
    except Exception as e:
        logger.error(f"根据坐标推断国家失败: {e}")
        return 'CN'


def get_solar_radiation_data(latitude: float, longitude: float, start_date: str, end_date: str) -> List[Dict]:
    """
    基于地理位置和时间生成太阳辐射数据（GHI, DNI, DHI）
    """
    try:
        from datetime import datetime, timedelta
        import math
        
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        solar_data = []
        
        for i in range(96):
            hour = (i // 4) % 24
            minute = (i % 4) * 15
            current_dt = start_dt + timedelta(minutes=15*i)
            
            # 计算太阳位置参数
            day_of_year = current_dt.timetuple().tm_yday
            
            # 太阳赤纬角（考虑季节变化）
            declination = 23.45 * math.sin(math.radians(360 * (284 + day_of_year) / 365))
            
            # 时角（考虑经度修正）
            solar_hour = hour + minute/60.0 + (longitude/15.0)  # 经度修正
            hour_angle = 15 * (solar_hour - 12)
            
            # 太阳高度角
            lat_rad = math.radians(latitude)
            dec_rad = math.radians(declination)
            ha_rad = math.radians(hour_angle)
            
            sin_altitude = (math.sin(lat_rad) * math.sin(dec_rad) + 
                           math.cos(lat_rad) * math.cos(dec_rad) * math.cos(ha_rad))
            
            solar_altitude = math.degrees(math.asin(max(-1, min(1, sin_altitude))))
            
            # 只有太阳高度角大于0度才有辐射
            if solar_altitude > 0:
                # 基础辐射强度（考虑季节和纬度）
                base_irradiance = 1000 * math.sin(math.radians(solar_altitude))  # W/m²
                
                # 季节修正因子
                season_factor = 1 + 0.3 * math.cos(math.radians(360 * (day_of_year - 173) / 365))
                
                # 纬度修正（高纬度地区辐射相对较弱）
                latitude_factor = 1 - abs(latitude) / 180 * 0.3
                
                # 应用修正
                corrected_irradiance = base_irradiance * season_factor * latitude_factor
                
                # 转换为kWh/m²/15min
                ghi = corrected_irradiance * 0.25 / 1000  # 15分钟 = 0.25小时
                dni = ghi * 0.7  # 直射辐射约占70%
                dhi = ghi * 0.3  # 散射辐射约占30%
            else:
                ghi = dni = dhi = 0.0
            
            solar_data.append({
                'ghi': ghi,
                'dni': dni,
                'dhi': dhi,
                'hour': hour,
                'minute': minute
            })
        
        return solar_data
        
    except Exception as e:
        logger.error(f"生成太阳辐射数据失败: {e}")
        return []


def generate_default_test_weather(latitude: float, longitude: float, start_time: datetime) -> List[Dict]:
    """
    当无法获取实际天气数据时使用
    生成默认的测试天气数据结构
    
    Args:
        latitude: 纬度
        longitude: 经度
        start_time: 开始时间
        
    Returns:
        List[Dict]: 96个默认的测试天气数据点
    """
    try:
        default_weather = []
        
        for i in range(96):
            hour = (i // 4) % 24
            minute = (i % 4) * 15
            current_time = start_time + timedelta(minutes=15*i)
            
            # 简单的日照模型：6:00-18:00有太阳
            if 6 <= hour <= 18:
                # 模拟太阳角度变化，正午时最强
                solar_factor = 1 - abs(hour - 12) / 6
                ghi = 0.3 * solar_factor  # 默认全球水平辐照度
                dni = 0.2 * solar_factor  # 默认法向直接日射辐照度
                dhi = 0.1 * solar_factor  # 默认漫射水平辐照度
            else:
                ghi = dni = dhi = 0.0
            
            enhanced_point = {
                # 太阳辐射数据
                'ghi': ghi,
                'dni': dni,
                'dhi': dhi,
                
                # 基础天气数据（默认晴朗天气）
                'air_temperature': 25.0,  # 25°C
                'cloud_cover': 20.0,      # 20%云覆盖
                'wind_speed': 10.0,       # 10km/h风速
                'relative_humidity': 50.0, # 50%相对湿度
                
                # 时间特征
                'hour': hour,
                'minute': minute,
                'dayofyear': current_time.timetuple().tm_yday,
                
                # 位置数据
                'latitude': latitude,
                'longitude': longitude,
                'elevation': 100.0  # 默认海拔100米
            }
            default_weather.append(enhanced_point)
        
        return default_weather
        
    except Exception as e:
        logger.error(f"生成默认测试天气数据失败: {e}")
        return []


def get_elevation_data(latitude: float, longitude: float) -> float:
    """
    基于经纬度映射表获取海拔数据
    """
    try:
        # 海拔映射表：[纬度范围, 经度范围, 海拔(米), 地区描述]
        # 按精度排序：越精确的区域越靠前
        elevation_map = [
            # === 具体场站位置（高精度） ===
            # 深圳场站 (22.5431°N, 114.0579°E)
            ([22.3, 22.8], [113.8, 114.3], 20, "深圳"),
            
            # 巴黎场站 (48.8566°N, 2.3522°E) 
            ([48.6, 49.1], [2.0, 2.7], 35, "巴黎"),
            
            # 荷兰场站区域 (阿姆斯特丹周边)
            ([52.0, 52.5], [4.5, 5.5], 0, "阿姆斯特丹"),   # 海平面附近
            ([51.8, 52.2], [5.2, 5.8], 2, "阿尔梅勒"),     # 默认坐标区域
            
            # === 中国主要地区 ===
            ([35, 45], [110, 125], 50, "华北平原"),         # 北京、天津、河北
            ([30, 35], [110, 125], 200, "长江中下游"),       # 湖北、湖南、江西
            ([20, 30], [110, 125], 100, "华南丘陵"),         # 广东、广西、福建（除深圳）
            ([25, 35], [100, 110], 500, "四川盆地"),         # 四川、重庆
            ([35, 50], [100, 120], 1500, "内蒙古高原"),      # 内蒙古
            ([25, 35], [85, 100], 4000, "青藏高原东部"),     # 西藏东部、青海
            ([30, 40], [75, 95], 4500, "青藏高原西部"),      # 西藏西部
            ([40, 50], [80, 95], 1000, "新疆盆地"),          # 新疆
            ([45, 55], [120, 135], 200, "东北平原"),         # 黑龙江、吉林、辽宁
            
            # === 欧洲主要地区 ===
            # 法国其他地区
            ([45, 49], [1, 4], 200, "法国中部"),             # 法国中部（除巴黎）
            ([43, 47], [4, 8], 300, "法国东南部"),           # 里昂、马赛
            ([47, 51], [-2, 3], 150, "法国北部"),            # 法国北部（除巴黎）
            
            # 荷兰其他地区
            ([50, 55], [3, 8], 50, "荷兰全境"),              # 荷兰其他地区
            
            # 其他欧洲地区
            ([48, 55], [5, 15], 200, "德国平原"),            # 德国北部
            ([45, 50], [5, 15], 500, "德国南部"),            # 德国南部、瑞士
            ([55, 70], [10, 30], 200, "斯堪的纳维亚"),       # 挪威、瑞典、芬兰
            ([35, 45], [10, 20], 400, "意大利"),             # 意大利
            ([35, 45], [-10, 5], 600, "西班牙"),             # 西班牙
            
            # 北美主要地区
            ([35, 45], [-125, -70], 300, "美国本土"),      # 美国大部分地区
            ([25, 35], [-105, -80], 200, "美国南部"),      # 美国南部
            ([40, 60], [-140, -60], 500, "加拿大"),        # 加拿大
            
            # 其他地区
            ([20, 40], [125, 145], 100, "日本"),           # 日本
            ([-40, -30], [140, 155], 300, "澳大利亚东南"), # 澳大利亚
            ([0, 15], [100, 120], 50, "东南亚"),           # 东南亚
            ([-10, 10], [-80, -35], 200, "南美洲"),        # 南美洲
        ]
        
        # 查找匹配的地区
        for lat_range, lon_range, elevation, region in elevation_map:
            if (lat_range[0] <= latitude <= lat_range[1] and 
                lon_range[0] <= longitude <= lon_range[1]):
                return float(elevation)
        
        # 如果没有匹配的地区，使用简单的规则估算
        elevation = estimate_elevation_by_region(latitude, longitude)
        return elevation
        
    except Exception as e:
        logger.error(f"获取海拔数据失败: {e}")
        return 100.0


def estimate_elevation_by_region(latitude: float, longitude: float) -> float:
    """
    基于地理区域的海拔估算
    """
    try:
        # 大洋
        if (-180 <= longitude <= 180 and -90 <= latitude <= 90):
            # 高原地区（大致范围）
            if (25 <= latitude <= 40 and 75 <= longitude <= 105):  # 青藏高原
                return 4000.0
            elif (35 <= latitude <= 55 and 95 <= longitude <= 125):  # 蒙古高原
                return 1200.0
            elif (-30 <= latitude <= 30 and -80 <= longitude <= -30):  # 南美安第斯山脉
                return 2000.0
            elif (35 <= latitude <= 50 and -120 <= longitude <= -100):  # 北美落基山脉
                return 1500.0
            
            # 沿海地区
            elif (abs(latitude) < 60 and 
                  ((-180 <= longitude <= -150) or (-10 <= longitude <= 40) or 
                   (100 <= longitude <= 180))):  # 沿海
                return 50.0
            
            # 内陆平原
            elif (30 <= latitude <= 60):  # 北半球温带
                return 300.0
            elif (-30 <= latitude <= 30):  # 热带地区
                return 150.0
            else:  # 其他地区
                return 200.0
        
        return 100.0  # 默认值
        
    except Exception as e:
        logger.error(f"估算海拔失败: {e}")
        return 100.0


def get_site_basic_info(site_no: str) -> Dict:
    """
    获取场站基本信息，与 LongTermLoadForecastSolver._set_site_basic_info() 保持一致
    
    Args:
        site_no: 场站编号
        
    Returns:
        Dict: 场站基本信息，包含解析好的经纬度
    """
    def _get_site_info(db):
        # 获取站点信息
        stmt = select(SiteDB).where(SiteDB.site_no == site_no)
        site_info = db.scalar(stmt)

        # 处理站点信息
        if site_info:
            station_name = site_info.site_no
            location_str = site_info.lat_and_lng
            time_zone = get_timezone_with_fallback(site_info.region)
        else:
            station_name = site_no
            location_str = "52.19340694027317,5.430548227543284"  # 默认坐标
            time_zone = "UTC"

        # 解析坐标
        try:
            if location_str is not None:
                # 解析经纬度字符串，格式: [31.230416', ' 121.473701]
                def parse_coordinates(location_str):
                    """解析特殊格式的坐标字符串"""
                    # 使用正则提取所有数字（包括小数）
                    pattern = r'-?\d+\.?\d*'
                    matches = re.findall(pattern, str(location_str))
                    return [float(match) for match in matches] if matches else [0.0, 0.0]
                # lat_lng = list(map(lambda x: float(re.search('[0-9.]{2,}',x).group()),location_str.split(',')))
                lat_lng = parse_coordinates(location_str)
                if len(lat_lng) >= 2:
                    latitude = lat_lng[0]
                    longitude = lat_lng[1]
                else:
                    latitude, longitude = 52.19340694027317, 5.430548227543284
            else:
                latitude, longitude = 52.19340694027317, 5.430548227543284
        except (ValueError, TypeError):
            logger.warning(f"场站{site_no}坐标解析失败，使用默认坐标")
            latitude, longitude = 52.19340694027317, 5.430548227543284

        return {
            'station_name': station_name,
            'location': location_str,  # 保留原始字符串
            'latitude': latitude,      # 解析后的纬度
            'longitude': longitude,    # 解析后的经度
            'time_zone': time_zone,
        }
    
    default_result = {
        'station_name': site_no,
        'location': "52.19340694027317,5.430548227543284",
        'latitude': 52.19340694027317,
        'longitude': 5.430548227543284,
        'time_zone': "UTC",
    }
    
    return execute_db_operation(f"获取场站{site_no}基本信息", default_result, _get_site_info)



def get_system_metadata(site_no: str) -> Dict:
    """
    获取系统元数据
    
    Args:
        site_no: 场站编号
        
    Returns:
        Dict: 系统元数据
    """
    # 获取场站基本信息（包含坐标）
    site_info = get_site_basic_info(site_no)
    latitude, longitude = site_info['latitude'], site_info['longitude']
    
    # 获取海拔
    elevation = get_elevation_data(latitude, longitude)
    
    def _get_max_capacity(db):
        site = db.query(SiteDB).filter(SiteDB.site_no == site_no).first()
        max_capacity = 1000.0  # 默认值
        if site is not None and hasattr(site, 'pv_max_power') and site.pv_max_power is not None:
            try:
                max_capacity = float(str(site.pv_max_power))
            except (ValueError, TypeError):
                max_capacity = 1000.0
        return max_capacity
    
    max_capacity = execute_db_operation(f"获取场站{site_no}最大容量", 1000.0, _get_max_capacity)
    
    # 系统元数据
    system_metadata = {
        'max_capacity': max_capacity,
        'azimuth': 180.0,             # 方位角 (度)
        'tilt': 30.0,                 # 倾斜度 (度)
        'latitude': latitude,
        'longitude': longitude,
        'elevation': elevation
    }
    
    return system_metadata


def get_weather_forecast(latitude: float, longitude: float, start_date: str, end_date: str) -> List[List]:
    try:
        data = weather_cache_manager.get_cached_weather_data(start_date, latitude, longitude)

        # 解析天气数据，生成96个15分钟间隔的数据点
        weather_points = []
        
        if 'days' in data:
            for day in data['days']:
                if 'hours' in day:
                    for hour_data in day['hours']:
                        # 每个小时生成4个15分钟的数据点
                        for i in range(4):
                            weather_point = [
                                hour_data.get('solarradiation', 0),  # 太阳辐射 (MJ/m²)
                                hour_data.get('cloudcover', 0),      # 云覆盖 (%)
                                hour_data.get('temp', 0),            # 温度 (°C)
                                hour_data.get('humidity', 0),        # 湿度 (%)
                                hour_data.get('uvindex', 0),         # UV指数
                                hour_data.get('visibility', 0),      # 能见度 (km)
                                hour_data.get('windspeed', 0),       # 风速 (km/h)
                                hour_data.get('pressure', 0),        # 气压 (mb)
                                hour_data.get('precip', 0),          # 降水量 (mm)
                                hour_data.get('snow', 0),            # 降雪量 (mm)
                                hour_data.get('conditions', ''),     # 天气状况
                                hour_data.get('icon', '')            # 天气图标
                            ]
                            weather_points.append(weather_point)
                            
                            if len(weather_points) >= 96:  # 确保只返回96个点
                                break
                        if len(weather_points) >= 96:
                            break
                if len(weather_points) >= 96:
                    break
        
        # 如果数据不足96个点，用最后一个点的数据填充
        while len(weather_points) < 96:
            if weather_points:
                weather_points.append(weather_points[-1])
            else:
                # 如果没有数据，使用默认值
                weather_points.append([0, 0, 25, 50, 0, 10, 0, 1013, 0, 0, 'clear', 'clear-day'])
        
        logger.info(f"成功获取天气数据，共{len(weather_points)}个数据点")
        return weather_points[:96]  # 确保返回96个点
        
    except Exception as e:
        logger.error(f"获取天气数据失败: {e}")
        # 返回默认天气数据
        default_weather = [[0, 0, 25, 50, 0, 10, 0, 1013, 0, 0, 'clear', 'clear-day'] for _ in range(96)]
        return default_weather



def get_current_electricity_price(site_no: str, timestamp: datetime, session) -> float:
    """
    获取当前时间点的电价
    
    Args:
        site_no: 场站编号
        timestamp: 时间点
        session: 数据库会话
    
    Returns:
        float: 当前电价
    """
    try:
        # 优先查询动态电价
        dynamic_price = session.query(SiteDynamicPriceDataDB).filter(
            SiteDynamicPriceDataDB.site_no == site_no,
            SiteDynamicPriceDataDB.start_time <= timestamp,
            SiteDynamicPriceDataDB.end_time > timestamp
        ).first()
        
        if dynamic_price:
            return float(dynamic_price.price)
        
        # 查询分时电价
        hour_minute = timestamp.strftime('%H:%M')
        time_price = session.query(SiteFixedAndTimeOfUsePriceDataDB).filter(
            SiteFixedAndTimeOfUsePriceDataDB.site_no == site_no,
            SiteFixedAndTimeOfUsePriceDataDB.belong_year == timestamp.year,
            SiteFixedAndTimeOfUsePriceDataDB.belong_month == timestamp.month,
            SiteFixedAndTimeOfUsePriceDataDB.start_time <= hour_minute,
            SiteFixedAndTimeOfUsePriceDataDB.end_time > hour_minute
        ).first()
        
        if time_price:
            return float(time_price.price)
        
        return 0.15  # 默认电价
        
    except Exception as e:
        logger.error(f"获取电价失败: {e}")
        return 0.15


def get_site_realtime_data(site_no: str, timestamp: datetime, data_frames: int) -> Dict:
    """
    从数据库获取场站实时运行数据，与 LongTermLoadForecastSolver._set_grid_and_station_info() 保持一致
    当ENABLE_REALTIME_DATA为False时返回模拟数据
    
    Args:
        site_no: 场站编号
        timestamp: 时间点
        data_frames: 数据帧数
    
    Returns:
        Dict: 包含实际运行数据的字典，每个字段都是长度为data_frames的数组
    """
    # 如果关闭了实时数据获取，返回模拟数据
    if not ENABLE_REALTIME_DATA:
        logger.info(f"实时数据获取已关闭，返回模拟实时数据 - 场站: {site_no}")
        # 返回模拟的实时数据数组
        return {
            'grid_power': [25.0] * data_frames,  # 模拟电网功率
            'grid_supply': [10.0] * data_frames,  # 模拟电网供电功率
            'power': [25.0] * data_frames,       # 模拟充电功率
            'charging_consumption': [15.0] * data_frames,  # 模拟充电消耗
            'number_of_chargers': [4] * data_frames,  # 模拟充电桩数量
            'active_chargers': [2] * data_frames,  # 模拟活跃充电桩
            'occupied_chargers': [2] * data_frames,
            'available_chargers': [2] * data_frames,
            'chargers_under_maintenance': [0] * data_frames,
            'electricity_pricing': [0.15] * data_frames,  # 默认电价
        }
    
    # 原有的数据库查询逻辑，按照 LongTermLoadForecastSolver 的方式
    def _get_realtime_data(db):
        # 将时间戳转换为当天00:00:00的毫秒时间戳
        start_date = timestamp.replace(hour=0, minute=0, second=0, microsecond=0)
        start_ts = int(start_date.timestamp() * 1000)
        end_ts = start_ts + data_frames * 15 * 60 * 1000  # 加上data_frames*15分钟的毫秒数

        # 查询数据库记录
        stmt = select(PVAndMeterRealtimeDataDB).where(
            PVAndMeterRealtimeDataDB.site_no == site_no,
            PVAndMeterRealtimeDataDB.ts >= start_ts,
            PVAndMeterRealtimeDataDB.ts < end_ts
        ).order_by(PVAndMeterRealtimeDataDB.ts)
        pv_and_meter_records = db.scalars(stmt).all()

        stmt = select(ESRealtimeDataDB).where(
            ESRealtimeDataDB.site_no == site_no,
            ESRealtimeDataDB.ts >= start_ts,
            ESRealtimeDataDB.ts < end_ts
        ).order_by(ESRealtimeDataDB.ts)
        es_records = db.scalars(stmt).all()

        # 充电枪采样数据查询
        stmt = select(
            ChargerRealtimeDataDB.site_no, ChargerRealtimeDataDB.ts, ChargerRealtimeDataDB.status,
            func.count().label('count')
        ).where(
            ChargerRealtimeDataDB.site_no == site_no,
            ChargerRealtimeDataDB.ts >= start_ts, 
            ChargerRealtimeDataDB.ts < end_ts
        ).group_by(
            ChargerRealtimeDataDB.site_no, ChargerRealtimeDataDB.ts, ChargerRealtimeDataDB.status
        ).order_by(ChargerRealtimeDataDB.ts, ChargerRealtimeDataDB.status)
        
        result = db.execute(stmt)
        charger_records = result.all()

        # 初始化数据数组
        grid_power = [0.0] * data_frames
        grid_supply = [0.0] * data_frames
        power = [0.0] * data_frames
        charging_consumption = [0.0] * data_frames
        
        # 处理光伏和电表数据
        for i, record in enumerate(pv_and_meter_records):
            if i >= data_frames:  # 防止索引越界
                break
                
            grid_power[i] = float(str(record.meter_value))
            # 能量 = 功率 * 时间(15分钟 = 0.25小时)
            grid_supply[i] = float(str(record.meter_value)) * 0.25

            # 充电站功率 = 光伏功率 + 储能功率 + 市电功率
            if i < len(es_records):
                power[i] = float(str(record.pv_power)) + float(str(es_records[i].es_power)) + float(str(record.meter_value))
            else:
                power[i] = float(str(record.pv_power)) + float(str(record.meter_value))
            
            # 充电站能量 = 充电站功率 * 时间(15分钟 = 0.25小时)
            charging_consumption[i] = power[i] * 0.25
        
        # 处理充电桩状态数据
        from collections import OrderedDict
        charger_status_statistics = OrderedDict()  # {ts: {status: count}, ...}
        for record in charger_records:
            if record.ts not in charger_status_statistics:
                charger_status_statistics[record.ts] = {}
            charger_status_statistics[record.ts][record.status] = record.count

        # 初始化充电桩状态数组
        number_of_chargers = [0] * data_frames
        active_chargers = [0] * data_frames
        occupied_chargers = [0] * data_frames
        available_chargers = [0] * data_frames
        chargers_under_maintenance = [0] * data_frames

        # 按时间戳顺序处理统计数据
        for i, ts in enumerate(sorted(charger_status_statistics.keys())):
            if i >= data_frames:  # 防止索引越界
                break
                
            status_counts = charger_status_statistics[ts]

            # 总充电枪数量
            total_chargers = sum(status_counts.values())
            number_of_chargers[i] = total_chargers

            # 活跃充电枪（正在充电）
            active_count = status_counts.get('Charging', 0)
            active_chargers[i] = active_count

            # 占用充电枪（正在充电、准备中、暂停等）
            occupied_statuses = ['Charging', 'Preparing', 'SuspendedEV', 'SuspendedEVSE', 'Reserved']
            occupied_count = sum(status_counts.get(status, 0) for status in occupied_statuses)
            occupied_chargers[i] = occupied_count

            # 可用充电枪
            available_count = status_counts.get('Available', 0)
            available_chargers[i] = available_count

            # 维护中充电枪
            maintenance_statuses = ['Faulted', 'Unavailable']
            maintenance_count = sum(status_counts.get(status, 0) for status in maintenance_statuses)
            chargers_under_maintenance[i] = maintenance_count
        
        # 获取电价信息数组
        electricity_pricing = []
        for i in range(data_frames):
            current_time = timestamp + timedelta(minutes=15 * i)
            current_price = get_current_electricity_price(site_no, current_time, db)
            electricity_pricing.append(current_price)
        
        return {
            'grid_power': grid_power,
            'grid_supply': grid_supply,
            'power': power,
            'charging_consumption': charging_consumption,
            'number_of_chargers': number_of_chargers,
            'active_chargers': active_chargers,
            'occupied_chargers': occupied_chargers,
            'available_chargers': available_chargers,
            'chargers_under_maintenance': chargers_under_maintenance,
            'electricity_pricing': electricity_pricing,
        }
    
    # 默认值数组
    default_result = {
        'grid_power': [0.0] * data_frames,
        'grid_supply': [0.0] * data_frames,
        'power': [0.0] * data_frames,
        'charging_consumption': [0.0] * data_frames,
        'number_of_chargers': [1] * data_frames,
        'active_chargers': [0] * data_frames,
        'occupied_chargers': [0] * data_frames,
        'available_chargers': [1] * data_frames,
        'chargers_under_maintenance': [0] * data_frames,
        'electricity_pricing': [0.15] * data_frames,
    }
    
    return execute_db_operation(f"获取场站{site_no}实时数据", default_result, _get_realtime_data)


def get_pv_history_data(site_no: str) -> List[float]:
    """
    从数据库获取过去24小时的光伏发电功率数据
    排除被控制的时间段
    
    Args:
        site_no: 场站编号
        
    Returns:
        List[float]: 96个15分钟间隔的光伏功率数据点，如果数据不可用则返回空列表
    """
    def _get_pv_data(db):
        # 计算时间范围：过去24小时
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=24)
        
        # 查询过去24小时的光伏数据
        query = db.query(PVAndMeterRealtimeDataDB).filter(
            PVAndMeterRealtimeDataDB.site_no == site_no,
            PVAndMeterRealtimeDataDB.created_at >= start_time,
            PVAndMeterRealtimeDataDB.created_at <= end_time,
            PVAndMeterRealtimeDataDB.pv_status == 'discharge'  # 只获取正常发电的数据
        ).order_by(PVAndMeterRealtimeDataDB.created_at)
        
        records = query.all()
        
        if not records:
            logger.warning(f"场站{site_no}过去24小时无光伏数据")
            return []
        
        # 将数据按15分钟间隔分组并计算平均值
        pv_data = []
        current_time = start_time
        
        while current_time < end_time and len(pv_data) < 96:
            next_time = current_time + timedelta(minutes=15)
            
            # 获取当前15分钟时间段内的数据
            period_records = []
            for record in records:
                if current_time <= record.created_at < next_time:
                    # 安全地转换数据库值为float
                    pv_power = float(str(record.pv_power))
                    period_records.append(pv_power)
            
            if period_records:
                # 计算平均值
                avg_power = sum(period_records) / len(period_records)
                pv_data.append(avg_power)
            else:
                # 如果没有数据，使用0
                pv_data.append(0.0)
            
            current_time = next_time
        
        # 检查数据质量：如果大部分数据为0或异常，认为数据不可用
        non_zero_count = sum(1 for power in pv_data if power > 0)
        if non_zero_count < 10:  # 如果有效数据点少于10个，认为数据不可用
            logger.warning(f"场站{site_no}过去24小时光伏数据质量较差，有效数据点: {non_zero_count}")
            return []
        
        logger.info(f"成功获取场站{site_no}过去24小时光伏数据，共{len(pv_data)}个数据点")
        return pv_data
    
    return execute_db_operation(f"获取场站{site_no}光伏历史数据", [], _get_pv_data)


def get_pv_mock_realtime_data(data_frames: int) -> Dict:
    """
    生成PV预测用的模拟实时数据
    
    Args:
        data_frames: 需要的数据帧数
        
    Returns:
        Dict: 模拟的实时数据
    """
    return {
        'grid_power': [25.0] * data_frames,  # 这里包含光伏发电功率
        'grid_supply': [10.0] * data_frames,
        'power': [25.0] * data_frames,
        'charging_consumption': [15.0] * data_frames,
        'number_of_chargers': [4] * data_frames,
        'active_chargers': [2] * data_frames,
        'occupied_chargers': [2] * data_frames,
        'available_chargers': [2] * data_frames,
        'chargers_under_maintenance': [0] * data_frames,
        'electricity_pricing': [0.15] * data_frames,
    }


def extract_weather_data(basic_weather: List, solar_data: List, weather_idx: int, solar_idx: int) -> Dict:
    """
    从天气数据中提取PV预测所需的字段
    
    Args:
        basic_weather: 基础天气数据
        solar_data: 太阳辐射数据
        weather_idx: 天气数据索引
        solar_idx: 太阳辐射数据索引
        
    Returns:
        Dict: 提取的天气数据
    """
    if basic_weather and solar_data:
        basic = basic_weather[weather_idx]
        solar = solar_data[solar_idx]
        
        return {
            'ghi': solar.get('ghi', 0.0),  # 全球水平辐照度
            'dni': solar.get('dni', 0.0),  # 法向直接日射辐照度  
            'dhi': solar.get('dhi', 0.0),  # 漫射水平辐照度
            'air_temperature': basic[2] if len(basic) > 2 else 25.0,  # 气温
            'cloud_cover': basic[1] if len(basic) > 1 else 20.0,  # 云覆盖
            'wind_speed': basic[6] if len(basic) > 6 else 10.0,  # 风速
            'relative_humidity': basic[3] if len(basic) > 3 else 50.0,  # 相对湿度
        }
    else:
        # 使用默认天气数据
        return {
            'ghi': 0.0,
            'dni': 0.0,
            'dhi': 0.0,
            'air_temperature': 25.0,
            'cloud_cover': 20.0,
            'wind_speed': 10.0,
            'relative_humidity': 50.0,
        }


def create_pv_data_row(timestamp: datetime, weather_data: Dict, generated_power: float) -> List:
    """
    创建PV预测数据行
    
    Args:
        timestamp: 时间戳
        weather_data: 天气数据
        generated_power: 光伏发电功率
        
    Returns:
        List: 数据行
    """
    timestamp_str = timestamp.strftime("%Y-%m-%d %H:%M:%S")
    
    return [
        timestamp_str,                    # timestamp
        weather_data['ghi'],             # GHI (全球水平辐照度)
        weather_data['dni'],             # DNI (法向直接日射辐照度)
        weather_data['dhi'],             # DHI (漫射水平辐照度)
        weather_data['air_temperature'], # air_temperature (气温)
        weather_data['cloud_cover'],     # cloud_cover (云覆盖)
        weather_data['wind_speed'],      # wind_speed (风速)
        weather_data['relative_humidity'], # relative_humidity (相对湿度)
        timestamp.hour,                  # hour (小时)
        timestamp.minute,                # minute (分钟)
        timestamp.timetuple().tm_yday,   # dayofyear (一年中的第几天)
        generated_power                  # generated_power (光伏发电功率)
    ]


def get_pv_default_input_data(site_no: str, current_time: datetime, data_frames: int) -> Dict:
    """
    生成PV预测的默认输入数据
    
    Args:
        site_no: 场站编号
        current_time: 当前时间
        
    Returns:
        Dict: 默认的输入数据
    """
    input_data = {
        "site_name": site_no,
        "site_id": site_no,
        "current_time": current_time.strftime("%Y-%m-%d %H:%M:%S"),
        "system_metadata": {
            "max_capacity": 1000.0,
            "azimuth": 180.0,
            "tilt": 30.0,
            "latitude": 52.19340694027317,
            "longitude": 5.430548227543284,
            "elevation": 100.0,
            "fields": [
                "Timestamp", "GHI", "DNI", "DHI", "air_temperature", "cloud_cover",
                "wind_speed", "relative_humidity", "hour", "minute",
                "dayofyear", "generated_power"
            ],
            "values": []
        }
    }
    
    # 生成默认的96行数据
    for i in range(data_frames):
        timestamp = current_time - timedelta(minutes=15 * (data_frames - 1 - i))
        timestamp_str = timestamp.strftime("%Y-%m-%d %H:%M:%S")
        row = [
            timestamp_str,  # timestamp
            0.0,           # GHI
            0.0,           # DNI
            0.0,           # DHI
            25.0,          # air_temperature
            20.0,          # cloud_cover
            10.0,          # wind_speed
            50.0,          # relative_humidity
            timestamp.hour,           # hour
            timestamp.minute,         # minute
            timestamp.timetuple().tm_yday,  # dayofyear
            0.0            # generated_power
        ]
        input_data["system_metadata"]["values"].append(row)
    
    return input_data