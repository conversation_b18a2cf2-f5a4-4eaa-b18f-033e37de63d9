import numpy as np
from application.algorithm.power_predict.spine import spine


def calcPowerCurveLowTemp(startSoc, currPow, pLimit, coeff_b1, coeff_b2, powerTable):
    # using the four point to fit the curve,the key point is to calculate the second point
    # now to calculate the soc of the second point
    deta = 90 - startSoc
    soc = startSoc + deta * coeff_b1
    # now to calculate the power of the second point
    if pLimit < coeff_b2:
        power = pLimit  # charger limited
    else:
        power = coeff_b2  # suppose 85kw is the ev's normal demand
    # it can reach 61732 error,which is the minimum
    x1 = startSoc
    y1 = currPow
    x2 = soc
    y2 = power
    x3 = 90
    y3 = 40
    x4 = 100
    y4 = 10
    x = np.array([x1, x2, x3, x4])
    y = np.array([y1, y2, y3, y4])
    smooth_x, smooth_y = spine(x, y)
    curve = list(zip(smooth_x, smooth_y))
    control_points = [(x1, y1), (x2, y2), (x3, y3), (x4, y4)]

    return control_points, curve


# draw a curve which is starting to descend from the point of startup_power
def calcPowerCurveDescending(startSoc, startup_power, pLimit, coeff):
    x1 = startSoc
    y1 = startup_power
    deta = 90 - startSoc
    x2 = startSoc + deta * coeff  # coeff is [0,1]
    y2 = startup_power
    x3 = 90
    y3 = 40
    x4 = 100
    y4 = 10
    x = np.array([x1, x2, x3, x4])
    y = np.array([y1, y2, y3, y4])
    smooth_x, smooth_y = spine(x, y)
    curve = list(zip(smooth_x, smooth_y))
    control_points = [(x1, y1), (x2, y2), (x3, y3), (x4, y4)]

    return control_points, curve


# para:
# startSoc:the starting soc
# pLimit:the evse powe limit,considerging the cable current limit
def calcPowerCurveNormal(startSoc, startup_power, pLimit, coeff, powerTable):
    # firstly,to calculate the four control points
    x1 = startSoc
    y1 = startup_power
    idealPower = powerTable[startSoc * 10]  # ideal power at startSoc
    idealPower = idealPower / 1000  # now unit is kw
    y2 = min(idealPower, pLimit)
    eff1 = (70 - startSoc) / 70  # range is [0,1]
    # pLmit is idealPower,then eff2 is 0; pLimit is 40kw,the eff2 is 1,range is[0,1]
    eff2 = (pLimit - 40) / (idealPower - 40)
    eff2 = np.clip(eff2, 0, 1)  # if pLimit is larger than idealPower,it is larger than 1
    eff2 = 1 - eff2
    avg_eff = coeff * eff1 + (1 - coeff) * eff2
    x2 = x1 * (1 - avg_eff) + 80 * avg_eff  # x2 is [x1,80]
    x3 = 90  # soc being 90
    y3 = 40  # unit is kw
    x4 = 100
    y4 = 10  # unit is kw
    x = np.array([x1, x2, x3, x4])
    y = np.array([y1, y2, y3, y4])
    smooth_x, smooth_y = spine(x, y)
    curve = list(zip(smooth_x, smooth_y))
    control_points = [(x1, y1), (x2, y2), (x3, y3), (x4, y4)]
    return control_points, curve


def calcPowerCurve(sSOC, startup_power, descending_flag, plimit, coeff_a, coeff_b1, coeff_b2, coeff_c, dict_power):
    # print(f"sSOC is {sSOC},startup_power is {startup_power},plimit is {plimit}")
    if (startup_power) < plimit * 0.6:  # unit is kw
        # it is in low temperature status:
        # print("use the calcPowerCurveLowTemp")
        return calcPowerCurveLowTemp(sSOC, startup_power, plimit, coeff_b1, coeff_b2, dict_power)
    elif descending_flag:
        # print("use the calcPowerCurveDescending")
        return calcPowerCurveDescending(sSOC, startup_power, plimit, coeff_c)
    else:
        # print("use calcPowerCurveNormal")
        return calcPowerCurveNormal(sSOC, startup_power, plimit, coeff_a, dict_power)


def calcPowerCurve_2(sSOC, startup_power, descending_flag, plimit, coeff_a, coeff_b1, coeff_b2, coeff_c, dict_power):
    # print(f"sSOC is {sSOC},startup_power is {startup_power},plimit is {plimit}")
    if (startup_power) < plimit * 0.6:  # unit is kw
        # it is in low temperature status:
        # print("use the calcPowerCurveLowTemp")
        return calcPowerCurveLowTemp(sSOC, startup_power, plimit, coeff_b1, coeff_b2, dict_power)
    else:
        # print("use calcPowerCurveNormal")
        return calcPowerCurveNormal(sSOC, startup_power, plimit, coeff_a, dict_power)