#!/usr/bin/env python3
"""
测试动态获取支持的国家列表功能
"""

import sys
import os
import logging
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('dynamic_countries_test.log', mode='w', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def test_dynamic_countries_config():
    """测试动态获取国家列表配置"""
    logger = setup_logging()
    
    logger.info("=" * 60)
    logger.info("测试动态获取支持的国家列表功能")
    logger.info("=" * 60)
    
    try:
        # 测试直接从 NordPoolAPI 获取
        logger.info("1. 测试直接从 NordPoolAPI 获取国家列表")
        from application.utils.electricity_price import NordPoolAPI
        
        api = NordPoolAPI()
        areas_from_api = api.areas
        logger.info(f"从 NordPoolAPI 获取的国家列表: {areas_from_api}")
        logger.info(f"国家数量: {len(areas_from_api)}")
        
        # 验证基本要求
        assert isinstance(areas_from_api, list), "areas 应该是一个列表"
        assert len(areas_from_api) > 0, "areas 列表不应该为空"
        assert all(isinstance(country, str) for country in areas_from_api), "所有国家代码应该是字符串"
        assert all(len(country) == 2 for country in areas_from_api), "所有国家代码应该是2位字符"
        
        logger.info("✅ NordPoolAPI 国家列表获取成功")
        
        # 测试配置类的动态获取方法
        logger.info("\n2. 测试 DynamicPriceRetryConfig 的动态获取方法")
        from application.algorithm_schedule.dynamic_price_scheduler import DynamicPriceRetryConfig
        
        areas_from_config = DynamicPriceRetryConfig.get_supported_countries()
        logger.info(f"从配置类获取的国家列表: {areas_from_config}")
        logger.info(f"国家数量: {len(areas_from_config)}")
        
        # 验证两个来源的一致性
        assert areas_from_config == areas_from_api, f"配置类获取的国家列表应该与API一致: {areas_from_config} != {areas_from_api}"
        
        logger.info("✅ 配置类动态获取方法验证成功")
        
        # 测试配置类的静态属性
        logger.info("\n3. 测试 DynamicPriceRetryConfig 的静态属性")
        static_countries = DynamicPriceRetryConfig.SUPPORTED_COUNTRIES
        logger.info(f"配置类静态属性的国家列表: {static_countries}")
        logger.info(f"国家数量: {len(static_countries)}")
        
        # 验证静态属性与动态获取的一致性
        assert static_countries == areas_from_api, f"静态属性应该与API一致: {static_countries} != {areas_from_api}"
        
        logger.info("✅ 配置类静态属性验证成功")
        
        return True
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_scheduler_with_dynamic_countries():
    """测试调度器使用动态国家列表"""
    logger = logging.getLogger(__name__)
    
    logger.info("\n" + "=" * 60)
    logger.info("测试调度器使用动态国家列表")
    logger.info("=" * 60)
    
    try:
        from application.algorithm_schedule.dynamic_price_scheduler import DynamicPriceScheduler
        from application.utils.electricity_price import NordPoolAPI
        
        # 获取预期的国家列表
        api = NordPoolAPI()
        expected_countries = api.areas
        
        # 创建调度器实例
        logger.info("创建 DynamicPriceScheduler 实例")
        scheduler = DynamicPriceScheduler()
        
        # 验证调度器使用的国家列表
        logger.info(f"调度器支持的国家: {scheduler.supported_countries}")
        logger.info(f"预期的国家列表: {expected_countries}")
        
        # 验证一致性
        assert scheduler.supported_countries == expected_countries, f"调度器国家列表应该与API一致: {scheduler.supported_countries} != {expected_countries}"
        
        # 验证重试状态初始化
        logger.info(f"重试计数初始化: {scheduler.countries_retry_count}")
        logger.info(f"成功状态初始化: {scheduler.countries_success_status}")
        
        # 验证所有国家都有对应的状态
        assert set(scheduler.countries_retry_count.keys()) == set(expected_countries), "重试计数应该包含所有国家"
        assert set(scheduler.countries_success_status.keys()) == set(expected_countries), "成功状态应该包含所有国家"
        
        # 验证初始状态
        assert all(count == 0 for count in scheduler.countries_retry_count.values()), "初始重试计数应该都是0"
        assert all(not status for status in scheduler.countries_success_status.values()), "初始成功状态应该都是False"
        
        logger.info("✅ 调度器动态国家列表验证成功")
        
        # 测试状态获取
        logger.info("\n测试状态获取功能")
        status = scheduler.get_retry_status()
        
        logger.info(f"状态中的支持国家: {status['supported_countries']}")
        assert status['supported_countries'] == expected_countries, "状态中的国家列表应该与预期一致"
        
        logger.info("✅ 状态获取功能验证成功")
        
        return True
        
    except Exception as e:
        logger.error(f"调度器测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_fallback_mechanism():
    """测试回退机制"""
    logger = logging.getLogger(__name__)
    
    logger.info("\n" + "=" * 60)
    logger.info("测试回退机制")
    logger.info("=" * 60)
    
    try:
        from application.algorithm_schedule.dynamic_price_scheduler import DynamicPriceRetryConfig
        from unittest.mock import patch
        
        # 模拟 NordPoolAPI 初始化失败
        logger.info("模拟 NordPoolAPI 初始化失败的情况")
        
        with patch('application.algorithm_schedule.dynamic_price_scheduler.NordPoolAPI') as mock_api:
            # 让 NordPoolAPI 初始化时抛出异常
            mock_api.side_effect = Exception("模拟的API初始化失败")
            
            # 调用动态获取方法
            fallback_countries = DynamicPriceRetryConfig.get_supported_countries()
            
            logger.info(f"回退机制返回的国家列表: {fallback_countries}")
            
            # 验证回退到默认列表
            expected_fallback = ['AT', 'BE', 'DE', 'FR', 'NL', 'PL']
            assert fallback_countries == expected_fallback, f"回退机制应该返回默认列表: {fallback_countries} != {expected_fallback}"
            
            logger.info("✅ 回退机制验证成功")
        
        return True
        
    except Exception as e:
        logger.error(f"回退机制测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("开始测试动态获取支持的国家列表功能")
    logger.info(f"测试时间: {datetime.now()}")
    
    tests = [
        ("动态国家列表配置", test_dynamic_countries_config),
        ("调度器动态国家列表", test_scheduler_with_dynamic_countries),
        ("回退机制", test_fallback_mechanism),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                logger.info(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
    
    logger.info(f"\n{'='*60}")
    logger.info(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有测试都通过了！")
        logger.info("动态获取国家列表功能验证成功")
        logger.info("特性:")
        logger.info("  ✅ 从 NordPoolAPI.areas 动态获取支持的国家")
        logger.info("  ✅ 配置类支持动态获取方法")
        logger.info("  ✅ 调度器自动使用动态国家列表")
        logger.info("  ✅ 支持回退机制，API失败时使用默认列表")
        return True
    else:
        logger.error("❌ 部分测试失败")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
