import unittest
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

from application.api_manage.energy_cloud_api import EnergyCloudAPI
from application.utils.idempotency import generate_idempotency_key


class TestEnergyCloudAPI(unittest.TestCase):
    """能源云API测试类"""

    def setUp(self):
        """测试前的准备工作"""
        self.base_url = "http://energy-cloud-api.example.com"
        self.site_no = "SITE001"
        self.start_time = datetime.now()

    def test_class_attributes(self):
        """测试类属性"""
        # 验证类属性是否正确设置
        self.assertTrue(hasattr(EnergyCloudAPI, 'base_url'))
        self.assertTrue(hasattr(EnergyCloudAPI, 'power_predict_curve_url'))
        self.assertTrue(hasattr(EnergyCloudAPI, 'electricity_price_curve_url'))
        
        # 验证URL构造是否正确
        self.assertTrue(EnergyCloudAPI.power_predict_curve_url.endswith('/power_predict_curve'))
        self.assertTrue(EnergyCloudAPI.electricity_price_curve_url.endswith('/electricit_price_curve'))

    @patch('requests.post')
    def test_send_power_predict_curves_success(self, mock_post):
        """测试成功发送预测曲线数据"""
        # 准备测试数据
        es_curve = [12.0] * 96
        pv_curve = [18.0] * 96
        load_curve = [25.0] * 96
        
        # 准备不同的开始时间（秒时间戳）
        es_start_time = int(self.start_time.timestamp())
        pv_start_time = int((self.start_time + timedelta(minutes=15)).timestamp())
        load_start_time = int((self.start_time + timedelta(minutes=30)).timestamp())

        # 模拟成功响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_post.return_value = mock_response

        # 执行测试（使用静态方法）
        result = EnergyCloudAPI.send_power_predict_curves(
            site_no=self.site_no,
            es_power_curve=es_curve,
            es_start_time=es_start_time,
            pv_power_curve=pv_curve,
            pv_start_time=pv_start_time,
            load_power_curve=load_curve,
            load_start_time=load_start_time
        )

        # 验证结果
        self.assertTrue(result)
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        self.assertEqual(call_args[0][0], EnergyCloudAPI.power_predict_curve_url)
        self.assertEqual(call_args[1]['headers'], {"Content-Type": "application/json"})
        
        # 验证请求数据包含幂等串和不同的开始时间
        request_data = call_args[1]['json']
        self.assertIn('bizSeq', request_data)
        self.assertEqual(len(request_data['bizSeq']), 20)  # 验证幂等串长度为20位
        
        # 验证每个曲线有不同的开始时间
        self.assertEqual(request_data['es_data']['start_time'], es_start_time * 1000)
        self.assertEqual(request_data['pv_data']['start_time'], pv_start_time * 1000)
        self.assertEqual(request_data['charger_data']['start_time'], load_start_time * 1000)

    @patch('requests.post')
    def test_send_power_predict_curves_invalid_points(self, mock_post):
        """测试发送无效点数的预测曲线数据"""
        # 准备测试数据（点数不足96个）
        es_curve = [12.0] * 95
        pv_curve = [18.0] * 96
        load_curve = [25.0] * 96

        # 模拟成功响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_post.return_value = mock_response

        # 执行测试（使用静态方法）
        result = EnergyCloudAPI.send_power_predict_curves(
            site_no=self.site_no,
            es_power_curve=es_curve,
            es_start_time=int(self.start_time.timestamp()),
            pv_power_curve=pv_curve,
            pv_start_time=int(self.start_time.timestamp()),
            load_power_curve=load_curve,
            load_start_time=int(self.start_time.timestamp())
        )

        # 验证结果
        self.assertTrue(result)  # 由于移除了点数验证，现在应该返回True
        mock_post.assert_called_once()

    @patch('requests.post')
    def test_send_power_predict_curves_optional_parameters(self, mock_post):
        """测试可选参数功能"""
        # 准备测试数据（只提供储能曲线）
        es_curve = [12.0] * 96

        # 模拟成功响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_post.return_value = mock_response

        # 执行测试（只传入储能曲线）
        result = EnergyCloudAPI.send_power_predict_curves(
            site_no=self.site_no,
            es_power_curve=es_curve,
            es_start_time=int(self.start_time.timestamp())
        )

        # 验证结果
        self.assertTrue(result)
        mock_post.assert_called_once()
        
        # 验证请求数据只包含储能数据
        call_args = mock_post.call_args
        request_data = call_args[1]['json']
        self.assertIn('es_data', request_data)
        self.assertNotIn('pv_data', request_data)
        self.assertNotIn('charger_data', request_data)
        self.assertIn('bizSeq', request_data)

    def test_send_power_predict_curves_no_curves(self):
        """测试不提供任何曲线数据的情况"""
        # 执行测试（不提供任何曲线）
        result = EnergyCloudAPI.send_power_predict_curves(
            site_no=self.site_no
        )

        # 验证结果
        self.assertFalse(result)

    def test_send_power_predict_curves_missing_start_time(self):
        """测试提供曲线但缺少对应开始时间的情况"""
        # 准备测试数据
        es_curve = [12.0] * 96
        
        # 执行测试（提供曲线但不提供开始时间）
        result = EnergyCloudAPI.send_power_predict_curves(
            site_no=self.site_no,
            es_power_curve=es_curve
            # 缺少 es_start_time
        )

        # 验证结果
        self.assertFalse(result)

    @patch('requests.post')
    def test_send_power_predict_curves_partial_curves(self, mock_post):
        """测试部分曲线数据的情况"""
        # 准备测试数据（只提供光伏和充电桩曲线）
        pv_curve = [18.0] * 96
        load_curve = [25.0] * 96
        
        pv_start_time = int(self.start_time.timestamp())
        load_start_time = int((self.start_time + timedelta(minutes=15)).timestamp())

        # 模拟成功响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_post.return_value = mock_response

        # 执行测试
        result = EnergyCloudAPI.send_power_predict_curves(
            site_no=self.site_no,
            pv_power_curve=pv_curve,
            pv_start_time=pv_start_time,
            load_power_curve=load_curve,
            load_start_time=load_start_time
        )

        # 验证结果
        self.assertTrue(result)
        mock_post.assert_called_once()
        
        # 验证请求数据包含光伏和充电桩数据，但不包含储能数据
        call_args = mock_post.call_args
        request_data = call_args[1]['json']
        self.assertNotIn('es_data', request_data)
        self.assertIn('pv_data', request_data)
        self.assertIn('charger_data', request_data)
        self.assertIn('bizSeq', request_data)
        
        # 验证不同的开始时间
        self.assertEqual(request_data['pv_data']['start_time'], pv_start_time * 1000)
        self.assertEqual(request_data['charger_data']['start_time'], load_start_time * 1000)

    def test_generate_idempotency_key_consistency(self):
        """测试幂等串生成的一致性"""
        # 准备相同的测试数据
        test_data = {
            "site_no": "SITE001",
            "es_data": {
                "start_time": 1640995200000,
                "power_curve": [12.0] * 96
            }
        }
        
        # 多次生成幂等串应该一致
        key1 = generate_idempotency_key(test_data)
        key2 = generate_idempotency_key(test_data)
        
        self.assertEqual(key1, key2)
        self.assertEqual(len(key1), 20)
        
        # 测试generate_biz_seq函数
        biz_seq1 = generate_idempotency_key(test_data)
        biz_seq2 = generate_idempotency_key(test_data)
        
        self.assertEqual(biz_seq1, biz_seq2)
        self.assertEqual(len(biz_seq1), 20)
        self.assertEqual(key1, biz_seq1)  # 两个函数应该产生相同结果
        
        # 不同数据应该生成不同的幂等串
        test_data2 = test_data.copy()
        test_data2["site_no"] = "SITE002"
        key3 = generate_idempotency_key(test_data2)
        
        self.assertNotEqual(key1, key3)

    @patch('requests.post')
    def test_send_power_predict_curves_api_error(self, mock_post):
        """测试API调用失败的情况"""
        # 准备测试数据
        es_curve = [12.0] * 96

        # 模拟API错误响应
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_response.text = "Internal Server Error"
        mock_post.return_value = mock_response

        # 执行测试
        result = EnergyCloudAPI.send_power_predict_curves(
            site_no=self.site_no,
            es_power_curve=es_curve,
            es_start_time=int(self.start_time.timestamp())
        )

        # 验证结果
        self.assertFalse(result)
        mock_post.assert_called_once()

    @patch('requests.post')
    def test_send_electricity_price_curve_success(self, mock_post):
        """测试成功发送电价曲线数据"""
        # 准备测试数据
        price_segments = {
            "biz_seq": "test_biz_seq_12345678",
            "electricity_price": [
                {
                    "region": "NO1",
                    "grid_name": "Nord Pool",
                    "time_zone": "UTC+0",
                    "freq": "T-1",
                    "can_use": True,
                    "price_data": [
                        {
                            "e_price": 0.05,
                            "start_time": 1640995200000,
                            "end_time": 1640998800000,
                            "time_zone": "UTC+0",
                            "unit": "EUR"
                        }
                    ] * 24
                }
            ]
        }

        # 模拟成功响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_post.return_value = mock_response

        # 执行测试
        result = EnergyCloudAPI.send_electricity_price_curve(price_segments=price_segments)

        # 验证结果
        self.assertTrue(result)
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        self.assertEqual(call_args[0][0], EnergyCloudAPI.electricity_price_curve_url)
        self.assertEqual(call_args[1]['headers'], {"Content-Type": "application/json"})
        self.assertEqual(call_args[1]['json'], price_segments)

    @patch('requests.post')
    def test_send_electricity_price_curve_invalid_duration(self, mock_post):
        """测试发送持续时间不是1小时的电价曲线数据"""
        # 准备测试数据（时间间隔不是1小时）
        price_segments = {
            "biz_seq": "test_biz_seq_12345678",
            "electricity_price": [
                {
                    "region": "NO1",
                    "grid_name": "Nord Pool",
                    "time_zone": "UTC+0",
                    "freq": "T-1",
                    "can_use": True,
                    "price_data": [
                        {
                            "e_price": 0.05,
                            "start_time": 1640995200000,
                            "end_time": 1640997000000,  # 30分钟而不是1小时
                            "time_zone": "UTC+0",
                            "unit": "EUR"
                        }
                    ] * 24
                }
            ]
        }

        # 模拟成功响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_post.return_value = mock_response

        # 执行测试
        result = EnergyCloudAPI.send_electricity_price_curve(price_segments=price_segments)

        # 验证结果（由于移除了验证，现在应该返回True）
        self.assertTrue(result)
        mock_post.assert_called_once()

    @patch('requests.post')
    def test_send_electricity_price_curve_discontinuous(self, mock_post):
        """测试发送不连续的电价曲线数据"""
        # 准备测试数据（时间不连续）
        price_segments = {
            "biz_seq": "test_biz_seq_12345678",
            "electricity_price": [
                {
                    "region": "NO1",
                    "grid_name": "Nord Pool",
                    "time_zone": "UTC+0",
                    "freq": "T-1",
                    "can_use": True,
                    "price_data": [
                        {
                            "e_price": 0.05,
                            "start_time": 1640995200000,
                            "end_time": 1640998800000,
                            "time_zone": "UTC+0",
                            "unit": "EUR"
                        },
                        {
                            "e_price": 0.06,
                            "start_time": 1641002400000,  # 跳过了一个小时
                            "end_time": 1641006000000,
                            "time_zone": "UTC+0",
                            "unit": "EUR"
                        }
                    ]
                }
            ]
        }

        # 模拟成功响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_post.return_value = mock_response

        # 执行测试
        result = EnergyCloudAPI.send_electricity_price_curve(price_segments=price_segments)

        # 验证结果（由于移除了验证，现在应该返回True）
        self.assertTrue(result)
        mock_post.assert_called_once()

    @patch('requests.post')
    def test_send_electricity_price_curve_api_error(self, mock_post):
        """测试电价曲线API调用失败的情况"""
        # 准备测试数据
        price_segments = {
            "biz_seq": "test_biz_seq_12345678",
            "electricity_price": [
                {
                    "region": "NO1",
                    "grid_name": "Nord Pool",
                    "time_zone": "UTC+0",
                    "freq": "T-1",
                    "can_use": True,
                    "price_data": [
                        {
                            "e_price": 0.05,
                            "start_time": 1640995200000,
                            "end_time": 1640998800000,
                            "time_zone": "UTC+0",
                            "unit": "EUR"
                        }
                    ] * 24
                }
            ]
        }

        # 模拟API错误响应
        mock_response = MagicMock()
        mock_response.status_code = 400
        mock_response.text = "Bad Request"
        mock_post.return_value = mock_response

        # 执行测试
        result = EnergyCloudAPI.send_electricity_price_curve(price_segments=price_segments)

        # 验证结果
        self.assertFalse(result)
        mock_post.assert_called_once()

    def test_send_electricity_price_curve_missing_biz_seq(self):
        """测试缺少biz_seq的电价曲线数据"""
        # 准备测试数据（缺少biz_seq）
        price_segments = {
            "electricity_price": [
                {
                    "region": "NO1",
                    "grid_name": "Nord Pool",
                    "time_zone": "UTC+0",
                    "freq": "T-1",
                    "can_use": True,
                    "price_data": []
                }
            ]
        }

        # 执行测试
        result = EnergyCloudAPI.send_electricity_price_curve(price_segments=price_segments)

        # 验证结果
        self.assertFalse(result)

    def test_send_electricity_price_curve_missing_electricity_price(self):
        """测试缺少electricity_price的电价曲线数据"""
        # 准备测试数据（缺少electricity_price）
        price_segments = {
            "biz_seq": "test_biz_seq_12345678"
        }

        # 执行测试
        result = EnergyCloudAPI.send_electricity_price_curve(price_segments=price_segments)

        # 验证结果
        self.assertFalse(result)

    def test_send_power_predict_curves_different_start_times_scenario(self):
        """测试实际业务场景：不同曲线有不同开始时间"""
        # 模拟实际业务场景
        base_time = datetime(2025, 1, 1, 0, 0, 0)  # 2025年1月1日午夜
        
        # 储能：立即开始（用于实时调度）
        es_start_time = int(base_time.timestamp())
        es_curve = [10.0] * 96
        
        # 光伏：从早上6点开始（日出时间）
        pv_start_time = int(base_time.replace(hour=6).timestamp())
        pv_curve = [0.0] * 24 + [15.0] * 48 + [0.0] * 24  # 白天有发电
        
        # 负载：从早上7点开始（用电高峰开始）
        load_start_time = int(base_time.replace(hour=7).timestamp())
        load_curve = [5.0] * 32 + [20.0] * 32 + [8.0] * 32  # 模拟用电曲线

        with patch('requests.post') as mock_post:
            # 模拟成功响应
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_post.return_value = mock_response

            # 执行测试
            result = EnergyCloudAPI.send_power_predict_curves(
                site_no=self.site_no,
                es_power_curve=es_curve,
                es_start_time=es_start_time,
                pv_power_curve=pv_curve,
                pv_start_time=pv_start_time,
                load_power_curve=load_curve,
                load_start_time=load_start_time
            )

            # 验证结果
            self.assertTrue(result)
            
            # 验证请求数据
            call_args = mock_post.call_args
            request_data = call_args[1]['json']
            
            # 验证时间戳转换正确
            self.assertEqual(
                request_data['es_data']['start_time'],
                es_start_time * 1000
            )
            self.assertEqual(
                request_data['pv_data']['start_time'],
                pv_start_time * 1000
            )
            self.assertEqual(
                request_data['charger_data']['start_time'],
                load_start_time * 1000
            )
            
            # 验证时间差异
            es_timestamp = request_data['es_data']['start_time']
            pv_timestamp = request_data['pv_data']['start_time']
            load_timestamp = request_data['charger_data']['start_time']
            
            # 光伏比储能晚6小时（6 * 60 * 60 * 1000 = 21600000毫秒）
            self.assertEqual(pv_timestamp - es_timestamp, 6 * 60 * 60 * 1000)
            
            # 负载比储能晚7小时（7 * 60 * 60 * 1000 = 25200000毫秒）
            self.assertEqual(load_timestamp - es_timestamp, 7 * 60 * 60 * 1000)


if __name__ == '__main__':
    unittest.main() 