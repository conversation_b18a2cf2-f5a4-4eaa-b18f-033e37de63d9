#!/usr/bin/env python3
"""
单独调用 storage_dispatch 的测试文件
将日志输出到 log.txt 中
"""

import sys
import os
import logging
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def setup_logging():
    """设置日志配置，将日志输出到 log.txt 文件"""
    # 创建日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 配置根日志记录器
    logging.basicConfig(
        level=logging.DEBUG,
        format=log_format,
        handlers=[
            # 文件处理器 - 输出到 log.txt
            logging.FileHandler('log.txt', mode='w', encoding='utf-8'),
            # 控制台处理器 - 同时输出到控制台
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # 设置特定模块的日志级别
    logging.getLogger('storage_dispatch').setLevel(logging.DEBUG)
    logging.getLogger('AI_EMS').setLevel(logging.INFO)
    
    return logging.getLogger(__name__)

def test_storage_dispatch():
    """测试 storage_dispatch 方法"""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("=" * 60)
        logger.info("开始测试 storage_dispatch 方法")
        logger.info("=" * 60)
        
        # 导入 storage_dispatch 方法
        from application.algorithm_schedule.storage_dispatch import storage_dispatch
        
        # 测试参数
        site_no = 'SITE001'
        data = None
        trigger = 'test_manual'
        
        logger.info(f"测试参数:")
        logger.info(f"  site_no: {site_no}")
        logger.info(f"  data: {data}")
        logger.info(f"  trigger: {trigger}")
        logger.info("-" * 40)
        
        # 调用 storage_dispatch
        logger.info("开始调用 storage_dispatch...")
        start_time = datetime.now()
        
        result = storage_dispatch(site_no=site_no, data=data, trigger=trigger)
        
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()
        
        logger.info(f"storage_dispatch 调用完成，耗时: {execution_time:.3f} 秒")
        logger.info("-" * 40)
        
        # 输出结果
        if result is not None:
            logger.info("调用成功!")
            logger.info(f"返回结果类型: {type(result)}")
            
            if isinstance(result, dict):
                logger.info("返回结果内容:")
                for key, value in result.items():
                    if isinstance(value, list) and len(value) > 10:
                        # 如果是长列表，只显示前几个和后几个元素
                        logger.info(f"  {key}: [{value[0]}, {value[1]}, ..., {value[-2]}, {value[-1]}] (共{len(value)}个元素)")
                    else:
                        logger.info(f"  {key}: {value}")
            else:
                logger.info(f"返回结果: {result}")
        else:
            logger.warning("调用失败，返回结果为 None")
            logger.warning("请检查:")
            logger.warning("  1. 数据库连接是否正常")
            logger.warning("  2. 场站数据是否存在")
            logger.warning("  3. 相关预测数据是否完整")
        
        logger.info("=" * 60)
        logger.info("测试完成")
        logger.info("=" * 60)
        
        return result
        
    except ImportError as e:
        logger.error(f"导入模块失败: {e}")
        logger.error("请确保项目路径正确，并且所有依赖都已安装")
        return None
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        logger.error(f"错误类型: {type(e).__name__}")
        
        # 记录详细的错误信息
        import traceback
        logger.error("详细错误信息:")
        logger.error(traceback.format_exc())
        
        return None

def test_multiple_scenarios():
    """测试多种场景"""
    logger = logging.getLogger(__name__)
    
    logger.info("\n" + "=" * 60)
    logger.info("开始多场景测试")
    logger.info("=" * 60)
    
    # 测试场景列表
    test_scenarios = [
        {
            'name': '正常场站测试',
            'site_no': 'SITE001',
            'data': None,
            'trigger': 'manual'
        },
        {
            'name': '不存在的场站测试',
            'site_no': 'NONEXISTENT_SITE',
            'data': None,
            'trigger': 'test'
        },
        {
            'name': '空场站编号测试',
            'site_no': '',
            'data': None,
            'trigger': 'test'
        },
        {
            'name': '带数据的测试',
            'site_no': 'SITE001',
            'data': {'test_key': 'test_value'},
            'trigger': 'scheduled'
        }
    ]
    
    results = {}
    
    for i, scenario in enumerate(test_scenarios, 1):
        logger.info(f"\n--- 场景 {i}: {scenario['name']} ---")
        
        try:
            from application.algorithm_schedule.storage_dispatch import storage_dispatch
            
            start_time = datetime.now()
            result = storage_dispatch(
                site_no=scenario['site_no'],
                data=scenario['data'],
                trigger=scenario['trigger']
            )
            end_time = datetime.now()
            
            execution_time = (end_time - start_time).total_seconds()
            
            results[scenario['name']] = {
                'result': result,
                'execution_time': execution_time,
                'success': result is not None
            }
            
            logger.info(f"执行时间: {execution_time:.3f} 秒")
            logger.info(f"结果: {'成功' if result is not None else '失败'}")
            
        except Exception as e:
            logger.error(f"场景执行失败: {e}")
            results[scenario['name']] = {
                'result': None,
                'execution_time': 0,
                'success': False,
                'error': str(e)
            }
    
    # 输出测试总结
    logger.info("\n" + "=" * 60)
    logger.info("多场景测试总结")
    logger.info("=" * 60)
    
    for scenario_name, result_info in results.items():
        status = "✅ 成功" if result_info['success'] else "❌ 失败"
        logger.info(f"{scenario_name}: {status} (耗时: {result_info['execution_time']:.3f}s)")
        
        if 'error' in result_info:
            logger.info(f"  错误: {result_info['error']}")
    
    return results

def main():
    """主函数"""
    # 设置日志
    logger = setup_logging()
    
    logger.info("测试开始时间: " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    logger.info("日志文件: log.txt")
    logger.info("Python版本: " + sys.version)
    logger.info("工作目录: " + os.getcwd())
    
    try:
        # 单个测试
        result = test_storage_dispatch()
        
        # 多场景测试
        multi_results = test_multiple_scenarios()
        
        # 最终总结
        logger.info("\n" + "=" * 60)
        logger.info("所有测试完成")
        logger.info("=" * 60)
        logger.info("请查看 log.txt 文件获取完整的日志信息")
        
        return result, multi_results
        
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        return None, None
        
    except Exception as e:
        logger.error(f"主程序执行失败: {e}")
        return None, None

if __name__ == '__main__':
    main()
