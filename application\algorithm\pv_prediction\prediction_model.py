import os
import pandas as pd
import numpy as np
import joblib
import json
from datetime import datetime, timedelta
import pickle
from application.algorithm.pv_prediction import pv_feature_engineering
from application.utils.logger import setup_logger
logger = setup_logger("pv_prediction", direction="algorithm")

INTERVAL_MINUTES = 15
NUM_INTERVALS = 96

# Given input_data
# input_data = {
#     "site_name": "Site Name",
#     "site_id": "Site ID",
#     "system_metadata": {
#         "max_capacity": 1000.0,
#         "azimuth": 180.0,
#         "tilt": 30.0,
#         "latitude": 40.1234,
#         "longitude": -74.5678,
#         "elevation": 50.0,
#         "fields": [
#             "Timestamp","GHI","DNI","DHI","air_temperature","cloud_cover",
#             "wind_speed","relative_humidity","hour","minute",
#             "dayofyear","generated_power"
#         ],
#         "values": [
#             ["2019-11-02 01:00:00",0.0,0.0,0.0,15.2,0.8,3.5,65.0,0,0,183,0.0],
#             ["2019-11-02 01:00:15",5.2,0.5,4.7,15.5,0.7,3.6,64.8,0,15,183,0.0],
#             # … up to 96 rows …
#             ["2019-11-01 01:00:00",10.1,2.3,7.8,20.5,0.3,4.2,60.1,23,45,183,450.0]
#         ]
#     }
# }


# ====================== Load model ==========================
the_path = os.path.dirname(os.path.abspath(__file__))
with open(os.path.join(the_path, "pv_xgb_model.pkl"), "rb") as f:
    pv_model = pickle.load(f)

# ====================== INPUT DATA PROCESSING ===============
target_feature = "generated_power"
window_features = {"generated_power": 96}
horizon_features = {}
forecast_features = {}
forecast_horizon = 96

time_granularity = 15 # in minutes
time_granularity_str = str(time_granularity) + 'min'

# ===============================================================


def prepare_features_from_input(input_data: dict) -> pd.DataFrame:
    meta = input_data["system_metadata"].copy()
    current_time = input_data["current_time"]
    fields = meta.pop("fields")
    values = meta.pop("values")

    df_inf = pd.DataFrame(values, columns=fields)
    for k, v in meta.items():
        df_inf[k] = v

    val_columns=['Timestamp','generated_power','DNI','DHI','air_temperature']
    df_inf = df_inf[val_columns]

    df_inf['Timestamp'] = pd.to_datetime(df_inf['Timestamp'])
    df_inf = df_inf.set_index('Timestamp')
    df_inf = df_inf[~df_inf.index.duplicated(keep='first')]
    df_15min = df_inf.resample('15min').ffill()
    df_inf = df_15min.reset_index()

    df_inf['Hour'] = df_inf['Timestamp'].dt.hour
    df_inf['DayOfWeek'] = df_inf['Timestamp'].dt.dayofweek

    prediction_feature_generation  = pv_feature_engineering.feature_engineering(df=df_inf, 
                                            target_feature=target_feature, 
                                            horizon_features=horizon_features, 
                                            window_features=window_features,
                                            forecast_features=forecast_features, 
                                            forecast_horizon=forecast_horizon,
                                            time_granularity=time_granularity,
                                            inference=True)
    
    prediction_engineered_data_inf = prediction_feature_generation.df
    feature_columns = [col for col in prediction_engineered_data_inf.columns if 'time' not in col.lower()]

    prediction_engineered_data_inf_point = prediction_engineered_data_inf[prediction_engineered_data_inf['Timestamp'] == current_time]
    prediction_engineered_data_inf_point = prediction_engineered_data_inf_point.sort_values(by='future_Timestamp').reset_index(drop=True)
    prediction_engineered_data_inf_point = prediction_engineered_data_inf_point[feature_columns]

    return prediction_engineered_data_inf_point

def predict_from_input(input_data: dict, start_time: pd.Timestamp) -> pd.DataFrame:
    try:
        X = prepare_features_from_input(input_data)
        preds = pv_model.predict(X)
        return preds
    except Exception as e:
        logger.error(f'pv_prediction predict_from_input error: {e}')
        return pd.DataFrame([])

if __name__ == "__main__":
    # Ensure your JSON file is named 'input_data.json' in the working dir
    # start_time = pd.to_datetime("2025-07-02T00:00:00Z")
    # # ================= LOAD INPUT DATA =========================
    with open("input_data.json", "r") as f:
        input_data = json.load(f)
    predictions = predict_from_input(input_data, pd.to_datetime(input_data['current_time']))
    logger.info(f'predictions: {predictions}')
  