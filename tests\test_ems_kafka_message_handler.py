import os
import unittest
from unittest.mock import Mock, patch

# 设置必要的环境变量
os.environ['CONFIG_ENCRYPTION_KEY'] = "tTZ256Kp4RTO2wOuZEocOxd3UuX0cbEXEeC2bJXh_0E="
os.environ['PYTHONPATH'] = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

from application.db_operate.db_operate import DBOperate
from application.kafka_operate.ems_kafka_message_handler import EMSMessageHandler


class TestEMSMessageHandler(unittest.TestCase):
    def setUp(self):
        """测试前的准备工作"""
        self.handler = EMSMessageHandler()
        # Mock DBOperate 实例，并设置所有需要的方法
        self.handler.db_operate = Mock(spec=DBOperate)
        
        # 确保所有需要的方法都存在
        methods_to_mock = [
            'check_site_status',
            'update_site_basic_info',
            'insert_pv_and_meter_realtime_data_db',
            'insert_es_realtime_data_db',
            'batch_insert_charger_realtime_data_db',
            'batch_insert_site_demand_data_db',
            'batch_insert_site_sell_price_data_db',
            'insert_site_fixed_and_time_of_use_price_data_db'
        ]
        
        for method_name in methods_to_mock:
            if not hasattr(self.handler.db_operate, method_name):
                setattr(self.handler.db_operate, method_name, Mock())
        
        # 设置 check_site_status 的返回值为 (True, True, True) 表示操作成功、场站存在、AI开启
        self.handler.db_operate.check_site_status.return_value = (True, True, True)
        # 设置 update_site_basic_info 的返回值为 True 表示更新成功
        self.handler.db_operate.update_site_basic_info.return_value = True

    @patch('application.kafka_operate.ems_kafka_message_handler.data_change_detector')
    def test_process_message_with_valid_data(self, mock_detector):
        """测试处理有效的消息数据"""
        # 模拟数据变化检测器返回数据发生变化
        mock_detector.is_data_changed.return_value = (True, "mock_hash")
        
        # 构造测试数据
        test_message = {
            "station": {
                "site_no": "test_site_001",
                "es_total_energy": 100,
                "lat_and_lng": [30.123, 120.456],
                "site_grid_limit": 200,
                "grid_reverse": "yes",
                "grid_name": "测试电网",
                "pv_can_control": 1
            },
            "ts": 1648888888000,
            "pv_inverter": {
                "pv_power": 50,
                "pv_status": "discharge"
            },
            "meter_grid": {
                "meter_value": 100
            },
            "es_data": [{
                "es_sn": "es_001",
                "es_soc": 80,
                "es_power": 30,
                "rated_cap": 100.0,
                "real_cap": 80.0,
                "es_max_soc": 100,
                "es_min_soc": 20,
                "status": "charging"
            }],
            "charger_data": {
                "bus_power_limit": 300,
                "pile_groups": [{
                    "pile_group_name": "group_001",
                    "pile_group_power_limit": 150,
                    "piles": [{
                        "pile_sn": "pile_001",
                        "guns": [{
                            "connector": 1,
                            "status": "Charging",
                            "power": 20,
                            "ocpp_limit": 30
                        }]
                    }]
                }]
            },
            "demand_data": [{
                "start_time": "2024-03-20T00:00:00",
                "end_time": "2024-03-20T23:59:59",
                "price": 1.5,
                "unit": "CNY/kWh",
                "total_demand_target": 1000,
                "target_demand_warning_ratio": 0.8
            }],
            "sell_price": [{
                "start_time": "2024-03-20T00:00:00",
                "end_time": "2024-03-20T23:59:59",
                "price": 0.8,
                "unit": "CNY/kWh"
            }],
            "purchase_price_data": [
                {
                    "belongYear": 2025,
                    "belongMonth": 6,
                    "startTime": "00:00",
                    "endTime": "12:00",
                    "timeType": 1,
                    "price": 1.12698,
                    "unit": "dollar"
                },
                {
                    "belongYear": 2025,
                    "belongMonth": 6,
                    "startTime": "12:00",
                    "endTime": "24:00",
                    "timeType": 2,
                    "price": 1.12698,
                    "unit": "dollar"
                }
            ]
        }

        # 执行测试
        self.handler.process_message(test_message)

        # 验证数据库操作是否被正确调用
        self.handler.db_operate.check_site_status.assert_called_once()
        self.handler.db_operate.update_site_basic_info.assert_called_once()
        self.handler.db_operate.insert_pv_and_meter_realtime_data_db.assert_called_once()
        self.handler.db_operate.insert_es_realtime_data_db.assert_called_once()
        self.handler.db_operate.batch_insert_charger_realtime_data_db.assert_called_once()
        self.handler.db_operate.batch_insert_site_demand_data_db.assert_called_once()
        self.handler.db_operate.batch_insert_site_sell_price_data_db.assert_called_once()
        self.handler.db_operate.insert_site_fixed_and_time_of_use_price_data_db.assert_called_once()
        
        # 验证数据变化检测器被调用
        self.assertEqual(mock_detector.is_data_changed.call_count, 4)  # station_data, demand_data, sell_price, purchase_price_data
        self.assertEqual(mock_detector.update_data_hash.call_count, 4)

    def test_process_message_with_missing_site_no(self):
        """测试处理缺少场站编号的消息"""
        test_message = {
            "station": {},
            "ts": 1648888888000
        }

        # 执行测试
        self.handler.process_message(test_message)

        # 验证没有进行任何数据库操作
        self.handler.db_operate.check_site_status.assert_not_called()
        self.handler.db_operate.update_site_basic_info.assert_not_called()
        self.handler.db_operate.insert_pv_and_meter_realtime_data_db.assert_not_called()

    @patch('application.kafka_operate.ems_kafka_message_handler.data_change_detector')
    def test_process_message_with_invalid_data(self, mock_detector):
        """测试处理无效的消息数据"""
        # 模拟数据变化检测器返回数据发生变化
        mock_detector.is_data_changed.return_value = (True, "mock_hash")
        
        test_message = {
            "station": {
                "site_no": "test_site_001",
                "es_total_energy": 100,  # 添加一些有效的场站数据
                "lat_and_lng": [30.123, 120.456],
                "site_grid_limit": 200,
                "grid_reverse": "yes",
                "grid_name": "测试电网",
                "pv_can_control": 1
            },
            "ts": 1648888888000,
            "pv_inverter": {
                "pv_power": 50
                # 缺少 pv_status
            },
            "meter_grid": {
                "meter_value": 100
            }
        }

        # 执行测试
        self.handler.process_message(test_message)

        # 验证检查了场站状态和更新了场站信息，但没有插入光伏数据
        self.handler.db_operate.check_site_status.assert_called_once()
        self.handler.db_operate.update_site_basic_info.assert_called_once()
        self.handler.db_operate.insert_pv_and_meter_realtime_data_db.assert_not_called()

    def test_process_message_with_empty_data(self):
        """测试处理空消息"""
        test_message = {}

        # 执行测试
        self.handler.process_message(test_message)

        # 验证没有进行任何数据库操作
        self.handler.db_operate.check_site_status.assert_not_called()
        self.handler.db_operate.update_site_basic_info.assert_not_called()
        self.handler.db_operate.insert_pv_and_meter_realtime_data_db.assert_not_called()

    @patch('application.kafka_operate.ems_kafka_message_handler.data_change_detector')
    def test_process_message_with_partial_data(self, mock_detector):
        """测试处理部分数据缺失的消息"""
        # 模拟数据变化检测器返回数据发生变化
        mock_detector.is_data_changed.return_value = (True, "mock_hash")
        
        test_message = {
            "station": {
                "site_no": "test_site_001",
                "es_total_energy": 100,  # 这个字段在 FIELD_MAP 中，所以 site_data 不会为空
                "lat_and_lng": [30.123, 120.456],
                "site_grid_limit": 200,
                "grid_reverse": "yes",
                "grid_name": "测试电网",
                "pv_can_control": 1
            },
            "ts": 1648888888000
            # 缺少其他数据
        }

        # 执行测试
        self.handler.process_message(test_message)

        # 验证检查了场站状态和更新了场站信息，但没有处理其他数据
        self.handler.db_operate.check_site_status.assert_called_once()
        self.handler.db_operate.update_site_basic_info.assert_called_once()
        self.handler.db_operate.insert_pv_and_meter_realtime_data_db.assert_not_called()
        self.handler.db_operate.insert_es_realtime_data_db.assert_not_called()
        self.handler.db_operate.batch_insert_charger_realtime_data_db.assert_not_called()
        self.handler.db_operate.batch_insert_site_demand_data_db.assert_not_called()
        self.handler.db_operate.batch_insert_site_sell_price_data_db.assert_not_called()
        self.handler.db_operate.insert_site_fixed_and_time_of_use_price_data_db.assert_not_called()

    @patch('application.kafka_operate.ems_kafka_message_handler.data_change_detector')
    def test_process_message_with_empty_purchase_price_data(self, mock_detector):
        """测试处理空的购买电价数据"""
        # 模拟数据变化检测器返回数据发生变化
        mock_detector.is_data_changed.return_value = (True, "mock_hash")
        
        test_message = {
            "station": {
                "site_no": "test_site_001",
                "es_total_energy": 100  # 添加一个字段确保site_data不为空
            },
            "purchase_price_data": []
        }

        # 执行测试
        self.handler.process_message(test_message)

        # 验证检查了场站状态并更新了站点信息，但没有处理购买电价数据（因为数据为空）
        self.handler.db_operate.check_site_status.assert_called_once()
        self.handler.db_operate.update_site_basic_info.assert_called_once()  # 有有效站点数据会更新
        self.handler.db_operate.insert_site_fixed_and_time_of_use_price_data_db.assert_not_called()

    @patch('application.kafka_operate.ems_kafka_message_handler.data_change_detector')
    def test_process_message_with_different_time_types(self, mock_detector):
        """测试处理不同时段类型的购买电价数据"""
        # 模拟数据变化检测器返回数据发生变化
        mock_detector.is_data_changed.return_value = (True, "mock_hash")
        
        test_message = {
            "station": {
                "site_no": "test_site_001",
                "es_total_energy": 100  # 添加一个字段确保site_data不为空
            },
            "purchase_price_data": [
                {
                    "belongYear": 2025,
                    "belongMonth": 6,
                    "startTime": "00:00",
                    "endTime": "08:00",
                    "timeType": 4,  # 谷时段
                    "price": 0.8,
                    "unit": "dollar"
                },
                {
                    "belongYear": 2025,
                    "belongMonth": 6,
                    "startTime": "08:00",
                    "endTime": "12:00",
                    "timeType": 2,  # 峰时段
                    "price": 1.5,
                    "unit": "dollar"
                },
                {
                    "belongYear": 2025,
                    "belongMonth": 6,
                    "startTime": "12:00",
                    "endTime": "18:00",
                    "timeType": 3,  # 平时段
                    "price": 1.2,
                    "unit": "dollar"
                },
                {
                    "belongYear": 2025,
                    "belongMonth": 6,
                    "startTime": "18:00",
                    "endTime": "24:00",
                    "timeType": 1,  # 尖时段
                    "price": 1.8,
                    "unit": "dollar"
                }
            ]
        }

        # 执行测试
        self.handler.process_message(test_message)

        # 验证调用了购买电价数据的处理方法
        self.handler.db_operate.check_site_status.assert_called_once()
        self.handler.db_operate.update_site_basic_info.assert_called_once()
        self.handler.db_operate.insert_site_fixed_and_time_of_use_price_data_db.assert_called_once()

        # 验证传递的数据参数
        call_args = self.handler.db_operate.insert_site_fixed_and_time_of_use_price_data_db.call_args
        self.assertEqual(call_args[0][0], "test_site_001")  # site_no
        self.assertEqual(len(call_args[0][1]), 4)  # 应该有4条数据

    @patch('application.kafka_operate.ems_kafka_message_handler.data_change_detector')
    def test_process_message_with_new_charger_data_structure(self, mock_detector):
        """测试处理新的charger_data结构"""
        # 模拟数据变化检测器返回数据发生变化
        mock_detector.is_data_changed.return_value = (True, "mock_hash")
        
        test_message = {
            "station": {
                "site_no": "test_site_001",
                "es_total_energy": 100
            },
            "ts": 1648888888000,
            "charger_data": {
                "bus_power_limit": 500,
                "pile_groups": [
                    {
                        "pile_group_name": "Group A",
                        "pile_group_power_limit": 200,
                        "piles": [
                            {
                                "pile_sn": "pile_001",
                                "guns": [
                                    {
                                        "connector": 1,
                                        "status": "Charging",
                                        "power": 50,
                                        "ocpp_limit": 60
                                    },
                                    {
                                        "connector": 2,
                                        "status": "Available",
                                        "power": 0,
                                        "ocpp_limit": 60
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        "pile_group_name": "Group B",
                        "pile_group_power_limit": 300,
                        "piles": [
                            {
                                "pile_sn": "pile_002",
                                "guns": [
                                    {
                                        "connector": 1,
                                        "status": "Preparing",
                                        "power": 30,
                                        "ocpp_limit": 80
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
        }

        # 执行测试
        self.handler.process_message(test_message)

        # 验证数据库操作被调用的次数
        self.handler.db_operate.check_site_status.assert_called_once()
        self.handler.db_operate.update_site_basic_info.assert_called_once()
        # 批量插入应该被调用一次，并且插入3条记录（2个桩组，3个枪，所有枪都有有效数据）
        self.handler.db_operate.batch_insert_charger_realtime_data_db.assert_called_once()
        
        # 验证批量插入的参数，确保传入了3条记录
        call_args = self.handler.db_operate.batch_insert_charger_realtime_data_db.call_args
        self.assertEqual(call_args[0][0], "test_site_001")  # site_no
        self.assertEqual(len(call_args[0][1]), 3)  # 应该有3条充电桩数据

    @patch('application.kafka_operate.ems_kafka_message_handler.data_change_detector')
    def test_process_message_with_missing_charger_data_fields(self, mock_detector):
        """测试处理缺少必要字段的charger_data"""
        # 模拟数据变化检测器返回数据发生变化
        mock_detector.is_data_changed.return_value = (True, "mock_hash")
        
        test_message = {
            "station": {
                "site_no": "test_site_001",
                "es_total_energy": 100
            },
            "ts": 1648888888000,
            "charger_data": {
                "bus_power_limit": 500,
                "pile_groups": [
                    {
                        "pile_group_name": "Group A",
                        "pile_group_power_limit": 200,
                        "piles": [
                            {
                                "pile_sn": "pile_001",
                                "guns": [
                                    {
                                        "connector": 1,
                                        "status": "Charging",
                                        # 缺少 power 字段
                                        "ocpp_limit": 60
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
        }

        # 执行测试
        self.handler.process_message(test_message)

        # 验证场站信息被更新，但充电桩数据不会被插入（因为缺少必要字段）
        self.handler.db_operate.check_site_status.assert_called_once()
        self.handler.db_operate.update_site_basic_info.assert_called_once()
        self.handler.db_operate.batch_insert_charger_realtime_data_db.assert_not_called()

    @patch('application.kafka_operate.ems_kafka_message_handler.data_change_detector')
    def test_process_message_with_multiple_price_data(self, mock_detector):
        """测试处理多条电价数据的批量插入效果"""
        # 模拟数据变化检测器返回数据发生变化
        mock_detector.is_data_changed.return_value = (True, "mock_hash")
        
        test_message = {
            "station": {
                "site_no": "test_site_001",
                "es_total_energy": 100
            },
            "ts": 1648888888000,
            "demand_data": [
                {
                    "start_time": "2025-06-01T00:00:00",
                    "end_time": "2025-06-30T23:59:59",
                    "price": 8.8,
                    "unit": "euro",
                    "total_demand_target": 100.0,
                    "target_demand_warning_ratio": 90.0
                },
                {
                    "start_time": "2025-07-01T00:00:00",
                    "end_time": "2025-07-31T23:59:59",
                    "price": 0,
                    "unit": "euro",
                    "total_demand_target": 120.0,
                    "target_demand_warning_ratio": 90.0
                },
                {
                    "start_time": "2025-08-01T00:00:00",
                    "end_time": "2025-08-31T23:59:59",
                    "price": 0,
                    "unit": "euro",
                    "total_demand_target": 120.0,
                    "target_demand_warning_ratio": 90.0
                }
            ],
            "sell_price": [
                {
                    "start_time": "2025-06-01T00:00:00",
                    "end_time": "2025-06-30T23:59:59",
                    "price": 0.5,
                    "unit": "euro"
                },
                {
                    "start_time": "2025-07-01T00:00:00",
                    "end_time": "2025-07-31T23:59:59",
                    "price": 0.6,
                    "unit": "euro"
                }
            ]
        }

        # 执行测试
        self.handler.process_message(test_message)

        # 验证批量插入方法被调用
        self.handler.db_operate.check_site_status.assert_called_once()
        self.handler.db_operate.update_site_basic_info.assert_called_once()
        self.handler.db_operate.batch_insert_site_demand_data_db.assert_called_once()
        self.handler.db_operate.batch_insert_site_sell_price_data_db.assert_called_once()

        # 验证批量插入的参数，确保传入了正确数量的记录
        demand_call_args = self.handler.db_operate.batch_insert_site_demand_data_db.call_args
        sell_price_call_args = self.handler.db_operate.batch_insert_site_sell_price_data_db.call_args
        
        self.assertEqual(demand_call_args[0][0], "test_site_001")  # site_no
        self.assertEqual(len(demand_call_args[0][1]), 3)  # 应该有3条需求电价数据
        
        self.assertEqual(sell_price_call_args[0][0], "test_site_001")  # site_no
        self.assertEqual(len(sell_price_call_args[0][1]), 2)  # 应该有2条卖电电价数据

    def test_process_message_with_ai_not_active(self):
        """测试处理AI未开启场站的消息"""
        # 设置 check_site_status 返回AI未开启状态
        self.handler.db_operate.check_site_status.return_value = (True, True, False)
        
        test_message = {
            "station": {
                "site_no": "test_site_001",
                "es_total_energy": 100
            },
            "ts": 1648888888000
        }

        # 执行测试
        self.handler.process_message(test_message)

        # 验证只调用了场站状态检查，其他操作都没有执行
        self.handler.db_operate.check_site_status.assert_called_once()
        self.handler.db_operate.update_site_basic_info.assert_not_called()
        self.handler.db_operate.insert_pv_and_meter_realtime_data_db.assert_not_called()
        self.handler.db_operate.insert_es_realtime_data_db.assert_not_called()
        self.handler.db_operate.batch_insert_charger_realtime_data_db.assert_not_called()
        self.handler.db_operate.batch_insert_site_demand_data_db.assert_not_called()
        self.handler.db_operate.batch_insert_site_sell_price_data_db.assert_not_called()
        self.handler.db_operate.insert_site_fixed_and_time_of_use_price_data_db.assert_not_called()

    def test_process_message_with_site_not_exist(self):
        """测试处理场站不存在的消息"""
        # 设置 check_site_status 返回场站不存在状态
        self.handler.db_operate.check_site_status.return_value = (True, False, False)
        
        test_message = {
            "station": {
                "site_no": "test_site_001",
                "es_total_energy": 100
            },
            "ts": 1648888888000
        }

        # 执行测试
        self.handler.process_message(test_message)

        # 验证只调用了场站状态检查，其他操作都没有执行
        self.handler.db_operate.check_site_status.assert_called_once()
        self.handler.db_operate.update_site_basic_info.assert_not_called()
        self.handler.db_operate.insert_pv_and_meter_realtime_data_db.assert_not_called()
        self.handler.db_operate.insert_es_realtime_data_db.assert_not_called()
        self.handler.db_operate.batch_insert_charger_realtime_data_db.assert_not_called()
        self.handler.db_operate.batch_insert_site_demand_data_db.assert_not_called()
        self.handler.db_operate.batch_insert_site_sell_price_data_db.assert_not_called()
        self.handler.db_operate.insert_site_fixed_and_time_of_use_price_data_db.assert_not_called()

    def test_process_message_with_check_site_status_failure(self):
        """测试场站状态检查失败的情况"""
        # 设置 check_site_status 返回操作失败状态
        self.handler.db_operate.check_site_status.return_value = (False, True, True)
        
        test_message = {
            "station": {
                "site_no": "test_site_001",
                "es_total_energy": 100
            },
            "ts": 1648888888000
        }

        # 执行测试
        self.handler.process_message(test_message)

        # 验证只调用了场站状态检查，其他操作都没有执行
        self.handler.db_operate.check_site_status.assert_called_once()
        self.handler.db_operate.update_site_basic_info.assert_not_called()
        self.handler.db_operate.insert_pv_and_meter_realtime_data_db.assert_not_called()
        self.handler.db_operate.insert_es_realtime_data_db.assert_not_called()
        self.handler.db_operate.batch_insert_charger_realtime_data_db.assert_not_called()
        self.handler.db_operate.batch_insert_site_demand_data_db.assert_not_called()
        self.handler.db_operate.batch_insert_site_sell_price_data_db.assert_not_called()
        self.handler.db_operate.insert_site_fixed_and_time_of_use_price_data_db.assert_not_called()

    def test_process_message_with_empty_station_data(self):
        """测试场站基本信息为空的情况"""
        test_message = {
            "station": {
                "site_no": "test_site_001"
                # 没有其他场站基本信息，但现在会跳过处理而不是直接返回
            },
            "ts": 1648888888000,
            "pv_inverter": {
                "pv_power": 50,
                "pv_status": "discharge"
            },
            "meter_grid": {
                "meter_value": 100
            }
        }

        # 执行测试
        self.handler.process_message(test_message)

        # 验证调用了场站状态检查，但由于站点数据为空，跳过了站点更新，继续处理其他数据
        self.handler.db_operate.check_site_status.assert_called_once()
        self.handler.db_operate.update_site_basic_info.assert_not_called()  # 空站点数据时不更新
        self.handler.db_operate.insert_pv_and_meter_realtime_data_db.assert_called_once()  # 其他数据正常处理

    @patch('application.kafka_operate.ems_kafka_message_handler.data_change_detector')
    def test_process_message_with_no_data_change(self, mock_detector):
        """测试数据没有变化时的处理逻辑"""
        # 模拟数据变化检测器返回数据没有变化
        mock_detector.is_data_changed.return_value = (False, "mock_hash")
        
        test_message = {
            "station": {
                "site_no": "test_site_001",
                "es_total_energy": 100,
                "lat_and_lng": [30.123, 120.456]
            },
            "demand_data": [{
                "start_time": "2024-03-20T00:00:00",
                "end_time": "2024-03-20T23:59:59",
                "price": 1.5,
                "unit": "CNY/kWh",
                "total_demand_target": 1000,
                "target_demand_warning_ratio": 0.8
            }]
        }

        # 执行测试
        self.handler.process_message(test_message)

        # 验证检查了场站状态，但由于数据没有变化，没有更新数据库
        self.handler.db_operate.check_site_status.assert_called_once()
        self.handler.db_operate.update_site_basic_info.assert_not_called()
        self.handler.db_operate.batch_insert_site_demand_data_db.assert_not_called()
        
        # 验证数据变化检测器被调用但没有更新哈希
        mock_detector.is_data_changed.assert_called()
        mock_detector.update_data_hash.assert_not_called()


if __name__ == '__main__':
    unittest.main()
