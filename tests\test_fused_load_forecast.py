import unittest
import random
import json
import os
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock
import pytz

# 导入matplotlib相关模块
try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    from matplotlib import rcParams
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

from application.algorithm_schedule.fused_load_forecast import (
    fused_load_forecast,
    _get_short_term_prediction,
    _get_long_term_prediction,
    _parse_time_list_with_start_time,
    _convert_to_15min_intervals
)
from application.db_operate.db_operate import DBOperate
from application.db_operate.models import (
    SiteDemandPredictionDB, 
    SiteLongTermLoadPredictionDB, 
    HybridLoadPredictionDB
)


class TestFusedLoadForecast(unittest.TestCase):
    """融合负载预测算法测试类"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.test_site_no = "TEST_SITE_FUSION"
        
        # 设置matplotlib中文字体（仅在有图形环境时）
        if MATPLOTLIB_AVAILABLE:
            try:
                rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
                rcParams['axes.unicode_minus'] = False
            except:
                pass
    
    def tearDown(self):
        """测试后的清理工作"""
        try:
            # 清理测试数据
            db_operate = DBOperate()
            with db_operate.get_db() as db:
                for table in [SiteDemandPredictionDB, SiteLongTermLoadPredictionDB, HybridLoadPredictionDB]:
                    db.query(table).filter(table.site_no.like(f"{self.test_site_no}%")).delete(synchronize_session=False)
                db.commit()
        except:
            pass
    
    def test_time_list_parsing_with_second_offsets(self):
        """测试秒偏移量格式的时间列表解析"""
        start_time = datetime.now(pytz.UTC)
        time_list_data = {
            "start_time": start_time.isoformat(),
            "second_offsets": [0, 60, 120, 180]
        }
        time_list_json = json.dumps(time_list_data)
        
        result = _parse_time_list_with_start_time(time_list_json, start_time)
        
        self.assertEqual(len(result), 4)
        self.assertEqual(result[0], start_time)
        self.assertEqual(result[1], start_time + timedelta(seconds=60))
    
    @patch('application.algorithm_schedule.fused_load_forecast.DBOperate')
    def test_fused_load_forecast_basic(self, mock_db_operate):
        """测试基本的融合负载预测功能"""
        # 模拟数据库操作
        mock_db = MagicMock()
        mock_db_operate.return_value = mock_db
        
        # 模拟返回的预测数据
        start_time = datetime.now(pytz.UTC)
        short_term_data = [(start_time + timedelta(minutes=i*15), 200) for i in range(8)]
        long_term_data = [(start_time + timedelta(minutes=i*15), 300) for i in range(96)]
        
        with patch('application.algorithm_schedule.fused_load_forecast._get_short_term_prediction',
                   return_value=short_term_data):
            with patch('application.algorithm_schedule.fused_load_forecast._get_long_term_prediction',
                       return_value=long_term_data):
                with patch('application.algorithm_schedule.fused_load_forecast._save_fused_result',
                           return_value=True):
                    
                    result = fused_load_forecast(self.test_site_no, {})
                    
                    self.assertIsNotNone(result)
                    self.assertEqual(len(result) if result else 0, 96)
    
    def test_misaligned_time_with_visualization(self):
        """测试时间不对齐的情况：长期预测从0:00开始，短期预测从8:30开始"""
        try:
            # 设置测试时间：今天凌晨0:00
            today_midnight = datetime.now(pytz.UTC).replace(hour=0, minute=0, second=0, microsecond=0)
            
            # 长期预测：从凌晨0:00开始，持续24小时，96个点（每15分钟一个）
            long_term_start = today_midnight
            long_data = {
                'site_no': f"{self.test_site_no}_MISALIGNED",
                'curve_start_time': long_term_start,
                'curve_end_time': long_term_start + timedelta(hours=24),
                'time_list': json.dumps({
                    "start_time": long_term_start.isoformat(),
                    "second_offsets": [900*i for i in range(96)]  # 每15分钟：900秒
                }),
                'power_list': json.dumps([random.randint(100, 350) for _ in range(96)])
            }
            
            # 短期预测：从上午8:30开始，持续3.5小时，每分钟一个点
            short_term_start = today_midnight.replace(hour=8, minute=30)
            duration_seconds = 3.5 * 3600  # 3.5小时转为秒
            short_data = {
                'site_no': f"{self.test_site_no}_MISALIGNED",
                'curve_start_time': short_term_start,
                'curve_end_time': short_term_start + timedelta(seconds=duration_seconds),
                'time_list': json.dumps({
                    "start_time": short_term_start.isoformat(),
                    "second_offsets": list(range(0, int(duration_seconds), 60))  # 每60秒（1分钟）
                }),
                'power_list': json.dumps([random.randint(200, 450) for _ in range(0, int(duration_seconds), 60)])
            }
            
            print(f"📋 时间不对齐测试场景:")
            print(f"   长期预测: {long_term_start.strftime('%Y-%m-%d %H:%M:%S')} 开始，24小时，96个15分钟点")
            print(f"   短期预测: {short_term_start.strftime('%Y-%m-%d %H:%M:%S')} 开始，3.5小时，{int(duration_seconds//60)}个1分钟点")
            print(f"   时间偏移: 短期预测比长期预测晚了 8.5 小时开始")
            
            # 写入数据库
            db_operate = DBOperate()
            with db_operate.get_db() as db:
                # 清理旧数据
                site_pattern = f"{self.test_site_no}_MISALIGNED"
                for table in [SiteDemandPredictionDB, SiteLongTermLoadPredictionDB, HybridLoadPredictionDB]:
                    db.query(table).filter(table.site_no == site_pattern).delete()
                
                # 插入测试数据
                db.add(SiteDemandPredictionDB(**short_data))
                db.add(SiteLongTermLoadPredictionDB(**long_data)) 
                db.commit()
            
            # 获取预测数据
            short_term_data = _get_short_term_prediction(db_operate, f"{self.test_site_no}_MISALIGNED")
            long_term_data = _get_long_term_prediction(db_operate, f"{self.test_site_no}_MISALIGNED")
            
            # 执行融合算法
            result = fused_load_forecast(f"{self.test_site_no}_MISALIGNED", {})
            
            # 验证结果
            self.assertIsNotNone(result)
            self.assertIsNotNone(short_term_data)
            self.assertIsNotNone(long_term_data)
            
            print(f"✓ 时间不对齐融合测试成功:")
            print(f"   短期预测数据点: {len(short_term_data) if short_term_data else 0}")
            print(f"   长期预测数据点: {len(long_term_data) if long_term_data else 0}")
            print(f"   融合结果数据点: {len(result) if result else 0}")
            
            # 生成可视化图表
            if MATPLOTLIB_AVAILABLE and result and short_term_data and long_term_data:
                self._generate_misaligned_visualization(short_term_data, long_term_data, result, 
                                                      long_term_start, short_term_start)
            
        except Exception as e:
            self.fail(f"时间不对齐集成测试失败: {e}")

    def test_simplified_time_alignment_rule(self):
        """测试简化的时间归属规则：< 7.5分钟归前面，>= 7.5分钟归后面"""
        try:
            # 设置基准时间：今天上午8:00
            base_time = datetime.now(pytz.UTC).replace(hour=8, minute=0, second=0, microsecond=0)

            # 创建测试数据，包含不同分钟数的时间点来验证归属规则
            test_cases = [
                # (偏移分钟, 偏移秒, 期望归属的15分钟间隔)
                (3, 0, "08:00"),    # 8:03 -> 3 < 7.5, 归到8:00-8:15
                (6, 30, "08:00"),   # 8:06:30 -> 6.5 < 7.5, 归到8:00-8:15
                (7, 0, "08:00"),    # 8:07 -> 7 < 7.5, 归到8:00-8:15
                (11, 0, "08:15"),   # 8:11 -> 11 >= 7.5, 归到8:15-8:30
                (14, 0, "08:15"),   # 8:14 -> 14 >= 7.5, 归到8:15-8:30
                (18, 0, "08:15"),   # 8:18 -> 18%15=3 < 7.5, 归到8:15-8:30
                (22, 0, "08:15"),   # 8:22 -> 22%15=7 < 7.5, 归到8:15-8:30
                (28, 0, "08:30"),   # 8:28 -> 28%15=13 >= 7.5, 归到8:30-8:45
                (33, 0, "08:30"),   # 8:33 -> 33%15=3 < 7.5, 归到8:30-8:45
                (37, 0, "08:30"),   # 8:37 -> 37%15=7 < 7.5, 归到8:30-8:45
                (52, 0, "08:45"),   # 8:52 -> 52%15=7 < 7.5, 归到8:45-9:00
                (58, 0, "09:00"),   # 8:58 -> 58%15=13 >= 7.5, 归到9:00-9:15（跨小时）
            ]

            # 构造时间字符串和功率数据
            time_list = []
            power_list = []
            expected_intervals = {}

            for i, (minute_offset, second_offset, expected_interval) in enumerate(test_cases):
                test_time = base_time + timedelta(minutes=minute_offset, seconds=second_offset)
                time_list.append(test_time.isoformat())
                power_list.append(100 + i * 10)  # 递增的功率值便于验证
                expected_intervals[expected_interval] = expected_intervals.get(expected_interval, []) + [100 + i * 10]

            print(f"📋 简化时间归属规则测试:")
            print(f"   基准时间: {base_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"   测试用例: {len(test_cases)}个时间点")

            # 调用转换函数
            result = _convert_to_15min_intervals(time_list, power_list)

            # 验证结果
            self.assertIsNotNone(result)
            print(f"   转换结果: {len(result)}个15分钟间隔")

            # 验证每个期望的间隔
            result_dict = {dt.strftime('%H:%M'): power for dt, power in result}

            for expected_interval, expected_powers in expected_intervals.items():
                self.assertIn(expected_interval, result_dict,
                            f"期望间隔 {expected_interval} 应该存在于结果中")

                expected_avg = round(sum(expected_powers) / len(expected_powers))
                actual_power = result_dict[expected_interval]
                self.assertEqual(actual_power, expected_avg,
                               f"间隔 {expected_interval} 的平均功率应该是 {expected_avg}，实际是 {actual_power}")

                print(f"   ✓ 间隔 {expected_interval}: {len(expected_powers)}个点, 平均功率={actual_power}kW")

            # 详细输出验证信息
            print(f"   规则验证:")
            for i, (minute_offset, second_offset, expected_interval) in enumerate(test_cases):
                test_time = base_time + timedelta(minutes=minute_offset, seconds=second_offset)
                minute_in_quarter = (minute_offset + second_offset / 60.0) % 15
                rule_desc = f"< 7.5 -> 前" if minute_in_quarter < 7.5 else f">= 7.5 -> 后"
                print(f"     {test_time.strftime('%H:%M:%S')} (quarter内:{minute_in_quarter:.1f}分, {rule_desc}) -> {expected_interval}")

            print(f"✓ 简化时间归属规则测试通过!")

        except Exception as e:
            self.fail(f"简化时间归属规则测试失败: {e}")

    def test_misaligned_time_with_simplified_rule(self):
        """测试使用简化规则的时间不对齐场景"""
        try:
            # 设置测试时间：今天凌晨0:00
            today_midnight = datetime.now(pytz.UTC).replace(hour=0, minute=0, second=0, microsecond=0)
            
            # 长期预测：从凌晨0:00开始，标准的15分钟间隔
            long_term_start = today_midnight
            long_data = {
                'site_no': f"{self.test_site_no}_SIMPLIFIED",
                'curve_start_time': long_term_start,
                'curve_end_time': long_term_start + timedelta(hours=24),
                'time_list': json.dumps({
                    "start_time": long_term_start.isoformat(),
                    "second_offsets": [900*i for i in range(96)]  # 标准15分钟间隔
                }),
                'power_list': json.dumps([random.randint(100, 350) for _ in range(96)])
            }
            
            # 短期预测：从上午8:06开始，每2分钟一个点，持续3小时
            # 这样会产生各种不对齐的时间点，用来测试简化规则
            short_term_start = today_midnight.replace(hour=8, minute=6)  # 8:06开始
            duration_minutes = 3 * 60  # 3小时
            interval_minutes = 2  # 每2分钟一个点
            
            # 生成不规则的时间偏移，模拟真实场景
            time_offsets = []
            for i in range(0, duration_minutes, interval_minutes):
                # 添加一些随机秒数偏移，模拟实际数据的不规整性
                random_seconds = random.randint(0, 30)
                time_offsets.append(i * 60 + random_seconds)
            
            short_data = {
                'site_no': f"{self.test_site_no}_SIMPLIFIED",
                'curve_start_time': short_term_start,
                'curve_end_time': short_term_start + timedelta(seconds=max(time_offsets)),
                'time_list': json.dumps({
                    "start_time": short_term_start.isoformat(),
                    "second_offsets": time_offsets
                }),
                'power_list': json.dumps([random.randint(200, 450) for _ in range(len(time_offsets))])
            }
            
            print(f"📋 简化规则时间不对齐测试场景:")
            print(f"   长期预测: {long_term_start.strftime('%Y-%m-%d %H:%M:%S')} 开始，96个标准15分钟点")
            print(f"   短期预测: {short_term_start.strftime('%Y-%m-%d %H:%M:%S')} 开始，{len(time_offsets)}个不规则时间点")
            print(f"   预期效果: 短期预测将按简化规则归属到长期预测的15分钟网格")
            
            # 写入数据库
            db_operate = DBOperate()
            with db_operate.get_db() as db:
                # 清理旧数据
                site_pattern = f"{self.test_site_no}_SIMPLIFIED"
                for table in [SiteDemandPredictionDB, SiteLongTermLoadPredictionDB, HybridLoadPredictionDB]:
                    db.query(table).filter(table.site_no == site_pattern).delete()
                
                # 插入测试数据
                db.add(SiteDemandPredictionDB(**short_data))
                db.add(SiteLongTermLoadPredictionDB(**long_data)) 
                db.commit()
            
            # 执行融合算法（这会使用新的简化规则）
            result = fused_load_forecast(f"{self.test_site_no}_SIMPLIFIED", {})
            
            # 验证结果
            self.assertIsNotNone(result)
            
            print(f"✓ 简化规则融合测试成功:")
            print(f"   融合结果数据点: {len(result) if result else 0}")
            
            # 分析时间对齐效果
            if result:
                result_times = [dt.strftime('%H:%M') for dt, _ in result]
                aligned_count = sum(1 for time_str in result_times if time_str.endswith((':00', ':15', ':30', ':45')))
                print(f"   时间对齐验证: {aligned_count}/{len(result)}个点在标准15分钟网格上")
                
                # 显示前几个融合结果
                print(f"   前10个融合结果:")
                for i, (dt, power) in enumerate(result[:10]):
                    print(f"     {dt.strftime('%H:%M')} -> {power}kW")
            
        except Exception as e:
            self.fail(f"简化规则时间不对齐测试失败: {e}")
    
    def _generate_misaligned_visualization(self, short_term_data, long_term_data, result, long_start, short_start):
        """生成时间不对齐情况的可视化图表"""
        if not MATPLOTLIB_AVAILABLE:
            return
            
        try:
            print("✓ 生成时间不对齐对比图表...")
            
            fig, ax = plt.subplots(figsize=(18, 10))
            
            # 提取时间和功率数据
            short_times = [dt for dt, _ in short_term_data]
            short_powers = [power for _, power in short_term_data]
            
            long_times = [dt for dt, _ in long_term_data]
            long_powers = [power for _, power in long_term_data]
            
            fused_times = [dt for dt, _ in result]
            fused_powers = [power for _, power in result]
            
            # 转换datetime为matplotlib可处理的数值
            short_dates = mdates.date2num(short_times)
            long_dates = mdates.date2num(long_times)  
            fused_dates = mdates.date2num(fused_times)
            
            # 绘制三条曲线
            ax.plot_date(long_dates, long_powers, 'g-', linewidth=2, 
                        label=f'长期预测 (从{long_start.strftime("%H:%M")}开始, 每15分钟)', 
                        marker='s', markersize=3, alpha=0.7)
            ax.plot_date(short_dates, short_powers, 'b-', linewidth=2, 
                        label=f'短期预测 (从{short_start.strftime("%H:%M")}开始, 15分钟平均)', 
                        marker='o', markersize=4)
            ax.plot_date(fused_dates, fused_powers, 'r-', linewidth=3, 
                        label='融合结果 (时间对齐后取最大值)', marker='^', markersize=5, linestyle='--')
            
            # 添加垂直线标记重要时间点
            long_start_num = float(mdates.date2num(long_start))
            short_start_num = float(mdates.date2num(short_start))
            ax.axvline(x=long_start_num, color='green', linestyle=':', alpha=0.5, 
                      label=f'长期预测开始 ({long_start.strftime("%H:%M")})')
            ax.axvline(x=short_start_num, color='blue', linestyle=':', alpha=0.5,
                      label=f'短期预测开始 ({short_start.strftime("%H:%M")})')
            
            # 设置图表格式
            ax.set_xlabel('时间', fontsize=12)
            ax.set_ylabel('功率 (kW)', fontsize=12)
            ax.set_title('时间不对齐的负载预测融合算法测试结果', fontsize=14, fontweight='bold')
            ax.legend(fontsize=10, loc='upper right')
            ax.grid(True, alpha=0.3)
            
            # 格式化时间轴
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
            ax.xaxis.set_major_locator(mdates.HourLocator(interval=2))
            plt.xticks(rotation=45)
            
            # 计算重叠时间范围的统计
            time_offset_hours = (short_start - long_start).total_seconds() / 3600
            
            # 添加详细统计信息
            stats_text = f'''时间不对齐测试统计:
长期预测: {len(long_term_data)}个点, 平均{sum(long_powers)/len(long_powers):.1f}kW
短期预测: {len(short_term_data)}个点, 平均{sum(short_powers)/len(short_powers):.1f}kW  
融合结果: {len(result)}个点, 平均{sum(fused_powers)/len(fused_powers):.1f}kW
时间偏移: {time_offset_hours:.1f}小时'''
            
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=9,
                   verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
            
            plt.tight_layout()
            
            # 保存图表
            output_path = os.path.join(os.path.dirname(__file__), 'fusion_misaligned_test_result.png')
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            print(f"✓ 时间不对齐测试图表已保存为 {output_path}")
            
            plt.close()
            
        except Exception as e:
            print(f"时间不对齐可视化生成失败: {e}")
    



if __name__ == '__main__':
    unittest.main(verbosity=2) 