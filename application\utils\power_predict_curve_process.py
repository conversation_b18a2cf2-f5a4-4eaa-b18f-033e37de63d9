from datetime import datetime
import pytz
from sqlalchemy import select
from typing import List
import math

from application.db_operate.db_operate import DBOperate
from application.db_operate.models import SiteDB
from application.utils.logger import setup_logger
from application.utils.timezone_mapping import get_timezone_with_fallback
from application.api_manage.energy_cloud_api import EnergyCloudAPI

logger = setup_logger("power_predict_curve_process", direction="utils")


class PowerPredictCurveProcessor:
    """
    功率预测曲线处理器
    
    提供功率预测曲线的保存、发送等功能的静态方法集合
    """
    
    @staticmethod
    def execute_db_operation(operation_name: str, default_result, operation_func):
        """
        通用数据库操作辅助函数，减少重复的数据库连接和错误处理代码
        
        Args:
            operation_name: 操作名称，用于日志记录
            default_result: 操作失败时的默认返回值
            operation_func: 接收数据库会话作为参数的操作函数
        
        Returns:
            操作结果或默认值
        """
        try:
            db_operate = DBOperate()
            with db_operate.get_db() as db:
                return operation_func(db)
        except Exception as e:
            logger.error(f"{operation_name}失败: {e}")
            return default_result

    @staticmethod
    def get_site_local_time(site_no: str) -> datetime:
        """
        获取充电桩场站所在地区的当前时间
        
        Args:
            site_no (str): 场站编号
            
        Returns:
            datetime: 场站所在地区的当前时间（带时区信息）
        """
        def get_site_timezone(db):
            """内部函数：从数据库获取场站时区信息"""
            try:
                # 查询站点信息
                stmt = select(SiteDB).where(SiteDB.site_no == site_no)
                site_info = db.scalar(stmt)
                
                if site_info and site_info.region:
                    # 获取时区字符串
                    timezone_str = get_timezone_with_fallback(site_info.region)
                    logger.info(f"场站 {site_no} 位于 {site_info.region}，时区: {timezone_str}")
                    return timezone_str
                else:
                    logger.warning(f"场站 {site_no} 的地区信息未找到，使用默认时区 UTC")
                    return "UTC"
                    
            except Exception as e:
                logger.error(f"查询场站 {site_no} 时区信息失败: {e}")
                return "UTC"
        
        try:
            # 使用数据库操作辅助函数获取时区信息
            timezone_str = PowerPredictCurveProcessor.execute_db_operation(
                operation_name=f"获取场站{site_no}时区信息",
                default_result="UTC",
                operation_func=get_site_timezone
            )
            
            # 将时区字符串转换为 pytz timezone 对象
            try:
                timezone_obj = pytz.timezone(timezone_str)
            except pytz.exceptions.UnknownTimeZoneError:
                logger.warning(f"未知时区 {timezone_str}，使用 UTC 时区")
                timezone_obj = pytz.UTC
            
            # 获取当前 UTC 时间并转换为目标时区
            utc_now = datetime.now(pytz.UTC)
            local_time = utc_now.astimezone(timezone_obj)
            
            logger.debug(f"场站 {site_no} 当前时间: {local_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
            return local_time
            
        except Exception as e:
            logger.error(f"获取场站 {site_no} 本地时间失败: {e}")
            # 发生异常时返回 UTC 时间
            return datetime.now(pytz.UTC)
    
    @staticmethod
    def save_power_predict_data_to_db(
            site_no: str,
            es_power_curve: List[float] = None,
            pv_power_curve: List[float] = None,
            load_power_curve: List[float] = None,
    ) -> bool:
        """
        保存功率预测曲线数据到数据库（支持曲线截断功能）

        Args:
            site_no (str): 场站编号
            es_power_curve (List[float], optional): 储能功率预测曲线，96个点，每15分钟一个点，从当前时刻开始的未来24小时
            pv_power_curve (List[float], optional): 光伏功率预测曲线，96个点，每15分钟一个点，从当前时刻开始的未来24小时
            load_power_curve (List[float], optional): 负载功率预测曲线，96个点，每15分钟一个点，从当前时刻开始的未来24小时
            
        Returns:
            bool: 保存是否成功
            
        Note:
            - 至少需要提供一个功率预测曲线
            - 输入曲线数据是从当前时刻开始的未来24小时数据（96个点）
            - 自动截断：只保留从当前时刻到当天24:00的数据点，跨天部分自动抛弃
            - 截断后的数据点数 points_to_keep = (24 - 当前小时) * 4
            - 如果存在历史曲线数据，则将历史曲线数据前（96-points_to_keep）个点与截断后的曲线数据进行合并
            - 如果不存在历史曲线数据，则前（96-points_to_keep）个点为-1，与截断后的曲线数据进行合并
        """
        try:
            db_operate = DBOperate()

            # 参数验证
            if not site_no or not isinstance(site_no, str):
                logger.error("场站编号不能为空且必须为字符串")
                return False
            
            # 检查至少有一个曲线数据
            provided_curves = {
                "储能功率": es_power_curve,
                "光伏功率": pv_power_curve, 
                "负载功率": load_power_curve
            }
            
            valid_curves = {name: curve for name, curve in provided_curves.items() 
                           if curve is not None and len(curve) > 0}
            
            if not valid_curves:
                logger.error(f"场站 {site_no}: 至少需要提供一个有效的功率预测曲线")
                return False
            
            # 验证曲线数据长度
            expected_length = 96
            for curve_name, curve_data in valid_curves.items():
                if len(curve_data) != expected_length:
                    logger.warning(f"场站 {site_no}: {curve_name}曲线长度为 {len(curve_data)}，期望长度为 {expected_length}")
                    return False
            
            # 获取场站本地时间和日期
            logger.debug(f"获取场站 {site_no} 的本地时间")
            local_time = PowerPredictCurveProcessor.get_site_local_time(site_no)
            local_date = local_time.strftime("%Y-%m-%d")

            # 1. 截断曲线
            # 计算截断点数：从当前时刻到当天24:00的点数
            current_hour = local_time.hour
            current_minute = local_time.minute
            total_remaining_minutes = (24 - current_hour) * 60 - current_minute       # 当前时刻到24:00的总分钟数
            points_to_keep = math.ceil(total_remaining_minutes / 15)                  # 转换为15分钟间隔数（向上取整）
            points_to_keep = max(0, min(points_to_keep, 96))                          # 确保不超过原始曲线长度和不小于0
            
            logger.info(f"场站 {site_no}: 当前时间 {local_time.strftime('%H:%M')}，保留前 {points_to_keep} 个点（至当天24:00）")
            
            # 截断：处理每个有效曲线（从索引0开始截断，保留前 points_to_keep 个点，这些点代表从当前时刻到当天24:00的数据）
            truncated_curves = {}
            for curve_name, curve_data in valid_curves.items():
                truncated_data = curve_data[:points_to_keep]
                truncated_curves[curve_name] = truncated_data

            # 2. 合并历史曲线数据
            # 获取场站对应日期现有的历史曲线数据
            existing_data = db_operate.get_power_predict_curve_for_ems(site_no, local_date)
            
            # 计算需要从历史数据中提取的点数（从0:00到当前时刻）
            historical_points_needed = 96 - points_to_keep
            
            # 曲线名称映射：从函数参数名到数据库字段名
            curve_field_mapping = {
                "储能功率": "es_power_list",
                "光伏功率": "pv_power_list", 
                "负载功率": "load_power_list"
            }
            
            # 构建完整的0:00-24:00曲线数据
            final_curves = {}
            for curve_name in truncated_curves:
                historical_part = []
                if existing_data and curve_field_mapping[curve_name] in existing_data:
                    # 如果存在历史曲线数据，则取历史曲线前(96-points_to_keep)个点与截断后的曲线合并
                    historical_curve = existing_data[curve_field_mapping[curve_name]]
                    if historical_curve and isinstance(historical_curve, list) and len(historical_curve) > 0:
                        # 确保历史数据有足够的点数
                        if len(historical_curve) >= historical_points_needed:
                            historical_part = historical_curve[:historical_points_needed]
                        else:
                            # 如果历史数据不足，用null填充
                            historical_part = [None] * historical_points_needed
                    else:
                        # 历史数据无效，用null填充
                        historical_part = [None] * historical_points_needed
                else:
                    # 如果不存在历史曲线数据，则前(96-points_to_keep)个点设为null，与截断后的曲线数据合并
                    historical_part = [None] * historical_points_needed

                # 合并历史数据和新预测数据
                final_curves[curve_name] = historical_part + truncated_curves[curve_name]
                logger.debug(f"{curve_name}: 合并{len(historical_part)}个历史点 + {len(truncated_curves[curve_name])}个新预测点")
            
            # 检查是否有最终曲线数据
            if not final_curves:
                logger.error(f"场站 {site_no}: 没有有效的最终曲线数据可以保存")
                return False
            
            # 3. 保存数据
            # 准备最终的数据用于保存（确保即使某个曲线为空也能正常处理）
            final_es_curve = final_curves.get("储能功率")
            final_pv_curve = final_curves.get("光伏功率") 
            final_load_curve = final_curves.get("负载功率")
            
            # 记录实际要保存的曲线
            curves_to_save = []
            if final_es_curve is not None:
                curves_to_save.append("储能功率")
            if final_pv_curve is not None:
                curves_to_save.append("光伏功率")
            if final_load_curve is not None:
                curves_to_save.append("负载功率")
                
            curves_summary = " | ".join(curves_to_save)
            logger.info(f"场站 {site_no}: 保存功率预测数据 - 日期:{local_date} 类型:[{curves_summary}] 共96点")
            
            # 验证数据库保存方法的参数要求
            if not any([final_es_curve, final_pv_curve, final_load_curve]):
                logger.error(f"场站 {site_no}: 所有最终曲线数据都为空，无法保存")
                return False
            
            # 保存完整的功率预测曲线数据到数据库（0:00-24:00）
            success = db_operate.save_power_predict_curve_for_ems(
                site_no=site_no,
                es_power_list=final_es_curve,
                pv_power_list=final_pv_curve, 
                load_power_list=final_load_curve,
                local_date=local_date
            )
            
            if success:
                logger.info(f"场站 {site_no}: 功率预测数据保存成功 - 日期:{local_date}")
            else:
                logger.error(f"场站 {site_no}: 功率预测数据保存失败 - 日期:{local_date}")
                
            return success

        except Exception as e:
            logger.error(f"场站 {site_no}: 保存功率预测曲线数据时发生异常: {str(e)}", exc_info=True)
            return False
        
    @staticmethod
    def send_power_predict_curves_to_energy_cloud(
            site_no: str,
            local_date: str
    ) -> bool:
        """
        发送功率预测曲线数据到能源云
        
        Args:
            site_no: 场站编号
            local_date: 本地日期，格式: YYYY-MM-DD
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 1. 验证参数
            if not site_no or not isinstance(site_no, str):
                logger.error("场站编号不能为空且必须为字符串")
                return False
                
            if not local_date or not isinstance(local_date, str):
                logger.error(f"场站 {site_no}: 日期不能为空且必须为字符串")
                return False

            logger.info(f"场站 {site_no}: 开始发送功率预测曲线数据到能源云 - 日期:{local_date}")

            # 2. 查询数据库获取功率预测曲线数据
            db_operate = DBOperate()
            existing_data = db_operate.get_power_predict_curve_for_ems(site_no, local_date)
            
            if not existing_data:
                logger.warning(f"场站 {site_no}: 未找到日期 {local_date} 的功率预测曲线数据")
                return False

            # 3. 提取曲线数据
            es_power_curve = existing_data.get('es_power_list')
            pv_power_curve = existing_data.get('pv_power_list') 
            load_power_curve = existing_data.get('load_power_list')

            # 4. 验证至少有一个有效曲线
            valid_curves = []
            if es_power_curve and isinstance(es_power_curve, list) and len(es_power_curve) > 0:
                valid_curves.append(f"储能功率({len(es_power_curve)}点)")
            if pv_power_curve and isinstance(pv_power_curve, list) and len(pv_power_curve) > 0:
                valid_curves.append(f"光伏功率({len(pv_power_curve)}点)")
            if load_power_curve and isinstance(load_power_curve, list) and len(load_power_curve) > 0:
                valid_curves.append(f"负载功率({len(load_power_curve)}点)")

            if not valid_curves:
                logger.error(f"场站 {site_no}: 未找到有效的功率预测曲线数据 - 日期:{local_date}")
                return False

            curves_info = " | ".join(valid_curves)
            logger.info(f"场站 {site_no}: 准备发送功率预测曲线 - 日期:{local_date} - {curves_info}")

            # 5. 调用能源云API发送数据
            success = EnergyCloudAPI.send_power_predict_curves(
                site_no=site_no,
                es_power_curve=es_power_curve if es_power_curve and len(es_power_curve) > 0 else None,
                pv_power_curve=pv_power_curve if pv_power_curve and len(pv_power_curve) > 0 else None,
                load_power_curve=load_power_curve if load_power_curve and len(load_power_curve) > 0 else None,
                local_date=local_date
            )

            if success:
                logger.info(f"场站 {site_no}: 功率预测曲线数据发送成功 - 日期:{local_date}")
            else:
                logger.error(f"场站 {site_no}: 功率预测曲线数据发送失败 - 日期:{local_date}")

            return success

        except Exception as e:
            logger.error(f"场站 {site_no}: 发送功率预测曲线数据到能源云时发生异常: {str(e)}", exc_info=True)
            return False
