import matplotlib.pyplot as plt
import numpy as np
# from scipy.integrate import trapz
import os
from application.algorithm.power_predict.calculate_power_curve import calcPowerCurve
import argparse
from application.algorithm.power_predict.plotSoc import getPowerSocCurve
from datetime import datetime
import json
import re
import pandas as pd


def fit(sSOC, startup_power, descending_flag, plimit, power_table, coeff_a, coeff_b1, coeff_b2, coeff_c):
    with open(power_table, "r") as file:
        f = json.load(file)
    power_series = f['powertable']
    dict_power = {index: value for index, value in enumerate(power_series)}
    error = 0
    control_points, fit_curve = calcPowerCurve(sSOC, startup_power, descending_flag, plimit, coeff_a, coeff_b1,
                                               coeff_b2, coeff_c, dict_power)
    # fit_curve is a list,whose element is tuple(soc,power)

    return fit_curve


def detect_descending(tarcur_list):    #  判断曲线在1min时是否出现了下降趋势
    max_tarcur = max(tarcur_list)
    if tarcur_list[-1] - max_tarcur > 0:
        return False
    else:
        return True


def find_power_limit(start_voltage, rated_power, gun_limit):
    pmax_by_curr = start_voltage * gun_limit / 1000  # unit is kw
    plimit = min(pmax_by_curr, rated_power)

    return plimit


def convert_to_time_power(vehicle_label, soc_power):
    matches = re.findall(r'_([\d.]+)kWh', vehicle_label)
    numbers = [float(m) for m in matches]
    battery = np.mean(numbers)
    time_power = []
    time = 0
    for value in soc_power:
        time += battery * 0.001 * 3600 / value[1]
        time_power.append((time, value[1]))

    return time_power


def load_json_for_test(jsonpath):
    with open(jsonpath) as file:
        json_data = json.load(file)

        fmt = " %Y-%m-%d %H:%M:%S.%f"
        voltage_at_startup = 0
        startup_power = 0
        max_target_cur = 0
        descending_flag = False  # indicate it will be descending from the point at 60th second
        tarcur_list = []
        for i, item in enumerate(json_data):
            if isinstance(item, dict):
                for k, v in item.items():
                    # print(f"-{k}:{v}")
                    if k == "MaximumCurrent":
                        maxim_current = v
                    if k == "MaximumVoltage":
                        maxim_voltage = v
            elif isinstance(item, list):
                cnt = 0
                for list_item in item:
                    # pdb.set_trace()
                    parts = list_item.split(',')
                    time_str = parts[7]
                    if '.' not in time_str:
                        time_str += ".000"
                    cnt = cnt + 1
                    if cnt == 1:
                        continue  # it is the title
                    elif cnt == 2:
                        initial_time = datetime.strptime(time_str, fmt)
                        continue
                    target_cur = float(parts[0])
                    tarcur_list.append(target_cur)
                    actual_cur = float(parts[1])
                    out_vol = float(parts[3])
                    actual_power = actual_cur * out_vol
                    time = datetime.strptime(time_str, fmt)
                    deta = (time - initial_time).total_seconds()
                    if deta >= 59 and deta <= 61:
                        startup_power = actual_power
                        voltage_at_startup = out_vol

        if voltage_at_startup == 0:
            print(f"voltage_at_startup is invalid")
        return voltage_at_startup, startup_power/1000, tarcur_list


def predict(vehicle_label, sSOC, power_1min, voltage_1min, current_list_1min, rated_power, gun_limit):
    '''
    :param vehicle_label: 车型识别结果
    :param sSOC: 起始soc
    :param power_1min: 充电1min时刻（调用算法时刻）功率，单位kW
    :param voltage_1min: 充电1min时刻（调用算法时刻）输出电压，单位V
    :param current_list_1min: 从充电开始到1min内目标电流list，单位A
    :param rated_power: 桩额定功率，单位kW
    :param gunlimit: 枪限制电流，单位A
    :return:
    '''
    current_dir = os.path.dirname(os.path.abspath(__file__))
    model_dir = os.path.join(current_dir, "model")
    
    model_match_path = os.path.join(model_dir, "vehicle_model_match.json")
    with open(model_match_path) as file:
        match_dict = json.loads(file.read())

    if vehicle_label in match_dict:
        model_name = match_dict[vehicle_label]
        power_table_path = os.path.join(model_dir, f"{model_name}_power_table.json")
        model_path = os.path.join(model_dir, f"{model_name}.json")
    else:
        print("Do not support this vehicle")
        return None

    with open(model_path, "r") as f:
        json_para = json.load(f)
        coeff_a = json_para["coeff_a"]
        coeff_b1 = json_para["coeff_b1"]
        coeff_b2 = json_para["coeff_b2"]
        coeff_c = json_para["coeff_c"]
    descending_flag = detect_descending(current_list_1min)
    plimit = find_power_limit(voltage_1min, rated_power, gun_limit)
    fitted_curve = fit(sSOC, power_1min, descending_flag, plimit, power_table_path, coeff_a, coeff_b1, coeff_b2,
                           coeff_c)
    time_power = convert_to_time_power(vehicle_label, fitted_curve)
    return time_power


if __name__ == "__main__":
    vehicle_label = "Audi_Q4@e-tron_76.6kWh,Cupra_Born_77kWh,Skoda_Enyaq@80_77kWh,Volkswagen_ID.4_77kWh,Volkswagen_ID.5_77kWh,Volkswagen_ID.BUZZ_77kWh"
    sSOC = 46
    # power_1min = 100
    # voltage_1min = 360
    # current_list_1min = []
    voltage_1min, power_1min, current_list_1min = load_json_for_test(os.path.join("volkswagen_test", "Audi_Q4@e-tron_76.6kWh,Cupra_Born_77kWh,Skoda_Enyaq@80_77kWh,Volkswagen_ID.4_52kWh,Volkswagen_ID.4_77kWh,Volkswagen_ID.5_77kWh,Volkswagen_ID.BUZZ_77kWh_007dfa09c88a_443.json"))
    charger_rated_power = 120
    gunlimit = 200
    predict(vehicle_label, sSOC, power_1min, voltage_1min, current_list_1min, charger_rated_power, gunlimit)