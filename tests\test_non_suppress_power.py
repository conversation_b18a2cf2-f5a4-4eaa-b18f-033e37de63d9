#!/usr/bin/env python3
"""
测试非压制功率预测功能的单元测试

使用方法:
coverage run -m unittest tests.test_non_suppress_power
或者:
python -m unittest tests.test_non_suppress_power

说明:
这个测试将验证非压制功率预测算法的完整流程，包括：
1. 查询充电会话
2. 获取充电记录和车型信息
3. 进行功率预测（从当前时刻到充电结束）
4. 保存预测结果
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import json
import os
import sys
from datetime import datetime, timedelta, timezone

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入需要测试的模块
from application.algorithm_schedule.non_suppress_power import NonSuppressPowerPrediction, non_suppress_power
from application.db_operate.db_operate import DBOperate


class TestNonSuppressPowerPrediction(unittest.TestCase):
    """非压制功率预测功能的单元测试类"""

    def setUp(self):
        """测试前的设置"""
        self.test_site_no = "TEST_SITE_123"
        self.test_local_id = "TEST_LOCAL_ID_001"

    def test_predict_power_for_vehicle(self):
        """测试单车功率预测功能"""
        # 创建模拟数据
        mock_charging_record = Mock()
        mock_charging_record.soc = 50.0

        mock_ev_model = Mock()
        mock_ev_model.max_power = 100.0
        mock_ev_model.capacity = 80.0

        # 创建预测实例
        with patch('application.algorithm_schedule.non_suppress_power.DBOperate'):
            predictor = NonSuppressPowerPrediction(self.test_site_no)

            # 执行预测
            result = predictor.predict_power_for_vehicle(
                self.test_local_id,
                [mock_charging_record],
                mock_ev_model
            )

            # 验证结果
            self.assertIsNotNone(result)
            self.assertIn("curve_start_time", result)
            self.assertIn("curve_end_time", result)
            self.assertIn("time_list", result)
            self.assertIn("power_list", result)

            # 验证时间和功率列表长度一致
            self.assertEqual(len(result["time_list"]), len(result["power_list"]))

            # 验证功率值合理性
            for power in result["power_list"]:
                self.assertGreaterEqual(power, 0)
                self.assertLessEqual(power, mock_ev_model.max_power)

    def test_aggregate_site_demand_curve_from_cache(self):
        """测试场站需求曲线叠加功能"""
        # 创建模拟的车辆预测缓存数据
        from datetime import timezone
        current_time = datetime.now(timezone.utc)

        vehicle_predictions_cache = [
            {
                "local_id": "vehicle_1",
                "curve_start_time": current_time,
                "curve_end_time": current_time + timedelta(hours=2),
                "time_list": [0, 60, 120, 180],  # 0, 1, 2, 3分钟
                "power_list": [90.0, 85.0, 80.0, 75.0]
            },
            {
                "local_id": "vehicle_2",
                "curve_start_time": current_time,
                "curve_end_time": current_time + timedelta(hours=1.5),
                "time_list": [0, 60, 120],  # 0, 1, 2分钟
                "power_list": [50.0, 45.0, 40.0]
            }
        ]

        # 创建预测实例
        with patch('application.algorithm_schedule.non_suppress_power.DBOperate'):
            predictor = NonSuppressPowerPrediction(self.test_site_no)

            # 执行叠加
            result = predictor.aggregate_site_demand_curve_from_cache(vehicle_predictions_cache)

            # 验证结果
            self.assertIsNotNone(result)
            self.assertIn("curve_start_time", result)
            self.assertIn("curve_end_time", result)
            self.assertIn("time_list", result)
            self.assertIn("power_list", result)

            # 验证叠加逻辑正确性
            # 在0分钟时，应该是两车功率之和：90 + 50 = 140
            # 在60秒时，应该是：85 + 45 = 130
            # 在120秒时，应该是：80 + 40 = 120
            time_list = result["time_list"]
            power_list = result["power_list"]

            # 找到对应时间点的功率
            if 0 in time_list:
                idx = time_list.index(0)
                self.assertAlmostEqual(power_list[idx], 140.0, places=1)

            if 60 in time_list:
                idx = time_list.index(60)
                self.assertAlmostEqual(power_list[idx], 130.0, places=1)

    @patch('application.algorithm_schedule.non_suppress_power.DBOperate')
    def test_run_prediction_no_sessions(self, mock_db_operate_class):
        """测试没有充电会话的情况"""
        # 设置模拟数据库操作
        mock_db_operate = Mock()
        mock_db_operate.get_charging_sessions_by_site.return_value = []
        mock_db_operate_class.return_value = mock_db_operate

        # 创建预测实例并运行
        predictor = NonSuppressPowerPrediction(self.test_site_no)
        result = predictor.run_prediction()

        # 验证结果
        expected_result = {
            "site_no": self.test_site_no,
            "total_sessions": 0,
            "successful_predictions": 0,
            "failed_predictions": 0
        }
        self.assertEqual(result, expected_result)

    @patch('application.algorithm_schedule.non_suppress_power.DBOperate')
    def test_run_prediction_with_sessions(self, mock_db_operate_class):
        """测试有充电会话的情况"""
        # 创建模拟会话
        mock_session = Mock()
        mock_session.local_id = self.test_local_id

        # 创建模拟充电记录
        mock_charging_record = Mock()
        mock_charging_record.soc = 60.0

        # 创建模拟车型
        mock_ev_model = Mock()
        mock_ev_model.max_power = 120.0
        mock_ev_model.capacity = 75.0

        # 设置模拟数据库操作
        mock_db_operate = Mock()
        mock_db_operate.get_charging_sessions_by_site.return_value = [mock_session]
        mock_db_operate.get_recent_charging_records.return_value = [mock_charging_record]
        mock_db_operate.get_ev_model_by_local_id.return_value = mock_ev_model
        mock_db_operate.save_non_suppress_power_prediction.return_value = True
        mock_db_operate.save_site_demand_prediction.return_value = True
        mock_db_operate_class.return_value = mock_db_operate

        # 创建预测实例并运行
        predictor = NonSuppressPowerPrediction(self.test_site_no)
        result = predictor.run_prediction()

        # 验证结果
        self.assertEqual(result["site_no"], self.test_site_no)
        self.assertEqual(result["total_sessions"], 1)
        self.assertEqual(result["successful_predictions"], 1)
        self.assertEqual(result["failed_predictions"], 0)
        self.assertTrue(result["site_demand_aggregated"])

        # 验证调用了正确的方法
        mock_db_operate.get_charging_sessions_by_site.assert_called_once_with(self.test_site_no)
        mock_db_operate.get_recent_charging_records.assert_called_once()
        mock_db_operate.get_ev_model_by_local_id.assert_called_once()
        mock_db_operate.save_non_suppress_power_prediction.assert_called_once()
        mock_db_operate.save_site_demand_prediction.assert_called_once()

    @patch('application.algorithm_schedule.non_suppress_power.DBOperate')
    def test_non_suppress_power_function(self, mock_db_operate_class):
        """测试非压制功率预测入口函数"""
        # 设置模拟数据库操作
        mock_db_operate = Mock()
        mock_db_operate.get_charging_sessions_by_site.return_value = []
        mock_db_operate_class.return_value = mock_db_operate

        # 调用入口函数
        result = non_suppress_power(self.test_site_no, {})

        # 验证结果
        self.assertIsNotNone(result)
        self.assertEqual(result["site_no"], self.test_site_no)
        self.assertIn("total_sessions", result)
        self.assertIn("successful_predictions", result)
        self.assertIn("failed_predictions", result)

    def test_power_calculation_logic(self):
        """测试功率计算逻辑"""
        # 创建模拟数据 - 高SOC情况
        mock_charging_record_high_soc = Mock()
        mock_charging_record_high_soc.soc = 95.0  # 高SOC

        mock_ev_model = Mock()
        mock_ev_model.max_power = 100.0
        mock_ev_model.capacity = 80.0

        # 创建预测实例
        with patch('application.algorithm_schedule.non_suppress_power.DBOperate'):
            predictor = NonSuppressPowerPrediction(self.test_site_no)

            # 执行预测
            result = predictor.predict_power_for_vehicle(
                self.test_local_id,
                [mock_charging_record_high_soc],
                mock_ev_model
            )

            # 验证高SOC时功率较低
            self.assertIsNotNone(result)
            if result["power_list"]:
                # 95%以上SOC时，功率应该很低（10%最大功率）
                initial_power = result["power_list"][0]
                expected_low_power = mock_ev_model.max_power * 0.1
                self.assertAlmostEqual(initial_power, expected_low_power, delta=5.0)

    def test_error_handling(self):
        """测试错误处理"""
        # 测试空充电记录的情况
        with patch('application.algorithm_schedule.non_suppress_power.DBOperate'):
            predictor = NonSuppressPowerPrediction(self.test_site_no)

            # 传入空的充电记录列表
            result = predictor.predict_power_for_vehicle(
                self.test_local_id,
                [],  # 空列表
                Mock()
            )

            # 应该返回None
            self.assertIsNone(result)

    def test_multiple_vehicle_power_curves_visualization(self):
        """测试多车辆非压制功率曲线叠加并可视化（适配曲线拟合方案）"""
        try:
            import matplotlib.pyplot as plt
            import matplotlib
            matplotlib.use('Agg')  # 使用非交互式后端

            # 配置matplotlib支持中文显示
            try:
                plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'Arial']
                plt.rcParams['axes.unicode_minus'] = False
            except:
                # 如果中文字体不可用，使用英文标签
                pass

        except ImportError:
            self.skipTest("matplotlib not available")

        # 模拟场站内10个车辆的充电情况
        current_time = datetime.now(timezone.utc)

        # 创建10个车辆的模拟数据，起始时间略有差异（模拟真实场景）
        vehicle_predictions = []
        vehicle_names = []

        for i in range(10):
            # 每个车辆的起始时间相差不同（模拟实际充电开始时间差异）
            # 模拟更真实的时间差异：有些车辆几乎同时开始，有些间隔较大
            start_time_offsets = [0, 30, 45, 120, 180, 240, 300, 450, 600, 720]  # 秒
            start_time_offset = start_time_offsets[i]
            start_time = current_time + timedelta(seconds=start_time_offset)

            # 模拟不同的车辆参数（更真实的参数分布）
            soc_values = [30, 45, 60, 75, 20, 85, 40, 55, 70, 90]  # 不同的起始SOC
            max_powers = [120, 100, 150, 80, 200, 60, 180, 90, 110, 75]  # 不同的最大功率
            capacities = [75, 80, 100, 60, 85, 50, 90, 70, 95, 65]  # 不同的电池容量

            soc = soc_values[i]
            max_power = max_powers[i]
            capacity = capacities[i]

            # 创建模拟的充电记录和车型
            mock_charging_record = Mock()
            mock_charging_record.soc = soc

            mock_ev_model = Mock()
            mock_ev_model.max_power = max_power
            mock_ev_model.capacity = capacity

            # 创建预测实例
            with patch('application.algorithm_schedule.non_suppress_power.DBOperate'):
                predictor = NonSuppressPowerPrediction(self.test_site_no)

                # 模拟在不同时间点进行预测，每个车辆的预测都是基于其开始时间
                with patch('application.algorithm_schedule.non_suppress_power.datetime') as mock_datetime:
                    # 设置当前时间为该车辆的实际开始时间
                    mock_datetime.now.return_value = start_time
                    mock_datetime.side_effect = lambda *args, **kw: datetime(*args, **kw)

                    # 获取预测结果
                    prediction = predictor.predict_power_for_vehicle(
                        f"vehicle_{i + 1}",
                        [mock_charging_record],
                        mock_ev_model
                    )

                if prediction and len(prediction["time_list"]) >= 2:  # 确保有足够的数据点用于曲线拟合
                    # 使用实际的开始时间和结束时间
                    actual_start_time = start_time
                    actual_end_time = start_time + timedelta(seconds=prediction["time_list"][-1])

                    vehicle_predictions.append({
                        "local_id": f"vehicle_{i + 1}",
                        "curve_start_time": actual_start_time,
                        "curve_end_time": actual_end_time,
                        "time_list": prediction["time_list"],  # 保持相对时间（从0开始）
                        "power_list": prediction["power_list"],
                        "soc": soc,
                        "max_power": max_power,
                        "capacity": capacity,
                        "start_offset_seconds": start_time_offset  # 记录起始时间偏移
                    })
                    vehicle_names.append(f"V{i + 1}(SOC:{soc}%,{max_power}kW,T+{start_time_offset}s)")

        # 验证我们有足够的预测数据用于曲线拟合
        self.assertGreater(len(vehicle_predictions), 0, "应该有至少一个成功的预测")
        print(f"\n成功生成 {len(vehicle_predictions)} 个车辆的功率预测数据")

        # 验证每个车辆的数据都符合曲线拟合的要求
        valid_vehicles = 0
        for vehicle in vehicle_predictions:
            if len(vehicle["time_list"]) >= 2 and len(vehicle["power_list"]) >= 2:
                valid_vehicles += 1
            else:
                print(f"警告: 车辆 {vehicle['local_id']} 数据点不足，无法进行曲线拟合")

        self.assertGreater(valid_vehicles, 0, "应该有至少一个车辆的数据符合曲线拟合要求")
        print(f"其中 {valid_vehicles} 个车辆的数据符合曲线拟合要求")

        # 进行场站需求曲线叠加（使用曲线拟合方案）
        with patch('application.algorithm_schedule.non_suppress_power.DBOperate'):
            predictor = NonSuppressPowerPrediction(self.test_site_no)
            aggregated_result = predictor.aggregate_site_demand_curve_from_cache(vehicle_predictions)

        # 验证叠加结果
        self.assertIsNotNone(aggregated_result, "叠加结果不应为空")
        self.assertIn("time_list", aggregated_result)
        self.assertIn("power_list", aggregated_result)

        # 验证时间间隔为60秒
        time_list = aggregated_result["time_list"]
        if len(time_list) > 1:
            time_intervals = [time_list[i + 1] - time_list[i] for i in range(len(time_list) - 1)]
            self.assertTrue(all(interval == 60 for interval in time_intervals),
                            "所有时间间隔应该都是60秒")

        print(f"场站总需求曲线生成成功，包含 {len(aggregated_result['time_list'])} 个时间点")
        print(f"📊 可视化展示包含所有 {len(vehicle_predictions)} 个车辆的拟合曲线")

        # 创建图形展示（适配曲线拟合方案）
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(16, 14))  # 增加尺寸以容纳更多曲线

        # 子图1：显示所有车辆的原始功率曲线
        ax1.set_title(f'Original Power Curves for {len(vehicle_predictions)} Vehicles',
                      fontsize=14, fontweight='bold')
        ax1.set_xlabel('Time (minutes)')
        ax1.set_ylabel('Power (kW)')
        ax1.grid(True, alpha=0.3)

        # 为每个车辆绘制原始功率曲线（显示实际的开始时间）
        colors = plt.cm.tab10(range(len(vehicle_predictions)))
        for idx, vehicle in enumerate(vehicle_predictions):
            # 计算基于实际开始时间的绝对时间轴
            start_offset_minutes = vehicle["start_offset_seconds"] / 60
            time_minutes = [start_offset_minutes + t / 60 for t in vehicle["time_list"]]  # 加上开始时间偏移
            ax1.plot(time_minutes, vehicle["power_list"],
                     label=vehicle_names[idx],
                     color=colors[idx],
                     linewidth=1.5,
                     alpha=0.8,
                     marker='o',  # 添加数据点标记
                     markersize=3)

        ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)

        # 子图2：显示曲线拟合效果（使用相同的拟合逻辑）
        ax2.set_title(f'Curve Fitting Visualization for All {len(vehicle_predictions)} Vehicles (Linear Interpolation)',
                      fontsize=14, fontweight='bold')
        ax2.set_xlabel('Time (minutes)')
        ax2.set_ylabel('Power (kW)')
        ax2.grid(True, alpha=0.3)

        # 使用numpy和scipy来展示拟合效果
        try:
            import numpy as np
            from scipy.interpolate import interp1d

            # 为了图表清晰度，使用不同的颜色映射来区分所有车辆
            import matplotlib.cm as cm
            import matplotlib.colors as mcolors
            
            # 创建更丰富的颜色映射，确保10个车辆都有不同颜色
            if len(vehicle_predictions) <= 10:
                # 使用tab10颜色映射，最多支持10种颜色
                color_map = plt.cm.tab10
            else:
                # 如果超过10个车辆，使用Set3颜色映射
                color_map = plt.cm.Set3
            
            colors_extended = [color_map(i / max(len(vehicle_predictions)-1, 1)) for i in range(len(vehicle_predictions))]

            # 显示所有车辆的拟合效果
            for idx, vehicle in enumerate(vehicle_predictions):  # 显示所有车辆
                time_array = np.array(vehicle["time_list"], dtype=float)
                power_array = np.array(vehicle["power_list"], dtype=float)
                start_offset_minutes = vehicle["start_offset_seconds"] / 60

                if len(time_array) >= 2:
                    # 创建拟合函数
                    fitting_func = interp1d(time_array, power_array, kind='linear',
                                            bounds_error=False, fill_value=0.0)

                    # 生成更密集的时间点用于显示拟合曲线
                    time_dense = np.linspace(time_array.min(), time_array.max(), 100)
                    power_fitted = fitting_func(time_dense)

                    # 绘制原始数据点（加上开始时间偏移）
                    ax2.scatter((time_array + start_offset_minutes * 60) / 60, power_array,
                                color=colors_extended[idx], s=30, alpha=0.9, zorder=5,
                                edgecolors='black', linewidth=0.5)

                    # 绘制拟合曲线（加上开始时间偏移）
                    ax2.plot((time_dense + start_offset_minutes * 60) / 60, power_fitted,
                             color=colors_extended[idx], linewidth=1.8, alpha=0.8,
                             label=f'V{idx+1} (fitted)')

            # 优化图例显示
            ax2.legend(fontsize=7, ncol=2, loc='upper right')

        except ImportError:
            ax2.text(0.5, 0.5, 'NumPy/SciPy not available for fitting visualization',
                     transform=ax2.transAxes, ha='center', va='center')

        # 子图3：显示叠加后的场站总需求曲线
        ax3.set_title('Site Total Demand Power Curve (Curve Fitting Aggregated)',
                      fontsize=14, fontweight='bold')
        ax3.set_xlabel('Time (minutes)')
        ax3.set_ylabel('Total Power (kW)')
        ax3.grid(True, alpha=0.3)

        # 绘制叠加后的曲线
        aggregated_time_minutes = [t / 60 for t in aggregated_result["time_list"]]
        ax3.plot(aggregated_time_minutes, aggregated_result["power_list"],
                 color='red', linewidth=3, label='Site Total Demand (Fitted)', marker='o', markersize=2)
        ax3.fill_between(aggregated_time_minutes, aggregated_result["power_list"],
                         alpha=0.3, color='red')

        # 添加统计信息
        max_power = max(aggregated_result["power_list"]) if aggregated_result["power_list"] else 0
        avg_power = sum(aggregated_result["power_list"]) / len(aggregated_result["power_list"]) if aggregated_result[
            "power_list"] else 0
        duration_hours = aggregated_time_minutes[-1] / 60 if aggregated_time_minutes else 0

        ax3.text(0.02, 0.98,
                 f'Peak Power: {max_power:.1f} kW\nAvg Power: {avg_power:.1f} kW\n'
                 f'Duration: {duration_hours:.1f} hours\nMethod: Curve Fitting',
                 transform=ax3.transAxes,
                 verticalalignment='top',
                 bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

        ax3.legend()

        plt.tight_layout()

        # 保存图片到测试目录
        output_path = os.path.join(os.path.dirname(__file__), 'non_suppress_power_curves.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()

        # 打印统计信息到控制台
        print(f"\n{'=' * 70}")
        print(f"Non-Suppress Power Prediction Test Results (Curve Fitting Method)")
        print(f"{'=' * 70}")
        print(f"Site No: {self.test_site_no}")
        print(f"Vehicle Count: {len(vehicle_predictions)}")
        print(f"Valid Fitting Vehicles: {valid_vehicles}")
        print(f"Prediction Duration: {duration_hours:.2f} hours")
        print(f"Peak Power: {max_power:.1f} kW")
        print(f"Average Power: {avg_power:.1f} kW")
        print(f"Time Interval: 60 seconds")
        print(f"Aggregation Method: Linear Interpolation Curve Fitting")
        print(f"Chart saved to: {output_path}")
        print(f"{'=' * 70}")

        # 打印每个车辆的详细信息
        print("\nVehicle Details:")
        print(
            f"{'No.':<4} {'Vehicle ID':<12} {'Init SOC':<8} {'Max Power':<9} {'Capacity':<8} {'Start Time':<10} {'Duration':<10} {'Data Points':<11}")
        print("-" * 85)
        for idx, vehicle in enumerate(vehicle_predictions):
            duration_min = vehicle["time_list"][-1] / 60 if vehicle["time_list"] else 0
            data_points = len(vehicle["time_list"])
            start_offset_min = vehicle["start_offset_seconds"] / 60
            print(f"{idx + 1:<4} {vehicle['local_id']:<12} {vehicle['soc']:<8}% "
                  f"{vehicle['max_power']:<9}kW {vehicle['capacity']:<8}kWh T+{start_offset_min:<7.1f}min {duration_min:<10.1f}min {data_points:<11}")

        # 验证测试结果（适配曲线拟合方案）
        self.assertGreater(max_power, 0, "Peak power should be greater than 0")
        self.assertGreater(avg_power, 0, "Average power should be greater than 0")
        self.assertTrue(os.path.exists(output_path), "Chart file should be created successfully")

        # 验证曲线拟合的合理性
        power_list = aggregated_result["power_list"]
        self.assertTrue(all(p >= 0 for p in power_list), "所有功率值应该为非负数")
        self.assertLessEqual(max_power, sum(max_powers), "峰值功率不应超过所有车辆最大功率之和")

        print(f"\n✅ 曲线拟合方案测试通过！生成了包含{len(power_list)}个数据点的场站总需求曲线")

    def test_non_suppress_power_api(self):
        """测试非压制功率预测API"""
        # 创建预测实例
        with patch('application.algorithm_schedule.non_suppress_power.DBOperate'):
            predictor = NonSuppressPowerPrediction(self.test_site_no)

            local_id = self.test_local_id

            # 创建模拟充电记录
            test_charge_data = [
                (163.0, 161.3, 366.8),
                (163.0, 163.4, 366.3),
                (190.0, 192.3, 367.8),
                (198.0, 196.5, 366.8),
                (199.0, 197.6, 369.3),
                (199.0, 198.7, 370.3),
                (199.0, 198.8, 371.8)
            ]

            # 将test_charge_data转换为mock_charging_record_list
            mock_charging_record_list = []
            for curr_demand, curr_output, vol_output in test_charge_data:
                mock_charging_record = Mock()
                mock_charging_record.vol_output = vol_output
                mock_charging_record.curr_output = curr_output
                mock_charging_record.curr_demand = curr_demand
                mock_charging_record_list.append(mock_charging_record)

            # 创建模拟车型
            mock_ev_model = Mock()
            mock_ev_model.vehicle_label = "Tesla_Model@3_57.5kWh,Tesla_Model@Y_57.5kWh"

            start_soc = 29.0
            non_suppress_rated_power = 120
            max_curr = 200

            prediction_data = predictor.predict_power_for_vehicle(local_id, mock_charging_record_list, mock_ev_model, start_soc, non_suppress_rated_power, max_curr)

            self.assertIsNotNone(prediction_data)

if __name__ == "__main__":
    unittest.main()
