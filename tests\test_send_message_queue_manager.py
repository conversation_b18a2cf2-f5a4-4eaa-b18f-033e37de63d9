#!/usr/bin/env python3
"""
测试send_message_queue_manager模块功能
"""

import sys
import time
import unittest
from datetime import datetime
from unittest.mock import patch, MagicMock
from application.kafka_operate.send_message_queue_manager import (
    send_control_result_to_queue,
    SendMessageQueueManager,
    _handle_storage_dispatch,
    _handle_charge_power_dispatch
)

# 添加项目根目录到路径
sys.path.insert(0, './')


class TestSendMessageQueueManager(unittest.TestCase):
    """测试send_message_queue_manager模块"""

    def setUp(self):
        """测试前准备"""
        self.test_site_no = "TEST_SITE_001"
        self.test_timestamp = int(datetime.now().timestamp())

        # 测试数据
        self.storage_dispatch_result = {
            'scheduling_time': self.test_timestamp,
            'es_scheduling_strategy': [-100, -20, 0, 20, 50, 80, 100, 60, 30, 10]
        }

        self.charge_power_dispatch_result = {
            'scheduling_time': self.test_timestamp,
            'site_charger_power_allocations': {
                'DE0040B1GP5C00046K_1': 60,
                'DE0040B1GP5C00046K_2': 30,
                'DE0040B1GP5C00047K_1': 45
            }
        }

        self.pv_control_result = {
            'scheduling_time': self.test_timestamp,
            'pv_power_limit': 150
        }

    def tearDown(self):
        """测试后清理"""
        pass

    @patch('application.kafka_operate.send_message_queue_manager._send_message_to_queue')
    def test_send_control_result_to_queue_storage_dispatch(self, mock_send_message):
        """测试储能调度控制结果发送"""
        # 调用函数
        send_control_result_to_queue(
            self.storage_dispatch_result,
            'storage_dispatch',
            self.test_site_no
        )

        # 验证是否调用了_send_message_to_queue
        mock_send_message.assert_called_once()
        
        # 获取调用参数
        call_args = mock_send_message.call_args[0]
        message = call_args[0]
        site_no = call_args[1]
        control_type = call_args[2]
        
        # 验证消息结构
        self.assertEqual(message["site_no"], self.test_site_no)
        self.assertEqual(message["ts"], str(self.test_timestamp * 1000))
        self.assertIsNone(message["charger_control"])
        self.assertIsNone(message["pv_inverter_control"])
        self.assertEqual(message["es_control"]["power"], self.storage_dispatch_result['es_scheduling_strategy'])
        self.assertEqual(site_no, self.test_site_no)
        self.assertEqual(control_type, "storage_dispatch")

    @patch('application.kafka_operate.send_message_queue_manager._send_message_to_queue')
    def test_send_control_result_to_queue_charge_power_dispatch(self, mock_send_message):
        """测试充电功率调度控制结果发送"""
        # 调用函数
        send_control_result_to_queue(
            self.charge_power_dispatch_result,
            'charge_power_dispatch',
            self.test_site_no
        )

        # 验证是否调用了_send_message_to_queue
        mock_send_message.assert_called_once()

        # 获取调用参数
        call_args = mock_send_message.call_args[0][0]

        # 验证消息结构
        self.assertEqual(call_args["site_no"], self.test_site_no)
        self.assertEqual(call_args["ts"], str(self.test_timestamp * 1000))
        self.assertIsNone(call_args["es_control"])
        self.assertIsNone(call_args["pv_inverter_control"])
        self.assertEqual(len(call_args["charger_control"]), 3)

        # 验证充电桩信息解析
        first_charger = call_args["charger_control"][0]
        self.assertIn("pile_sn", first_charger)
        self.assertIn("connect_id", first_charger)
        self.assertIn("power", first_charger)

    @patch('application.kafka_operate.send_message_queue_manager._send_message_to_queue')
    def test_send_control_result_to_queue_pv_control(self, mock_send_message):
        """测试光伏控制结果发送"""
        # 调用函数
        send_control_result_to_queue(
            self.pv_control_result,
            'pv_control',
            self.test_site_no
        )

        # 验证是否调用了_send_message_to_queue
        mock_send_message.assert_called_once()

        # 获取调用参数
        call_args = mock_send_message.call_args[0][0]

        # 验证消息结构
        self.assertEqual(call_args["site_no"], self.test_site_no)
        self.assertEqual(call_args["ts"], str(self.test_timestamp * 1000))
        self.assertIsNone(call_args["es_control"])
        self.assertIsNone(call_args["charger_control"])
        self.assertEqual(call_args["pv_inverter_control"]["power_limit"], 150)

    def test_send_control_result_to_queue_unknown_type(self):
        """测试未知控制类型"""
        with patch('application.kafka_operate.send_message_queue_manager.logger') as mock_logger:
            send_control_result_to_queue(
                {"test": "data"},
                'unknown_type',
                self.test_site_no
            )

            # 验证是否记录了错误日志
            mock_logger.error.assert_called_once_with(f"场站 {self.test_site_no} 未知的控制类型: unknown_type")



    @patch('application.kafka_operate.send_message_queue_manager._send_message_to_queue')
    def test_handle_storage_dispatch_empty_strategy(self, mock_send_message):
        """测试处理空的储能调度策略"""
        empty_result = {
            'scheduling_time': self.test_timestamp,
            'es_scheduling_strategy': []
        }

        with patch('application.kafka_operate.send_message_queue_manager.logger') as mock_logger:
            _handle_storage_dispatch(empty_result, self.test_site_no)

            # 验证记录了警告日志
            mock_logger.warning.assert_called_once_with(f"场站 {self.test_site_no} 储能调度策略为空")

            # 验证没有调用发送消息
            mock_send_message.assert_not_called()

    @patch('application.kafka_operate.send_message_queue_manager._send_message_to_queue')
    def test_handle_charge_power_dispatch_invalid_id(self, mock_send_message):
        """测试处理无效的充电桩ID"""
        invalid_result = {
            'scheduling_time': self.test_timestamp,
            'site_charger_power_allocations': {
                'INVALIDID': 60,  # 没有下划线的无效ID
                'DE0040B1GP5C00046K_1': 30  # 有效的ID
            }
        }

        with patch('application.kafka_operate.send_message_queue_manager.logger') as mock_logger:
            _handle_charge_power_dispatch(invalid_result, self.test_site_no)

            # 验证记录了警告日志
            mock_logger.warning.assert_called_once_with(f"场站 {self.test_site_no} 无法解析充电桩连接ID: INVALIDID")

            # 验证仍然调用了发送消息（因为有有效的ID）
            mock_send_message.assert_called_once()

            # 验证消息中只包含有效的充电桩
            call_args = mock_send_message.call_args[0][0]
            self.assertEqual(len(call_args["charger_control"]), 1)
            self.assertEqual(call_args["charger_control"][0]["pile_sn"], "DE0040B1GP5C00046K")

    @patch('application.kafka_operate.send_message_queue_manager._send_message_to_queue')
    def test_send_storage_control_message(self, mock_send_message):
        """测试发送储能控制消息"""
        power = 100
        timestamp_ms = self.test_timestamp * 1000

        _send_storage_control_message(self.test_site_no, power, timestamp_ms)

        # 验证调用了发送消息
        mock_send_message.assert_called_once()

        # 验证消息内容
        call_args = mock_send_message.call_args[0][0]
        self.assertEqual(call_args["site_no"], self.test_site_no)
        self.assertEqual(call_args["ts"], str(timestamp_ms))
        self.assertEqual(call_args["es_control"]["power"], power)
        self.assertIsNone(call_args["charger_control"])
        self.assertIsNone(call_args["pv_inverter_control"])
        self.assertIsNotNone(call_args["biz_seq"])

    def test_send_message_queue_manager_singleton(self):
        """测试SendMessageQueueManager单例模式"""
        manager1 = SendMessageQueueManager()
        manager2 = SendMessageQueueManager()

        # 验证是同一个实例
        self.assertIs(manager1, manager2)

        # 验证队列是同一个
        self.assertIs(manager1.queue, manager2.queue)

    def test_send_message_queue_manager_put_get(self):
        """测试SendMessageQueueManager的put和get方法"""
        # 重置单例实例以确保测试独立性
        SendMessageQueueManager._instance = None
        SendMessageQueueManager._queue = None

        with patch('queue.Queue') as mock_queue_class:
            mock_queue = MagicMock()
            mock_queue_class.return_value = mock_queue

            manager = SendMessageQueueManager()

            # 测试put方法
            test_message = {"topic": "test_topic", "value": b"test_message"}
            manager.put(test_message)

            mock_queue.put.assert_called_once_with(test_message)

            # 测试get方法
            mock_queue.get.return_value = test_message
            result = manager.get(timeout=1.0)

            mock_queue.get.assert_called_once_with(timeout=1.0)
            self.assertEqual(result, test_message)

    def test_send_control_result_to_queue_exception_handling(self):
        """测试send_control_result_to_queue异常处理"""
        # 创建一个会抛出异常的控制结果
        invalid_result = None

        with patch('application.kafka_operate.send_message_queue_manager.logger') as mock_logger:
            send_control_result_to_queue(invalid_result, 'storage_dispatch', self.test_site_no)

            # 验证记录了错误日志
            mock_logger.error.assert_called_once()
            error_call = mock_logger.error.call_args[0][0]
            self.assertTrue(error_call.startswith(f"场站 {self.test_site_no} 发送控制结果失败:"))


if __name__ == "__main__":
    # 运行所有测试
    unittest.main(verbosity=2)
