
import pandas as pd

# ======================= FEATURE EXTRACTION ================================================================================

class feature_engineering:
    def __init__(self, 
                 df: pd.DataFrame,
                 target_feature: str, 
                 horizon_features: dict = {}, 
                 window_features: dict = {},
                 forecast_features: dict = {},
                 forecast_horizon: int = 96,
                 time_granularity: int = 15,
                 inference: bool = False):
        
        self.df = df
        self.target_feature = target_feature
        self.horizon_features = horizon_features
        self.window_features = window_features
        self.forecast_features = forecast_features
        self.forecast_horizon = forecast_horizon
        self.time_granularity =  time_granularity
        self.inference = inference

        # Generate features
        self.generate_window_features()
        self.generate_horizon_features()
        self.df.dropna(inplace=True)
        self.df = self.df.sort_values(by=['Timestamp', 'horizon'], ascending=[True, True])
        self.df = self.df.reset_index(drop=True)
        

    def generate_horizon_features(self):

        rows = []
        for h in range(1, self.forecast_horizon + 1):
            # Copy the base data (features that remain the same for the base time t)
            temp = self.df.copy(deep=True)
            # Create the target column as the power demand at time t+h.
            if not self.inference:
                temp['target'] = temp[self.target_feature].shift(-h)
            # Add a column indicating the forecast horizon (i.e. which future step)
            temp['horizon'] = h
            # Optionally, compute a future timestamp: this assumes constant 15-min intervals.
            temp['future_Timestamp'] = temp['Timestamp'] + pd.Timedelta(minutes=self.time_granularity * h)
            temp['future_MinuteOfDay'] = temp['future_Timestamp'].dt.hour * 60 + temp['future_Timestamp'].dt.minute
            temp['future_DayOfWeek'] = temp['future_Timestamp'].dt.dayofweek
            temp['future_Hour'] = temp['future_Timestamp'].dt.hour

            if len(self.horizon_features) > 0:
                for feature in self.horizon_features:
                    # reflect the future time step. Here we create new columns with a 'future_' prefix.
                    temp['future_' + feature] = temp[feature].shift(-h)
            
            # Drop rows with NaN values (which occur at the end due to shifting)
            temp = temp.dropna()
            
            # Append to the list of rows
            rows.append(temp)

        self.df = pd.concat(rows)


    def generate_window_features(self,):

        if len(self.window_features) > 0:
            for feature in self.window_features:
                lags = self.window_features[feature]
                # 修改为从1开始，与训练时的模型保持一致
                for lag in range(1, lags + 1):
                    self.df[f'{feature}_{lag}'] = self.df[feature].shift(lag)

#====================== TRAIN TEST SPLIT ===============================================

def train_test_window(df: pd.DataFrame, training_window: int ,  testing_window: int ):
    """ 
    Takes a time series dataset and returns the train and test set based on set time window in days
        df: a pandas dataframe
        training_window: length of the historical data in days to consider
        testing_window: length of window in days of historical data use for testing (less than training_window)
    
        returns train and test dataset
    """

    training_window_date = df.Timestamp.max() - pd.DateOffset(days=training_window)
    testing_window_date = df.Timestamp.max() - pd.DateOffset(days=testing_window)

    train_set = df[(df['Timestamp'] >= training_window_date) & (df['Timestamp'] <= testing_window_date)].copy()
    test_set = df[df['Timestamp'] > testing_window_date].copy()

    train_set = train_set.sort_values('Timestamp')
    test_set = test_set.sort_values('Timestamp')

    return train_set, test_set
