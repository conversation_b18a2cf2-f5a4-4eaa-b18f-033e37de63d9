import json
import time
import traceback
import pytz
from datetime import datetime, timezone
from copy import deepcopy

from application.api_manage.energy_cloud_api import EnergyCloudAPI
from application.db_operate.db_operate import DBOperate
from application.utils.constants import APIEndpoints
from application.utils.electricity_price import NordPoolAPI
from application.utils.idempotency import generate_idempotency_key
from application.utils.logger import setup_logger

logger = setup_logger("dynamic_price_scheduler", direction="dynamic_price_scheduler")
# 中国测试数据模拟开关
CHINA_DATA_DEBUG = True

def get_current_time():
    current_time = datetime.now(pytz.timezone('CET'))
    formatted_time = current_time.strftime('%H:%M')

    return formatted_time

def get_cet_offset():
    now = datetime.now()
    cet = pytz.timezone('CET')

    cet_time = now.astimezone(cet)

    hours_offset = int(cet_time.utcoffset().total_seconds() / 3600)
    return hours_offset


def convert_to_utc_timestamp(date_str):
    # 将字符串中的 Z 替换为 +00:00 以符合 fromisoformat 的输入格式
    dt = datetime.fromisoformat(date_str.replace("Z", "+00:00"))
    timestamp = int(dt.timestamp()) * 1000
    return timestamp


def convert_iso_to_readable_utc(iso_datetime_str):
    utc_datetime = datetime.strptime(iso_datetime_str, "%Y-%m-%dT%H:%M:%SZ")

    datetime_str = utc_datetime.strftime("%Y-%m-%d %H:%M:%S")

    return datetime_str


def format_electricity_price_data(price_datas):
    prices = price_datas.get('prices', [])
    unit = price_datas.get('marketMainCurrency', 'EUR')
    result = [
        {
            "e_price": price['price'] / 1000,
            "start_time": convert_to_utc_timestamp(price['deliveryStart']),
            "end_time": convert_to_utc_timestamp(price['deliveryEnd']),
            "unit": unit
        }
        for price in prices
    ]
    return result


def formate_electricity_price_data_to_db(price_datas):
    prices = price_datas.get('prices', [])
    unit = price_datas.get('marketMainCurrency', 'EUR')
    result = [
        {
            "price": price['price'] / 1000,
            "start_time": convert_iso_to_readable_utc(price['deliveryStart']),
            "end_time": convert_iso_to_readable_utc(price['deliveryEnd']),
            "unit": unit
        }
        for price in prices
    ]
    return result


def get_dynamic_electricity_price(areas, date, currency="EUR"):
    try:
        api = NordPoolAPI()
        price_result = api.get_data(market="DayAhead", areas=areas, currency=currency, date=date)
        price_datas = price_result[0]
        return price_datas

    except Exception as e:
        logger.error('get price failed:')
        logger.error(traceback.print_exc())
        return


def send_electricity_price_with_retry(price_segments):
    """
    带重试机制的电价曲线发送函数
    重试间隔：1, 2, 4, 8, 16, 32, 64分钟

    Args:
        price_segments: 电价数据

    Returns:
        bool: 最终是否发送成功
    """
    retry_intervals = [1, 2, 4, 8, 16, 32, 64]  # 重试间隔（分钟）

    for attempt in range(len(retry_intervals) + 1):  # 总共尝试8次（1次初始 + 7次重试）
        try:
            logger.info(f"尝试发送电价曲线数据，第{attempt + 1}次")
            logger.info(json.dumps(price_segments))

            # 调用API发送数据
            result = EnergyCloudAPI.send_electricity_price_curve(price_segments=price_segments)

            # 检查返回值是否为True（成功）
            if result is True:
                return True
            else:
                logger.warning(f"电价曲线数据发送失败，返回值: {result}")

                # 如果不是最后一次尝试，则等待后重试
                if attempt < len(retry_intervals):
                    wait_minutes = retry_intervals[attempt]
                    logger.info(f"等待{wait_minutes}分钟后进行第{attempt + 2}次重试...")
                    time.sleep(wait_minutes * 60)  # 转换为秒
                else:
                    logger.error(f"电价曲线数据发送最终失败，已尝试{attempt + 1}次，返回值: {result}")
                    return False

        except Exception as e:
            logger.error(f"发送电价曲线数据时发生异常: {str(e)}")

            # 如果不是最后一次尝试，则等待后重试
            if attempt < len(retry_intervals):
                wait_minutes = retry_intervals[attempt]
                logger.info(f"等待{wait_minutes}分钟后进行第{attempt + 2}次重试...")
                time.sleep(wait_minutes * 60)  # 转换为秒
            else:
                logger.error(f"电价曲线数据发送最终失败，已尝试{attempt + 1}次，最后异常: {str(e)}")
                return False

    return False


def get_region_prices(regions, date):
    try:
        db_opa = DBOperate()
        electricity_prices = []
        today = datetime.now(tz=pytz.timezone('CET')).date()

        for region in regions:
            price_datas = get_dynamic_electricity_price(areas=region, date=date)
            if not price_datas:
                logger.error('price_datas is empty')
                continue

            db_price_datas = formate_electricity_price_data_to_db(price_datas=price_datas)
            db_opa.save_site_electricity_price(region=region, datas=db_price_datas, date=date)

            front_end_price_datas = format_electricity_price_data(price_datas)
            electricity_price = {
                "region": region,
                "grid_name": "Nord Pool",
                "time_zone": f"UTC+{get_cet_offset()}",
                "refresh_time": f"D-0 {get_current_time()}" if date == today else f"D-1 {get_current_time()}",
                "freq": "H-1",
                "can_use": True if len(front_end_price_datas) == 24 else False,
                "price_data": front_end_price_datas
            }
            # 只有当can_use为True时才添加到结果列表中
            if electricity_price["can_use"]:
                electricity_prices.append(electricity_price)
            else:
                logger.warning(f"区域 {region} 电价数据不完整，预期24条记录，实际获得 {len(front_end_price_datas)} 条，跳过添加")

        return electricity_prices
    
    except Exception as e:
        return []


def dynamic_price_fetch(site_no, data):
    """
    动态电价获取算法调度入口
    :param site_no: 场站编号
    :param data: 相关输入数据
    :return: 电价信息
    """
    logger.info(f'dynamic price fetch start.date:{data}')
    date = data
    regions = NordPoolAPI().areas
    logger.info(f'regions:{regions}')

    electricity_prices = get_region_prices(regions, date)


    # 使用工具函数生成幂等串，传入字典而不是JSON字符串
    price_segments = {
        "biz_seq": generate_idempotency_key({"electricity_prices": electricity_prices}),
        "electricity_price": electricity_prices
    }

    # 使用带重试机制的方法调用
    # 中国环境测试 使用欧洲数据模拟
    if CHINA_DATA_DEBUG:
        price_datas = price_segments['electricity_price']
        for price_data in price_datas:
            if price_data['can_use']:
                china_data = deepcopy(price_data)
                china_data['region'] = 'CN'
                china_data['grid_name'] = 'EPEX'
                price_segments['electricity_price'].append(china_data)
                break
    success = send_electricity_price_with_retry(price_segments=price_segments)

    if success:
        logger.info("电价曲线数据发送成功")
    else:
        logger.error("电价曲线数据发送失败，已达到最大重试次数")

    return price_segments


if __name__ == '__main__':
    base_url = APIEndpoints.ENERGY_CLOUD_BASE_URL
    print(base_url)
    dynamic_price_fetch(site_no='123', data=datetime.now().date())
