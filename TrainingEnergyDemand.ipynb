{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "import U\n", "import evaluation_metrics as eval\n", "from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score\n", "\n", "import xgboost as xgb\n", "\n", "# ===================1. DATA PROCESSING ======================\n", "\n", "# ===== ===== == decide which columns to keep == ===== =======\n", "evse_cols = {'create_time': True,\n", "             'power': True,\n", "             'evse_sn': True,\n", "             'location_id': True,\n", "             'location_name': <PERSON><PERSON><PERSON>,\n", "             'total_electrical_power': False}\n", "\n", "# ===== ===== define evse time interval parameter in seconds ===\n", "evse_time_interval = 30 \n", "evse_time_interval = str(evse_time_interval) + \"s\"\n", "\n", "# ===== ===== define evcs time interval parameter in minutes ===\n", "time_granularity = 15 # in minutes\n", "time_granularity_str = str(time_granularity) + 'min'\n", "\n", "# ======== == Specify the file path of station data ===== ======\n", "file_path = 'common_common_cn_evse_meter_upload_list.txt'  # give your actual file path\n", "\n", "\n", "# @@@@@@@@@@@@@@@ EXECUTION @@@@@@@@@@@@@@@@@@@@@@@@@\n", "# ======= ==== = Read the data into a pandas dataFrame ==============\n", "evse_data = pd.read_csv(file_path, delimiter='|')\n", "evse_data = evse_data.drop_duplicates()\n", "evse_data['create_time'] = pd.to_datetime(evse_data['create_time'], unit='ms')\n", "\n", "evse_cols_to_keep = [col for col in evse_cols if evse_cols[col] == True]\n", "evse_data = evse_data[evse_cols_to_keep]\n", "\n", "# ====== ====== preprocess the evse data ===== =========== ========= \n", "evse_data_processor = U.evse_sn_data_processing(data = evse_data, time_intervals= evse_time_interval)\n", "evse_processed_data = evse_data_processor.chargers_data\n", "\n", "# ==  ===== ======= process evse data === ==== ======================\n", "evcs_data_processor = U.evcs_data_processing(data = evse_processed_data, time_granularity=time_granularity_str)\n", "evcs_data = evcs_data_processor.charging_stations_data\n", "\n", "del evse_cols_to_keep\n", "del evse_data\n", "del evse_data_processor\n", "del evse_processed_data\n", "del evcs_data_processor\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# ======================= 2. FEATUR ENGINEERING =======================================\n", "\n", "# ==== ====== define feature engineering parameters ======= ====== =====================\n", "window_features = {\"total_power_demand_KWh\": 96}\n", "horizon_features = {}\n", "forecast_features = {}\n", "target_feature = \"total_power_demand_KWh\"\n", "forecast_horizon = 96\n", "# ===== ====== define train and test set window in days ==== ====== ==== ================\n", "training_window = 30*12\n", "testing_window = 30*2\n", "# ======== ==== define the name of the station ===== === =================================\n", "# station_id = 97\n", "station_id = list(evcs_data.keys())[0]\n", "\n", "\n", "\n", "# @@@@@@@@@@@@@@@@ EXECUTION @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@\n", "#=== ====== === get station data, split data into train/test and do feature engineering ===\n", "station_data = evcs_data[station_id]\n", "train_set, test_set = U.train_test_window(df=station_data, training_window=training_window, testing_window=testing_window)\n", "\n", "feature_generation  = U.feature_engineering(df=train_set, \n", "                                          target_feature=target_feature, \n", "                                          horizon_features=horizon_features, \n", "                                          window_features=window_features,\n", "                                          forecast_features=forecast_features, \n", "                                          forecast_horizon=forecast_horizon,\n", "                                          time_granularity=time_granularity)\n", "\n", "station_engineered_data = feature_generation.df\n", "station_engineered_data = station_engineered_data.sort_values(by=['Timestamp', 'horizon'], ascending=[True, True])\n", "\n", "\n", "del feature_generation\n", "del evcs_data"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# ============== 3. FEATURE SELECTION ===================================\n", "\n", "feature_columns = []\n", "feature_columns = feature_columns + [f'{feature}_{lag}' for feature in window_features for lag in range(1, window_features[feature] + 1)]\n", "# feature_columns = feature_columns + [f'{feature}_{lag}' for feature in horizon_features for lag in range(1, horizon_features[feature] + 1)]\n", "# feature_columns = feature_columns + [f'{feature}_{lag}' for feature in forecast_features for lag in range(1, forecast_features[feature] + 1)]\n", "feature_columns = feature_columns + ['future_DayOfWeek', 'future_MinuteOfDay', 'horizon', ] \n", "feature_columns = feature_columns + ['total_power_demand_KWh', 'MinuteOfDay'] #, 'Hour']#, 'Month']\n", "\n", "# Saperate target and training features\n", "\n", "# @@@@@@@@@@@@@@@@ EXECUTION @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@\n", "\n", "X = station_engineered_data[feature_columns]\n", "y = station_engineered_data['target']\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0]\tvalidation_0-rmse:2.31616\tvalidation_1-rmse:2.27331\n", "[1]\tvalidation_0-rmse:2.05066\tvalidation_1-rmse:2.08732\n", "[2]\tvalidation_0-rmse:1.90158\tvalidation_1-rmse:2.00323\n", "[3]\tvalidation_0-rmse:1.80234\tvalidation_1-rmse:1.94021\n", "[4]\tvalidation_0-rmse:1.73992\tvalidation_1-rmse:1.92674\n", "[5]\tvalidation_0-rmse:1.69648\tvalidation_1-rmse:1.91437\n", "[6]\tvalidation_0-rmse:1.66667\tvalidation_1-rmse:1.90411\n", "[7]\tvalidation_0-rmse:1.64541\tvalidation_1-rmse:1.91233\n", "[8]\tvalidation_0-rmse:1.62770\tvalidation_1-rmse:1.90792\n", "[9]\tvalidation_0-rmse:1.61499\tvalidation_1-rmse:1.91062\n", "[10]\tvalidation_0-rmse:1.60550\tvalidation_1-rmse:1.90684\n", "[11]\tvalidation_0-rmse:1.58950\tvalidation_1-rmse:1.89989\n", "[12]\tvalidation_0-rmse:1.57673\tvalidation_1-rmse:1.90117\n", "[13]\tvalidation_0-rmse:1.56457\tvalidation_1-rmse:1.89809\n", "[14]\tvalidation_0-rmse:1.55488\tvalidation_1-rmse:1.89655\n", "[15]\tvalidation_0-rmse:1.54632\tvalidation_1-rmse:1.89751\n", "[16]\tvalidation_0-rmse:1.53719\tvalidation_1-rmse:1.90010\n", "[17]\tvalidation_0-rmse:1.52861\tvalidation_1-rmse:1.91051\n", "[18]\tvalidation_0-rmse:1.52410\tvalidation_1-rmse:1.91095\n", "[19]\tvalidation_0-rmse:1.51886\tvalidation_1-rmse:1.90965\n", "[20]\tvalidation_0-rmse:1.51515\tvalidation_1-rmse:1.91128\n", "[21]\tvalidation_0-rmse:1.51115\tvalidation_1-rmse:1.91159\n", "[22]\tvalidation_0-rmse:1.50813\tvalidation_1-rmse:1.91331\n", "[23]\tvalidation_0-rmse:1.50477\tvalidation_1-rmse:1.91332\n", "[24]\tvalidation_0-rmse:1.50141\tvalidation_1-rmse:1.91294\n", "[25]\tvalidation_0-rmse:1.49439\tvalidation_1-rmse:1.91834\n", "[26]\tvalidation_0-rmse:1.48869\tvalidation_1-rmse:1.92269\n", "[27]\tvalidation_0-rmse:1.48584\tvalidation_1-rmse:1.92309\n", "[28]\tvalidation_0-rmse:1.48007\tvalidation_1-rmse:1.92475\n", "[29]\tvalidation_0-rmse:1.47408\tvalidation_1-rmse:1.92305\n", "[30]\tvalidation_0-rmse:1.47041\tvalidation_1-rmse:1.92128\n", "[31]\tvalidation_0-rmse:1.46855\tvalidation_1-rmse:1.92146\n", "[32]\tvalidation_0-rmse:1.46611\tvalidation_1-rmse:1.92217\n", "[33]\tvalidation_0-rmse:1.46262\tvalidation_1-rmse:1.92431\n", "[34]\tvalidation_0-rmse:1.46041\tvalidation_1-rmse:1.92496\n", "[35]\tvalidation_0-rmse:1.45738\tvalidation_1-rmse:1.92295\n", "[36]\tvalidation_0-rmse:1.45483\tvalidation_1-rmse:1.92346\n", "[37]\tvalidation_0-rmse:1.45183\tvalidation_1-rmse:1.92401\n", "[38]\tvalidation_0-rmse:1.44775\tvalidation_1-rmse:1.92263\n", "[39]\tvalidation_0-rmse:1.44003\tvalidation_1-rmse:1.92588\n", "[40]\tvalidation_0-rmse:1.43672\tvalidation_1-rmse:1.92618\n", "[41]\tvalidation_0-rmse:1.43447\tvalidation_1-rmse:1.92463\n", "[42]\tvalidation_0-rmse:1.43267\tvalidation_1-rmse:1.92475\n", "[43]\tvalidation_0-rmse:1.43144\tvalidation_1-rmse:1.92541\n", "[44]\tvalidation_0-rmse:1.43032\tvalidation_1-rmse:1.92717\n", "[45]\tvalidation_0-rmse:1.42780\tvalidation_1-rmse:1.92770\n", "[46]\tvalidation_0-rmse:1.42498\tvalidation_1-rmse:1.92824\n", "[47]\tvalidation_0-rmse:1.42233\tvalidation_1-rmse:1.92811\n", "[48]\tvalidation_0-rmse:1.42068\tvalidation_1-rmse:1.92830\n", "[49]\tvalidation_0-rmse:1.41915\tvalidation_1-rmse:1.92885\n", "[50]\tvalidation_0-rmse:1.41587\tvalidation_1-rmse:1.92948\n", "[51]\tvalidation_0-rmse:1.41201\tvalidation_1-rmse:1.93048\n", "[52]\tvalidation_0-rmse:1.40868\tvalidation_1-rmse:1.93254\n", "[53]\tvalidation_0-rmse:1.40623\tvalidation_1-rmse:1.93218\n", "[54]\tvalidation_0-rmse:1.40310\tvalidation_1-rmse:1.93190\n", "[55]\tvalidation_0-rmse:1.40193\tvalidation_1-rmse:1.93265\n", "[56]\tvalidation_0-rmse:1.39994\tvalidation_1-rmse:1.93240\n", "[57]\tvalidation_0-rmse:1.39734\tvalidation_1-rmse:1.93468\n", "[58]\tvalidation_0-rmse:1.39441\tvalidation_1-rmse:1.93571\n", "[59]\tvalidation_0-rmse:1.39030\tvalidation_1-rmse:1.93670\n", "[60]\tvalidation_0-rmse:1.38826\tvalidation_1-rmse:1.93749\n", "[61]\tvalidation_0-rmse:1.38592\tvalidation_1-rmse:1.93921\n", "[62]\tvalidation_0-rmse:1.38389\tvalidation_1-rmse:1.93964\n", "[63]\tvalidation_0-rmse:1.38263\tvalidation_1-rmse:1.93976\n", "[64]\tvalidation_0-rmse:1.38113\tvalidation_1-rmse:1.93933\n", "[65]\tvalidation_0-rmse:1.37755\tvalidation_1-rmse:1.94056\n", "[66]\tvalidation_0-rmse:1.37634\tvalidation_1-rmse:1.94115\n", "[67]\tvalidation_0-rmse:1.37421\tvalidation_1-rmse:1.94119\n", "[68]\tvalidation_0-rmse:1.37215\tvalidation_1-rmse:1.94198\n", "[69]\tvalidation_0-rmse:1.37089\tvalidation_1-rmse:1.94239\n", "[70]\tvalidation_0-rmse:1.36845\tvalidation_1-rmse:1.94274\n", "[71]\tvalidation_0-rmse:1.36658\tvalidation_1-rmse:1.94272\n", "[72]\tvalidation_0-rmse:1.36403\tvalidation_1-rmse:1.94410\n", "[73]\tvalidation_0-rmse:1.36136\tvalidation_1-rmse:1.94540\n", "[74]\tvalidation_0-rmse:1.35988\tvalidation_1-rmse:1.94513\n", "[75]\tvalidation_0-rmse:1.35778\tvalidation_1-rmse:1.94483\n", "[76]\tvalidation_0-rmse:1.35644\tvalidation_1-rmse:1.94513\n", "[77]\tvalidation_0-rmse:1.35408\tvalidation_1-rmse:1.94637\n", "[78]\tvalidation_0-rmse:1.35123\tvalidation_1-rmse:1.94696\n", "[79]\tvalidation_0-rmse:1.35028\tvalidation_1-rmse:1.94698\n", "[80]\tvalidation_0-rmse:1.34772\tvalidation_1-rmse:1.94796\n", "[81]\tvalidation_0-rmse:1.34643\tvalidation_1-rmse:1.94901\n", "[82]\tvalidation_0-rmse:1.34442\tvalidation_1-rmse:1.94972\n", "[83]\tvalidation_0-rmse:1.34341\tvalidation_1-rmse:1.95059\n", "[84]\tvalidation_0-rmse:1.34055\tvalidation_1-rmse:1.95307\n", "[85]\tvalidation_0-rmse:1.33857\tvalidation_1-rmse:1.95400\n", "[86]\tvalidation_0-rmse:1.33639\tvalidation_1-rmse:1.95396\n", "[87]\tvalidation_0-rmse:1.33546\tvalidation_1-rmse:1.95428\n", "[88]\tvalidation_0-rmse:1.33426\tvalidation_1-rmse:1.95494\n", "[89]\tvalidation_0-rmse:1.33158\tvalidation_1-rmse:1.95735\n", "[90]\tvalidation_0-rmse:1.32947\tvalidation_1-rmse:1.95857\n", "[91]\tvalidation_0-rmse:1.32705\tvalidation_1-rmse:1.96029\n", "[92]\tvalidation_0-rmse:1.32539\tvalidation_1-rmse:1.96065\n", "[93]\tvalidation_0-rmse:1.32400\tvalidation_1-rmse:1.96075\n", "[94]\tvalidation_0-rmse:1.32320\tvalidation_1-rmse:1.96063\n", "[95]\tvalidation_0-rmse:1.32207\tvalidation_1-rmse:1.96104\n", "[96]\tvalidation_0-rmse:1.32056\tvalidation_1-rmse:1.96129\n", "[97]\tvalidation_0-rmse:1.31861\tvalidation_1-rmse:1.96189\n", "[98]\tvalidation_0-rmse:1.31742\tvalidation_1-rmse:1.96186\n", "[99]\tvalidation_0-rmse:1.31553\tvalidation_1-rmse:1.96272\n"]}, {"data": {"text/html": ["<style>#sk-container-id-1 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-1 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-1 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-1 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-1 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-1 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-1 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-1 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-1 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-1 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-1 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-1 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-1 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-1 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-1 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-1 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-1 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>XGBRegressor(base_score=None, booster=None, callbacks=None,\n", "             colsample_bylevel=None, colsample_bynode=None,\n", "             colsample_bytree=None, device=None, early_stopping_rounds=None,\n", "             enable_categorical=False, eval_metric=None, feature_types=None,\n", "             gamma=None, grow_policy=None, importance_type=None,\n", "             interaction_constraints=None, learning_rate=None, max_bin=None,\n", "             max_cat_threshold=None, max_cat_to_onehot=None,\n", "             max_delta_step=None, max_depth=None, max_leaves=None,\n", "             min_child_weight=None, missing=nan, monotone_constraints=None,\n", "             multi_strategy=None, n_estimators=None, n_jobs=None,\n", "             num_parallel_tree=None, random_state=None, ...)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" checked><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;XGBRegressor<span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>XGBRegressor(base_score=None, booster=None, callbacks=None,\n", "             colsample_bylevel=None, colsample_bynode=None,\n", "             colsample_bytree=None, device=None, early_stopping_rounds=None,\n", "             enable_categorical=False, eval_metric=None, feature_types=None,\n", "             gamma=None, grow_policy=None, importance_type=None,\n", "             interaction_constraints=None, learning_rate=None, max_bin=None,\n", "             max_cat_threshold=None, max_cat_to_onehot=None,\n", "             max_delta_step=None, max_depth=None, max_leaves=None,\n", "             min_child_weight=None, missing=nan, monotone_constraints=None,\n", "             multi_strategy=None, n_estimators=None, n_jobs=None,\n", "             num_parallel_tree=None, random_state=None, ...)</pre></div> </div></div></div></div>"], "text/plain": ["XGBRegressor(base_score=None, booster=None, callbacks=None,\n", "             colsample_bylevel=None, colsample_bynode=None,\n", "             colsample_bytree=None, device=None, early_stopping_rounds=None,\n", "             enable_categorical=False, eval_metric=None, feature_types=None,\n", "             gamma=None, grow_policy=None, importance_type=None,\n", "             interaction_constraints=None, learning_rate=None, max_bin=None,\n", "             max_cat_threshold=None, max_cat_to_onehot=None,\n", "             max_delta_step=None, max_depth=None, max_leaves=None,\n", "             min_child_weight=None, missing=nan, monotone_constraints=None,\n", "             multi_strategy=None, n_estimators=None, n_jobs=None,\n", "             num_parallel_tree=None, random_state=None, ...)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# ============== 3. TRAINING PREDICTION MODEL =============================\n", "\n", "#============ define parameters\n", "split_ratio = 0.8 # train validation split\n", "n_estimators = 4000 # number of estimators for xgb model\n", "max_depth = 3\n", "early_stopping_rounds = 100\n", "\n", "# @@@@@@@@@@@@@@@@ EXECUTION @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@\n", "\n", "split_idx = int(split_ratio * len(station_engineered_data))\n", "\n", "X_train = X.iloc[:split_idx]\n", "X_validate  = X.iloc[split_idx:]\n", "y_train = y.iloc[:split_idx]\n", "y_validate = y.iloc[split_idx:]\n", "\n", "\n", "model = xgb.XGBRegressor(\n", "    objective='reg:squarederror'\n", ")\n", "\n", "model.fit(\n", "    X_train, \n", "    y_train, \n", "    eval_set=[(X_train, y_train), (X_validate, y_validate)],\n", "    verbose=True          \n", ")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " Overall Evaluation Metrics on VALIDATION SET \n", ": MAE :, 1.3675089623109713 RMSE : 1.9627225356169469, R² : 0.4198832984763953 sMAPE (%) :, 42.8741415148643\n", "\n", " Per-Horizon Evaluation Metrics: on VALIDATION SET \n", "Horizon t+ 1: MAE = 0.7483, RMSE = 1.1352, R² = 0.8049, sMAPE = 23.8259%\n", "Horizon t+ 2: MAE = 0.8423, RMSE = 1.2818, R² = 0.7513, sMAPE = 26.2061%\n", "Horizon t+ 3: MAE = 0.9395, RMSE = 1.4256, R² = 0.6924, sMAPE = 28.9442%\n", "Horizon t+ 4: MAE = 1.0212, RMSE = 1.5348, R² = 0.6435, sMAPE = 31.3327%\n", "Horizon t+ 5: MAE = 1.0764, RMSE = 1.6171, R² = 0.6043, sMAPE = 32.7637%\n", "Horizon t+ 6: MAE = 1.1328, RMSE = 1.6800, R² = 0.5729, sMAPE = 34.6607%\n", "Horizon t+ 7: MAE = 1.1680, RMSE = 1.7251, R² = 0.5497, sMAPE = 35.6233%\n", "Horizon t+ 8: MAE = 1.2021, RMSE = 1.7664, R² = 0.5280, sMAPE = 36.5990%\n", "Horizon t+ 9: MAE = 1.2237, RMSE = 1.7908, R² = 0.5149, sMAPE = 37.2299%\n", "Horizon t+10: MAE = 1.2448, RMSE = 1.8150, R² = 0.5017, sMAPE = 38.0175%\n", "Horizon t+11: MAE = 1.2667, RMSE = 1.8402, R² = 0.4878, sMAPE = 38.7166%\n", "Horizon t+12: MAE = 1.2813, RMSE = 1.8593, R² = 0.4771, sMAPE = 39.1765%\n", "Horizon t+13: MAE = 1.2964, RMSE = 1.8737, R² = 0.4690, sMAPE = 39.7899%\n", "Horizon t+14: MAE = 1.3014, RMSE = 1.8786, R² = 0.4663, sMAPE = 40.0022%\n", "Horizon t+15: MAE = 1.3104, RMSE = 1.8895, R² = 0.4601, sMAPE = 40.2827%\n", "Horizon t+16: MAE = 1.3148, RMSE = 1.8991, R² = 0.4546, sMAPE = 40.3995%\n", "Horizon t+17: MAE = 1.3178, RMSE = 1.9081, R² = 0.4494, sMAPE = 40.5272%\n", "Horizon t+18: MAE = 1.3225, RMSE = 1.9172, R² = 0.4442, sMAPE = 40.6881%\n", "Horizon t+19: MAE = 1.3280, RMSE = 1.9256, R² = 0.4394, sMAPE = 40.8952%\n", "Horizon t+20: MAE = 1.3385, RMSE = 1.9381, R² = 0.4321, sMAPE = 41.2915%\n", "Horizon t+21: MAE = 1.3461, RMSE = 1.9509, R² = 0.4247, sMAPE = 41.5660%\n", "Horizon t+22: MAE = 1.3536, RMSE = 1.9549, R² = 0.4224, sMAPE = 41.8912%\n", "Horizon t+23: MAE = 1.3594, RMSE = 1.9636, R² = 0.4173, sMAPE = 42.0961%\n", "Horizon t+24: MAE = 1.3609, RMSE = 1.9666, R² = 0.4156, sMAPE = 42.2273%\n", "Horizon t+25: MAE = 1.3664, RMSE = 1.9697, R² = 0.4139, sMAPE = 42.4876%\n", "Horizon t+26: MAE = 1.3732, RMSE = 1.9720, R² = 0.4126, sMAPE = 42.7920%\n", "Horizon t+27: MAE = 1.3817, RMSE = 1.9734, R² = 0.4119, sMAPE = 43.1998%\n", "Horizon t+28: MAE = 1.3820, RMSE = 1.9746, R² = 0.4113, sMAPE = 43.3586%\n", "Horizon t+29: MAE = 1.3849, RMSE = 1.9753, R² = 0.4110, sMAPE = 43.5867%\n", "Horizon t+30: MAE = 1.3864, RMSE = 1.9776, R² = 0.4097, sMAPE = 43.6780%\n", "Horizon t+31: MAE = 1.3887, RMSE = 1.9772, R² = 0.4100, sMAPE = 43.7731%\n", "Horizon t+32: MAE = 1.3891, RMSE = 1.9756, R² = 0.4111, sMAPE = 43.7944%\n", "Horizon t+33: MAE = 1.3900, RMSE = 1.9761, R² = 0.4109, sMAPE = 43.8860%\n", "Horizon t+34: MAE = 1.3879, RMSE = 1.9781, R² = 0.4098, sMAPE = 43.8564%\n", "Horizon t+35: MAE = 1.3907, RMSE = 1.9802, R² = 0.4087, sMAPE = 43.9122%\n", "Horizon t+36: MAE = 1.3932, RMSE = 1.9840, R² = 0.4065, sMAPE = 44.0487%\n", "Horizon t+37: MAE = 1.3928, RMSE = 1.9881, R² = 0.4041, sMAPE = 44.0679%\n", "Horizon t+38: MAE = 1.3950, RMSE = 1.9946, R² = 0.4003, sMAPE = 44.1593%\n", "Horizon t+39: MAE = 1.3949, RMSE = 1.9987, R² = 0.3979, sMAPE = 44.1417%\n", "Horizon t+40: MAE = 1.3986, RMSE = 2.0032, R² = 0.3953, sMAPE = 44.2218%\n", "Horizon t+41: MAE = 1.4068, RMSE = 2.0068, R² = 0.3932, sMAPE = 44.4488%\n", "Horizon t+42: MAE = 1.4140, RMSE = 2.0155, R² = 0.3881, sMAPE = 44.6198%\n", "Horizon t+43: MAE = 1.4196, RMSE = 2.0207, R² = 0.3850, sMAPE = 44.7716%\n", "Horizon t+44: MAE = 1.4242, RMSE = 2.0273, R² = 0.3811, sMAPE = 44.9137%\n", "Horizon t+45: MAE = 1.4308, RMSE = 2.0350, R² = 0.3765, sMAPE = 45.1017%\n", "Horizon t+46: MAE = 1.4358, RMSE = 2.0411, R² = 0.3729, sMAPE = 45.2193%\n", "Horizon t+47: MAE = 1.4398, RMSE = 2.0430, R² = 0.3718, sMAPE = 45.4086%\n", "Horizon t+48: MAE = 1.4371, RMSE = 2.0416, R² = 0.3728, sMAPE = 45.3136%\n", "Horizon t+49: MAE = 1.4333, RMSE = 2.0391, R² = 0.3744, sMAPE = 45.1759%\n", "Horizon t+50: MAE = 1.4356, RMSE = 2.0428, R² = 0.3722, sMAPE = 45.2171%\n", "Horizon t+51: MAE = 1.4362, RMSE = 2.0456, R² = 0.3706, sMAPE = 45.2601%\n", "Horizon t+52: MAE = 1.4359, RMSE = 2.0492, R² = 0.3686, sMAPE = 45.1932%\n", "Horizon t+53: MAE = 1.4369, RMSE = 2.0496, R² = 0.3684, sMAPE = 45.2519%\n", "Horizon t+54: MAE = 1.4367, RMSE = 2.0510, R² = 0.3677, sMAPE = 45.2345%\n", "Horizon t+55: MAE = 1.4367, RMSE = 2.0486, R² = 0.3692, sMAPE = 45.2381%\n", "Horizon t+56: MAE = 1.4374, RMSE = 2.0488, R² = 0.3692, sMAPE = 45.3098%\n", "Horizon t+57: MAE = 1.4332, RMSE = 2.0453, R² = 0.3715, sMAPE = 45.2054%\n", "Horizon t+58: MAE = 1.4353, RMSE = 2.0468, R² = 0.3706, sMAPE = 45.2653%\n", "Horizon t+59: MAE = 1.4346, RMSE = 2.0447, R² = 0.3720, sMAPE = 45.1894%\n", "Horizon t+60: MAE = 1.4336, RMSE = 2.0402, R² = 0.3749, sMAPE = 45.1647%\n", "Horizon t+61: MAE = 1.4311, RMSE = 2.0378, R² = 0.3765, sMAPE = 45.1002%\n", "Horizon t+62: MAE = 1.4297, RMSE = 2.0352, R² = 0.3782, sMAPE = 45.0542%\n", "Horizon t+63: MAE = 1.4285, RMSE = 2.0293, R² = 0.3819, sMAPE = 45.0642%\n", "Horizon t+64: MAE = 1.4280, RMSE = 2.0238, R² = 0.3854, sMAPE = 45.0972%\n", "Horizon t+65: MAE = 1.4272, RMSE = 2.0231, R² = 0.3859, sMAPE = 45.0882%\n", "Horizon t+66: MAE = 1.4285, RMSE = 2.0230, R² = 0.3860, sMAPE = 45.1522%\n", "Horizon t+67: MAE = 1.4264, RMSE = 2.0233, R² = 0.3859, sMAPE = 45.0740%\n", "Horizon t+68: MAE = 1.4259, RMSE = 2.0244, R² = 0.3854, sMAPE = 44.9789%\n", "Horizon t+69: MAE = 1.4284, RMSE = 2.0282, R² = 0.3832, sMAPE = 44.9888%\n", "Horizon t+70: MAE = 1.4303, RMSE = 2.0311, R² = 0.3815, sMAPE = 45.0414%\n", "Horizon t+71: MAE = 1.4326, RMSE = 2.0346, R² = 0.3795, sMAPE = 45.0921%\n", "Horizon t+72: MAE = 1.4338, RMSE = 2.0372, R² = 0.3780, sMAPE = 45.0948%\n", "Horizon t+73: MAE = 1.4307, RMSE = 2.0345, R² = 0.3796, sMAPE = 44.9976%\n", "Horizon t+74: MAE = 1.4305, RMSE = 2.0346, R² = 0.3795, sMAPE = 44.9625%\n", "Horizon t+75: MAE = 1.4331, RMSE = 2.0358, R² = 0.3787, sMAPE = 45.0219%\n", "Horizon t+76: MAE = 1.4312, RMSE = 2.0334, R² = 0.3802, sMAPE = 45.0077%\n", "Horizon t+77: MAE = 1.4318, RMSE = 2.0334, R² = 0.3801, sMAPE = 45.0056%\n", "Horizon t+78: MAE = 1.4314, RMSE = 2.0317, R² = 0.3811, sMAPE = 44.9363%\n", "Horizon t+79: MAE = 1.4288, RMSE = 2.0278, R² = 0.3834, sMAPE = 44.8809%\n", "Horizon t+80: MAE = 1.4275, RMSE = 2.0249, R² = 0.3850, sMAPE = 44.8874%\n", "Horizon t+81: MAE = 1.4274, RMSE = 2.0202, R² = 0.3878, sMAPE = 44.8739%\n", "Horizon t+82: MAE = 1.4251, RMSE = 2.0164, R² = 0.3900, sMAPE = 44.8517%\n", "Horizon t+83: MAE = 1.4255, RMSE = 2.0142, R² = 0.3913, sMAPE = 44.9311%\n", "Horizon t+84: MAE = 1.4216, RMSE = 2.0114, R² = 0.3929, sMAPE = 44.8045%\n", "Horizon t+85: MAE = 1.4225, RMSE = 2.0124, R² = 0.3922, sMAPE = 44.8494%\n", "Horizon t+86: MAE = 1.4197, RMSE = 2.0127, R² = 0.3919, sMAPE = 44.8154%\n", "Horizon t+87: MAE = 1.4164, RMSE = 2.0110, R² = 0.3928, sMAPE = 44.7695%\n", "Horizon t+88: MAE = 1.4173, RMSE = 2.0104, R² = 0.3930, sMAPE = 44.8367%\n", "Horizon t+89: MAE = 1.4207, RMSE = 2.0117, R² = 0.3918, sMAPE = 44.9481%\n", "Horizon t+90: MAE = 1.4233, RMSE = 2.0129, R² = 0.3907, sMAPE = 45.0464%\n", "Horizon t+91: MAE = 1.4275, RMSE = 2.0172, R² = 0.3878, sMAPE = 45.2293%\n", "Horizon t+92: MAE = 1.4289, RMSE = 2.0189, R² = 0.3866, sMAPE = 45.2816%\n", "Horizon t+93: MAE = 1.4295, RMSE = 2.0208, R² = 0.3852, sMAPE = 45.3216%\n", "Horizon t+94: MAE = 1.4289, RMSE = 2.0217, R² = 0.3844, sMAPE = 45.2944%\n", "Horizon t+95: MAE = 1.4293, RMSE = 2.0201, R² = 0.3852, sMAPE = 45.2665%\n", "Horizon t+96: MAE = 1.4337, RMSE = 2.0223, R² = 0.3837, sMAPE = 45.3999%\n"]}], "source": ["# ============== 4. EVALUATE MODEL PERFORMANCE =============================\n", "\n", "epsilon = np.mean(station_engineered_data.target) # for sMAPE Small constant to stabilize division\n", "predictions = model.predict(X_validate)\n", "\n", "# # @@@@@@@@@@@@@@@@ EXECUTION @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@\n", "\n", "overall_mae   = mean_absolute_error(y_validate, predictions)\n", "overall_rmse  = np.sqrt(mean_squared_error(y_validate, predictions))\n", "overall_r2    = r2_score(y_validate, predictions)\n", "overall_smape = eval.smape(y_validate, predictions, epsilon=epsilon)\n", "\n", "print(f\"\\n Overall Evaluation Metrics on VALIDATION SET \\n: MAE :, {overall_mae} RMSE : {overall_rmse}, R² : {overall_r2} sMAPE (%) :, {overall_smape}\")\n", "\n", "# === === ====== Evaluate Horizon Prediction ====== ===== ======= ==========\n", "\n", "horizon_test = station_engineered_data.iloc[split_idx:]['horizon']\n", "unique_horizons = sorted(horizon_test.unique())\n", "\n", "print(\"\\n Per-Horizon Evaluation Metrics: on VALIDATION SET \")\n", "for h in unique_horizons:\n", "    mask = (horizon_test == h)\n", "    # Ensure we use the NumPy arrays for slicing as well\n", "    y_h = y_validate[mask.values]\n", "    pred_h = predictions[mask.values]\n", "    mae_h   = mean_absolute_error(y_h, pred_h)\n", "    rmse_h  = np.sqrt(mean_squared_error(y_h, pred_h))\n", "    r2_h    = r2_score(y_h, pred_h)\n", "    smape_h = eval.smape(y_h, pred_h, epsilon=epsilon)\n", "    print(f\"Horizon t+{int(h):2d}: MAE = {mae_h:.4f}, RMSE = {rmse_h:.4f}, R² = {r2_h:.4f}, sMAPE = {smape_h:.4f}%\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " Overall Evaluation Metrics on TEST SET \n", ": MAE :, 1.2904730236862214 RMSE : 1.8130379272373998, R² : 0.18369883320530855 sMAPE (%) :, 45.60246837672171\n", "\n", " Per-Horizon Evaluation Metrics on TEST SET:\n", "Horizon t+ 1: MAE = 0.7131, RMSE = 1.1053, R² = 0.6953, sMAPE = 26.2390%\n", "Horizon t+ 2: MAE = 0.7831, RMSE = 1.2147, R² = 0.6320, sMAPE = 28.2557%\n", "Horizon t+ 3: MAE = 0.8623, RMSE = 1.3320, R² = 0.5576, sMAPE = 30.6643%\n", "Horizon t+ 4: MAE = 0.9249, RMSE = 1.4125, R² = 0.5025, sMAPE = 32.9244%\n", "Horizon t+ 5: MAE = 0.9743, RMSE = 1.4862, R² = 0.4493, sMAPE = 34.3000%\n", "Horizon t+ 6: MAE = 1.0321, RMSE = 1.5452, R² = 0.4047, sMAPE = 36.5250%\n", "Horizon t+ 7: MAE = 1.0517, RMSE = 1.5722, R² = 0.3838, sMAPE = 37.1803%\n", "Horizon t+ 8: MAE = 1.0971, RMSE = 1.6282, R² = 0.3392, sMAPE = 38.5017%\n", "Horizon t+ 9: MAE = 1.1167, RMSE = 1.6520, R² = 0.3197, sMAPE = 39.1654%\n", "Horizon t+10: MAE = 1.1482, RMSE = 1.6816, R² = 0.2953, sMAPE = 40.3449%\n", "Horizon t+11: MAE = 1.1732, RMSE = 1.7063, R² = 0.2744, sMAPE = 41.1914%\n", "Horizon t+12: MAE = 1.1813, RMSE = 1.7151, R² = 0.2670, sMAPE = 41.5285%\n", "Horizon t+13: MAE = 1.1958, RMSE = 1.7278, R² = 0.2562, sMAPE = 42.1772%\n", "Horizon t+14: MAE = 1.2053, RMSE = 1.7393, R² = 0.2463, sMAPE = 42.5569%\n", "Horizon t+15: MAE = 1.2116, RMSE = 1.7447, R² = 0.2416, sMAPE = 42.8343%\n", "Horizon t+16: MAE = 1.2242, RMSE = 1.7576, R² = 0.2305, sMAPE = 43.1172%\n", "Horizon t+17: MAE = 1.2283, RMSE = 1.7675, R² = 0.2218, sMAPE = 43.1667%\n", "Horizon t+18: MAE = 1.2303, RMSE = 1.7719, R² = 0.2180, sMAPE = 43.1409%\n", "Horizon t+19: MAE = 1.2352, RMSE = 1.7782, R² = 0.2125, sMAPE = 43.2291%\n", "Horizon t+20: MAE = 1.2397, RMSE = 1.7848, R² = 0.2067, sMAPE = 43.3300%\n", "Horizon t+21: MAE = 1.2448, RMSE = 1.7900, R² = 0.2021, sMAPE = 43.5052%\n", "Horizon t+22: MAE = 1.2533, RMSE = 1.7977, R² = 0.1953, sMAPE = 43.7357%\n", "Horizon t+23: MAE = 1.2625, RMSE = 1.8056, R² = 0.1883, sMAPE = 43.9957%\n", "Horizon t+24: MAE = 1.2751, RMSE = 1.8153, R² = 0.1796, sMAPE = 44.4248%\n", "Horizon t+25: MAE = 1.2823, RMSE = 1.8216, R² = 0.1739, sMAPE = 44.6303%\n", "Horizon t+26: MAE = 1.2840, RMSE = 1.8246, R² = 0.1714, sMAPE = 44.6804%\n", "Horizon t+27: MAE = 1.2846, RMSE = 1.8292, R² = 0.1672, sMAPE = 44.6663%\n", "Horizon t+28: MAE = 1.2887, RMSE = 1.8353, R² = 0.1617, sMAPE = 44.7999%\n", "Horizon t+29: MAE = 1.2919, RMSE = 1.8382, R² = 0.1591, sMAPE = 44.9359%\n", "Horizon t+30: MAE = 1.2944, RMSE = 1.8386, R² = 0.1588, sMAPE = 45.0851%\n", "Horizon t+31: MAE = 1.2987, RMSE = 1.8383, R² = 0.1592, sMAPE = 45.3343%\n", "Horizon t+32: MAE = 1.2998, RMSE = 1.8371, R² = 0.1604, sMAPE = 45.4509%\n", "Horizon t+33: MAE = 1.3018, RMSE = 1.8354, R² = 0.1620, sMAPE = 45.5965%\n", "Horizon t+34: MAE = 1.2985, RMSE = 1.8311, R² = 0.1659, sMAPE = 45.5386%\n", "Horizon t+35: MAE = 1.3006, RMSE = 1.8284, R² = 0.1685, sMAPE = 45.6589%\n", "Horizon t+36: MAE = 1.3054, RMSE = 1.8293, R² = 0.1677, sMAPE = 45.8437%\n", "Horizon t+37: MAE = 1.3080, RMSE = 1.8311, R² = 0.1661, sMAPE = 45.9552%\n", "Horizon t+38: MAE = 1.3109, RMSE = 1.8326, R² = 0.1648, sMAPE = 46.0585%\n", "Horizon t+39: MAE = 1.3139, RMSE = 1.8348, R² = 0.1629, sMAPE = 46.1380%\n", "Horizon t+40: MAE = 1.3204, RMSE = 1.8402, R² = 0.1580, sMAPE = 46.3764%\n", "Horizon t+41: MAE = 1.3263, RMSE = 1.8451, R² = 0.1536, sMAPE = 46.6091%\n", "Horizon t+42: MAE = 1.3300, RMSE = 1.8476, R² = 0.1513, sMAPE = 46.7634%\n", "Horizon t+43: MAE = 1.3298, RMSE = 1.8485, R² = 0.1506, sMAPE = 46.8160%\n", "Horizon t+44: MAE = 1.3280, RMSE = 1.8479, R² = 0.1512, sMAPE = 46.6859%\n", "Horizon t+45: MAE = 1.3295, RMSE = 1.8450, R² = 0.1540, sMAPE = 46.7621%\n", "Horizon t+46: MAE = 1.3289, RMSE = 1.8426, R² = 0.1562, sMAPE = 46.7864%\n", "Horizon t+47: MAE = 1.3295, RMSE = 1.8439, R² = 0.1551, sMAPE = 46.9000%\n", "Horizon t+48: MAE = 1.3290, RMSE = 1.8441, R² = 0.1550, sMAPE = 46.9396%\n", "Horizon t+49: MAE = 1.3271, RMSE = 1.8439, R² = 0.1552, sMAPE = 46.8682%\n", "Horizon t+50: MAE = 1.3224, RMSE = 1.8398, R² = 0.1590, sMAPE = 46.7061%\n", "Horizon t+51: MAE = 1.3222, RMSE = 1.8399, R² = 0.1590, sMAPE = 46.7527%\n", "Horizon t+52: MAE = 1.3228, RMSE = 1.8397, R² = 0.1593, sMAPE = 46.8061%\n", "Horizon t+53: MAE = 1.3244, RMSE = 1.8412, R² = 0.1579, sMAPE = 46.9084%\n", "Horizon t+54: MAE = 1.3238, RMSE = 1.8401, R² = 0.1590, sMAPE = 46.9820%\n", "Horizon t+55: MAE = 1.3258, RMSE = 1.8420, R² = 0.1574, sMAPE = 47.0324%\n", "Horizon t+56: MAE = 1.3285, RMSE = 1.8444, R² = 0.1554, sMAPE = 47.1173%\n", "Horizon t+57: MAE = 1.3284, RMSE = 1.8429, R² = 0.1569, sMAPE = 47.1292%\n", "Horizon t+58: MAE = 1.3308, RMSE = 1.8439, R² = 0.1561, sMAPE = 47.3043%\n", "Horizon t+59: MAE = 1.3332, RMSE = 1.8434, R² = 0.1566, sMAPE = 47.4512%\n", "Horizon t+60: MAE = 1.3379, RMSE = 1.8438, R² = 0.1565, sMAPE = 47.6522%\n", "Horizon t+61: MAE = 1.3406, RMSE = 1.8450, R² = 0.1554, sMAPE = 47.7432%\n", "Horizon t+62: MAE = 1.3467, RMSE = 1.8512, R² = 0.1499, sMAPE = 47.9592%\n", "Horizon t+63: MAE = 1.3525, RMSE = 1.8590, R² = 0.1429, sMAPE = 48.1057%\n", "Horizon t+64: MAE = 1.3571, RMSE = 1.8683, R² = 0.1344, sMAPE = 48.2383%\n", "Horizon t+65: MAE = 1.3671, RMSE = 1.8823, R² = 0.1215, sMAPE = 48.5226%\n", "Horizon t+66: MAE = 1.3709, RMSE = 1.8890, R² = 0.1154, sMAPE = 48.6235%\n", "Horizon t+67: MAE = 1.3767, RMSE = 1.8934, R² = 0.1114, sMAPE = 48.8147%\n", "Horizon t+68: MAE = 1.3783, RMSE = 1.8966, R² = 0.1085, sMAPE = 48.8403%\n", "Horizon t+69: MAE = 1.3802, RMSE = 1.8972, R² = 0.1081, sMAPE = 48.9074%\n", "Horizon t+70: MAE = 1.3803, RMSE = 1.8969, R² = 0.1085, sMAPE = 48.9070%\n", "Horizon t+71: MAE = 1.3812, RMSE = 1.8969, R² = 0.1087, sMAPE = 48.9829%\n", "Horizon t+72: MAE = 1.3806, RMSE = 1.8954, R² = 0.1102, sMAPE = 49.0165%\n", "Horizon t+73: MAE = 1.3843, RMSE = 1.9002, R² = 0.1058, sMAPE = 49.1187%\n", "Horizon t+74: MAE = 1.3830, RMSE = 1.8989, R² = 0.1072, sMAPE = 49.0502%\n", "Horizon t+75: MAE = 1.3825, RMSE = 1.8970, R² = 0.1091, sMAPE = 49.1108%\n", "Horizon t+76: MAE = 1.3801, RMSE = 1.8935, R² = 0.1125, sMAPE = 49.1617%\n", "Horizon t+77: MAE = 1.3826, RMSE = 1.8929, R² = 0.1132, sMAPE = 49.2493%\n", "Horizon t+78: MAE = 1.3831, RMSE = 1.8914, R² = 0.1147, sMAPE = 49.2298%\n", "Horizon t+79: MAE = 1.3840, RMSE = 1.8920, R² = 0.1143, sMAPE = 49.2299%\n", "Horizon t+80: MAE = 1.3837, RMSE = 1.8918, R² = 0.1146, sMAPE = 49.2565%\n", "Horizon t+81: MAE = 1.3883, RMSE = 1.8960, R² = 0.1107, sMAPE = 49.3791%\n", "Horizon t+82: MAE = 1.3880, RMSE = 1.8940, R² = 0.1126, sMAPE = 49.4182%\n", "Horizon t+83: MAE = 1.3901, RMSE = 1.8940, R² = 0.1127, sMAPE = 49.4834%\n", "Horizon t+84: MAE = 1.3913, RMSE = 1.8941, R² = 0.1127, sMAPE = 49.4846%\n", "Horizon t+85: MAE = 1.3918, RMSE = 1.8942, R² = 0.1126, sMAPE = 49.5158%\n", "Horizon t+86: MAE = 1.3946, RMSE = 1.8961, R² = 0.1109, sMAPE = 49.6343%\n", "Horizon t+87: MAE = 1.3966, RMSE = 1.8974, R² = 0.1098, sMAPE = 49.7141%\n", "Horizon t+88: MAE = 1.3963, RMSE = 1.8999, R² = 0.1075, sMAPE = 49.6987%\n", "Horizon t+89: MAE = 1.3962, RMSE = 1.9004, R² = 0.1072, sMAPE = 49.6799%\n", "Horizon t+90: MAE = 1.3937, RMSE = 1.8985, R² = 0.1089, sMAPE = 49.5803%\n", "Horizon t+91: MAE = 1.3924, RMSE = 1.8998, R² = 0.1078, sMAPE = 49.5265%\n", "Horizon t+92: MAE = 1.3922, RMSE = 1.8977, R² = 0.1099, sMAPE = 49.5190%\n", "Horizon t+93: MAE = 1.3900, RMSE = 1.8948, R² = 0.1127, sMAPE = 49.4432%\n", "Horizon t+94: MAE = 1.3871, RMSE = 1.8937, R² = 0.1137, sMAPE = 49.3514%\n", "Horizon t+95: MAE = 1.3835, RMSE = 1.8896, R² = 0.1176, sMAPE = 49.2553%\n", "Horizon t+96: MAE = 1.3802, RMSE = 1.8858, R² = 0.1213, sMAPE = 49.1495%\n"]}], "source": ["# ============== 5. EVALUATE ON A TEST SET =============================\n", "\n", "test_feature_generation  = U.feature_engineering(df=test_set, \n", "                                          target_feature=target_feature, \n", "                                          horizon_features=horizon_features, \n", "                                          window_features=window_features,\n", "                                          forecast_features=forecast_features, \n", "                                          forecast_horizon=forecast_horizon,\n", "                                          time_granularity=time_granularity)\n", "\n", "test_engineered_data = test_feature_generation.df\n", "del test_feature_generation\n", "\n", "Xttt = test_engineered_data[feature_columns]\n", "yttt = test_engineered_data['target']\n", "\n", "predictions = model.predict(Xttt)\n", "\n", "# --- Evaluate the Model ---\n", "overall_mae   = mean_absolute_error(yttt, predictions)\n", "overall_rmse  = np.sqrt(mean_squared_error(yttt, predictions))\n", "overall_r2    = r2_score(yttt, predictions)\n", "overall_smape = eval.smape(yttt, predictions, epsilon=epsilon)\n", "\n", "print(f\"\\n Overall Evaluation Metrics on TEST SET \\n: MAE :, {overall_mae} RMSE : {overall_rmse}, R² : {overall_r2} sMAPE (%) :, {overall_smape}\")\n", "\n", "# === === ====== Evaluate Horizon Prediction ====== ===== ======= ==========\n", "\n", "horizon_test = test_engineered_data['horizon']\n", "unique_horizons = sorted(horizon_test.unique())\n", "\n", "print(\"\\n Per-Horizon Evaluation Metrics on TEST SET:\")\n", "for h in unique_horizons:\n", "    mask = (horizon_test == h)\n", "    # Ensure we use the NumPy arrays for slicing as well\n", "    y_h = yttt[mask.values]\n", "    pred_h = predictions[mask.values]\n", "    mae_h   = mean_absolute_error(y_h, pred_h)\n", "    rmse_h  = np.sqrt(mean_squared_error(y_h, pred_h))\n", "    r2_h    = r2_score(y_h, pred_h)\n", "    smape_h = eval.smape(y_h, pred_h, epsilon=epsilon)\n", "    print(f\"Horizon t+{int(h):2d}: MAE = {mae_h:.4f}, RMSE = {rmse_h:.4f}, R² = {r2_h:.4f}, sMAPE = {smape_h:.4f}%\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}