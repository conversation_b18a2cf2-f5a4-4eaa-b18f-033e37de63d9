import gzip
import logging
import os
import shutil
from datetime import datetime
from logging.handlers import RotatingFileHandler


def setup_logger(name: str, direction: str = None, console_output: bool = True) -> logging.Logger:
    """配置日志，确保日志文件始终以utf-8编码写入，避免乱码问题"""
    # 确保日志目录存在
    if direction:
        log_dir = os.path.join(os.getcwd(), "logs", direction)
    else:
        log_dir = os.path.join(os.getcwd(), "logs")
    os.makedirs(log_dir, exist_ok=True)

    logger = logging.getLogger(name)
    
    # 如果logger已经有处理器，直接返回
    if logger.handlers:
        return logger
        
    # 从环境变量获取日志级别，支持多种格式
    log_level_str = os.environ.get('LOG_LEVEL', 'INFO').upper()
    log_level_map = {
        'DEBUG': logging.DEBUG,
        'INFO': logging.INFO,
        'WARNING': logging.WARNING,
        'ERROR': logging.ERROR,
        'CRITICAL': logging.CRITICAL
    }
    
    log_level = log_level_map.get(log_level_str, logging.INFO)
    logger.setLevel(log_level)

    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # 控制台处理器 - 根据console_output参数决定是否添加
    if console_output:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

    # 文件处理器，强制指定utf-8编码，避免日志乱码
    log_file = os.path.join(log_dir, f"{name}.log")

    # 自定义的RotatingFileHandler，确保轮转时使用时间戳
    class TimedRotatingFileHandler(RotatingFileHandler):
        def __init__(self, filename, maxBytes=0, backupCount=0, encoding=None):
            super().__init__(filename, maxBytes=maxBytes, backupCount=backupCount, encoding=encoding)
            self.baseFilename = filename
            self.maxBytes = maxBytes
            self.backupCount = backupCount
            self.encoding = encoding

        def doRollover(self):
            if self.stream:
                self.stream.close()
                self.stream = None

            # 生成时间戳
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            # 修改文件名格式，将时间戳放在.log之前
            rotated_log = f"{os.path.splitext(self.baseFilename)[0]}_{timestamp}.log"

            # 重命名当前日志文件
            if os.path.exists(self.baseFilename):
                os.rename(self.baseFilename, rotated_log)

            # 压缩轮转后的文件
            if os.path.exists(rotated_log):
                gz_log = f"{rotated_log}.gz"
                try:
                    with open(rotated_log, 'rb') as f_in:
                        with gzip.open(gz_log, 'wb') as f_out:
                            shutil.copyfileobj(f_in, f_out)
                    os.remove(rotated_log)  # 删除原始文件
                    logger.debug(f"日志文件压缩完成: {rotated_log} -> {gz_log}")
                except Exception as e:
                    logger.error(f"压缩日志文件失败 {rotated_log}: {str(e)}")

            # 创建新的日志文件
            self.stream = self._open()

    # 使用自定义的RotatingFileHandler
    file_handler = TimedRotatingFileHandler(
        log_file,
        maxBytes=100 * 1024 * 1024,  # 100MB
        backupCount=100,
        encoding='utf-8'  # 明确指定编码，防止乱码
    )
    file_handler.setLevel(log_level)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    return logger
