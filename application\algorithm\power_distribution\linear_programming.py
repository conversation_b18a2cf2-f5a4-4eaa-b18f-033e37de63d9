import os
import time
import datetime
import bisect
import random
import json
from typing import List, <PERSON><PERSON>
from collections import OrderedDict
from decimal import Decimal

from ortools.linear_solver import pywraplp

from application.algorithm.power_distribution.validator import InputData
from application.algorithm.power_distribution.n1_pile_distribution_dp import N1PileBestPowerDistribution
from application.utils.logger import setup_logger


logger = setup_logger("power_distribution_linear_programming", direction="algorithm", console_output=False)


class DecimalEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理Decimal类型"""
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        return super(DecimalEncoder, self).default(obj)


def calculate_objective_value(demand: list, solution: list) -> float:
    n, m = len(demand), len(demand[0])

    objective = 0
    for i in range(n):
        for j in range(m):
            objective += abs(solution[i][j] - demand[i][j])
    return round(objective, 4)


def get_split_pile_indexs(piles_info: OrderedDict) -> <PERSON>ple[List[int], List[int]]:
    not_n1_pile_indexs = []
    n1_pile_indexs = []
    for i, (p_sn, p_info) in enumerate(piles_info.items()):
        # TODO: n1桩判断条件需要优化或修改
        if p_info["gun_num"] > 2:
            n1_pile_indexs.append(i)
        else:
            not_n1_pile_indexs.append(i)
    return not_n1_pile_indexs, n1_pile_indexs


def get_validated_processed_data(input_data: dict):
    validated_data = InputData(**input_data)
    # 桩信息，按桩编号排序
    validated_data.site_pile_info.sort()

    piles_info = OrderedDict()
    for pile_sn in validated_data.site_pile_info:
        piles_info[pile_sn] = {"gun_num": 0, "rated_power": 0.0, "directed_connections": None,
                               "module_limits": [], "gun_demands": [], "vehicle_vols": []}

    max_gun_num = max([md_item.gunNum for md_item in validated_data.module_info])
    for md_item in validated_data.module_info:
        p_sn = md_item.sn
        piles_info[p_sn]["gun_num"] = md_item.gunNum
        piles_info[p_sn]["rated_power"] = md_item.ratedPower
        piles_info[p_sn]["directed_connections"] = md_item.directedConnections
        piles_info[p_sn]["gun_demands"] = [0] * max_gun_num
        piles_info[p_sn]["vehicle_vols"] = [0] * max_gun_num
        for ml in md_item.moduleList:
            piles_info[p_sn]["module_limits"].append(ml.unitPower * ml.unitNum)

    cur_utc_ts = int(datetime.datetime.now(datetime.UTC).timestamp())
    for k, v in validated_data.demand_info.items():
        p_sn, g_no = k.split("_")
        # 60s 一个采样点
        time_list = [v.predicted_time + i * 60 for i in range(len(v.vehicle_non_suppressed_power_list))]
        # 使用bisect包进行二分查找, 找到cur_utc_ts在time_list中的位置
        idx = bisect.bisect_right(time_list, cur_utc_ts) - 1
        g_demand = v.vehicle_non_suppressed_power_list[idx]

        piles_info[p_sn]["gun_demands"][int(g_no) - 1] = g_demand
        piles_info[p_sn]["vehicle_vols"][int(g_no) - 1] = v.vehicle_vol
    return piles_info, validated_data


class LinearProgrammingPowerDistributionSolver:
    def __init__(self, input_data: dict, default_delta: float = 0.0):
        self.input_data, self.default_delta = input_data, default_delta

        piles_info, validated_data = get_validated_processed_data(input_data)
        self.piles_info = piles_info
        self.validated_data = validated_data

        N, M = len(piles_info), max([p_info["gun_num"] for p_info in piles_info.values()])
        self.N, self.M = N, M

        (self.d_gun, self.vehicle_vols, self.pile_power,
         self.pile_module_power, self.delta) = self._get_initial_data(piles_info)

    def _get_initial_data(self, piles_info: OrderedDict):
        d_gun = [[0] * self.M for _ in range(self.N)]
        vehicle_vols = [[0] * self.M for _ in range(self.N)]
        pile_power = [0] * self.N
        pile_module_power = []
        for i, (p_sn, pinfo_item) in enumerate(piles_info.items()):
            for j in range(len(pinfo_item["gun_demands"])):
                d_gun[i][j] = pinfo_item["gun_demands"][j]
                vehicle_vols[i][j] = pinfo_item["vehicle_vols"][j]
            pile_power[i] = pinfo_item["rated_power"]
            pile_module_power.append(pinfo_item["module_limits"])

        delta = [[self.default_delta] * self.M for _ in range(self.N)]
        for i in range(self.N):
            for j in range(self.M):
                if d_gun[i][j] == 0:
                    delta[i][j] = 0
        return d_gun, vehicle_vols, pile_power, pile_module_power, delta

    def _create_vars(self, solver):
        # 创建变量
        pgun = []  # [[pgun_i_j, ...], ...]
        for i in range(self.N):
            temp = []
            for j in range(self.M):
                if self.d_gun[i][j] == 0:
                    var = solver.NumVar(0, 0, f'pgun_{i}_{j}')  # 上下界都是0, 没有功率分配
                else:
                    var = solver.NumVar(0, self.pile_power[i], f'pgun_{i}_{j}')
                temp.append(var)
            pgun.append(temp)
        return pgun

    def _create_objective(self, solver, pgun):
        # 目标函数
        objective = solver.Objective()

        # 创建辅助变量来处理绝对值
        z = [[solver.NumVar(0, solver.infinity(), f'z_{i}_{j}') for j in range(self.M)] for i in range(self.N)]
        # 添加绝对值约束
        for i in range(self.N):
            for j in range(self.M):
                if self.d_gun[i][j] == 0:  # 只对有需求的枪头添加约束
                    continue

                # 添加约束：z[i][j] >= |DGun[i][j] - pgun[i][j] - Delta[i][j]|
                expr = self.d_gun[i][j] - pgun[i][j] - self.delta[i][j]
                solver.Add(z[i][j] >= expr)
                solver.Add(z[i][j] >= -expr)

        # 目标函数设置为所有 z[i][j] 的和
        for i in range(self.N):
            for j in range(self.M):
                if self.d_gun[i][j] == 0:
                    continue
                objective.SetCoefficient(z[i][j], 1.0)
        objective.SetMinimization()
        return objective

    def _create_constraints(self, solver, pgun):
        # 1.场站功率限制 + 储能和光伏功率限制
        total_power_limit = (self.validated_data.pv_power + self.validated_data.es_power +
                             self.validated_data.site_demand_limit)
        solver.Add(sum([pgun[i][j] for i in range(self.N) for j in range(self.M)]) <= total_power_limit)

        # 2. 桩功率限制
        for i in range(self.N):
            solver.Add(sum([pgun[i][j] for j in range(self.M)]) <= self.pile_power[i])

        # 3. 非N1桩的模组功率限制
        not_n1_pile_indexs, n1_pile_indexs = get_split_pile_indexs(self.piles_info)
        for i in not_n1_pile_indexs:
            for j in range(self.M):
                if self.d_gun[i][j] == 0:
                    continue
                additional_power = sum([self.pile_module_power[i][k] for k in range(2) if k != j
                                        and self.d_gun[i][k] == 0])
                solver.Add(pgun[i][j] <= self.pile_module_power[i][j] + additional_power)

        # 4. N1桩的模组功率限制
        for i in n1_pile_indexs:
            pile_sn = self.validated_data.site_pile_info[i]
            directed_connections = self.piles_info[pile_sn]["directed_connections"]
            gun_demands, module_limits, vehicle_vols = self.d_gun[i], self.pile_module_power[i], self.vehicle_vols[i]
            n1_pile = N1PileBestPowerDistribution(
                gun_demands, module_limits, directed_connections, vehicle_vols=vehicle_vols)
            _, best_power_distribution = n1_pile.get_best_power_distribution()
            for j in range(self.M):
                if self.d_gun[i][j] == 0:
                    continue
                limit_p = sum([module_limits[k - 1] for k in best_power_distribution[j]])
                solver.Add(pgun[i][j] <= limit_p)

    def _get_format_result(self, solver, status, pgun):
        ret_values = {
            "status": False,
            "scheduling_time": int(time.time()),
            "site_charger_power_allocations": {}
        }
        pgun_solver_values = [[0 for _ in range(self.M)] for _ in range(self.N)]
        if status == solver.OPTIMAL:
            ret_values["status"] = True
            for i in range(self.N):
                for j in range(self.M):
                    if self.d_gun[i][j] == 0:
                        continue
                    p_sn = self.validated_data.site_pile_info[i]
                    key = f"{p_sn}_{j + 1}"
                    pgun_solution_value = round(pgun[i][j].solution_value(), 2)
                    ret_values["site_charger_power_allocations"][key] = pgun_solution_value
                    pgun_solver_values[i][j] = pgun_solution_value
        return ret_values, pgun_solver_values
    
    def _get_solver(self) -> Tuple[pywraplp.Solver, List[List[pywraplp.Variable]], pywraplp.Objective]:
        # 创建线性规划求解器
        solver = pywraplp.Solver.CreateSolver('GLOP')

        # 创建变量
        pgun = self._create_vars(solver)

        # 约束条件
        self._create_constraints(solver, pgun)

        # 目标函数, 使用绝对值差值
        objective = self._create_objective(solver, pgun)
        return solver, pgun, objective

    def get_solve_result(self):
        solver, pgun, objective = self._get_solver()
        # 求解
        status = solver.Solve()
        ret_values, pgun_solver_values = self._get_format_result(solver, status, pgun)

        # log input & output
        logger.info(f"\nInput："
                    f"\n\t{json.dumps(self.input_data, ensure_ascii=False, cls=DecimalEncoder)}"
                    f"\nOutput(线性规划求解器结果): \n\t{pgun_solver_values}")
        return ret_values, pgun_solver_values


class PowerDistributionMultipleSolutionsSolver(LinearProgrammingPowerDistributionSolver):
    def __get_current_solution(self, pgun):
        current_solution = []
        for i in range(self.N):
            row = []
            for j in range(self.M):
                row.append(round(pgun[i][j].solution_value(), 6))
            current_solution.append(row)
        return current_solution
    
    def __is_new_solution(self, current_solution: list, solutions: list, epsilon: float) -> bool:
        is_new_solution = True
        for existing_solution in solutions:
            is_same = True
            for i in range(self.N):
                for j in range(self.M):
                    if abs(current_solution[i][j] - existing_solution[i][j]) > epsilon:
                        is_same = False
                        break
                if not is_same:
                    break
            if is_same:
                is_new_solution = False
                break
        return is_new_solution
    
    def get_multiple_solutions(self, max_iterations: int = 10 * 1000, epsilon: float = 1e-6) -> List[List[List[float]]]:
        solver, pgun, objective = self._get_solver()

        # 第一次求解，获取最优目标值
        status = solver.Solve()
        if status != solver.OPTIMAL:
            return []
        optimal_value = objective.Value()

        # 存储所有找到的解
        solutions = []
        solutions.append(self.__get_current_solution(pgun))

        # 固定目标函数值
        # 创建辅助变量来处理绝对值约束
        z = [[solver.NumVar(0, solver.infinity(), f'z_fixed_{i}_{j}') for j in range(self.M)] for i in range(self.N)]
        # 添加绝对值约束
        for i in range(self.N):
            for j in range(self.M):
                if self.d_gun[i][j] == 0:
                    continue
                expr = self.d_gun[i][j] - pgun[i][j] - self.delta[i][j]
                solver.Add(z[i][j] >= expr)
                solver.Add(z[i][j] >= -expr)
        # 添加约束：目标函数值必须等于最优值
        objective_sum = solver.Sum([z[i][j] for i in range(self.N) for j in range(self.M) if self.d_gun[i][j] != 0])
        solver.Add(objective_sum == optimal_value)

        # 清空原始目标函数，准备随机化探索
        objective.Clear()
        objective.SetMinimization()

        # 枚举顶点解
        for _ in range(max_iterations):
            # 随机化目标函数以探索不同顶点, 为每个变量设置随机系数来探索不同的顶点解
            objective.Clear()
            for i in range(self.N):
                for j in range(self.M):
                    if self.d_gun[i][j] == 0:
                        continue
                    # 设置随机系数，范围在[-1, 1]之间
                    random_coeff = random.uniform(-1, 1)
                    objective.SetCoefficient(pgun[i][j], random_coeff)
            
            # 求解
            status = solver.Solve()
            if status != solver.OPTIMAL:
                continue

            # 获取当前解
            current_solution = self.__get_current_solution(pgun)

            # 检查是否为新解
            is_new_solution = self.__is_new_solution(current_solution, solutions, epsilon)
            if is_new_solution:
                solutions.append(current_solution)
                
                # 添加约束以排除当前解
                # 使用线性约束：所有变量的加权和不能等于当前解的加权和, 这样可以有效排除当前解，同时保持线性规划的特性
                constraint_expr = []
                solution_sum = 0
                for i in range(self.N):
                    for j in range(self.M):
                        if self.d_gun[i][j] != 0:
                            # 使用随机权重来构造约束
                            weight = random.uniform(0.1, 1.0)
                            constraint_expr.append(weight * pgun[i][j])
                            solution_sum += weight * current_solution[i][j]
                
                if constraint_expr:
                    # 添加约束：加权和不能等于当前解的加权和
                    constraint_sum = solver.Sum(constraint_expr)
                    # 使用两个不等式来排除等于的情况
                    solver.Add(constraint_sum >= solution_sum + epsilon)
                    # 或者使用相反的约束
                    # solver.Add(constraint_sum <= solution_sum - epsilon)
        return solutions


if __name__ == "__main__":
    # 使用测试数据进行测试
    base_dir = os.path.dirname(os.path.abspath(__file__))
    for file_name in os.listdir(os.path.join(base_dir, "test_data")):
        test_data_path = os.path.join(base_dir, "test_data", file_name)
        with open(test_data_path, "r") as f:
            t_input_data = json.load(f)
        print(f"\n\n" + "=" * 50 + f"测试数据: {file_name}" + "=" * 50)
        
        # 测试原始的线性规划求解器
        print("=" * 10 + "原始线性规划求解器 " + "=" * 10)
        t_solver = LinearProgrammingPowerDistributionSolver(t_input_data)
        t_ret_values, t_pgun_solver_values = t_solver.get_solve_result()
        print("功率分配需求：\n", t_solver.d_gun)
        print("原始线性规划求解器结果: \n", t_pgun_solver_values)
        print(f"原始线性规划求解器目标值: {calculate_objective_value(t_solver.d_gun, t_pgun_solver_values)}")
            
        # 测试多解求解器
        print("=" * 10 + "多解求解器" + "=" * 10)
        m_solver = PowerDistributionMultipleSolutionsSolver(t_input_data)
        m_solutions = m_solver.get_multiple_solutions()
        print("功率分配需求：\n", m_solver.d_gun)
        print(f"多解求解器结果[{len(m_solutions)}个解]: \n", m_solutions)
        for solution in m_solutions:
            print(f"多解求解器目标值: {calculate_objective_value(m_solver.d_gun, solution)}")
