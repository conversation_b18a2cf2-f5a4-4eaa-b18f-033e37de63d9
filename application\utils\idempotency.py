"""
幂等性工具模块

提供生成幂等串的功能，用于确保API调用的幂等性
"""

import hashlib
import json
from typing import Dict, Any


def generate_idempotency_key(data: Dict[str, Any], length: int = 20) -> str:
    """
    生成幂等串
    
    Args:
        data: 用于生成幂等串的数据字典
        length: 幂等串长度，默认20位
        
    Returns:
        str: 指定长度的十六进制幂等串
        
    Examples:
        >>> data = {"site_no": "SITE001", "timestamp": 1640995200000}
        >>> key = generate_idempotency_key(data)
        >>> len(key)
        20
        >>> isinstance(key, str)
        True
    """
    if not isinstance(data, dict):
        raise ValueError("data参数必须是字典类型")
        
    if length <= 0:
        raise ValueError("length参数必须大于0")
    
    # 将数据转换为JSON字符串并排序键值确保一致性
    json_str = json.dumps(data, sort_keys=True, ensure_ascii=False)
    
    # 使用SHA256生成哈希值
    sha256 = hashlib.sha256()
    sha256.update(json_str.encode('utf-8'))
    
    # 取指定长度的十六进制字符作为幂等串
    hash_hex = sha256.hexdigest()
    
    # 如果请求的长度超过哈希值长度，返回完整哈希值
    if length > len(hash_hex):
        return hash_hex
    
    return hash_hex[:length]
