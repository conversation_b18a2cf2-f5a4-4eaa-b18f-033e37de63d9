# 储能柜充放电功率规划算法 v5.0

## 算法概述

本算法是一个成熟的储能系统充放电功率规划优化器，基于线性规划方法，实现购电成本和需量费的双重优化。算法考虑了光伏发电预测、电价变化、需量费机制、储能系统物理限制等多个因素，生成24小时、15分钟粒度的充放电功率计划。

**🔥 v5.0新版本特性**：
- ✅ **轻量级功率变化惩罚机制**：解决时间粒度不匹配导致的多解问题
- ✅ **SOC回归约束**：确保24小时后储能状态完全恢复
- ✅ **经济效益分析**：自动计算储能系统节省的金额
- ✅ **需量费优化**：同时最小化电量费和需量费
- ✅ **平滑性保证**：减少充放电状态切换，延长设备寿命

## 核心特性

### 1. 优化目标
- **三重成本最小化**: 电量费 + 需量费 + 轻量级功率变化惩罚
- **需量峰值控制**: 通过储能调节避免过高的需量费用
- **状态切换优化**: 减少频繁充放电切换，保护设备
- **削峰填谷**: 配合光伏发电，减少高峰时段购电
- **经济效益量化**: 自动计算相对无储能系统的节省金额

### 2. 核心技术创新

#### 2.1 轻量级功率变化惩罚机制
```python
# 目标函数
minimize: 电量费 + 需量费 + 0.03 × Σ|P[i+1] - P[i]|
```

**解决的问题**：
- 电价时间粒度（1小时）与功率决策粒度（15分钟）不匹配
- 避免同一小时内的断续充放电现象
- 减少设备启停损耗

**技术优势**：
- 惩罚系数仅0.03，轻量级设计不影响经济性
- 保持线性规划框架，求解效率高
- 显著减少功率变化：平均减少19%的切换

#### 2.2 SOC回归约束
```python
# 等式约束
Σ(P_charge[i] × efficiency × 0.25 - P_discharge[i] × 0.25) = 0
```

**功能特点**：
- 确保24小时后SOC = 初始SOC
- 保证策略可重复执行
- 维持储能系统长期稳定运行

#### 2.3 经济效益自动计算
```python
# 成本对比
无储能成本 = 电量费_无储能 + 需量费_无储能
有储能成本 = 电量费_有储能 + 需量费_有储能
节省金额 = 无储能成本 - 有储能成本
```

### 3. 约束条件
- **SOC约束**: 严格遵守最低和最高SOC限制（20%-95%）
- **功率约束**: 基于储能额定功率的充放电限制
- **SOC回归约束**: 确保24小时后回到初始状态
- **容量约束**: 基于电池总容量进行规划
- **循环约束**: 限制每日充放电循环次数（≤2次）
- **需量约束**: 考虑需量限制和计费时段
- **功率变化约束**: 轻量级惩罚减少状态切换

### 4. 输入参数

```python
inputs = {
    'start_time': '2025-05-21 12:30:00',  # 数据起始时间
    'pv_predicted_list': [15, 16, ..., 9],  # 光伏24h发电预测，15分钟间隔，96个点
    'hybrid_load_predicted_list': [20, 25, ..., 15],  # 负载预测，15分钟间隔，96个点
    'electricity_purchase_price_list': [1.5, 1.2, ..., 0.6],  # 24h购电价格，1h间隔
    'es_soc': 50,                          # 当前电池SOC (%)
    'es_total_energy': 100,                # 电池总容量 (kWh)
    'es_rated_power': 100,                 # 储能额定功率 (kW)
    'site_grid_limit': 1200,               # 场站电网功率限制 (kW)
    'demand_data': [                       # 需量费数据
        {
            "start_time": "2025-05-21 00:00:00",
            "end_time": "2025-06-21 23:59:00",
            "price": 0.875,                # 需量费单价 (美元/kW)
            "unit": "dollar",
            "total_demand_target": 100,    # 需量限制 (kW)
            "target_demand_warning_ratio": 95.00
        }
    ],
    'min_soc': 20,                         # 最低SOC限制 (%)
    'max_soc': 90                          # 最高SOC限制 (%)
}
```

### 5. 输出结果

```python
# 优化结果
result = {
    'scheduling_time': 1748327973,                    # 调度时间戳
    'es_scheduling_strategy': [-100, -20, ..., 20]   # 96个点的功率计划
}

# 详细分析输出
Power change penalty coefficient: 0.03 yuan/kW (lightweight)
Strategy range: [-100.00, 100.00] kW
Total charging energy: 420.00 kWh
Total discharging energy: 399.00 kWh
Cost without storage: 14843.95 yuan
Cost with storage: 13686.90 yuan
Money saved: 1157.05 yuan (7.8% savings)
```

## 算法架构

### 1. 数据预处理
- 输入验证和数据格式检查
- 时间字符串解析和时段映射
- 时间粒度转换（1小时 → 15分钟）
- 功率限制计算

### 2. 优化模型建立

#### 决策变量（总计288+n个）
```python
# [P_charge[0:96], P_discharge[0:96], demand_peak[0:n], delta_pos[1:96], delta_neg[1:96]]
# P_charge: 96个充电功率变量
# P_discharge: 96个放电功率变量  
# demand_peak: n个需量峰值变量（n为需量时段数）
# delta_pos: 95个正向功率变化量
# delta_neg: 95个负向功率变化量
```

#### 目标函数
```python
minimize: 
  Σ(P_grid_buy[i] × purchase_price[i] × 0.25)    # 电量费
  + Σ(demand_peak[j] × demand_price[j])          # 需量费
  + 0.03 × Σ(delta_pos[i] + delta_neg[i])        # 功率变化惩罚
```

#### 约束条件矩阵
- **功率限制约束**: 192个（充放电功率上限）
- **需量峰值约束**: 96×n个（购电功率≤需量峰值）
- **SOC边界约束**: 192个（SOC上下限）
- **循环次数约束**: 1个（总充电量限制）
- **功率变化约束**: 190个（正负向变化定义）
- **SOC回归约束**: 1个（能量平衡）

### 3. 线性规划求解
- 使用scipy.optimize.linprog进行求解
- 采用HiGHS求解器提高计算效率
- 处理不可行解的备用策略

### 4. 经济效益分析
- 计算无储能系统基准成本
- 对比有储能系统优化成本
- 量化储能系统经济价值

## 功率变化惩罚详解

### 🎯 问题背景

**时间粒度不匹配问题**：
- 电价数据：1小时粒度（24个点）
- 功率决策：15分钟粒度（96个点）
- 结果：同一小时内多种等价最优解

**示例问题**：
```python
# 在第10小时（电价1.2元/kWh），以下方案完全等价：
方案A: [100kW, 0kW, 0kW, 0kW]     # 集中充电
方案B: [25kW, 25kW, 25kW, 25kW]   # 均匀充电
方案C: [50kW, 0kW, 50kW, 0kW]     # 断续充电
# 总充电量相同(25kWh)，成本相同(30元)
```

### 🔧 解决方案

**轻量级功率变化惩罚**：
```python
# 增加辅助变量
delta_pos[i] >= P_net[i+1] - P_net[i]    # 正向功率变化
delta_neg[i] >= P_net[i] - P_net[i+1]    # 负向功率变化

# 目标函数增加惩罚项
penalty = 0.03 × Σ(delta_pos[i] + delta_neg[i])
```

**技术特点**：
- **轻量级**：惩罚系数0.03，不过度影响经济性
- **线性化**：保持线性规划框架，求解效率高
- **有效性**：减少19%的功率变化，24%的显著切换

### 📊 效果验证

| 功能 | 改进前 | 改进后 | 改善幅度 |
|------|---------|---------|----------|
| 总充电量 | 420.00 kWh | 420.00 kWh | ✅ 保持 |
| 总放电量 | 399.00 kWh | 399.00 kWh | ✅ 保持 |
| 功率变化 | 1391.32 kW | 1124.43 kW | **-19%** |
| 显著切换 | 17次 | 13次 | **-24%** |

## 需量费优化详解

### 🎯 需量费机制

需量费是基于用电功率峰值的收费机制：

```
需量费 = max(P_grid_buy在计费时段内) × 需量单价
```

### 📊 数学模型

#### 需量约束
```
对于每个需量计费时段j，每个时间点i：
P_grid_buy[i] ≤ demand_peak[j]

其中：
P_grid_buy[i] = max(0, load[i] + P_charge[i] - pv[i] - P_discharge[i])
```

### 🔧 优化逻辑

1. **电量费优化**: 在电价低时充电，电价高时放电
2. **需量费优化**: 通过储能放电削减用电峰值
3. **功率变化优化**: 减少不必要的充放电切换
4. **三重平衡**: 在电量费、需量费和平滑性之间找到最优平衡点

## 使用方法

### 🚀 推理接口（推荐使用）

**对外提供的标准推理接口**: `energy_storage_optimization_runner`

```python
from energy_storage_optimization import energy_storage_optimization_runner

# 准备输入数据（按照标准格式）
inputs = {
    'start_time': '2025-05-21 00:00:00',
    'pv_predicted_list': [...],  # 96个点，15分钟间隔
    'hybrid_load_predicted_list': [...],  # 96个点，15分钟间隔  
    'electricity_purchase_price_list': [...],  # 24个点，1小时间隔
    'es_soc': 50,
    'es_total_energy': 210,
    'es_rated_power': 100,
    'demand_data': [...],
    'min_soc': 20,
    'max_soc': 95
}

# 调用推理接口
result = energy_storage_optimization_runner(inputs)

# 获取结果
power_schedule = result['es_scheduling_strategy']  # 96个功率值
scheduling_time = result['scheduling_time']        # 调度时间戳
```

**接口特点**：
- ✅ **简洁易用**: 一行代码完成优化
- ✅ **标准格式**: 输入输出格式固定，便于集成
- ✅ **高性能**: 内置所有优化参数，开箱即用
- ✅ **稳定可靠**: 包含异常处理和备用策略

### 1. 基本使用（详细版）

```python
from energy_storage_optimization import EnergyStorageOptimizer

# 创建优化器实例
optimizer = EnergyStorageOptimizer()

# 检查关键参数
print(f"功率变化惩罚系数: {optimizer.power_change_penalty}")  # 0.03
print(f"最大循环次数: {optimizer.max_cycles_per_day}")       # 2
print(f"储能效率: {optimizer.efficiency}")                  # 0.95

# 准备输入数据
inputs = {...}  # 按照输入参数格式

# 执行优化
result = optimizer.optimize_energy_storage(inputs)

# 获取详细结果（自动输出）
```

### 2. 输出信息解读

```python
# 系统配置信息
Power change penalty coefficient: 0.03 yuan/kW (lightweight)
Strategy range: [-100.00, 100.00] kW

# 能量统计
Total charging energy: 420.00 kWh      # 总充电量
Total discharging energy: 399.00 kWh   # 总放电量

# 经济效益分析
Cost without storage: 14843.95 yuan    # 无储能成本
Cost with storage: 13686.90 yuan       # 有储能成本  
Money saved: 1157.05 yuan (7.8% savings)  # 节省金额
```

### 3. 运行示例

```bash
# 直接运行算法文件（包含推理接口演示）
python energy_storage_optimization.py

# 查看完整功能演示
python example_usage.py
```

### 4. 推理接口使用示例

```python
# 推理接口调用示例
from energy_storage_optimization import energy_storage_optimization_runner

# 使用推理接口（最简单的调用方式）
inputs = {
    'start_time': '2025-05-21 00:00:00',
    'pv_predicted_list': [0] * 96,  # 光伏预测数据
    'hybrid_load_predicted_list': [100] * 96,  # 负载预测数据
    'electricity_purchase_price_list': [1.2] * 24,  # 电价数据
    'es_soc': 50,
    'es_total_energy': 210,
    'es_rated_power': 100,
    'demand_data': [{
        "start_time": "2025-05-21 00:00:00",
        "end_time": "2025-06-21 23:59:00", 
        "price": 10.875,
        "total_demand_target": 600
    }],
    'min_soc': 20,
    'max_soc': 95
}

result = energy_storage_optimization_runner(inputs)
print(f"优化成功，策略长度: {len(result['es_scheduling_strategy'])}")
```

## 算法优势

### 1. 工程实用性
- **平滑性保证**: 减少设备启停损耗，延长设备寿命
- **状态可恢复**: SOC回归约束确保策略可重复执行
- **参数可调**: 功率变化惩罚系数可根据实际需求调整

### 2. 经济效益明确
- **自动计算节省金额**: 直观展示储能系统投资价值
- **多重成本优化**: 电量费+需量费+设备保护成本
- **投资回收分析**: 基于日节省金额评估投资回收期

### 3. 技术先进性
- **多约束优化**: 同时处理6类约束条件
- **线性规划框架**: 保证全局最优解
- **计算效率高**: 典型优化时间 < 0.02秒

### 4. 适应性强
- **时间段灵活**: 支持任意需量计费时段
- **多场景适用**: 工商业、工厂、商业建筑等
- **扩展性好**: 易于增加新的约束和目标

## 应用场景

### 1. 工商业储能（主要场景）
- **需量管理**: 削减用电峰值，显著降低需量费
- **峰谷套利**: 配合分时电价，优化购电成本
- **光伏消纳**: 提高自发自用比例
- **设备保护**: 减少充放电切换，延长电池寿命

### 2. 大型工厂
- **生产调度配合**: 储能调度配合生产计划
- **需量费控制**: 精确控制工厂用电峰值
- **电能质量**: 提供功率支撑和调节

### 3. 商业建筑群
- **建筑能效**: 优化空调、照明等设备用电
- **成本分摊**: 多建筑共享储能系统
- **绿色建筑**: 配合可再生能源使用

## 技术指标

### 基本参数
- **时间分辨率**: 15分钟
- **规划周期**: 24小时（96个时间点）
- **求解时间**: < 0.02秒
- **求解器**: HiGHS线性规划求解器

### 决策变量
- **总数**: 288 + n个（n为需量时段数）
- **充放电功率**: 192个
- **需量峰值**: n个
- **功率变化量**: 190个

### 约束条件
- **不等式约束**: 570 + 96×n个
- **等式约束**: 1个（SOC回归）
- **变量边界**: 288 + n个

### 性能指标
- **功率变化减少**: 19%
- **显著切换减少**: 24%
- **经济效益**: 典型7.8%的成本节省
- **循环效率**: 保持95%往返效率

## 安装和依赖

### 依赖包
```text
numpy>=1.21.0
scipy>=1.7.0
matplotlib>=3.5.0  # 用于结果可视化（可选）
```

### 安装方法
```bash
# 安装依赖包
pip install -r requirements.txt

# 验证安装
python -c "import numpy, scipy; print('Dependencies OK')"
```

## 参数调优指南

### 1. 功率变化惩罚系数调优
```python
# 默认值：0.03（推荐）
optimizer.power_change_penalty = 0.03

# 调优建议：
# 0.01-0.02: 轻微平滑化，保持经济性
# 0.03-0.05: 平衡平滑性和经济性（推荐）
# 0.05+:     强平滑化，可能影响经济性
```

### 2. SOC范围设置
```python
# 建议设置
'min_soc': 20,  # 保护电池，避免深度放电
'max_soc': 90,  # 预留容量，避免过充
```

### 3. 循环次数限制
```python
# 保护电池寿命
self.max_cycles_per_day = 2  # 每日最多2次完整循环
```

## 注意事项

### 1. 数据质量要求
- **时间格式**: 严格按照"YYYY-MM-DD HH:MM:SS"
- **数据长度**: 光伏和负载数据必须为96个点
- **电价数据**: 必须为24个小时点
- **需量数据**: 时间段应覆盖优化周期

### 2. 参数设置建议
- **惩罚系数**: 建议保持0.03，除非有特殊需求
- **SOC范围**: 建议20%-90%，预留保护余量
- **需量限制**: 设置应合理，避免过于严格

### 3. 实际应用考虑
- **策略验证**: 建议先小规模测试，验证效果
- **参数更新**: 定期更新预测数据和电价信息
- **性能监控**: 关注实际运行效果，必要时调整参数

## 🎉 版本更新记录

### v5.0 - 轻量级功率变化惩罚版（当前版本）
- ✅ **新增轻量级功率变化惩罚机制**（0.03系数）
- ✅ **解决时间粒度不匹配问题**，减少断续充电
- ✅ **SOC回归约束**，确保24小时后状态恢复
- ✅ **经济效益自动计算**，量化储能投资价值
- ✅ **功率变化减少19%**，设备保护显著改善
- ✅ **保持高放电量**，经济效益不受影响

### v4.0 - 需量费优化版
- ✅ 新增需量费优化功能
- ✅ 双重成本优化：电量费 + 需量费
- ✅ 支持多个需量计费时段
- ✅ 适用于工商业需量管理

### v3.0 - 仅购电版
- ✅ 删除售电功能，简化模型
- ✅ 目标函数优化为购电成本最小化
- ✅ 适用于自发自用储能系统

### v2.0 - 简化版
- ✅ 决策变量减少50%
- ✅ 计算效率提升50%
- ✅ 基础优化功能完整

### v1.0 - 原始版
- ✅ 完整的4组决策变量
- ✅ 基础线性规划框架

---

## 总结

**v5.0版本算法**实现了储能充放电功率规划的三重优化目标：
1. **经济性优化**: 最小化电量费和需量费
2. **平滑性优化**: 减少设备启停损耗  
3. **可持续性**: 确保系统长期稳定运行