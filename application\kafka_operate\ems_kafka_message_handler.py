from datetime import datetime

import pytz

from application.db_operate.db_operate import DBOperate
from application.utils.logger import setup_logger
from application.utils.data_change_detector import data_change_detector

logger = setup_logger("ems_kafka_consumer", direction="kafka")


class EMSMessageHandler:
    def __init__(self):
        self.db_operate = DBOperate()

    def process_message(self, message_data):
        """
        处理Kafka消息，将数据保存到对应的数据库表中
        """
        try:
            self.handle_message_data(message_data)
            logger.info(f"Processing message success, message: {message_data}")
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}", exc_info=True)

    def handle_message_data(self, message_data):
        # 获取场站编号
        site_no = message_data.get("station", {}).get("site_no")
        if not site_no:
            logger.error("Missing site_no in message")
            return

        # 1. 检查场站基本信息并更新，同时获取AI状态
        site_data = SiteDBAdapter.adapt(message_data.get("station", {}))
        if not site_data:
            logger.info(f"场站 {site_no} 基本信息为空，跳过消息处理")
            return
        
        success, site_existed, ai_active = self.db_operate.update_site_db(site_no, site_data)
        
        if not success:
            logger.error(f"场站 {site_no} 基本信息更新失败，跳过消息处理")
            return
        
        # 检查场站是否存在
        if not site_existed:
            logger.info(f"场站 {site_no} 没有记录，说明没有开启AI能力，跳过消息处理")
            return
        
        # 检查场站是否开启了AI能力
        if not ai_active:
            logger.info(f"场站 {site_no} 未开启AI能力，跳过消息处理")
            return

        # 2. 处理光伏和电表实时数据
        pv_meter_data = PVAndMeterRealtimeDataDBAdapter.adapt(message_data)
        if pv_meter_data:
            self.db_operate.insert_pv_and_meter_realtime_data_db(site_no, pv_meter_data)

        # 3. 处理储能实时数据
        if message_data.get("es_data"):
            for es_data in message_data["es_data"]:
                es_realtime_data = ESRealtimeDataDBAdapter.adapt(es_data, message_data.get("ts"))
                if es_realtime_data:
                    self.db_operate.insert_es_realtime_data_db(site_no, es_realtime_data)

        # 4. 处理充电桩实时数据
        if message_data.get("charger_data"):
            charger_data_obj = message_data["charger_data"]
            bus_power_limit = charger_data_obj.get("bus_power_limit")
            pile_groups = charger_data_obj.get("pile_groups", [])
            
            # 收集所有充电桩数据，准备批量插入
            charger_data_list = []
            
            for pile_group in pile_groups:
                pile_group_name = pile_group.get("pile_group_name")
                pile_group_power_limit = pile_group.get("pile_group_power_limit")
                piles = pile_group.get("piles", [])
                
                for pile in piles:
                    pile_sn = pile.get("pile_sn")
                    guns = pile.get("guns", [])
                    
                    if pile_sn and guns:
                        for gun in guns:
                            charger_data = ChargerRealtimeDataDBAdapter.adapt(
                                gun, 
                                message_data.get("ts"),
                                bus_power_limit,
                                pile_group_name,
                                pile_group_power_limit,
                                pile_sn  # 添加pile_sn参数
                            )
                            if charger_data:
                                charger_data_list.append(charger_data)
            
            # 批量插入充电桩实时数据
            if charger_data_list:
                self.db_operate.batch_insert_charger_realtime_data_db(site_no, charger_data_list)

        # 5. 处理需求电价数据（带变化检测）
        if message_data.get("demand_data"):
            # 先对原始业务数据进行变化检测（排除存储元数据影响）
            original_demand_data = message_data["demand_data"]
            is_changed, data_hash = data_change_detector.is_data_changed(
                site_no=site_no, 
                data_type="demand_data", 
                data=original_demand_data
            )
            
            if is_changed:
                # 数据发生变化，进行适配和插入
                demand_data_list = []
                for demand_data in original_demand_data:
                    demand_price_data = SiteDemandDataDBAdapter.adapt(demand_data)
                    if demand_price_data:
                        demand_data_list.append(demand_price_data)
                
                if demand_data_list:
                    # 执行数据库插入
                    success = self.db_operate.batch_insert_site_demand_data_db(site_no, demand_data_list)
                    if success:
                        # 插入成功后更新哈希值
                        data_change_detector.update_data_hash(site_no, "demand_data", data_hash)
                        logger.info(f"场站 {site_no} 需求电价数据发生变化，已插入 {len(demand_data_list)} 条记录")
                    else:
                        logger.error(f"场站 {site_no} 需求电价数据插入失败")
                else:
                    logger.error(f"场站 {site_no} 需求电价数据发生变化但适配后为空，跳过插入")
            else:
                logger.debug(f"场站 {site_no} 需求电价数据无变化，跳过插入")

        # 6. 处理卖电电价数据（带变化检测）
        if message_data.get("sell_price"):
            # 先对原始业务数据进行变化检测（排除存储元数据影响）
            original_sell_price = message_data["sell_price"]
            is_changed, data_hash = data_change_detector.is_data_changed(
                site_no=site_no, 
                data_type="sell_price", 
                data=original_sell_price
            )
            
            if is_changed:
                # 数据发生变化，进行适配和插入
                sell_price_data_list = []
                for sell_price in original_sell_price:
                    sell_price_data = SiteSellPriceDataDBAdapter.adapt(sell_price)
                    if sell_price_data:
                        sell_price_data_list.append(sell_price_data)
                
                if sell_price_data_list:
                    # 执行数据库插入
                    success = self.db_operate.batch_insert_site_sell_price_data_db(site_no, sell_price_data_list)
                    if success:
                        # 插入成功后更新哈希值
                        data_change_detector.update_data_hash(site_no, "sell_price", data_hash)
                        logger.info(f"场站 {site_no} 卖电电价数据发生变化，已插入 {len(sell_price_data_list)} 条记录")
                    else:
                        logger.error(f"场站 {site_no} 卖电电价数据插入失败")
                else:
                    logger.error(f"场站 {site_no} 卖电电价数据发生变化但适配后为空，跳过插入")
            else:
                logger.debug(f"场站 {site_no} 卖电电价数据无变化，跳过插入")

        # 7. 处理固定电价和分时电价数据（带变化检测）
        if message_data.get("purchase_price_data"):
            purchase_price_data_list = SiteFixedAndTimeOfUsePriceDataDBAdapter.adapt(message_data["purchase_price_data"])
            if purchase_price_data_list:
                is_changed, data_hash = data_change_detector.is_data_changed(
                    site_no=site_no, 
                    data_type="purchase_price_data", 
                    data=purchase_price_data_list
                )
                
                if is_changed:
                    # 数据发生变化，执行插入
                    success = self.db_operate.insert_site_fixed_and_time_of_use_price_data_db(site_no, purchase_price_data_list)
                    if success:
                        # 插入成功后更新哈希值
                        data_change_detector.update_data_hash(site_no, "purchase_price_data", data_hash)
                        logger.info(f"场站 {site_no} 购买电价数据发生变化，已插入 {len(purchase_price_data_list)} 条记录")
                    else:
                        logger.error(f"场站 {site_no} 购买电价数据插入失败")
                else:
                    logger.debug(f"场站 {site_no} 购买电价数据无变化，跳过插入")


class SiteDBAdapter:
    FIELD_MAP = {
        "es_total_energy": "es_total_energy",
        "region": "region",
        "lat_and_lng": ("lat_and_lng", lambda v: str(v)),
        "site_grid_limit": "site_grid_limit",
        "grid_reverse": ("grid_reverse", lambda v: 1 if v == "1" or v == 1 else 0),
        "grid_name": "grid_name", 
        "pv_can_control": "pv_can_control",
        "purchase_price_type": "purchase_price_type",
        "load_limit": "load_limit",  # 场站总线负载限制功率
        "pv_max_power": "pv_max_power",
    }

    @classmethod
    def adapt(cls, station_data):
        if not station_data:
            return None
        
        result = {}
        for k, v in cls.FIELD_MAP.items():
            if station_data.get(k) is not None:
                if isinstance(v, tuple):
                    db_field, func = v
                    result[db_field] = func(station_data[k])
                else:
                    result[v] = station_data[k]
        return result


class PVAndMeterRealtimeDataDBAdapter:
    @classmethod
    def adapt(cls, message_data):
        if not message_data:
            return None

        result = {
            "ts": message_data.get("ts"),
            "pv_power": message_data.get("pv_inverter", {}).get("pv_power"),
            "pv_status": message_data.get("pv_inverter", {}).get("pv_status"),
            "meter_value": message_data.get("meter_grid", {}).get("meter_value"),
        }
        
        # 检查必要字段是否存在（允许数字字段为0）
        if all([result.get("ts"), result.get("pv_power") is not None, 
                result.get("pv_status"), result.get("meter_value") is not None]):
            return result
        return None


class ESRealtimeDataDBAdapter:
    @classmethod
    def adapt(cls, es_data, ts):
        if not es_data or not ts:
            return None

        result = {
            "ts": ts,
            "es_sn": es_data.get("es_sn"),
            "es_soc": es_data.get("es_soc"),
            "es_power": es_data.get("es_power"),
            "rated_cap": es_data.get("rated_cap"),
            "real_cap": es_data.get("real_cap"),
            "es_max_soc": es_data.get("es_max_soc"),
            "es_min_soc": es_data.get("es_min_soc"),
            "status": es_data.get("status"),
        }
        
        # 检查必要字段是否存在（允许数字字段为0）
        if all([result.get("ts"), result.get("es_sn"), result.get("es_soc") is not None, 
                result.get("es_power") is not None, result.get("rated_cap") is not None,
                result.get("real_cap") is not None, result.get("es_max_soc") is not None,
                result.get("es_min_soc") is not None, result.get("status")]):
            return result
        return None


class ChargerRealtimeDataDBAdapter:
    @classmethod
    def adapt(cls, gun_data, ts, bus_power_limit, pile_group_name, pile_group_power_limit, pile_sn):
        if not gun_data or not ts:
            return None

        result = {
            "ts": ts,
            "connector": gun_data.get("connector"),
            "status": gun_data.get("status"),
            "power": gun_data.get("power"),
            "ocpp_limit": gun_data.get("ocpp_limit"),
            "bus_power_limit": bus_power_limit,
            "pile_group_name": pile_group_name,
            "pile_group_power_limit": pile_group_power_limit,
            "pile_sn": pile_sn,
        }
        
        # 检查必要字段是否存在
        if all([result.get("ts"), result.get("connector"), result.get("status"), 
                result.get("power") is not None, result.get("ocpp_limit") is not None,
                result.get("bus_power_limit") is not None, result.get("pile_group_power_limit") is not None,
                result.get("pile_sn") is not None]):
            return result
        return None


class SiteDemandDataDBAdapter:
    @classmethod
    def adapt(cls, demand_data):
        if not demand_data:
            return None

        result = {
            "version": datetime.now(pytz.UTC).strftime("%Y%m%d_%H%M%S"),  # 生成版本号
            "start_time": demand_data.get("start_time"),
            "end_time": demand_data.get("end_time"),
            "price": demand_data.get("price"),
            "unit": demand_data.get("unit"),
            "total_demand_target": demand_data.get("total_demand_target"),
            "target_demand_warning_ratio": demand_data.get("target_demand_warning_ratio"),
        }
        
        # 检查必要字段是否存在（允许price为0）
        if all([result.get("version"), result.get("start_time"), result.get("end_time"), 
                result.get("price") is not None, result.get("unit"),
                result.get("total_demand_target") is not None, result.get("target_demand_warning_ratio") is not None]):
            return result
        return None


class SiteSellPriceDataDBAdapter:
    @classmethod
    def adapt(cls, sell_price_data):
        if not sell_price_data:
            return None

        result = {
            "version": datetime.now(pytz.UTC).strftime("%Y%m%d_%H%M%S"),  # 生成版本号
            "start_time": sell_price_data.get("start_time"),
            "end_time": sell_price_data.get("end_time"),
            "price": sell_price_data.get("price"),
            "unit": sell_price_data.get("unit"),
        }
        
        # 检查必要字段是否存在（允许price为0）
        if all([result.get("version"), result.get("start_time"), result.get("end_time"), 
                result.get("price") is not None, result.get("unit")]):
            return result
        return None


class SiteFixedAndTimeOfUsePriceDataDBAdapter:
    @classmethod
    def adapt(cls, purchase_price_data_list):
        """
        适配固定电价和分时电价数据
        
        Args:
            purchase_price_data_list: List[Dict], 购买电价数据列表
            
        Returns:
            List[Dict]: 适配后的数据列表
        """
        if not purchase_price_data_list:
            return None

        adapted_data_list = []
        for price_data in purchase_price_data_list:
            result = {
                "belong_year": price_data.get("belongYear"),
                "belong_month": price_data.get("belongMonth"),
                "start_time": price_data.get("startTime"),
                "end_time": price_data.get("endTime"),
                "time_type": price_data.get("timeType"),
                "price": price_data.get("price"),
                "unit": price_data.get("unit"),
            }
            
            # 检查必要字段是否存在（允许price为0）
            if all([result.get("belong_year"), result.get("belong_month"), result.get("start_time"), 
                    result.get("end_time"), result.get("time_type"), result.get("price") is not None, 
                    result.get("unit")]):
                adapted_data_list.append(result)
            else:
                logger.warning(f"跳过无效的购买电价数据: {price_data}")

        return adapted_data_list if adapted_data_list else None
