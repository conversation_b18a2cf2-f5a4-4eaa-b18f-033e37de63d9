#!/usr/bin/env python3
"""
动态电价获取快速测试脚本
用于快速验证 dynamic_price_fetch 函数是否正常工作
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from application.algorithm_schedule.dynamic_price_fetch import dynamic_price_fetch


def test_today_price():
    """测试获取今天的电价"""
    print("🔍 测试获取今天的电价...")
    
    try:
        today = datetime.now().date()
        result = dynamic_price_fetch("QUICK_TEST", today)
        
        if result and result.get('electricity_price'):
            print(f"✅ 成功获取今天 ({today}) 的电价")
            print(f"   业务序列号: {result['biz_seq']}")
            print(f"   区域数量: {len(result['electricity_price'])}")
            
            # 显示第一个区域的基本信息
            if result['electricity_price']:
                first_region = result['electricity_price'][0]
                print(f"   第一个区域: {first_region['region']}")
                print(f"   价格点数量: {len(first_region['price_data'])}")
                print(f"   数据可用: {first_region['can_use']}")
                
                # 显示第一个价格点
                if first_region['price_data']:
                    first_price = first_region['price_data'][0]
                    print(f"   第一个价格点: {first_price['e_price']} {first_price['unit']}")
            
            return True
        else:
            print("❌ 获取今天电价失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False


def test_yesterday_price():
    """测试获取昨天的电价"""
    print("\n🔍 测试获取昨天的电价...")
    
    try:
        yesterday = datetime.now().date() - timedelta(days=1)
        result = dynamic_price_fetch("QUICK_TEST_YESTERDAY", yesterday)
        
        if result and result.get('electricity_price'):
            print(f"✅ 成功获取昨天 ({yesterday}) 的电价")
            print(f"   区域数量: {len(result['electricity_price'])}")
            return True
        else:
            print("❌ 获取昨天电价失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False


def test_data_structure():
    """测试数据结构完整性"""
    print("\n🔍 测试数据结构完整性...")
    
    try:
        today = datetime.now().date()
        result = dynamic_price_fetch("STRUCTURE_TEST", today)
        
        if not result:
            print("❌ 无返回数据")
            return False
        
        # 检查顶层结构
        if 'biz_seq' not in result or 'electricity_price' not in result:
            print("❌ 顶层数据结构不完整")
            return False
        
        print("✅ 顶层数据结构正确")
        
        # 检查电价数据结构
        if result['electricity_price']:
            price_data = result['electricity_price'][0]
            required_keys = ['region', 'grid_name', 'time_zone', 'freq', 'can_use', 'price_data']
            
            for key in required_keys:
                if key not in price_data:
                    print(f"❌ 电价数据缺少字段: {key}")
                    return False
            
            print("✅ 电价数据结构正确")
            
            # 检查价格点结构
            if price_data['price_data']:
                price_point = price_data['price_data'][0]
                required_point_keys = ['e_price', 'start_time', 'end_time', 'unit']
                
                for key in required_point_keys:
                    if key not in price_point:
                        print(f"❌ 价格点数据缺少字段: {key}")
                        return False
                
                print("✅ 价格点数据结构正确")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据结构测试失败: {str(e)}")
        return False


def test_multiple_regions():
    """测试多区域数据获取"""
    print("\n🔍 测试多区域数据获取...")
    
    try:
        today = datetime.now().date()
        result = dynamic_price_fetch("MULTI_REGION_TEST", today)
        
        if result and result.get('electricity_price'):
            regions = [region['region'] for region in result['electricity_price']]
            print(f"✅ 成功获取 {len(regions)} 个区域的数据")
            print(f"   区域列表: {', '.join(regions)}")
            
            # 检查每个区域的数据完整性
            complete_regions = 0
            for region_data in result['electricity_price']:
                if region_data['can_use'] and len(region_data['price_data']) == 24:
                    complete_regions += 1
            
            print(f"   完整数据区域: {complete_regions}/{len(regions)}")
            return True
        else:
            print("❌ 多区域数据获取失败")
            return False
            
    except Exception as e:
        print(f"❌ 多区域测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("🚀 动态电价获取快速测试")
    print("=" * 50)
    
    test_results = []
    
    # 执行所有测试
    test_results.append(test_today_price())
    test_results.append(test_yesterday_price())
    test_results.append(test_data_structure())
    test_results.append(test_multiple_regions())
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过! 动态电价获取功能正常")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关功能")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
