import unittest

import numpy as np
import matplotlib.pyplot as plt

from application.utils.curves import curve, concat_curves_vertical
from application.utils.parser import parse_charging_data


class TestParser(unittest.TestCase):
    def test_parse_charging_data(self):
        data = parse_charging_data("tests/data/Tesla_Model@S_72.5kWh_54f8f00ca3c2_3080.json")
        print(data)


class TestCurves(unittest.TestCase):
    def test_curve(self):
        # Generate sample data
        x = np.linspace(0, 10, 100)
        y_dict = {
            "Sine Wave": np.sin(x),
            "Quadratic Function": 0.1 * x**2,
        }
        
        # Plot curves
        fig, ax = curve(
            x, 
            y_dict,
            title="Multi Curves",
            xlabel="time (s)",
            ylabel="value",
            # styles=['-', '--', '-.', ':'],
            markers=['o', 's', '^', 'x'],
            markevery=10,  # Plot a marker every 10 points
            # You can modify the plotting code to achieve this
        )
        plt.show()

    def test_concat_curves_vertical(self):
        # Create three different charts
        x1 = np.linspace(0, 10, 100)
        y_dict1 = {
            "Sine Wave": np.sin(x1),
            "Cosine Wave": np.cos(x1)
        }
        fig1, ax1 = curve(x1, y_dict1, title="Trigonometric Function Curves", xlabel="Time (s)", ylabel="Amplitude")
        
        x2 = np.linspace(0, 5, 50)
        y_dict2 = {
            "Quadratic Function": x2**2,
            "Cubic Function": x2**3,
            "Quartic Function": x2**4
        }
        fig2, ax2 = curve(x2, y_dict2, title="Polynomial Functions", xlabel="x", ylabel="y")
        
        x3 = np.linspace(-2, 2, 200)
        y_dict3 = {
            "Gaussian Function": np.exp(-x3**2),
            "Lorentzian Function": 1/(1+x3**2)
        }
        fig3, ax3 = curve(x3, y_dict3, title="Statistical Distribution Functions", xlabel="x", ylabel="Density")
        
        # Vertically concatenate these charts
        figure_list = [(fig1, ax1), (fig2, ax2), (fig3, ax3)]
        combined_fig = concat_curves_vertical(
            figure_list,
            titles=["1. Trigonometric Function Curves", "2. Polynomial Functions", "3. Statistical Distribution Functions"],
            figsize=(10, None),  # Fixed width, auto-calculated height
            spacing=0.4,  # Adjust subplot spacing
            save_path="combined_plots.png",
            dpi=150
        )
        
        plt.show()


def generate_test_data():
    # Generate sample data
    x = np.linspace(0, 10, 100)
    data = {
        "Sine Wave": np.sin(x),
        "Quadratic Function": 0.1 * x**2,
    }
    
    # Plot curves
    plt.figure(figsize=(10, 6))
    for name, values in data.items():
        plt.plot(x, values, label=name)
    
    plt.title("Test Data Visualization")
    plt.xlabel("X")
    plt.ylabel("Y")
    plt.legend()
    plt.grid(True)
    
    # Plot markers every 10 points
    for name, values in data.items():
        plt.plot(x[::10], values[::10], 'o', alpha=0.5)
    
    plt.show()


if __name__ == "__main__":
    unittest.main()
