import os
import pandas as pd
from . import helper
import pickle
import json
from application.utils.logger import setup_logger
logger = setup_logger("load_prediction", direction="algorithm")

the_path = os.path.dirname(os.path.abspath(__file__))
# ===================== Load model from file ======================
with open(os.path.join(the_path, 'load_xgb_model.pkl'), 'rb') as f:
    model = pickle.load(f)

# ====================== INPUT DATA PROCESSING ===============
window_features = {"total_power_demand_KWh": 96}
horizon_features = {}
forecast_features = {}
target_feature = "total_power_demand_KWh"
forecast_horizon = 96
time_granularity = 15 # in minutes
time_granularity_str = str(time_granularity) + 'min'

# ===============================================================
def prepare_features_from_input(input_data: dict) -> pd.DataFrame:
    current_time = input_data["current_time"]
    fields = input_data["fields"]
    values = input_data["time_series"]
    df_inf = pd.DataFrame(values, columns=fields)

    val_columns = ['Timestamp', 'DayOfWeek', 'Hour', 'total_power_demand_KWh']
    df_inf.rename(columns={'charging_consumption': 'total_power_demand_KWh'}, inplace=True)

    try:
        df_inf['Timestamp'] = pd.to_datetime(df_inf['Timestamp'], unit='ms')
    except:
        df_inf['Timestamp'] = pd.to_datetime(df_inf['Timestamp'])
    df_inf = df_inf.set_index('Timestamp')
    df_inf = df_inf[~df_inf.index.duplicated(keep='first')]
    df_15min = df_inf.resample('15min').ffill()
    df_inf = df_15min.reset_index()

    df_inf['Hour'] = df_inf['Timestamp'].dt.hour
    df_inf['DayOfWeek'] = df_inf['Timestamp'].dt.dayofweek
    df_inf = df_inf[val_columns]

    prediction_feature_generation  = helper.feature_engineering(df=df_inf, 
                                            target_feature=target_feature, 
                                            horizon_features=horizon_features, 
                                            window_features=window_features,
                                            forecast_features=forecast_features, 
                                            forecast_horizon=forecast_horizon,
                                            time_granularity=time_granularity,
                                            inference=True)

    prediction_engineered_data_inf = prediction_feature_generation.df
    feature_columns = [col for col in prediction_engineered_data_inf.columns if 'time' not in col.lower()]

    prediction_engineered_data_inf_point = prediction_engineered_data_inf[prediction_engineered_data_inf['Timestamp'] == current_time]
    prediction_engineered_data_inf_point = prediction_engineered_data_inf_point.sort_values(by='future_Timestamp').reset_index(drop=True)
    prediction_engineered_data_inf_point = prediction_engineered_data_inf_point[feature_columns]

    return prediction_engineered_data_inf_point

def predict_from_input(input_data: dict) -> list:
    try:
        X = prepare_features_from_input(input_data)
        preds = model.predict(X)
        return list(preds)
    except Exception as e:
        logger.error(f'load_prediction predict_from_input error: {e}')
        return []

if __name__ == "__main__":
    with open("input_data.json", "r") as f:
        input_data = json.load(f)
    load_prediction_values = predict_from_input(input_data)
    logger.info(f'load_prediction_values: {load_prediction_values}')