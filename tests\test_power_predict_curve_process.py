import unittest
import sys
import os
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock, Mock
import pytz

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# 设置测试环境变量，避免配置加载错误
os.environ['TESTING'] = 'true'
os.environ['CONFIG_ENCRYPTION_KEY'] = "tTZ256Kp4RTO2wOuZEocOxd3UuX0cbEXEeC2bJXh_0E="
os.environ['AVANTML_SDK_CONFIG_PATH'] = 'skip_config_loading'

# Mock外部依赖，避免导入错误
mock_modules = [
    'requests',
    'confluent_kafka',
    'application.api_manage.energy_cloud_api'
]

for module in mock_modules:
    sys.modules[module] = MagicMock()

# Mock EnergyCloudAPI类
mock_energy_cloud_api = MagicMock()
sys.modules['application.api_manage.energy_cloud_api'].EnergyCloudAPI = mock_energy_cloud_api

from application.utils.power_predict_curve_process import PowerPredictCurveProcessor
from application.db_operate.db_operate import DBOperate


class TestPowerPredictCurveProcess(unittest.TestCase):
    """功率预测曲线处理单元测试"""

    def setUp(self):
        """测试前准备"""
        self.test_site_no = "TEST_SITE_001"
        self.test_date = "2024-01-15"
        
        # 创建标准的96点测试数据（24小时 * 4个点/小时）
        self.valid_es_curve = [10.5 + i * 0.1 for i in range(96)]  # 储能功率曲线
        self.valid_pv_curve = [5.0 + i * 0.05 for i in range(96)]  # 光伏功率曲线  
        self.valid_load_curve = [20.0 + i * 0.2 for i in range(96)]  # 负载功率曲线
        
        # 创建历史数据模拟
        self.existing_data_mock = {
            'es_power_list': [8.0 + i * 0.1 for i in range(96)],
            'pv_power_list': [3.0 + i * 0.05 for i in range(96)],
            'load_power_list': [15.0 + i * 0.15 for i in range(96)]
        }

    @patch('application.utils.power_predict_curve_process.PowerPredictCurveProcessor.get_site_local_time')
    @patch('application.utils.power_predict_curve_process.DBOperate')
    def test_save_power_predict_data_success(self, mock_db_operate_class, mock_get_local_time):
        """测试正常保存功率预测数据"""
        # 模拟当前时间为上午10:30
        mock_time = datetime(2024, 1, 15, 10, 30, 0, tzinfo=pytz.UTC)
        mock_get_local_time.return_value = mock_time
        
        # 模拟数据库操作
        mock_db_instance = MagicMock()
        mock_db_operate_class.return_value = mock_db_instance
        mock_db_instance.get_power_predict_curve_for_ems.return_value = self.existing_data_mock
        mock_db_instance.save_power_predict_curve_for_ems.return_value = True
        
        # 执行测试
        result = PowerPredictCurveProcessor.save_power_predict_data_to_db(
            site_no=self.test_site_no,
            es_power_curve=self.valid_es_curve,
            pv_power_curve=self.valid_pv_curve,
            load_power_curve=self.valid_load_curve
        )
        
        # 验证结果
        self.assertTrue(result)
        
        # 验证数据库方法被正确调用
        mock_db_instance.get_power_predict_curve_for_ems.assert_called_once_with(
            self.test_site_no, "2024-01-15"
        )
        mock_db_instance.save_power_predict_curve_for_ems.assert_called_once()
        
        # 验证保存的参数
        save_call_args = mock_db_instance.save_power_predict_curve_for_ems.call_args
        self.assertEqual(save_call_args.kwargs['site_no'], self.test_site_no)
        self.assertEqual(save_call_args.kwargs['local_date'], "2024-01-15")
        
        # 验证每个曲线都不为空
        self.assertIsNotNone(save_call_args.kwargs['es_power_list'])
        self.assertIsNotNone(save_call_args.kwargs['pv_power_list'])
        self.assertIsNotNone(save_call_args.kwargs['load_power_list'])
        
        # 验证曲线长度为96（完整的24小时数据）
        self.assertEqual(len(save_call_args.kwargs['es_power_list']), 96)
        self.assertEqual(len(save_call_args.kwargs['pv_power_list']), 96)
        self.assertEqual(len(save_call_args.kwargs['load_power_list']), 96)

    @patch('application.utils.power_predict_curve_process.PowerPredictCurveProcessor.get_site_local_time')
    @patch('application.utils.power_predict_curve_process.DBOperate')
    def test_curve_truncation_logic(self, mock_db_operate_class, mock_get_local_time):
        """测试曲线截断逻辑"""
        # 模拟当前时间为下午14:30（还剩9.5小时到24:00）
        mock_time = datetime(2024, 1, 15, 14, 30, 0, tzinfo=pytz.UTC)
        mock_get_local_time.return_value = mock_time
        
        # 模拟数据库操作
        mock_db_instance = MagicMock()
        mock_db_operate_class.return_value = mock_db_instance
        mock_db_instance.get_power_predict_curve_for_ems.return_value = self.existing_data_mock
        mock_db_instance.save_power_predict_curve_for_ems.return_value = True
        
        # 执行测试
        result = PowerPredictCurveProcessor.save_power_predict_data_to_db(
            site_no=self.test_site_no,
            es_power_curve=self.valid_es_curve
        )
        
        # 验证结果
        self.assertTrue(result)
        
        # 计算预期的截断点数：从14:30到24:00 = 9.5小时 = 570分钟 = 38个15分钟点
        expected_points_to_keep = 38  # math.ceil(570 / 15) = 38
        expected_historical_points = 96 - expected_points_to_keep  # 58个历史点
        
        # 验证保存的曲线数据结构
        save_call_args = mock_db_instance.save_power_predict_curve_for_ems.call_args
        saved_es_curve = save_call_args.kwargs['es_power_list']
        
        # 验证曲线总长度为96
        self.assertEqual(len(saved_es_curve), 96)
        
        # 验证前面的历史数据部分
        historical_part = saved_es_curve[:expected_historical_points]
        self.assertEqual(len(historical_part), expected_historical_points)
        
        # 验证历史数据内容：应该来自existing_data_mock的前58个点
        expected_historical_data = self.existing_data_mock['es_power_list'][:expected_historical_points]
        self.assertEqual(historical_part, expected_historical_data, 
                        "历史数据部分应该与现有数据的前58个点一致")
        
        # 验证后面的新预测数据部分  
        prediction_part = saved_es_curve[expected_historical_points:]
        self.assertEqual(len(prediction_part), expected_points_to_keep)
        
        # 验证新预测数据内容：应该来自valid_es_curve的前38个点（截断后的数据）
        expected_prediction_data = self.valid_es_curve[:expected_points_to_keep]
        self.assertEqual(prediction_part, expected_prediction_data,
                        "新预测数据部分应该与输入曲线的前38个点一致")

    @patch('application.utils.power_predict_curve_process.PowerPredictCurveProcessor.get_site_local_time')
    @patch('application.utils.power_predict_curve_process.DBOperate')
    def test_no_historical_data_scenario(self, mock_db_operate_class, mock_get_local_time):
        """测试无历史数据的情况"""
        # 模拟当前时间为上午8:00
        mock_time = datetime(2024, 1, 15, 8, 0, 0, tzinfo=pytz.UTC)
        mock_get_local_time.return_value = mock_time
        
        # 模拟数据库操作：无历史数据
        mock_db_instance = MagicMock()
        mock_db_operate_class.return_value = mock_db_instance
        mock_db_instance.get_power_predict_curve_for_ems.return_value = None
        mock_db_instance.save_power_predict_curve_for_ems.return_value = True
        
        # 执行测试
        result = PowerPredictCurveProcessor.save_power_predict_data_to_db(
            site_no=self.test_site_no,
            pv_power_curve=self.valid_pv_curve
        )
        
        # 验证结果
        self.assertTrue(result)
        
        # 计算预期值：从8:00到24:00 = 16小时 = 64个点
        expected_points_to_keep = 64
        expected_historical_points = 96 - expected_points_to_keep  # 32个历史点
        
        # 验证保存的数据
        save_call_args = mock_db_instance.save_power_predict_curve_for_ems.call_args
        saved_pv_curve = save_call_args.kwargs['pv_power_list']
        
        # 验证前面32个点为None（因为无历史数据）
        historical_part = saved_pv_curve[:expected_historical_points]
        self.assertTrue(all(point is None for point in historical_part))
        
        # 验证后面64个点为预测数据
        prediction_part = saved_pv_curve[expected_historical_points:]
        self.assertEqual(len(prediction_part), expected_points_to_keep)
        # 验证是原始预测数据的前64个点
        self.assertEqual(prediction_part, self.valid_pv_curve[:expected_points_to_keep])

    def test_invalid_parameters(self):
        """测试无效参数"""
        # 测试空的场站编号
        result = PowerPredictCurveProcessor.save_power_predict_data_to_db(
            site_no="",
            es_power_curve=self.valid_es_curve
        )
        self.assertFalse(result)
        
        # 测试None场站编号
        result = PowerPredictCurveProcessor.save_power_predict_data_to_db(
            site_no=None,
            es_power_curve=self.valid_es_curve
        )
        self.assertFalse(result)
        
        # 测试没有提供任何曲线数据
        result = PowerPredictCurveProcessor.save_power_predict_data_to_db(
            site_no=self.test_site_no
        )
        self.assertFalse(result)
        
        # 测试空的曲线数据
        result = PowerPredictCurveProcessor.save_power_predict_data_to_db(
            site_no=self.test_site_no,
            es_power_curve=[],
            pv_power_curve=[],
            load_power_curve=[]
        )
        self.assertFalse(result)

    @patch('application.utils.power_predict_curve_process.PowerPredictCurveProcessor.get_site_local_time')
    @patch('application.utils.power_predict_curve_process.DBOperate')
    def test_invalid_curve_length(self, mock_db_operate_class, mock_get_local_time):
        """测试无效的曲线长度"""
        mock_time = datetime(2024, 1, 15, 12, 0, 0, tzinfo=pytz.UTC)
        mock_get_local_time.return_value = mock_time
        
        # 测试长度不正确的曲线（应该是96个点）
        invalid_curve = [10.0] * 50  # 只有50个点
        
        result = PowerPredictCurveProcessor.save_power_predict_data_to_db(
            site_no=self.test_site_no,
            es_power_curve=invalid_curve
        )
        
        self.assertFalse(result)

    @patch('application.utils.power_predict_curve_process.PowerPredictCurveProcessor.get_site_local_time')
    @patch('application.utils.power_predict_curve_process.DBOperate')
    def test_database_save_failure(self, mock_db_operate_class, mock_get_local_time):
        """测试数据库保存失败的情况"""
        mock_time = datetime(2024, 1, 15, 12, 0, 0, tzinfo=pytz.UTC)
        mock_get_local_time.return_value = mock_time
        
        # 模拟数据库保存失败
        mock_db_instance = MagicMock()
        mock_db_operate_class.return_value = mock_db_instance
        mock_db_instance.get_power_predict_curve_for_ems.return_value = None
        mock_db_instance.save_power_predict_curve_for_ems.return_value = False
        
        result = PowerPredictCurveProcessor.save_power_predict_data_to_db(
            site_no=self.test_site_no,
            es_power_curve=self.valid_es_curve
        )
        
        self.assertFalse(result)

    @patch('application.utils.power_predict_curve_process.PowerPredictCurveProcessor.get_site_local_time')
    @patch('application.utils.power_predict_curve_process.DBOperate')
    def test_midnight_edge_case(self, mock_db_operate_class, mock_get_local_time):
        """测试午夜边界情况"""
        # 模拟当前时间为23:45（只剩15分钟到24:00）
        mock_time = datetime(2024, 1, 15, 23, 45, 0, tzinfo=pytz.UTC)
        mock_get_local_time.return_value = mock_time
        
        mock_db_instance = MagicMock()
        mock_db_operate_class.return_value = mock_db_instance
        mock_db_instance.get_power_predict_curve_for_ems.return_value = self.existing_data_mock
        mock_db_instance.save_power_predict_curve_for_ems.return_value = True
        
        result = PowerPredictCurveProcessor.save_power_predict_data_to_db(
            site_no=self.test_site_no,
            load_power_curve=self.valid_load_curve
        )
        
        self.assertTrue(result)
        
        # 验证只保留1个点（23:45到24:00）
        save_call_args = mock_db_instance.save_power_predict_curve_for_ems.call_args
        saved_load_curve = save_call_args.kwargs['load_power_list']
        
        # 前95个点应该是历史数据，最后1个点是新预测数据
        self.assertEqual(len(saved_load_curve), 96)
        
        # 验证前95个点是历史数据（来自existing_data_mock）
        historical_part = saved_load_curve[:95]
        expected_historical_data = self.existing_data_mock['load_power_list'][:95]
        self.assertEqual(historical_part, expected_historical_data,
                        "前95个点应该与历史数据一致")
        
        # 验证最后一个点是新预测数据的第一个点
        self.assertEqual(saved_load_curve[-1], self.valid_load_curve[0],
                        "最后一个点应该是新预测数据的第一个点")

    @patch('application.utils.power_predict_curve_process.PowerPredictCurveProcessor.get_site_local_time')
    @patch('application.utils.power_predict_curve_process.DBOperate')
    def test_exception_handling(self, mock_db_operate_class, mock_get_local_time):
        """测试异常处理"""
        mock_time = datetime(2024, 1, 15, 12, 0, 0, tzinfo=pytz.UTC)
        mock_get_local_time.return_value = mock_time
        
        # 模拟数据库操作抛出异常
        mock_db_instance = MagicMock()
        mock_db_operate_class.return_value = mock_db_instance
        mock_db_instance.get_power_predict_curve_for_ems.side_effect = Exception("Database connection failed")
        
        result = PowerPredictCurveProcessor.save_power_predict_data_to_db(
            site_no=self.test_site_no,
            es_power_curve=self.valid_es_curve
        )
        
        # 应该捕获异常并返回False
        self.assertFalse(result)


class TestPowerPredictCurveProcessIntegration(unittest.TestCase):
    """功率预测曲线处理集成测试（使用真实数据库）"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试数据库连接并创建必要的表"""
        try:
            from application.db_operate.models import Base
            from sqlalchemy import text
            
            # 创建数据库操作实例
            db_operate = DBOperate()
            
            with db_operate.get_db() as db:
                # 检查PowerPredictCurveForEMS表是否存在
                result = db.execute(text("SHOW TABLES LIKE 'PowerPredictCurveForEMS'"))
                table_exists = result.fetchone() is not None
                
                if not table_exists:
                    print("创建PowerPredictCurveForEMS表...")
                    
                    # 创建表的SQL语句
                    create_table_sql = """
                    CREATE TABLE `PowerPredictCurveForEMS` (
                        `id` BIGINT NOT NULL AUTO_INCREMENT,
                        `site_no` VARCHAR(50) NOT NULL COMMENT '场站编号',
                        `es_power_list` VARCHAR(2000) NOT NULL COMMENT '储能功率列表',
                        `pv_power_list` VARCHAR(2000) NOT NULL COMMENT '光伏功率列表',
                        `load_power_list` VARCHAR(2000) NOT NULL COMMENT '融合负载功率列表',
                        `local_date` VARCHAR(10) NOT NULL COMMENT '本地日期，格式：YYYY-MM-DD',
                        `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        PRIMARY KEY (`id`),
                        KEY `idx_site_date` (`site_no`, `local_date`),
                        UNIQUE KEY `uk_site_date` (`site_no`, `local_date`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
                    """
                    
                    # 执行创建表语句
                    db.execute(text(create_table_sql))
                    db.commit()
                    print("PowerPredictCurveForEMS表创建成功！")
                else:
                    print("PowerPredictCurveForEMS表已存在")
                    
        except Exception as e:
            print(f"设置测试数据库时出错: {e}")
            raise
    
    def setUp(self):
        """测试前准备"""
        self.test_site_no = "REAL_DB_TEST_SITE_001"
        self.test_date = "2024-01-15"
        
        # 创建测试数据
        self.test_es_curve = [10.0 + i * 0.1 for i in range(96)]  # 储能功率曲线
        self.test_pv_curve = [5.0 + i * 0.05 for i in range(96)]  # 光伏功率曲线  
        self.test_load_curve = [20.0 + i * 0.2 for i in range(96)]  # 负载功率曲线
        
        # 创建数据库操作实例
        self.db_operate = DBOperate()
    
    def tearDown(self):
        """测试后清理"""
        try:
            # 清理测试数据，避免影响其他测试
            with self.db_operate.get_db() as db:
                # 删除测试数据（如果存在）
                from application.db_operate.models import PowerPredictCurveForEmsDB
                db.query(PowerPredictCurveForEmsDB).filter(
                    PowerPredictCurveForEmsDB.site_no == self.test_site_no,
                    PowerPredictCurveForEmsDB.local_date == self.test_date
                ).delete()
                db.commit()
        except Exception as e:
            print(f"清理测试数据时出错: {e}")
    
    def test_real_database_save_and_read(self):
        """真实数据库测试：保存和读取功率预测曲线数据"""
        print(f"\n开始真实数据库测试 - 场站: {self.test_site_no}, 日期: {self.test_date}")
        
        try:
            # 1. 首先确保没有测试数据存在
            existing_data = self.db_operate.get_power_predict_curve_for_ems(
                self.test_site_no, self.test_date
            )
            if existing_data:
                print(f"警告: 发现已存在的测试数据，将进行覆盖")
            
            # 2. 保存测试数据
            print("步骤1: 保存功率预测曲线数据...")
            save_success = self.db_operate.save_power_predict_curve_for_ems(
                site_no=self.test_site_no,
                es_power_list=self.test_es_curve,
                pv_power_list=self.test_pv_curve,
                load_power_list=self.test_load_curve,
                local_date=self.test_date
            )
            
            # 验证保存成功
            self.assertTrue(save_success, "数据库保存操作应该成功")
            print("数据保存成功")
            
            # 3. 读取保存的数据
            print("步骤2: 读取功率预测曲线数据...")
            retrieved_data = self.db_operate.get_power_predict_curve_for_ems(
                self.test_site_no, self.test_date
            )
            
            # 验证读取成功
            self.assertIsNotNone(retrieved_data, "应该能读取到保存的数据")
            print("数据读取成功")
            
            # 4. 验证数据完整性
            print("步骤3: 验证数据完整性...")
            
            # 验证基本字段
            self.assertEqual(retrieved_data['site_no'], self.test_site_no)
            self.assertEqual(retrieved_data['local_date'], self.test_date)
            
            # 验证曲线数据
            self.assertIn('es_power_list', retrieved_data)
            self.assertIn('pv_power_list', retrieved_data)
            self.assertIn('load_power_list', retrieved_data)
            
            # 验证曲线长度
            self.assertEqual(len(retrieved_data['es_power_list']), 96)
            self.assertEqual(len(retrieved_data['pv_power_list']), 96)
            self.assertEqual(len(retrieved_data['load_power_list']), 96)
            
            # 验证数据内容
            self.assertEqual(retrieved_data['es_power_list'], self.test_es_curve)
            self.assertEqual(retrieved_data['pv_power_list'], self.test_pv_curve)
            self.assertEqual(retrieved_data['load_power_list'], self.test_load_curve)
            
            print("数据完整性验证通过")
            
            # 5. 输出数据摘要
            print("\n数据摘要:")
            print(f"  储能功率曲线: {len(retrieved_data['es_power_list'])}个点, "
                  f"范围: {min(retrieved_data['es_power_list']):.2f} - {max(retrieved_data['es_power_list']):.2f}")
            print(f"  光伏功率曲线: {len(retrieved_data['pv_power_list'])}个点, "
                  f"范围: {min(retrieved_data['pv_power_list']):.2f} - {max(retrieved_data['pv_power_list']):.2f}")
            print(f"  负载功率曲线: {len(retrieved_data['load_power_list'])}个点, "
                  f"范围: {min(retrieved_data['load_power_list']):.2f} - {max(retrieved_data['load_power_list']):.2f}")
            
            print("🎉 真实数据库测试完成！")
            
        except Exception as e:
            print(f"真实数据库测试失败: {e}")
            raise
    
    def test_real_database_partial_save(self):
        """真实数据库测试：部分数据保存（只保存储能功率）"""
        print(f"\n开始部分数据保存测试 - 场站: {self.test_site_no}, 日期: {self.test_date}")
        
        try:
            # 只保存储能功率数据
            save_success = self.db_operate.save_power_predict_curve_for_ems(
                site_no=self.test_site_no,
                es_power_list=self.test_es_curve,
                local_date=self.test_date
            )
            
            self.assertTrue(save_success, "部分数据保存应该成功")
            
            # 读取数据验证
            retrieved_data = self.db_operate.get_power_predict_curve_for_ems(
                self.test_site_no, self.test_date
            )
            
            self.assertIsNotNone(retrieved_data)
            self.assertEqual(retrieved_data['es_power_list'], self.test_es_curve)
            
            # 其他字段应该为空数组（因为数据库字段是NOT NULL）
            self.assertEqual(retrieved_data.get('pv_power_list'), [])
            self.assertEqual(retrieved_data.get('load_power_list'), [])
            
            print("部分数据保存测试通过")
            
        except Exception as e:
            print(f"部分数据保存测试失败: {e}")
            raise


def create_test_suite():
    """创建测试套件"""
    suite = unittest.TestSuite()
    
    # 添加单元测试
    suite.addTest(unittest.makeSuite(TestPowerPredictCurveProcess))
    
    # 添加真实数据库集成测试（默认启用）
    suite.addTest(unittest.makeSuite(TestPowerPredictCurveProcessIntegration))
    
    return suite


if __name__ == '__main__':
    # 运行测试
    print("=" * 60)
    print("开始运行功率预测曲线处理单元测试")
    print("=" * 60)
    
    # 创建测试套件
    suite = create_test_suite()
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果摘要
    print("\n" + "=" * 60)
    print("测试结果摘要:")
    print(f"运行测试数: {result.testsRun}")
    print(f"失败测试数: {len(result.failures)}")
    print(f"错误测试数: {len(result.errors)}")
    print(f"跳过测试数: {len(result.skipped)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    print("=" * 60)
    
    # 返回退出码
    sys.exit(0 if result.wasSuccessful() else 1)
