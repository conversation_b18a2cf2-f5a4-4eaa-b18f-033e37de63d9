from datetime import datetime

from pytz import UTC
from sqlalchemy.dialects.mysql import TINYINT
from sqlalchemy import Column, Integer, String, Float, DateTime, BigInteger, DECIMAL
from sqlalchemy.dialects.mysql import JSON
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


# SQLAlchemy 模型类
class SiteDB(Base):
    """场站数据库表"""
    __tablename__ = 'Site'

    site_no = Column(String(50), primary_key=True)
    demand_limit = Column(Integer, nullable=True, comment='月最优demand_limit值')
    es_total_energy = Column(Integer, nullable=True, comment='储能电池容量，单位kwh')
    region = Column(String(255), nullable=True, comment='场站所在国家/地区')
    lat_and_lng = Column(String(100), nullable=True, comment='场站的经纬度信息，用于获取天气信息')
    site_grid_limit = Column(Integer, nullable=True, comment='场站电网功率限制，单位kw')
    grid_reverse = Column(TINYINT, nullable=True, comment='电网能否逆流 1 可以 0 不可以')
    grid_name = Column(String(100), nullable=True, comment='电力公司名称')
    pv_can_control = Column(Integer, nullable=True, comment='光伏是否可调 1可调  0不可调')
    is_ai_active = Column(TINYINT, nullable=False, comment='是否开启AI能力  1 start 0 stop')
    purchase_price_type = Column(Integer, nullable=True, comment='购买电价类型 1：固定电价,2：分时电价,3：动态电价')
    pv_max_power = Column(DECIMAL(10, 2), nullable=True, comment='光伏最大发电功率，单位kW')
    created_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))


class PileDB(Base):
    """充电桩数据库表"""
    __tablename__ = 'Pile'

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    pile_sn = Column(String(50), unique=True, nullable=False)
    gun_num = Column(Integer, nullable=False)
    pile_type = Column(String(20), nullable=False)
    rated_power = Column(DECIMAL(10, 2), nullable=False, comment='运维发送的功率')
    site_no = Column(String(50), nullable=False, index=True)
    created_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))


class ChargerDB(Base):
    """充电枪数据库表"""
    __tablename__ = 'Charger'

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    charger_sn = Column(String(50), unique=True, nullable=False)
    gun_no = Column(Integer, nullable=False)
    bom_index = Column(Integer, nullable=False)
    pile_sn = Column(String(50), nullable=False, index=True)
    max_curr = Column(Float, nullable=False, default=0, comment='枪线最大电流')
    created_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))


class ModuleDB(Base):
    """模块数据库表"""
    __tablename__ = 'Module'

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    pile_sn = Column(String(50), nullable=False, index=True)
    type = Column(String(20), nullable=False,  default='unknown', comment='模块类型')
    module_no = Column(Integer, nullable=False)
    unit_power = Column(DECIMAL(10, 2), nullable=False)
    unit_num = Column(Integer, nullable=False)
    created_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))


class ChargerSessionDB(Base):
    """充电会话数据库表"""
    __tablename__ = 'ChargerSession'

    local_id = Column(String(50), primary_key=True, comment='桩本地订单ID')
    site_no = Column(String(50), nullable=False, index=True, comment='场站ID')
    charger_sn = Column(String(50), nullable=False, index=True)
    mac_addr = Column(String(50), nullable=False)
    start_time = Column(DateTime, nullable=False)
    end_time = Column(DateTime)
    status = Column(String(10), nullable=False)
    created_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))


class EVModelDB(Base):
    """电动汽车模型数据库表"""
    __tablename__ = 'EVModel'

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    local_id = Column(String(50), unique=True, nullable=False, comment='桩本地订单ID')
    charger_sn = Column(String(50), nullable=False, index=True)
    mac_addr = Column(String(50), nullable=False)
    capacity = Column(DECIMAL(10, 2), nullable=False)
    max_power = Column(DECIMAL(10, 2), nullable=False)
    max_current = Column(DECIMAL(10, 2), nullable=False)
    max_voltage = Column(DECIMAL(10, 2), nullable=False)
    recognized_vehicle = Column(String(100), comment='车型识别结果')
    predict_status = Column(Integer, default=0, comment='预测状态，见代码')
    created_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))


class ChargingRecordDB(Base):
    """充电记录数据库表"""
    __tablename__ = 'ChargingRecord'

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    local_id = Column(String(50), nullable=False, index=True, comment='桩本地订单ID')
    charger_sn = Column(String(50), nullable=False, index=True)
    soc = Column(DECIMAL(5, 2), nullable=False)
    curr_output = Column(Float, nullable=False, comment='实际电流')
    vol_output = Column(Float, nullable=False, comment='实际电压')
    curr_demand = Column(Float, nullable=False, comment='需求电流')
    max_power = Column(Float, nullable=False, comment='最大功率')
    consumed_energy = Column(Float, nullable=False, comment='累计能耗')
    created_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC), comment='创建时间')
    report_at = Column(DateTime, nullable=False, comment='上报时间')


class PileTaskDB(Base):
    """桩功率分配策略记录数据库表"""
    __tablename__ = 'PileTask'

    id = Column(String(50), primary_key=True, comment='桩功率分配策略记录id')
    site_no = Column(String(50), nullable=False, index=True)
    generate_time = Column(DateTime, nullable=False, comment='调度计划生成的时刻')
    pile_power_list = Column(String(2000), nullable=False, comment='桩的功率分配详细情况')
    created_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))


class EnergyStorageTaskDB(Base):
    """储能调度记录数据库表"""
    __tablename__ = 'EnergyStorageTask'

    id = Column(BigInteger, primary_key=True, comment='储能调度记录id',autoincrement=True)
    site_no = Column(String(50), nullable=False)
    es_sn = Column(String(50), nullable=True, index=True, comment='储能电池编号')
    scheduling_time = Column(Integer, nullable=False, comment='调度时间')
    es_scheduling_strategy = Column(JSON, nullable=True, comment='储能调度策略')
    scheduling_time_interval = Column(Integer, nullable=False, default=15, comment='调度时间间隔')
    created_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))


class PVTaskDB(Base):
    """光伏控制记录数据库表"""
    __tablename__ = 'PVTask'

    id = Column(String(50), primary_key=True, comment='光伏控制记录id')
    site_no = Column(String(50), nullable=False, index=True)
    generate_time = Column(DateTime, nullable=False, comment='调度计划生成的时刻')
    generate_time_power = Column(DECIMAL(10, 2), nullable=False, comment='计划生成时间点功率')
    time_list = Column(String(2000), nullable=False)
    power_list = Column(String(2000), nullable=False)
    created_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))


class PVCurvePredictionDB(Base):
    """光伏曲线预测数据库表 预测结果记录日志 数据库不保存历史预测结果"""
    __tablename__ = 'PVCurvePrediction'

    site_no = Column(String(50), primary_key=True)
    curve_start_time = Column(DateTime, nullable=False, comment='曲线开始时刻')
    curve_end_time = Column(DateTime, nullable=False, comment='曲线结束时刻')
    time_list = Column(String(2000), nullable=False)
    power_list = Column(String(2000), nullable=False)
    created_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))


class ChargerNonSuppressPowerPredictionDB(Base):
    """充电车辆的非压制功率预测"""
    __tablename__ = 'ChargerNonSuppressPowerPrediction'

    local_id = Column(String(50), primary_key=True)
    curve_start_time = Column(DateTime, nullable=False, comment='曲线开始时刻')
    curve_end_time = Column(DateTime, nullable=False, comment='曲线结束时刻')
    time_list = Column(String(2000), nullable=False)
    power_list = Column(String(2000), nullable=False)
    created_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))


class SiteDemandPredictionDB(Base):
    """场站未来一段时间总demand负载预测 预测结果记录日志 数据库不保存历史预测结果"""
    __tablename__ = 'SiteDemandPrediction'

    site_no = Column(String(50), primary_key=True)
    curve_start_time = Column(DateTime, nullable=False, comment='曲线开始时刻')
    curve_end_time = Column(DateTime, nullable=False, comment='曲线结束时刻')
    time_list = Column(String(2000), nullable=False)
    power_list = Column(String(2000), nullable=False)
    created_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))


class SiteLongTermLoadPredictionDB(Base):
    """场站未来24h demand限制下的负载预测 预测结果记录日志 数据库不保存历史预测结果"""
    __tablename__ = 'SiteLongTermLoadPrediction'

    site_no = Column(String(50), primary_key=True)
    curve_start_time = Column(DateTime, nullable=False, comment='曲线开始时刻')
    curve_end_time = Column(DateTime, nullable=False, comment='曲线结束时刻')
    time_list = Column(String(2000), nullable=False)
    power_list = Column(String(2000), nullable=False)
    created_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))


class HybridLoadPredictionDB(Base):
    """融合负载预测数据库表 预测结果记录日志 数据库不保存历史预测结果"""
    __tablename__ = 'HybridLoadPrediction'

    site_no = Column(String(50), primary_key=True)
    curve_start_time = Column(DateTime, nullable=False, comment='曲线开始时刻')
    curve_end_time = Column(DateTime, nullable=False, comment='曲线结束时刻')
    time_list = Column(String(2000), nullable=False)
    power_list = Column(String(2000), nullable=False)
    created_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))


class PVAndMeterRealtimeDataDB(Base):
    """场站实时数据表"""
    __tablename__ = 'PVAndMeterRealtimeData'

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    site_no = Column(String(50), nullable=False, index=True)
    ts = Column(BigInteger, nullable=False, comment='采样点时间毫秒时间戳')
    pv_power = Column(Integer, nullable=False, comment='当前光伏发电功率，单位 kW')
    pv_status = Column(String(20), nullable=False, comment='光伏状态：discharge/non-discharge')
    meter_value = Column(Integer, nullable=False, comment='市电电表输入功率')
    created_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC))


class ESRealtimeDataDB(Base):
    """储能实时数据表"""
    __tablename__ = 'ESRealtimeData'

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    site_no = Column(String(50), nullable=False, index=True)
    ts = Column(BigInteger, nullable=False, comment='采样点时间毫秒时间戳')
    es_sn = Column(String(50), nullable=False, comment='储能设备序列号')
    es_soc = Column(Integer, nullable=False, comment='储能电池状态（SOC）')
    es_power = Column(Integer, nullable=False, comment='储能电池功率，单位 kW')
    rated_cap = Column(DECIMAL(10, 2), nullable=False, comment='额定容量 单位kwh')
    real_cap = Column(DECIMAL(10, 2), nullable=False, comment='实时容量 单位kwh')
    es_max_soc = Column(Integer, nullable=False, comment='最大SOC')
    es_min_soc = Column(Integer, nullable=False, comment='最小SOC')
    status = Column(String(20), nullable=False, comment='状态：discharge/charging/standby')
    created_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC))


class ChargerRealtimeDataDB(Base):
    """充电桩实时数据表"""
    __tablename__ = 'ChargerRealtimeData'

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    site_no = Column(String(50), nullable=False, index=True)
    ts = Column(BigInteger, nullable=False, comment='采样点时间毫秒时间戳')
    pile_sn = Column(String(50), nullable=False, comment='充电桩序列号')
    bus_power_limit = Column(Integer, nullable=False, comment='桩总线功率限制')
    pile_group_name = Column(String(100), nullable=True, comment='桩所属桩组名')
    pile_group_power_limit = Column(Integer, nullable=False, comment='桩组功率限制')
    connector = Column(Integer, nullable=False, comment='充电枪接口编号')
    status = Column(String(20), nullable=False, comment='充电枪状态')
    power = Column(Integer, nullable=False, comment='充电枪功率')
    ocpp_limit = Column(Integer, nullable=False, comment='最大功率')
    created_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC))


class SiteSellPriceDataDB(Base):
    """场站卖电电价数据表"""
    __tablename__ = 'SiteSellPriceData'

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    site_no = Column(String(50), nullable=False, index=True)
    version = Column(String(50), nullable=False, comment='电价版本号，格式：YYYYMMDD_HHMMSS')
    start_time = Column(DateTime, nullable=False, comment='电价生效开始时间')
    end_time = Column(DateTime, nullable=False, comment='电价生效结束时间')
    price = Column(DECIMAL(10, 4), nullable=False, comment='电价')
    unit = Column(String(20), nullable=False, comment='电价单位')
    created_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))


class SiteDynamicPriceDataDB(Base):
    """场站采购电价数据表 动态电价"""
    __tablename__ = 'SiteDynamicPriceData'

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    site_no = Column(String(50), nullable=True, index=False)
    region = Column(String(50), nullable=False, index=True)
    date = Column(String(50), nullable=False, comment='电价日期，格式：YYYYMMDD')
    start_time = Column(DateTime, nullable=False, comment='电价生效开始时间')
    end_time = Column(DateTime, nullable=False, comment='电价生效结束时间')
    price = Column(DECIMAL(10, 4), nullable=False, comment='电价')
    unit = Column(String(20), nullable=False, comment='电价单位')
    created_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))


class SiteDemandDataDB(Base):
    """场站demand收费表"""
    __tablename__ = 'SiteDemandData'

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    site_no = Column(String(50), nullable=False, index=True)
    version = Column(String(50), nullable=False, comment='电价版本号，格式：YYYYMMDD_HHMMSS')
    start_time = Column(DateTime, nullable=False, comment='生效开始时间')
    end_time = Column(DateTime, nullable=False, comment='生效结束时间')
    price = Column(DECIMAL(10, 4), nullable=False, comment='电价')
    unit = Column(String(20), nullable=False, comment='电价单位')
    total_demand_target = Column(Integer, nullable=False, comment='总需求目标')
    target_demand_warning_ratio = Column(DECIMAL(5, 2), nullable=False, comment='目标需求警告比例')
    created_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))


class SiteFixedAndTimeOfUsePriceDataDB(Base):
    """场站购买电价数据表 固定电价 分时电价"""
    __tablename__ = 'SiteFixedAndTimeOfUsePriceData'

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    site_no = Column(String(50), nullable=False, index=True)
    belong_year = Column(Integer, nullable=False, comment='所属年份')
    belong_month = Column(Integer, nullable=False, comment='所属月份')
    start_time = Column(String(5), nullable=False, comment='时段划分开始时间，HH:mm格式')
    end_time = Column(String(5), nullable=False, comment='时段划分结束时间，HH:mm格式')
    time_type = Column(Integer, nullable=False, comment='时段类型 1:尖, 2:峰, 3:平, 4:谷')
    price = Column(DECIMAL(10, 4), nullable=False, comment='买电单价')
    unit = Column(String(20), nullable=False, comment='电价单位 dollar:美元, rmb:人民币')
    created_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime, nullable=False, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))
