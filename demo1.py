from datetime import datetime, timedel<PERSON>


def convert_to_hourly_prices(data):
    result = []
    full_day = data['today']

    # 提取日期（从第一条记录的开始时间中提取）
    date_str = full_day[0]['start_time'].split(" ")[0]
    current_time = datetime.strptime(f"{date_str} 00:00:00", "%Y-%m-%d %H:%M:%S")
    end_of_day = datetime.strptime(f"{date_str} 23:59:59", "%Y-%m-%d %H:%M:%S")

    # 构建逐小时时间段
    hourly_slots = []
    while current_time < end_of_day:
        next_time = current_time + timedelta(hours=1)
        if next_time > end_of_day:
            next_time = end_of_day
        hourly_slots.append((current_time, next_time))
        current_time = next_time

    # 匹配每个小时的价格
    for slot_start, slot_end in hourly_slots:
        for item in full_day:
            item_start = datetime.strptime(item['start_time'], "%Y-%m-%d %H:%M:%S")
            item_end = datetime.strptime(item['end_time'], "%Y-%m-%d %H:%M:%S")
            if item_start <= slot_start < item_end:
                result.append({
                    "start_time": slot_start.strftime("%Y-%m-%d %H:%M:%S"),
                    "end_time": slot_end.strftime("%Y-%m-%d %H:%M:%S"),
                    "price": item['price'],
                    "unit": item['unit']
                })
                break
    return result


import json

data = {
	"today": [{
		"start_time": "2025-07-16 00:00:00",
		"end_time": "2025-07-16 08:00:00",
		"price": 0.2699,
		"unit": "RMB",
		"version": "20250716_140000"
	}, {
		"start_time": "2025-07-16 08:00:00",
		"end_time": "2025-07-16 10:00:00",
		"price": 0.6951,
		"unit": "RMB",
		"version": "20250716_140000"
	}, {
		"start_time": "2025-07-16 10:00:00",
		"end_time": "2025-07-16 12:00:00",
		"price": 1.176,
		"unit": "RMB",
		"version": "20250716_140000"
	}, {
		"start_time": "2025-07-16 12:00:00",
		"end_time": "2025-07-16 14:00:00",
		"price": 0.6951,
		"unit": "RMB",
		"version": "20250716_140000"
	},{
		"start_time": "2025-07-16 14:00:00",
		"end_time": "2025-07-16 23:59:59",
		"price": 1.176,
		"unit": "RMB",
		"version": "20250716_140000"
	}]
}

hourly_prices = convert_to_hourly_prices(data)
for item in hourly_prices:
    print(item)
