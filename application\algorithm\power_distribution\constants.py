from collections import defaultdict

MODULE_NUM = 8
MODULES_EDGES = [(1, 2), (2, 3), (3, 4), (4, 1), 
                 (1, 6), (2, 7), (3, 8), (4, 5),
                 (6, 7), (7, 8), (8, 5), (5, 6)]
N1_MODULES_GRAPH = defaultdict(list)
for edge in MODULES_EDGES:
    N1_MODULES_GRAPH[edge[0]].append(edge[1])
    N1_MODULES_GRAPH[edge[1]].append(edge[0])

DEFAULT_DIRECTED_CONNECTIONS = {
    0: 2,
    1: 4,
    2: 6,
    3: 8,
    
    4: 1,
    5: 3,
    6: 5,
    7: 7
}
