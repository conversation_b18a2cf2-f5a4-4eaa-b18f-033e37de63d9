from typing import List, Dict, Optional, Tuple, Set
from copy import deepcopy
from itertools import combinations
from collections import deque, defaultdict

from application.utils.logger import setup_logger
from application.algorithm.power_distribution.constants import DEFAULT_DIRECTED_CONNECTIONS, N1_MODULES_GRAPH

logger = setup_logger("n1_pile_distribution_dp", direction="algorithm", console_output=False)


class N1PileBestPowerDistribution:
    def __init__(self, gun_demands: List[float], modules_powers: List[float],
                 directed_connections: Optional[Dict[int, int]] = None,
                 vehicle_vols: Optional[List[float]] = None, switch_current_limit: float = 350.00):
        """
        Args:
            gun_demands: 枪头功率需求, 单位: kW, eg: [120.00, 75.25, 80.30, 100.00]
            modules_powers: 模块功率, 单位: kW, eg: [40, 80, 40, 80, 40, 80, 40, 80]
            directed_connections: 枪头与模块的连接关系, 单位: 枪头编号, eg: {0: 2, 1: 4, 2: 6, 3: 8, 4: 1, 5: 3, 6: 5, 7: 7}
            vehicle_vols: 车辆电压平台, 单位: V, eg: [400, 400, 400, 400]
            switch_current_limit: 开关电流限制, 单位: A, eg: 350.00
        """
        self.gun_demands = self._get_init_processed_gun_demands(gun_demands, modules_powers)
        if vehicle_vols is None:
            self.vehicle_vols = [400] * len(gun_demands)  # 默认充电枪对应的车辆电压为400V平台
        else:
            self.vehicle_vols = vehicle_vols
        self.modules_powers = modules_powers
        self.directed_connections = DEFAULT_DIRECTED_CONNECTIONS if directed_connections is None \
            else directed_connections
        self.switch_current_limit = switch_current_limit

    @staticmethod
    def _get_init_processed_gun_demands(gun_demands: List[float], modules_powers: List[float]) -> List[float]:
        pile_limit = sum(modules_powers)
        # 需求大于充电桩功率限制，削减超标需求
        ret_gun_demands = []
        for i in range(len(gun_demands)):
            fmt_val = min(gun_demands[i], pile_limit)
            ret_gun_demands.append(fmt_val)
        return ret_gun_demands

    @staticmethod
    def _is_valid_subgraph(sub_graph_nodes: List[int], root_node: int):
        travel_visited = {}  # {node: 1, ...}

        def dfs(node: int):
            if node in travel_visited or node not in sub_graph_nodes:
                return

            travel_visited[node] = 1
            for neighbor in N1_MODULES_GRAPH[node]:
                if neighbor in travel_visited or neighbor not in sub_graph_nodes:
                    continue
                dfs(neighbor)

        dfs(root_node)
        return sum(travel_visited.values()) == len(sub_graph_nodes)

    @staticmethod
    def __get_directed_graph(bfs_paths: List[List[int]]) -> Dict[int, List[int]]:
        directed_graph = defaultdict(list)
        for path in bfs_paths:
            for i in range(len(path) - 1):
                if path[i + 1] not in directed_graph[path[i]]:
                    directed_graph[path[i]].append(path[i + 1])
        return directed_graph

    def _is_over_current_limit(self, sub_graph_nodes: List[int], root_node: int, threshold_step: int = 3):
        directed_connections_reverse = {v: k for k, v in self.directed_connections.items()}
        vehicle_vol = self.vehicle_vols[directed_connections_reverse[root_node]]

        # 最短路径算法
        all_paths = []
        visited = {root_node}
        queue = deque([(root_node, [root_node])])
        while queue:
            cur_node, cur_path = queue.popleft()
            for neighbor in N1_MODULES_GRAPH[cur_node]:
                if neighbor in visited or neighbor not in sub_graph_nodes:
                    continue

                visited.add(neighbor)
                new_path = cur_path + [neighbor]
                queue.append((neighbor, new_path))
                all_paths.append(new_path)
        
        # 构建有向图
        new_directed_graph = self.__get_directed_graph(all_paths)

        # 深度优先（后序）遍历，计算总电流
        def dfs(node: int) -> float:
            ret_current = self.modules_powers[node - 1] * 1000 / vehicle_vol

            for nex_node in new_directed_graph[node]:
                ret_current += dfs(nex_node)
            return ret_current
        
        # 减去枪头直连的电流
        root_node_current = self.modules_powers[root_node - 1] * 1000 / vehicle_vol
        check_current = dfs(root_node) - root_node_current
        return check_current > self.switch_current_limit
    
    def _get_all_choices(self, optional_nodes: List[int], start_node: int,
                         directed_connections_reverse: Dict[int, int]):
        # 剪枝：枪直连节点需求小于等于模块功率，则直接返回
        s_node_demand = self.gun_demands[directed_connections_reverse[start_node]]
        if s_node_demand <= self.modules_powers[start_node - 1]:
            return [[start_node]]

        extra_nodes = set(optional_nodes) - {start_node}

        def get_branches(choose_cnt: int):
            # choose_cnt >= 1
            all_combinations = combinations(extra_nodes, choose_cnt - 1)  # 至少返回一个 [()]
            branches = []
            for combo in all_combinations:
                branch = [start_node] + list(combo)
                # 剪枝：检查子图是否有效 && 电流是否超标
                if self._is_valid_subgraph(branch, start_node) and not self._is_over_current_limit(branch, start_node):
                    branches.append(branch)
            return branches

        choices = []
        for k in range(1, len(optional_nodes) + 1):
            iter_choices = get_branches(k)
            choices.extend(iter_choices)
        return choices
    
    def calculate_objective_value(self, gun_distribution: List[List[int]], unused_cnt: int = 0) -> float:
        total_abs_diff, used_gun_cnt = 0, 0
        # 计算绝对值偏差和
        for gun_idx, dst_nodes in enumerate(gun_distribution):
            distributed_powers = sum([self.modules_powers[i - 1] for i in dst_nodes])

            if len(dst_nodes) == 0:
                continue
            gun_abs_diff = abs(self.gun_demands[gun_idx] / distributed_powers - 1)
            total_abs_diff += gun_abs_diff
            used_gun_cnt += 1
        
        # 未被分配的模组越多，则越不重要
        alpha = 10 ** unused_cnt
        return round(total_abs_diff / used_gun_cnt * alpha, 5)
    
    def distributed_unused_modules(self, min_gun_distribution: List[List[int]]) -> Tuple[List[List[int]], int]:
        min_gun_distribution = deepcopy(min_gun_distribution)

        used_modules = []
        gun_remain_demands = []  # [[gun_idx, remain_ratio, is_gain_new_module], ...]
        for gun_idx, dst_nodes in enumerate(min_gun_distribution):
            used_modules.extend(dst_nodes)

            distributed_powers = sum([self.modules_powers[i - 1] for i in dst_nodes])
            remain_demand = self.gun_demands[gun_idx] - distributed_powers
            if remain_demand > 0:
                remain_ratio = round(remain_demand / self.gun_demands[gun_idx], 5)
                gun_remain_demands.append([gun_idx, remain_ratio, False])
        
        gun_remain_demands.sort(key=lambda x: x[1], reverse=True)
        unused_modules = [node for node in range(1, len(self.modules_powers) + 1) if node not in used_modules]
        cnt = 0
        for unused_node in unused_modules:
            for item in gun_remain_demands:
                gun_idx, remain_demand, is_gain_new_module = item
                if is_gain_new_module:
                    continue
                
                new_branch = min_gun_distribution[gun_idx] + [unused_node]
                if self._is_valid_subgraph(new_branch, new_branch[0]) and not self._is_over_current_limit(new_branch, new_branch[0]):
                    item[2] = True
                    min_gun_distribution[gun_idx] = new_branch
                    cnt += 1
                    break
        
        # 取min(有额外需求的枪数量, 未被分配的模组数量)
        min_cnt = min(len(gun_remain_demands), len(unused_modules))
        return min_gun_distribution, min_cnt - cnt  # (新的分配方案, 未被分配的模组数量)

    def get_all_possible_gun_distributions(self):
        gun_num, module_num = len(self.gun_demands), len(self.modules_powers)
        directed_connections_reverse = {v: k for k, v in self.directed_connections.items()}

        used_guns = [i for i in range(gun_num) if self.gun_demands[i] > 0]
        total_distributions = []
        if len(used_guns) == 0:
            return total_distributions

        # dfs 生成所有可能的分配方案
        def dfs(optional_nodes: List[int], used_gun_index: int, gun_distribution: List[List[int]]):
            if used_gun_index == len(used_guns):
                format_distri = []
                for gun_idx in range(gun_num):
                    if gun_idx in used_guns:
                        format_distri.append(deepcopy(gun_distribution[used_guns.index(gun_idx)]))
                    else:
                        format_distri.append([])
                total_distributions.append(format_distri)
                return

            # 枪直连节点
            gun_start_node = self.directed_connections[used_guns[used_gun_index]]
            choices = self._get_all_choices(optional_nodes, gun_start_node, directed_connections_reverse)
            for choice in choices:
                next_gun_index = used_gun_index + 1
                if next_gun_index < len(used_guns):
                    # 枪直连节点
                    next_gun_start_node = self.directed_connections[used_guns[next_gun_index]]
                    next_optional_nodes_set = {next_gun_start_node}.union(set(optional_nodes) - set(choice))
                else:
                    next_optional_nodes_set = set()

                gun_distribution.append(choice)
                dfs(list(next_optional_nodes_set), next_gun_index, gun_distribution)
                gun_distribution.pop()

        # 初始化可选节点
        used_nodes = [self.directed_connections[i] for i in used_guns]
        init_optional_nodes = [self.directed_connections[used_guns[0]]]
        init_optional_nodes += [i for i in range(1, module_num + 1) if i not in used_nodes]
        dfs(init_optional_nodes, 0, [])

        # 返回所有被使用的枪的分配方案，已使用的枪不为空，且至少含有一个节点，未被使用的枪的分配方案为空
        return total_distributions

    def get_best_power_distribution(self):
        gun_num = len(self.gun_demands)
        used_guns = [i for i in range(gun_num) if self.gun_demands[i] > 0]
        if len(used_guns) == 0:
            return 0, [[] for _ in range(gun_num)]

        total_distributions = self.get_all_possible_gun_distributions()

        # 计算所有方案的平均绝对偏差, 并返回偏差最小的方案
        all_dis_list = []
        for gun_distribution in total_distributions:
            avg_abs_diff = self.calculate_objective_value(gun_distribution)
            all_dis_list.append((avg_abs_diff, gun_distribution))
        
        # 排序，并找出所有偏差最小的方案
        all_dis_list.sort(key=lambda x: x[0])
        all_min_dis_list = [item for item in all_dis_list if item[0] == all_dis_list[0][0]]

        # 分配未被使用的模组
        ret_dis = []  # [(min_abs_diff, gun_distribution), ...]
        for _, gun_distribution in all_min_dis_list:
            new_gun_distribution, unused_cnt = self.distributed_unused_modules(gun_distribution)
            avg_abs_diff = self.calculate_objective_value(new_gun_distribution, unused_cnt)
            ret_dis.append((avg_abs_diff, new_gun_distribution))
        
        ret_dis.sort(key=lambda x: x[0])
        min_abs_diff = ret_dis[0][0]
        ret_dis = [item for item in ret_dis if item[0] == min_abs_diff]
        
        # log input & output
        logger.info(f"\nInput(N1桩功率需求):"
                    f"\n\t{self.gun_demands}"
                    f"\nOutput(所有最佳分配方案): \n\t{ret_dis}")
        return ret_dis[0][0], ret_dis[0][1]


if __name__ == "__main__":
    md_powers, veh_vols = [40, 80, 40, 80, 40, 80, 40, 80], [400, 400, 400, 400]
    test_demands = [
        [75, 0, 0, 0],  # 0.0625, [[2], [], [], []]
        [170, 90, 70, 128],  # 0.12604, [[2, 1, 3], [4, 5], [6], [8, 7]]
        [75, 135, 0, 220],  # 0.10069, [[2], [4, 1, 5], [], [8, 3, 6, 7]]
        [140, 180, 90, 70],  # 0.125, [[2, 1, 7], [4, 3, 5], [6], [8]]
        [95, 105, 85, 70],  # 0.1875, [[2, 3], [4, 1], [6, 5], [8]]
    ]
    
    for i, t_g_demands in enumerate(test_demands):
        n1_pile = N1PileBestPowerDistribution(t_g_demands, md_powers, vehicle_vols=veh_vols)
        r1, r2 = n1_pile.get_best_power_distribution()

        print("\n" + "=" * 50 + f"测试数据: {i + 1}" + "=" * 50)
        print("Input:\n", t_g_demands)
        print("Output:\n", r1, r2)
