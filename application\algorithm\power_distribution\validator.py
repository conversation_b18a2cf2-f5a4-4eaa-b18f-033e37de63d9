from typing import List, Dict, Optional
from pydantic import BaseModel


class DemandInfoValueItem(BaseModel):
    predicted_time: int
    vehicle_non_suppressed_power_list: List[float]
    vehicle_vol: float


class ModuleListItem(BaseModel):
    moduleNo: int
    unitNum: int
    unitPower: float


class GunBusListItem(BaseModel):
    sn: Optional[str] = None
    busNo: int
    gunNo: int
    bomIndex: int


class GunMaxCurrItem(BaseModel):
    gunNo: int
    maxCurr: float


class ModuleInfoItem(BaseModel):
    sn: str
    chargeType: str
    gunNum: int
    ratedPower: float
    directedConnections: Optional[Dict[int, int]] = None
    moduleList: Optional[List[ModuleListItem]] = None
    gunBusList: Optional[List[GunBusListItem]] = None
    gunMaxCurr: Optional[List[GunMaxCurrItem]] = None


class InputData(BaseModel):
    pv_power: float
    es_power: float
    site_grid_limit: float
    site_demand_limit: float
    site_pile_info: List[str]
    module_info: List[ModuleInfoItem]
    demand_info: Dict[str, DemandInfoValueItem]
