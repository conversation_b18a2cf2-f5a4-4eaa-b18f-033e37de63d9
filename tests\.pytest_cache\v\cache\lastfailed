{"test_energy_cloud_api.py": true, "test_energy_cloud_api.py::TestEnergyCloudAPI::test_send_electricity_price_curve_discontinuous": true, "test_energy_cloud_api.py::TestEnergyCloudAPI::test_send_electricity_price_curve_invalid_duration": true, "test_energy_cloud_api.py::TestEnergyCloudAPI::test_send_electricity_price_curve_success": true, "test_dynamic_price_fetch_retry.py": true, "test_weather_comprehensive.py::TestWeatherUtilityFunctions::test_formate_date_invalid_input": true}