import sys
import threading
from typing import Dict

from application.algorithm.vehicle_identification import ml
from application.algorithm.vehicle_identification.predict import predict as vehicle_identify
from application.algorithm_schedule.status_enumeration import ChargePileStatus
from application.db_operate.db_operate import DBOperate
from application.utils.event_bus import event_bus, EventType
from application.utils.logger import setup_logger

sys.modules['ml'] = ml
logger = setup_logger("noc_algorithm_schedule", direction="algorithm_schedule")


class NocAlgorithmSchedule:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(NocAlgorithmSchedule, cls).__new__(cls)
            return cls._instance

    def __init__(self):
        self.db_operate = DBOperate()
        self.last_short_term_forecast_time = None

    def handle_charger_event(self, charger_sn, status) -> None:
        """
        处理插/拔的事件
        此处只会有3个状态VEHICLE_CONNECTED FORMAL_CHARGING CHARGING_COMPLETED
        更新chargerSession表充电状态
        """
        try:
            # 更新chargerSession表充电状态
            self.db_operate.update_charger_session_status(charger_sn, status)

            # 如果是插枪事件，触发非压制功率预测
            if status == ChargePileStatus.VEHICLE_CONNECTED:
                # 获取场站编号
                site_no = self.db_operate.get_site_no_by_charger_sn(charger_sn)
                if not site_no:
                    logger.error(f"Failed to get site_no for charger_sn: {charger_sn}")
                    return

                # 发布插枪事件
                event_bus.publish(
                    EventType.CHARGER_PLUGGED,
                    site_no=site_no,
                    charger_sn=charger_sn
                )

                logger.info(f"Published charger plugged event for site {site_no}, charger {charger_sn}")

        except Exception as e:
            logger.error(f"Error in handle_charger_event: {str(e)}")
            raise

    def recognize_vehicle(self, ev_model_data: Dict, is_startup=False):
        """车型识别算法"""
        try:
            mac_addr = ev_model_data.get('mac_addr')
            local_id = ev_model_data.get('local_id')
            capacity = ev_model_data.get("capacity") * 1000
            max_voltage = ev_model_data.get("max_voltage")
            max_current = ev_model_data.get("max_current")
            max_power = ev_model_data.get("max_power") * 1000

            # 调用车型识别算法
            input_param = {
                'MACAddress': mac_addr,
                'BatteryCapacity': capacity,
                'MaximumVoltage': max_voltage,
                'MaximumCurrent': max_current,
                'MaximumPower': max_power
            }

            if is_startup:
                recognized_vehicle = vehicle_identify(input_param)
                logger.info(f"车型识别输入参数 {input_param} 启动帧车型识别结果 {recognized_vehicle}")
            else:
                current_soc = ev_model_data.get("soc")
                consumed_energy = ev_model_data.get("consumed_energy")
                # 获取起始SOC
                start_soc = self.db_operate.get_start_soc_by_local_id(local_id)
                if start_soc is None:
                    logger.error(f"Failed to get start SOC for local_id: {local_id}")
                    return False

                # 检查SOC差值是否满足条件
                soc_diff = abs(current_soc - start_soc)
                if soc_diff <= 2:
                    logger.warning(f"SOC difference too small for local_id: {local_id}. "
                                   f"Current SOC: {current_soc}, Start SOC: {start_soc}, "
                                   f"Difference: {soc_diff}")
                    return False

                recognized_vehicle = vehicle_identify(input_param, start_soc, current_soc, consumed_energy)
                # 关键日志，数据库不记录过程，日志记录，不可删除
                logger.info(f"车型识别输入参数 {input_param} 二次车型识别结果 {recognized_vehicle}")

            if not recognized_vehicle:
                logger.warning(f"枪: {ev_model_data.get('charger_sn')} 车型识别失败")
                return False
            if "Unknown" in recognized_vehicle:
                logger.info(f"Unknown 车型识别结果 {recognized_vehicle}")
            # 创建EVModel记录
            ev_model_data['recognized_vehicle'] = recognized_vehicle
            if not self.db_operate.create_or_update_ev_model(ev_model_data):
                logger.error(f"Failed to create ev model for local_id: {local_id}")
                return False

            return True
        except Exception as e:
            logger.error(f"Vehicle recognition failed: {str(e)}")
            raise

