#!/usr/bin/env python3
"""
测试动态电价重试机制
"""

import sys
import os
import logging
import time
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('dynamic_price_retry_test.log', mode='w', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def test_retry_mechanism():
    """测试重试机制"""
    logger = setup_logging()
    
    logger.info("=" * 60)
    logger.info("开始测试动态电价重试机制")
    logger.info("=" * 60)
    
    try:
        from application.algorithm_schedule.dynamic_price_scheduler import DynamicPriceScheduler, DynamicPriceRetryConfig
        
        # 创建调度器实例
        scheduler = DynamicPriceScheduler()
        
        # 测试场景1：模拟部分国家获取失败
        logger.info("\n--- 测试场景1：模拟部分国家获取失败 ---")
        
        # 模拟API调用
        call_count = {}
        for country in DynamicPriceRetryConfig.SUPPORTED_COUNTRIES:
            call_count[country] = 0
        
        def mock_get_dynamic_electricity_price(areas, date, currency="EUR"):
            """模拟API调用"""
            call_count[areas] += 1
            
            # 模拟不同国家的不同行为
            if areas == 'DE':
                # 德国第一次就成功
                return {
                    'prices': [{'price': 100 + i, 'deliveryStart': f'2024-01-01T{i:02d}:00:00Z', 'deliveryEnd': f'2024-01-01T{i+1:02d}:00:00Z'} for i in range(24)],
                    'marketMainCurrency': 'EUR'
                }
            elif areas == 'FR':
                # 法国第二次成功
                if call_count[areas] >= 2:
                    return {
                        'prices': [{'price': 200 + i, 'deliveryStart': f'2024-01-01T{i:02d}:00:00Z', 'deliveryEnd': f'2024-01-01T{i+1:02d}:00:00Z'} for i in range(24)],
                        'marketMainCurrency': 'EUR'
                    }
                return None
            elif areas == 'NL':
                # 荷兰第三次成功
                if call_count[areas] >= 3:
                    return {
                        'prices': [{'price': 300 + i, 'deliveryStart': f'2024-01-01T{i:02d}:00:00Z', 'deliveryEnd': f'2024-01-01T{i+1:02d}:00:00Z'} for i in range(24)],
                        'marketMainCurrency': 'EUR'
                    }
                return None
            else:
                # 其他国家都失败
                return None
        
        # 模拟数据库操作
        mock_db = Mock()
        mock_db.save_site_electricity_price = Mock()
        
        with patch('application.algorithm_schedule.dynamic_price_scheduler.get_dynamic_electricity_price', side_effect=mock_get_dynamic_electricity_price), \
             patch('application.algorithm_schedule.dynamic_price_scheduler.DBOperate', return_value=mock_db), \
             patch('application.algorithm_schedule.dynamic_price_scheduler.time.sleep') as mock_sleep:  # 加速测试
            
            # 设置短重试间隔进行测试
            original_intervals = DynamicPriceRetryConfig.RETRY_INTERVALS
            DynamicPriceRetryConfig.RETRY_INTERVALS = [0.01, 0.02, 0.03, 0.04, 0.05]  # 使用很短的间隔
            
            try:
                test_date = datetime.now().date() + timedelta(days=1)
                success_countries, failed_countries = scheduler._fetch_all_countries_with_retry(test_date)
                
                logger.info(f"成功获取的国家: {success_countries}")
                logger.info(f"失败的国家: {failed_countries}")
                logger.info(f"各国调用次数: {call_count}")
                
                # 验证结果
                assert 'DE' in success_countries, "德国应该成功"
                assert 'FR' in success_countries, "法国应该成功"
                assert 'NL' in success_countries, "荷兰应该成功"
                assert len(failed_countries) == 3, f"应该有3个国家失败，实际: {failed_countries}"
                
                # 验证重试次数
                assert call_count['DE'] == 1, f"德国应该调用1次，实际: {call_count['DE']}"
                assert call_count['FR'] == 2, f"法国应该调用2次，实际: {call_count['FR']}"
                assert call_count['NL'] == 3, f"荷兰应该调用3次，实际: {call_count['NL']}"
                
                logger.info("✅ 测试场景1通过")
                
            finally:
                # 恢复原始配置
                DynamicPriceRetryConfig.RETRY_INTERVALS = original_intervals
        
        # 测试场景2：测试数据验证
        logger.info("\n--- 测试场景2：测试数据验证 ---")
        
        # 测试无效数据
        invalid_data_cases = [
            (None, "空数据"),
            ({}, "空字典"),
            ({'prices': []}, "空prices数组"),
            ({'prices': [{'price': 100}] * 12}, "数据不完整（12小时）"),
            ({'prices': [{'price': 100}] * 24}, "缺少必要字段"),
        ]
        
        for invalid_data, description in invalid_data_cases:
            is_valid = scheduler._is_valid_country_price_data(invalid_data, "TEST")
            logger.info(f"无效数据测试 ({description}): {is_valid} (期望: False)")
            assert not is_valid, f"无效数据应该返回 False: {description}"
        
        # 测试有效数据
        valid_data = {
            'prices': [
                {
                    'price': 100 + i,
                    'deliveryStart': f'2024-01-01T{i:02d}:00:00Z',
                    'deliveryEnd': f'2024-01-01T{i+1:02d}:00:00Z'
                } for i in range(24)
            ],
            'marketMainCurrency': 'EUR'
        }
        
        is_valid = scheduler._is_valid_country_price_data(valid_data, "TEST")
        logger.info(f"有效数据测试: {is_valid} (期望: True)")
        assert is_valid, "有效数据应该返回 True"
        
        logger.info("✅ 测试场景2通过")
        
        # 测试场景3：测试状态获取
        logger.info("\n--- 测试场景3：测试状态获取 ---")
        
        status = scheduler.get_retry_status()
        logger.info("重试状态信息:")
        logger.info(f"  支持的国家: {status['supported_countries']}")
        logger.info(f"  重试次数统计: {status['countries_retry_count']}")
        logger.info(f"  成功状态: {status['countries_success_status']}")
        logger.info(f"  配置信息: {status['config']}")
        
        assert 'countries_retry_count' in status, "状态应该包含重试次数"
        assert 'countries_success_status' in status, "状态应该包含成功状态"
        assert 'supported_countries' in status, "状态应该包含支持的国家"
        assert 'config' in status, "状态应该包含配置信息"
        
        logger.info("✅ 测试场景3通过")
        
        logger.info("\n" + "=" * 60)
        logger.info("所有测试通过！重试机制工作正常")
        logger.info("=" * 60)
        
        return True
        
    except ImportError as e:
        logger.error(f"导入模块失败: {e}")
        return False
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_config_validation():
    """测试配置验证"""
    logger = logging.getLogger(__name__)
    
    logger.info("\n" + "=" * 60)
    logger.info("测试配置验证")
    logger.info("=" * 60)
    
    try:
        from application.algorithm_schedule.dynamic_price_scheduler import DynamicPriceRetryConfig
        
        # 验证配置的合理性
        assert len(DynamicPriceRetryConfig.SUPPORTED_COUNTRIES) == 6, "应该支持6个国家"
        assert DynamicPriceRetryConfig.MAX_RETRIES_PER_COUNTRY > 0, "最大重试次数应该大于0"
        assert len(DynamicPriceRetryConfig.RETRY_INTERVALS) > 0, "重试间隔列表不能为空"
        assert DynamicPriceRetryConfig.MAX_TOTAL_RETRY_TIME > 0, "最大总重试时间应该大于0"
        
        logger.info("配置验证:")
        logger.info(f"  支持的国家: {DynamicPriceRetryConfig.SUPPORTED_COUNTRIES}")
        logger.info(f"  每国家最大重试次数: {DynamicPriceRetryConfig.MAX_RETRIES_PER_COUNTRY}")
        logger.info(f"  重试间隔: {DynamicPriceRetryConfig.RETRY_INTERVALS} 分钟")
        logger.info(f"  最大总重试时间: {DynamicPriceRetryConfig.MAX_TOTAL_RETRY_TIME} 分钟")
        logger.info(f"  电价发布时间: {DynamicPriceRetryConfig.PRICE_PUBLISH_TIME}:10")
        
        logger.info("✅ 配置验证通过")
        return True
        
    except Exception as e:
        logger.error(f"配置验证失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("开始测试动态电价重试机制")
    logger.info(f"测试时间: {datetime.now()}")
    logger.info(f"Python版本: {sys.version}")
    
    success1 = test_config_validation()
    success2 = test_retry_mechanism()
    
    if success1 and success2:
        logger.info("\n🎉 所有测试都通过了！")
        logger.info("动态电价重试机制已成功实现")
        logger.info("特性:")
        logger.info("  ✅ 支持6个国家的电价获取")
        logger.info("  ✅ 每个国家独立重试机制")
        logger.info("  ✅ 智能重试间隔")
        logger.info("  ✅ 后台重试任务")
        logger.info("  ✅ 数据有效性验证")
        logger.info("  ✅ 详细的状态监控")
        return True
    else:
        logger.error("\n❌ 部分测试失败")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
