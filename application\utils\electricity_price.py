import json
import time
import requests
import logging
import os
import base64
from pathlib import Path
from requests.auth import HTTPBasicAuth
from cryptography.fernet import Fernet


class NordPoolAPI:
    _instance = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance.__initialized = False
        return cls._instance

    def __init__(self):
        if self.__initialized:
            return
        self.get_account()
        self.token_url = "https://sts.nordpoolgroup.com/connect/token"
        self.data_url = 'https://data-api.nordpoolgroup.com/api/v2/Auction/Prices/ByAreas'
        self.auth_header = "Basic Y2xpZW50X21hcmtldGRhdGFfYXBpOmNsaWVudF9tYXJrZXRkYXRhX2FwaQ=="
        # self.token = self.get_access_token()
        self.token_info = self.get_access_token()
        self.token_expiry_time = time.time() + self.token_info['expires_in']
        self.areas = ['AT', 'BE', 'DE', 'FR', 'NL', 'PL']
        self.__initialized = True

    def get_account(self):
        path = Path(__file__).resolve().parent.parent
        with open(os.path.join(path, 'settings', "secret.key"), "rb") as f:
            key = f.read()
        fernet = Fernet(key)
        with open(os.path.join(path, 'settings', "credentials.enc"), "rb") as f:
            token = f.read()
        decrypted_json = fernet.decrypt(token).decode()
        data = json.loads(decrypted_json)
        self.username = data["username"]
        self.password = data["password"]

    def get_access_token(self):
        try:
            payload = f"grant_type=password&scope=marketdata_api&username={self.username}&password={self.password}"
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "Authorization": self.auth_header
            }
            response = requests.post(self.token_url, data=payload, headers=headers)
            return response.json()
        except requests.exceptions.HTTPError as http_err:
            raise
        except Exception as err:
            logging.error(f"Other error occurred: {err}")
            raise

    def is_token_expired(self):
        return time.time() >= self.token_expiry_time

    def refresh_token(self):
        self.token_info = self.get_access_token()
        self.token_expiry_time = time.time() + self.token_info['expires_in']

    def get_token(self):
        if self.is_token_expired():
            self.refresh_token()
        # Return the current access token
        return self.token_info['access_token']

    def get_data(self, market: str, areas: str, currency: str, date: str):
        if areas not in self.areas:
            raise PermissionError(f"403 Forbidden: The region:{areas} don't have permission to access this resource.")
        try:
            access_token = self.get_token()
            params = {
                'market': market,
                'areas': areas,
                'currency': currency,
                'date': date
            }
            headers = {
                'Authorization': f'Bearer {access_token}'
            }
            response = requests.get(self.data_url, headers=headers, params=params)
            response.raise_for_status()

            return response.json()

        except Exception as err:
            raise err


if __name__ == "__main__":
    api = NordPoolAPI()
    price_data = api.get_data(market="DayAhead", areas="NL", currency="EUR", date="2025-07-20")
    print(price_data)
