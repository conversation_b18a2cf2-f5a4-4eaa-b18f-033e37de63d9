[{"MACAddress": "f07f0c00b435", "MaximumCurrent": 125, "MaximumVoltage": 400.20000000000005, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 13.89, "VehicleLabel": "BMW_i3@60Ah_18.8kWh", "Source": "bmw_i3_60Ah_2015.pcapng"}, {"MACAddress": "ec65ccb123a1", "MaximumCurrent": 500.0, "MaximumVoltage": 399.0, "MaximumPower": 250000, "BatteryCapacity": null, "BMSDataFrequency": 2.639, "VehicleLabel": "BMW_iX@xDrive40_71kWh", "Source": "bwm_ix-xDrive40_xxx_2022.pcapng"}, {"MACAddress": "007DFA07DF9E", "MaximumCurrent": 500.0, "MaximumVoltage": 408.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.138, "VehicleLabel": "Cupra_Born_77kWh", "Source": "cupra_born_77kwh_xxx.pcapng"}, {"MACAddress": "001823380566", "MaximumCurrent": 500.0, "MaximumVoltage": 414.0, "MaximumPower": 207000, "BatteryCapacity": null, "BMSDataFrequency": 4.546, "VehicleLabel": "Ford_Mustang@Mach-E_91kWh", "Source": "ford_mustang-mach-e_xxx_2023.pcapng"}, {"MACAddress": "e00ee10211b3", "MaximumCurrent": 200.0, "MaximumVoltage": 421.40000000000003, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 9.089, "VehicleLabel": "Hyundai_Kona@electric_64kWh", "Source": "hyundai_kona_64kwh.pcapng"}, {"MACAddress": "001a377186d5", "MaximumCurrent": 285, "MaximumVoltage": 471, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 6.648, "VehicleLabel": "Jaguar_I-Pace_84.7kWh", "Source": "jaguar_i-pace_xxx_2020.pcapng"}, {"MACAddress": "04e77e70a410", "MaximumCurrent": 230.0, "MaximumVoltage": 427.20000000000005, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 9.998, "VehicleLabel": "KIA_Niro@EV_64.8kWh", "Source": "kia_eniro_64.8kwh_2023_1.pcapng"}, {"MACAddress": "04e77e711483", "MaximumCurrent": 230.0, "MaximumVoltage": 427.20000000000005, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 9.398, "VehicleLabel": "KIA_Niro@EV_64.8kWh", "Source": "kia_eniro_64.8kwh_2023_2.pcapng"}, {"MACAddress": "44422f03f8ef", "MaximumCurrent": 200.0, "MaximumVoltage": 421.40000000000003, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 9.026, "VehicleLabel": "KIA_Niro@EV_64kWh", "Source": "kia_eniro_64kwh_2021.pcapng"}, {"MACAddress": "fca47a133cd7", "MaximumCurrent": 385, "MaximumVoltage": 452.40000000000003, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 2.223, "VehicleLabel": "MG_4_61.7kWh", "Source": "mg_4_64kwh_2023.pcapng"}, {"MACAddress": "fca47a10dc3e", "MaximumCurrent": 217.8, "MaximumVoltage": 450, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 2.217, "VehicleLabel": "MG_ZS@SUV_49kWh", "Source": "mg_zs_xxx_xxx.pcapng"}, {"MACAddress": "a453ee0468f4", "MaximumCurrent": 250, "MaximumVoltage": 470, "MaximumPower": 100000, "BatteryCapacity": 50000, "BMSDataFrequency": 6.951, "VehicleLabel": "Opel_Corsa_46.3kWh", "Source": "opel_corsae_50kwh_2023.pcapng"}, {"MACAddress": "a453ee0430fe", "MaximumCurrent": 250, "MaximumVoltage": 470, "MaximumPower": 100000, "BatteryCapacity": 50000, "BMSDataFrequency": 7.009, "VehicleLabel": "Peugeot_e-2008_46.3kWh", "Source": "peugeot_e2008_50kwh_2023.pcapng"}, {"MACAddress": "a453ee0526bb", "MaximumCurrent": 250, "MaximumVoltage": 470, "MaximumPower": 100000, "BatteryCapacity": 50000, "BMSDataFrequency": 6.997, "VehicleLabel": "Peugeot_e-208_46.3kWh", "Source": "peugeot_e208_50kwh_2023.pcapng"}, {"MACAddress": "7cbc8440758f", "MaximumCurrent": 165.0, "MaximumVoltage": 412.0, "MaximumPower": 50000, "BatteryCapacity": null, "BMSDataFrequency": 5.0, "VehicleLabel": "Renault_ZOE_52kWh", "Source": "renault_zoe_xxx_2020.pcapng"}, {"MACAddress": "007DFA03F9F5", "MaximumCurrent": 120.0, "MaximumVoltage": 357.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.203, "VehicleLabel": "Skoda_Citigo_32.3kWh", "Source": "skoda_citygo_xxx_2020.pcapng"}, {"MACAddress": "007dfa072a2e", "MaximumCurrent": 350.0, "MaximumVoltage": 469.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 6.259, "VehicleLabel": "Skoda_Enyaq@60_58kWh", "Source": "skoda_iv60_58kwh_2022.pcapng"}, {"MACAddress": "007DFA09F6B7", "MaximumCurrent": 500.0, "MaximumVoltage": 408.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.146, "VehicleLabel": "Skoda_Enyaq@80_77kWh", "Source": "skoda_iv80_77kwh_2023.pcapng"}, {"MACAddress": "dc442711e7d5", "MaximumCurrent": 549.0, "MaximumVoltage": 423.0, "MaximumPower": 232000, "BatteryCapacity": null, "BMSDataFrequency": 19.62, "VehicleLabel": "Tesla_Model@3_49kWh", "Source": "tesla_model3_57.5kwh_2019.pcapng"}, {"MACAddress": "dc442712c19f", "MaximumCurrent": 799.0, "MaximumVoltage": 423.0, "MaximumPower": 326000, "BatteryCapacity": null, "BMSDataFrequency": 28.463, "VehicleLabel": "Tesla_Model@3_75kWh", "Source": "tesla_model3_75kwh_2019.pcapng"}, {"MACAddress": "98ed5cab4c02", "MaximumCurrent": 799.0, "MaximumVoltage": 423.0, "MaximumPower": 326000, "BatteryCapacity": null, "BMSDataFrequency": 28.229, "VehicleLabel": "Tesla_Model@Y_75kWh", "Source": "tesla_modely_75kwh_2022.pcapng"}, {"MACAddress": "48c58db86911", "MaximumCurrent": 375.0, "MaximumVoltage": 475, "MaximumPower": null, "BatteryCapacity": 66840, "BMSDataFrequency": 7.119, "VehicleLabel": "Volvo_XC40_67kWh", "Source": "volvo_xc40_67kwh_2022.pcapng"}, {"MACAddress": "007dfa06babe", "MaximumCurrent": 500.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 6.766, "VehicleLabel": "Volkswagen_ID.3_58kWh", "Source": "vw_id3_58kwh_2023.pcapng"}, {"MACAddress": "007DFA081C75", "MaximumCurrent": 500.0, "MaximumVoltage": 408.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.03, "VehicleLabel": "Volkswagen_ID.4_77kWh", "Source": "vw_id4_77kwh_2022.pcapng"}, {"MACAddress": "007dfa067204", "MaximumCurrent": 500.0, "MaximumVoltage": 408.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.144, "VehicleLabel": "Volkswagen_ID.5_77kWh", "Source": "vw_id5_77kwh_2022.pcapng"}, {"MACAddress": "00182398275E", "MaximumCurrent": 500, "MaximumVoltage": 460, "MaximumPower": 230000, "BatteryCapacity": null, "BMSDataFrequency": 3.483, "VehicleLabel": "Mercedes-Benz_EQE_90.6kWh", "Source": "benz_eqe_xxx_2023.pcapng"}, {"MACAddress": "00182335A6F2", "MaximumCurrent": 500, "MaximumVoltage": 460, "MaximumPower": 230000, "BatteryCapacity": null, "BMSDataFrequency": 3.502, "VehicleLabel": "Mercedes-Benz_EQS_107.8kWh", "Source": "benz_eqs450+_xxx_2022.pcapng"}, {"MACAddress": "0c86c7018317", "MaximumCurrent": 500.0, "MaximumVoltage": 430.0, "MaximumPower": 250000, "BatteryCapacity": null, "BMSDataFrequency": 2.634, "VehicleLabel": "BMW_i4@eDrive35_67kWh", "Source": "bwm_i4_xxx_2023.pcapng"}, {"MACAddress": "0c86c700a1c9", "MaximumCurrent": 500.0, "MaximumVoltage": 453.0, "MaximumPower": 250000, "BatteryCapacity": null, "BMSDataFrequency": 2.626, "VehicleLabel": "BMW_iX@xDrive50_105.2kWh", "Source": "bwm_ix_xxx_2022.pcapng"}, {"MACAddress": "001823A84679", "MaximumCurrent": 500.0, "MaximumVoltage": 414.0, "MaximumPower": 207000, "BatteryCapacity": null, "BMSDataFrequency": 4.411, "VehicleLabel": "Ford_Mustang@Mach-E_91kWh", "Source": "ford_mach-e_xxx_2023.pcapng"}, {"MACAddress": "18b6cc702d34", "MaximumCurrent": 320.0, "MaximumVoltage": 619.2, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 9.95, "VehicleLabel": "GENESIS_G80_82.5kWh", "Source": "genesis_g80_xxx_2023.pcapng"}, {"MACAddress": "e00ee1025f1b", "MaximumCurrent": 200.0, "MaximumVoltage": 421.40000000000003, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 8.951, "VehicleLabel": "Hyundai_Kona@electric_64kWh", "Source": "hyundai_kona_xxx_2021.pcapng"}, {"MACAddress": "9012a1706bfa", "MaximumCurrent": 310.0, "MaximumVoltage": 825.6, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 9.984, "VehicleLabel": "Hyundai_Ioniq@5_74kWh", "Source": "hyundai_loniq5_xxx_2022.pcapng"}, {"MACAddress": "04e77e00908c", "MaximumCurrent": 310.0, "MaximumVoltage": 825.6, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 9.78, "VehicleLabel": "KIA_EV6_54kWh", "Source": "kia_ev6_xxx_2022.pcapng"}, {"MACAddress": "ac965b5be769", "MaximumCurrent": 500.1, "MaximumVoltage": 926.0, "MaximumPower": 350000, "BatteryCapacity": 106040, "BMSDataFrequency": 0.919, "VehicleLabel": "Lucid_Air@Grand@Touring_112kWh", "Source": "lucid_grand-touring_xxx_2022.pcapng"}, {"MACAddress": "f07f0c1e1f15", "MaximumCurrent": 400.0, "MaximumVoltage": 726.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.145, "VehicleLabel": "Porsche_Taycan_71kWh", "Source": "prosche_taycan_xxx_2023.pcapng"}, {"MACAddress": "706979c00bd3", "MaximumCurrent": 500, "MaximumVoltage": 480, "MaximumPower": 250000, "BatteryCapacity": 135000, "BMSDataFrequency": 7.955, "VehicleLabel": "Rivian_R1S_128.9kWh", "Source": "rivian-r1t_xxx_2022.pcapng"}, {"MACAddress": "706979c13f15", "MaximumCurrent": 500, "MaximumVoltage": 480, "MaximumPower": 250000, "BatteryCapacity": 135000, "BMSDataFrequency": 5.413, "VehicleLabel": "Rivian_R1S_128.9kWh", "Source": "rivian_r1s_xxx_2023.pcapng"}, {"MACAddress": "98ed5cc119ed", "MaximumCurrent": 499.0, "MaximumVoltage": 430.0, "MaximumPower": 214000, "BatteryCapacity": null, "BMSDataFrequency": 27.934, "VehicleLabel": "Tesla_Model@3_57.5kWh", "Source": "tesla_model3_57.5kwh.pcapng"}, {"MACAddress": "98ed5cc119ed", "MaximumCurrent": 499.0, "MaximumVoltage": 430.0, "MaximumPower": 214000, "BatteryCapacity": null, "BMSDataFrequency": 27.601, "VehicleLabel": "Tesla_Model@3_57.5kWh", "Source": "tesla_model3_57.5kwh1.pcapng"}, {"MACAddress": "98ed5c8703a7", "MaximumCurrent": 399.0, "MaximumVoltage": 417.0, "MaximumPower": 254000, "BatteryCapacity": null, "BMSDataFrequency": 30.806, "VehicleLabel": "Tesla_Model@X_85.5kWh", "Source": "tesla_modelx-90d_xxx_2017.pcapng"}, {"MACAddress": "98ed5cdf1105", "MaximumCurrent": 799.0, "MaximumVoltage": 481.0, "MaximumPower": 326000, "BatteryCapacity": null, "BMSDataFrequency": 27.651, "VehicleLabel": "Tesla_Model@X_95kWh", "Source": "tesla_modelx_64kwh_2023.pcapng"}, {"MACAddress": "98ed5ca568e2", "MaximumCurrent": 799.0, "MaximumVoltage": 423.0, "MaximumPower": 326000, "BatteryCapacity": null, "BMSDataFrequency": 27.799, "VehicleLabel": "Tesla_Model@Y_75kWh", "Source": "tesla_modely_64kwh_2022.pcapng"}, {"MACAddress": "007DFA092202", "MaximumCurrent": 500.0, "MaximumVoltage": 413.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.144, "VehicleLabel": "Volkswagen_ID.4_77kWh", "Source": "vw_id4_xxx_2023.pcapng"}, {"MACAddress": "dc4427129fcd", "MaximumCurrent": 799.0, "MaximumVoltage": 423.0, "MaximumPower": 326000.0, "BatteryCapacity": null, "BMSDataFrequency": 30.468, "VehicleLabel": "Tesla_Model@3_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231202_165058.log"}, {"MACAddress": "e00ee100555b", "MaximumCurrent": 200.0, "MaximumVoltage": 421.4, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 9.929, "VehicleLabel": "Hyundai_Kona@electric_64kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231202_165235.log"}, {"MACAddress": "48c58db898c3", "MaximumCurrent": 375.0, "MaximumVoltage": 475.0, "MaximumPower": null, "BatteryCapacity": 76640.0, "BMSDataFrequency": 7.188, "VehicleLabel": "Polestar_2_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231202_165235.log"}, {"MACAddress": "0c86c703ce81", "MaximumCurrent": 500.0, "MaximumVoltage": 430.0, "MaximumPower": 250000.0, "BatteryCapacity": null, "BMSDataFrequency": 2.644, "VehicleLabel": "BMW_i4@eDrive35_67kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231202_171602.log"}, {"MACAddress": "184cae114025", "MaximumCurrent": 165.0, "MaximumVoltage": 412.0, "MaximumPower": 50000.0, "BatteryCapacity": null, "BMSDataFrequency": 5.031, "VehicleLabel": "Renault_ZOE_52kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231202_171602.log"}, {"MACAddress": "007dfa074d1f", "MaximumCurrent": 500.0, "MaximumVoltage": 413.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.22, "VehicleLabel": "Skoda_Enyaq@80_77kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231203_095011.log"}, {"MACAddress": "48c58db20e8a", "MaximumCurrent": 375.0, "MaximumVoltage": 475.0, "MaximumPower": null, "BatteryCapacity": 77840.0, "BMSDataFrequency": 7.181, "VehicleLabel": "Volvo_XC40_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231203_095011.log"}, {"MACAddress": "007dfa0764d1", "MaximumCurrent": 500.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.192, "VehicleLabel": "Skoda_Enyaq@60_58kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231203_130910.log"}, {"MACAddress": "e00ee100261d", "MaximumCurrent": 200.0, "MaximumVoltage": 412.8, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 9.853, "VehicleLabel": "Hyundai_Ioniq_28kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231203_141422.log"}, {"MACAddress": "000102030405", "MaximumCurrent": 250.0, "MaximumVoltage": 470.0, "MaximumPower": 100000.0, "BatteryCapacity": 50000.0, "BMSDataFrequency": 7.137, "VehicleLabel": "Peugeot_e-208_46.3kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231203_141422.log"}, {"MACAddress": "007dfa08397f", "MaximumCurrent": 350.0, "MaximumVoltage": 469.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.227, "VehicleLabel": "Cupra_Born_58kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231203_152727.log"}, {"MACAddress": "98ed5cdd56cc", "MaximumCurrent": 499.0, "MaximumVoltage": 430.0, "MaximumPower": 214000.0, "BatteryCapacity": null, "BMSDataFrequency": 26.566, "VehicleLabel": "Tesla_Model@3_57.5kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231203_152727.log"}, {"MACAddress": "f07f0c0e38bf", "MaximumCurrent": 125.0, "MaximumVoltage": 411.6, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 13.715, "VehicleLabel": "MINI_Cooper@SE_28.9kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231203_170210.log"}, {"MACAddress": "481693e488b1", "MaximumCurrent": 375.0, "MaximumVoltage": 475.0, "MaximumPower": null, "BatteryCapacity": 66400.0, "BMSDataFrequency": 7.187, "VehicleLabel": "Volvo_XC40_67kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231203_000000.log"}, {"MACAddress": "007dfa06c183", "MaximumCurrent": 500.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.234, "VehicleLabel": "Skoda_Enyaq@60_58kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231203_000000.log"}, {"MACAddress": "7cbc844246f3", "MaximumCurrent": 165.0, "MaximumVoltage": 412.0, "MaximumPower": 50000.0, "BatteryCapacity": null, "BMSDataFrequency": 5.266, "VehicleLabel": "Renault_ZOE_52kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231203_113100.log"}, {"MACAddress": "007dfa0976e9", "MaximumCurrent": 500.0, "MaximumVoltage": 408.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.147, "VehicleLabel": "Cupra_Born_77kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231203_113100.log"}, {"MACAddress": "98ed5cd79bf3", "MaximumCurrent": 499.0, "MaximumVoltage": 430.0, "MaximumPower": 214000.0, "BatteryCapacity": null, "BMSDataFrequency": 26.667, "VehicleLabel": "Tesla_Model@Y_57.5kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231203_113100.log"}, {"MACAddress": "ec65ccb075ec", "MaximumCurrent": 500.0, "MaximumVoltage": 489.0, "MaximumPower": 250000.0, "BatteryCapacity": null, "BMSDataFrequency": 2.645, "VehicleLabel": "BMW_i4@eDrive40_80.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231203_142042.log"}, {"MACAddress": "d00ea4022d46", "MaximumCurrent": 350.0, "MaximumVoltage": 459.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.137, "VehicleLabel": "Audi_e-tron_64.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231203_142042.log"}, {"MACAddress": "04e77e00fdb1", "MaximumCurrent": 230.0, "MaximumVoltage": 427.2, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 10.708, "VehicleLabel": "KIA_Niro@EV_64.8kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231203_162044.log"}, {"MACAddress": "007dfa08bb11", "MaximumCurrent": 500.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.213, "VehicleLabel": "Skoda_Enyaq@60_58kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231203_165855.log"}, {"MACAddress": "48c58db2b276", "MaximumCurrent": 375.0, "MaximumVoltage": 475.0, "MaximumPower": null, "BatteryCapacity": 74200.0, "BMSDataFrequency": 7.195, "VehicleLabel": "Polestar_2_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231203_190146.log"}, {"MACAddress": "0018230f75ac", "MaximumCurrent": 500.0, "MaximumVoltage": 422.0, "MaximumPower": 211000.0, "BatteryCapacity": null, "BMSDataFrequency": 4.605, "VehicleLabel": "Ford_Mustang@Mach-E_72.6kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231204_133346.log"}, {"MACAddress": "00182334072b", "MaximumCurrent": 500.0, "MaximumVoltage": 422.0, "MaximumPower": 211000.0, "BatteryCapacity": null, "BMSDataFrequency": 4.604, "VehicleLabel": "Ford_Mustang@Mach-E_72.6kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231204_161130.log"}, {"MACAddress": "48c58db2b276", "MaximumCurrent": 375.0, "MaximumVoltage": 475.0, "MaximumPower": null, "BatteryCapacity": 74340.0, "BMSDataFrequency": 7.194, "VehicleLabel": "Polestar_2_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231204_204319.log"}, {"MACAddress": "007dfa07c0b8", "MaximumCurrent": 500.0, "MaximumVoltage": 408.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.135, "VehicleLabel": "Volkswagen_ID.BUZZ_77kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231204_000000.log"}, {"MACAddress": "007dfa09c8a6", "MaximumCurrent": 500.0, "MaximumVoltage": 413.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.145, "VehicleLabel": "Volkswagen_ID.4_77kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231204_113103.log"}, {"MACAddress": "9c36f80053a2", "MaximumCurrent": 350.0, "MaximumVoltage": 653.6, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.684, "VehicleLabel": "KIA_EV9_96kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231204_125952.log"}, {"MACAddress": "007dfa0713c6", "MaximumCurrent": 500.0, "MaximumVoltage": 408.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.138, "VehicleLabel": "Volkswagen_ID.5_77kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231204_193832.log"}, {"MACAddress": "fca47a111414", "MaximumCurrent": 217.8, "MaximumVoltage": 450.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 2.219, "VehicleLabel": "MG_ZS@SUV_49kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231204_193832.log"}, {"MACAddress": "7cbc84412d27", "MaximumCurrent": 165.0, "MaximumVoltage": 412.0, "MaximumPower": 50000.0, "BatteryCapacity": null, "BMSDataFrequency": 5.379, "VehicleLabel": "Renault_ZOE_52kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231205_000000.log"}, {"MACAddress": "a453ee016565", "MaximumCurrent": 250.0, "MaximumVoltage": 470.0, "MaximumPower": 100000.0, "BatteryCapacity": 50000.0, "BMSDataFrequency": 7.139, "VehicleLabel": "Citroen_Jumpy_68kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231205_000000.log"}, {"MACAddress": "007dfa08cf2a", "MaximumCurrent": 500.0, "MaximumVoltage": 408.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.123, "VehicleLabel": "Volkswagen_ID.BUZZ_77kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231205_132652.log"}, {"MACAddress": "007dfa099a5e", "MaximumCurrent": 500.0, "MaximumVoltage": 413.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.215, "VehicleLabel": "Skoda_Enyaq@80_77kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231205_144211.log"}, {"MACAddress": "f07f0c0a88fe", "MaximumCurrent": 350.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.133, "VehicleLabel": "Audi_e-tron_86.5kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231205_154104.log"}, {"MACAddress": "007dfa065b56", "MaximumCurrent": 500.0, "MaximumVoltage": 408.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.133, "VehicleLabel": "Volkswagen_ID.4_77kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231205_174635.log"}, {"MACAddress": "ec65ccabe44f", "MaximumCurrent": 500.0, "MaximumVoltage": 488.0, "MaximumPower": 250000.0, "BatteryCapacity": null, "BMSDataFrequency": 2.644, "VehicleLabel": "BMW_i4@eDrive40_80.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231205_000001.log"}, {"MACAddress": "007dfa07e16f", "MaximumCurrent": 500.0, "MaximumVoltage": 408.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.141, "VehicleLabel": "Volkswagen_ID.4_77kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231205_134954.log"}, {"MACAddress": "481693e50484", "MaximumCurrent": 375.0, "MaximumVoltage": 475.0, "MaximumPower": null, "BatteryCapacity": 66840.0, "BMSDataFrequency": 7.196, "VehicleLabel": "Polestar_2_67kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231205_145548.log"}, {"MACAddress": "007dfa02c483", "MaximumCurrent": 125.0, "MaximumVoltage": 369.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.383, "VehicleLabel": "Volkswagen_Golf_32kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231205_160346.log"}, {"MACAddress": "fca47a111414", "MaximumCurrent": 217.8, "MaximumVoltage": 450.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 2.221, "VehicleLabel": "MG_ZS@SUV_49kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231205_184525.log"}, {"MACAddress": "f07f0c0b5f94", "MaximumCurrent": 350.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.137, "VehicleLabel": "Audi_e-tron_86.5kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231206_000000.log"}, {"MACAddress": "a453ee007e79", "MaximumCurrent": 250.0, "MaximumVoltage": 470.0, "MaximumPower": 100000.0, "BatteryCapacity": 50000.0, "BMSDataFrequency": 7.131, "VehicleLabel": "Opel_Mokka_46.3kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231206_095106.log"}, {"MACAddress": "f07f0c05d8a2", "MaximumCurrent": 350.0, "MaximumVoltage": 459.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.139, "VehicleLabel": "Audi_e-tron_64.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231206_120420.log"}, {"MACAddress": "ec65ccdc3f38", "MaximumCurrent": 500.0, "MaximumVoltage": 347.0, "MaximumPower": 250000.0, "BatteryCapacity": 100000.0, "BMSDataFrequency": 2.649, "VehicleLabel": "BMW_iX1@xDrive30_64.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231206_133626.log"}, {"MACAddress": "007dfa062e1e", "MaximumCurrent": 500.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.142, "VehicleLabel": "Cupra_Born_58kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231206_133626.log"}, {"MACAddress": "007dfa06c029", "MaximumCurrent": 500.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.138, "VehicleLabel": "Volkswagen_ID.3_58kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231206_000000.log"}, {"MACAddress": "a453ee016565", "MaximumCurrent": 250.0, "MaximumVoltage": 470.0, "MaximumPower": 100000.0, "BatteryCapacity": 50000.0, "BMSDataFrequency": 7.139, "VehicleLabel": "Citroen_Jumpy_68kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231206_000000.log"}, {"MACAddress": "f07f0c0b1d21", "MaximumCurrent": 350.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.124, "VehicleLabel": "Audi_e-tron_86.5kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231206_093122.log"}, {"MACAddress": "dc442713904e", "MaximumCurrent": 799.0, "MaximumVoltage": 423.0, "MaximumPower": 326000.0, "BatteryCapacity": null, "BMSDataFrequency": 30.359, "VehicleLabel": "Tesla_Model@3_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231207_100251.log"}, {"MACAddress": "007dfa095f23", "MaximumCurrent": 500.0, "MaximumVoltage": 408.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.141, "VehicleLabel": "Volkswagen_ID.4_77kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231207_112021.log"}, {"MACAddress": "007dfa06b455", "MaximumCurrent": 500.0, "MaximumVoltage": 408.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.14, "VehicleLabel": "Volkswagen_ID.BUZZ_77kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231207_132642.log"}, {"MACAddress": "48c58db635e9", "MaximumCurrent": 375.0, "MaximumVoltage": 475.0, "MaximumPower": null, "BatteryCapacity": 75340.0, "BMSDataFrequency": 7.191, "VehicleLabel": "Polestar_2_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231207_145803.log"}, {"MACAddress": "98ed5ca0546d", "MaximumCurrent": 799.0, "MaximumVoltage": 423.0, "MaximumPower": 326000.0, "BatteryCapacity": null, "BMSDataFrequency": 26.588, "VehicleLabel": "Tesla_Model@3_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231207_211604.log"}, {"MACAddress": "e00ee1009000", "MaximumCurrent": 200.0, "MaximumVoltage": 421.4, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 9.793, "VehicleLabel": "Hyundai_Kona@electric_64kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231207_000000.log"}, {"MACAddress": "ec65ccf82b6e", "MaximumCurrent": 500.0, "MaximumVoltage": 347.0, "MaximumPower": 250000.0, "BatteryCapacity": 100000.0, "BMSDataFrequency": 2.636, "VehicleLabel": "BMW_iX1@xDrive30_64.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231207_132327.log"}, {"MACAddress": "007dfa0895cd", "MaximumCurrent": 500.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.138, "VehicleLabel": "Volkswagen_ID.3_58kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231207_132327.log"}, {"MACAddress": "a453ee016565", "MaximumCurrent": 250.0, "MaximumVoltage": 470.0, "MaximumPower": 100000.0, "BatteryCapacity": 50000.0, "BMSDataFrequency": 7.134, "VehicleLabel": "Citroen_Jumpy_68kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231208_000000.log"}, {"MACAddress": "9012a17198d2", "MaximumCurrent": 350.0, "MaximumVoltage": 806.4, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 10.806, "VehicleLabel": "Hyundai_Ioniq@6_74kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231208_121128.log"}, {"MACAddress": "04e77e002861", "MaximumCurrent": 350.0, "MaximumVoltage": 825.6, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 10.781, "VehicleLabel": "KIA_EV6_74kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231208_000000.log"}, {"MACAddress": "007dfa082e4e", "MaximumCurrent": 500.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.139, "VehicleLabel": "Skoda_Enyaq@60_58kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231208_095940.log"}, {"MACAddress": "0c86c700b864", "MaximumCurrent": 500.0, "MaximumVoltage": 488.0, "MaximumPower": 250000.0, "BatteryCapacity": null, "BMSDataFrequency": 2.646, "VehicleLabel": "BMW_i4@M50_80.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231204_000000.log"}, {"MACAddress": "98ed5ced525d", "MaximumCurrent": 799.0, "MaximumVoltage": 423.0, "MaximumPower": 326000.0, "BatteryCapacity": null, "BMSDataFrequency": 28.774, "VehicleLabel": "Tesla_Model@Y_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231204_000000.log"}, {"MACAddress": "98ed5cc073d0", "MaximumCurrent": 499.0, "MaximumVoltage": 430.0, "MaximumPower": 214000.0, "BatteryCapacity": null, "BMSDataFrequency": 26.634, "VehicleLabel": "Tesla_Model@3_57.5kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231208_121128.log"}, {"MACAddress": "007dfa066642", "MaximumCurrent": 500.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.196, "VehicleLabel": "Skoda_Enyaq@60_58kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231208_184411.log"}, {"MACAddress": "48c58db20e8a", "MaximumCurrent": 375.0, "MaximumVoltage": 475.0, "MaximumPower": null, "BatteryCapacity": 77800.0, "BMSDataFrequency": 7.186, "VehicleLabel": "Volvo_XC40_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231208_184411.log"}, {"MACAddress": "007dfa06f6d7", "MaximumCurrent": 500.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.219, "VehicleLabel": "Skoda_Enyaq@60_58kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231208_135658.log"}, {"MACAddress": "00182311afa6", "MaximumCurrent": 500.0, "MaximumVoltage": 414.0, "MaximumPower": 207000.0, "BatteryCapacity": null, "BMSDataFrequency": 4.746, "VehicleLabel": "Ford_Mustang@Mach-E_91kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231208_135658.log"}, {"MACAddress": "007dfa09640a", "MaximumCurrent": 500.0, "MaximumVoltage": 413.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.253, "VehicleLabel": "Skoda_Enyaq@80_77kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231208_163453.log"}, {"MACAddress": "007dfa0995c6", "MaximumCurrent": 500.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.139, "VehicleLabel": "Skoda_Enyaq@60_58kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231208_163453.log"}, {"MACAddress": "fca47a101f64", "MaximumCurrent": 234.3, "MaximumVoltage": 469.8, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 2.202, "VehicleLabel": "MG_ZS@SUV_42.5kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231209_001504.log"}, {"MACAddress": "98ed5ced4c02", "MaximumCurrent": 549.0, "MaximumVoltage": 418.0, "MaximumPower": 230000.0, "BatteryCapacity": null, "BMSDataFrequency": 26.427, "VehicleLabel": "Tesla_Model@Y_57.5kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231209_001504.log"}, {"MACAddress": "98ed5cc3ac40", "MaximumCurrent": 499.0, "MaximumVoltage": 430.0, "MaximumPower": 214000.0, "BatteryCapacity": null, "BMSDataFrequency": 26.657, "VehicleLabel": "Tesla_Model@Y_57.5kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231209_091844.log"}, {"MACAddress": "44422f03e72a", "MaximumCurrent": 200.0, "MaximumVoltage": 421.4, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 9.776, "VehicleLabel": "KIA_Niro@EV_64kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231209_103742.log"}, {"MACAddress": "007dfa03803d", "MaximumCurrent": 125.0, "MaximumVoltage": 369.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.343, "VehicleLabel": "Volkswagen_Golf_32kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231209_113722.log"}, {"MACAddress": "007dfa02c49c", "MaximumCurrent": 125.0, "MaximumVoltage": 369.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.358, "VehicleLabel": "Volkswagen_Golf_32kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231209_113722.log"}, {"MACAddress": "e00ee100beb3", "MaximumCurrent": 200.0, "MaximumVoltage": 421.4, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 9.856, "VehicleLabel": "Hyundai_Kona@electric_64kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231209_125115.log"}, {"MACAddress": "dc4427124fd3", "MaximumCurrent": 799.0, "MaximumVoltage": 423.0, "MaximumPower": 326000.0, "BatteryCapacity": null, "BMSDataFrequency": 30.423, "VehicleLabel": "Tesla_Model@3_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231209_125115.log"}, {"MACAddress": "f07f0c1ced8b", "MaximumCurrent": 125.0, "MaximumVoltage": 411.6, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 13.631, "VehicleLabel": "MINI_Cooper@SE_28.9kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231209_152250.log"}, {"MACAddress": "48c58db53acd", "MaximumCurrent": 375.0, "MaximumVoltage": 475.0, "MaximumPower": null, "BatteryCapacity": 76200.0, "BMSDataFrequency": 7.201, "VehicleLabel": "Polestar_2_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231209_172508.log"}, {"MACAddress": "48c58db898c3", "MaximumCurrent": 375.0, "MaximumVoltage": 475.0, "MaximumPower": null, "BatteryCapacity": 77000.0, "BMSDataFrequency": 7.19, "VehicleLabel": "Polestar_2_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231209_172508.log"}, {"MACAddress": "9012a1008817", "MaximumCurrent": 350.0, "MaximumVoltage": 774.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 10.722, "VehicleLabel": "Hyundai_Ioniq@5_70kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231209_000526.log"}, {"MACAddress": "ec65ccdc3f38", "MaximumCurrent": 500.0, "MaximumVoltage": 347.0, "MaximumPower": 250000.0, "BatteryCapacity": 100000.0, "BMSDataFrequency": 2.622, "VehicleLabel": "BMW_iX1@xDrive30_64.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231209_091636.log"}, {"MACAddress": "f07f0c228313", "MaximumCurrent": 400.0, "MaximumVoltage": 458.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.141, "VehicleLabel": "Audi_q8@e-tron_106kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231209_091636.log"}, {"MACAddress": "0018231141fe", "MaximumCurrent": 500.0, "MaximumVoltage": 422.0, "MaximumPower": 211000.0, "BatteryCapacity": null, "BMSDataFrequency": 4.756, "VehicleLabel": "Ford_Mustang@Mach-E_72.6kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231209_112346.log"}, {"MACAddress": "001823360f66", "MaximumCurrent": 251.0, "MaximumVoltage": 408.0, "MaximumPower": 111700.0, "BatteryCapacity": 42000.0, "BMSDataFrequency": 5.934, "VehicleLabel": "Fiat_500e_37.3kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231209_112346.log"}, {"MACAddress": "f07f0c099f73", "MaximumCurrent": 125.0, "MaximumVoltage": 415.1, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 13.779, "VehicleLabel": "BMW_i3@120Ah_37.9kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231209_124906.log"}, {"MACAddress": "007dfa085698", "MaximumCurrent": 500.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.19, "VehicleLabel": "Skoda_Enyaq@60_58kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231209_141955.log"}, {"MACAddress": "007dfa0715d3", "MaximumCurrent": 350.0, "MaximumVoltage": 418.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.216, "VehicleLabel": "Audi_Q4@e-tron_52kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231209_151714.log"}, {"MACAddress": "f07f0c06f142", "MaximumCurrent": 350.0, "MaximumVoltage": 459.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.145, "VehicleLabel": "Audi_e-tron_86.5kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231209_151714.log"}, {"MACAddress": "007dfa06c658", "MaximumCurrent": 500.0, "MaximumVoltage": 408.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.142, "VehicleLabel": "Volkswagen_ID.4_77kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231210_000000.log"}, {"MACAddress": "007dfa095954", "MaximumCurrent": 500.0, "MaximumVoltage": 413.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.205, "VehicleLabel": "Skoda_Enyaq@80_77kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231210_000000.log"}, {"MACAddress": "dc44271270c3", "MaximumCurrent": 549.0, "MaximumVoltage": 423.0, "MaximumPower": 232000.0, "BatteryCapacity": null, "BMSDataFrequency": 30.33, "VehicleLabel": "Tesla_Model@3_49kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231210_122234.log"}, {"MACAddress": "007dfa08ce2a", "MaximumCurrent": 500.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.138, "VehicleLabel": "Volkswagen_ID.3_58kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231210_125603.log"}, {"MACAddress": "a453ee031209", "MaximumCurrent": 250.0, "MaximumVoltage": 470.0, "MaximumPower": 100000.0, "BatteryCapacity": 50000.0, "BMSDataFrequency": 7.141, "VehicleLabel": "Opel_Mokka_46.3kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231210_132507.log"}, {"MACAddress": "04e77e00fdb1", "MaximumCurrent": 230.0, "MaximumVoltage": 427.2, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 10.802, "VehicleLabel": "KIA_Niro@EV_64.8kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231210_140959.log"}, {"MACAddress": "dc4427139070", "MaximumCurrent": 799.0, "MaximumVoltage": 423.0, "MaximumPower": 326000.0, "BatteryCapacity": null, "BMSDataFrequency": 30.339, "VehicleLabel": "Tesla_Model@3_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231210_150735.log"}, {"MACAddress": "007dfa099294", "MaximumCurrent": 500.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.139, "VehicleLabel": "Volkswagen_ID.3_58kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231210_000000.log"}, {"MACAddress": "04e77e7038c4", "MaximumCurrent": 350.0, "MaximumVoltage": 427.2, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 10.832, "VehicleLabel": "KIA_Niro@EV_64.8kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231210_000000.log"}, {"MACAddress": "007dfa074c73", "MaximumCurrent": 500.0, "MaximumVoltage": 408.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.138, "VehicleLabel": "Volkswagen_ID.4_77kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231210_112101.log"}, {"MACAddress": "98ed5cc073d0", "MaximumCurrent": 499.0, "MaximumVoltage": 430.0, "MaximumPower": 214000.0, "BatteryCapacity": null, "BMSDataFrequency": 26.572, "VehicleLabel": "Tesla_Model@3_57.5kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231210_122639.log"}, {"MACAddress": "007dfa09e905", "MaximumCurrent": 500.0, "MaximumVoltage": 408.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.137, "VehicleLabel": "Audi_Q4@e-tron_76.6kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231210_133024.log"}, {"MACAddress": "cc882637f966", "MaximumCurrent": 60.0, "MaximumVoltage": 430.0, "MaximumPower": 25800.0, "BatteryCapacity": null, "BMSDataFrequency": 4.19, "VehicleLabel": "Mercedes-Benz_CLA_10.9kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231210_133024.log"}, {"MACAddress": "ec65cca14b19", "MaximumCurrent": 500.0, "MaximumVoltage": 488.0, "MaximumPower": 250000.0, "BatteryCapacity": null, "BMSDataFrequency": 2.642, "VehicleLabel": "BMW_i4@M50_80.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231210_152128.log"}, {"MACAddress": "ec65ccabe44f", "MaximumCurrent": 500.0, "MaximumVoltage": 488.0, "MaximumPower": 250000.0, "BatteryCapacity": null, "BMSDataFrequency": 2.64, "VehicleLabel": "BMW_i4@eDrive40_80.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231210_152128.log"}, {"MACAddress": "a453ee039441", "MaximumCurrent": 250.0, "MaximumVoltage": 470.0, "MaximumPower": 100000.0, "BatteryCapacity": 50000.0, "BMSDataFrequency": 7.137, "VehicleLabel": "Peugeot_e-208_46.3kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231210_165618.log"}, {"MACAddress": "0c86c700b864", "MaximumCurrent": 500.0, "MaximumVoltage": 488.0, "MaximumPower": 250000.0, "BatteryCapacity": null, "BMSDataFrequency": 2.648, "VehicleLabel": "BMW_i4@M50_80.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231210_181227.log"}, {"MACAddress": "fca47a111414", "MaximumCurrent": 217.8, "MaximumVoltage": 450.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 2.22, "VehicleLabel": "MG_ZS@SUV_49kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231210_181227.log"}, {"MACAddress": "9012a1006c9a", "MaximumCurrent": 310.0, "MaximumVoltage": 619.2, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 10.822, "VehicleLabel": "Hyundai_Ioniq@5_54kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231210_152128.log"}, {"MACAddress": "001823347e46", "MaximumCurrent": 500.0, "MaximumVoltage": 422.0, "MaximumPower": 211000.0, "BatteryCapacity": null, "BMSDataFrequency": 4.72, "VehicleLabel": "Ford_Mustang@Mach-E_72.6kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231210_180500.log"}, {"MACAddress": "a453ee049a45", "MaximumCurrent": 250.0, "MaximumVoltage": 470.0, "MaximumPower": 100000.0, "BatteryCapacity": 50000.0, "BMSDataFrequency": 7.136, "VehicleLabel": "Citroen_e-Berlingo_46.3kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231211_000000.log"}, {"MACAddress": "e00ee100261d", "MaximumCurrent": 200.0, "MaximumVoltage": 412.8, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 9.782, "VehicleLabel": "Hyundai_Ioniq_28kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231211_000000.log"}, {"MACAddress": "98ed5cac24ad", "MaximumCurrent": 799.0, "MaximumVoltage": 423.0, "MaximumPower": 326000.0, "BatteryCapacity": null, "BMSDataFrequency": 28.447, "VehicleLabel": "Tesla_Model@Y_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231211_150241.log"}, {"MACAddress": "481693e73eed", "MaximumCurrent": 375.0, "MaximumVoltage": 475.0, "MaximumPower": null, "BatteryCapacity": 67000.0, "BMSDataFrequency": 7.192, "VehicleLabel": "Volvo_XC40_67kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231211_155729.log"}, {"MACAddress": "dc442713904e", "MaximumCurrent": 799.0, "MaximumVoltage": 423.0, "MaximumPower": 326000.0, "BatteryCapacity": null, "BMSDataFrequency": 30.463, "VehicleLabel": "Tesla_Model@3_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231211_000000.log"}, {"MACAddress": "007dfa06a87c", "MaximumCurrent": 500.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.138, "VehicleLabel": "Skoda_Enyaq@60_58kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231211_151306.log"}, {"MACAddress": "ec65ccabe44f", "MaximumCurrent": 500.0, "MaximumVoltage": 488.0, "MaximumPower": 250000.0, "BatteryCapacity": null, "BMSDataFrequency": 2.644, "VehicleLabel": "BMW_i4@eDrive40_80.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231211_151306.log"}, {"MACAddress": "e00ee100555b", "MaximumCurrent": 200.0, "MaximumVoltage": 421.4, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 9.869, "VehicleLabel": "Hyundai_Kona@electric_64kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231211_151306.log"}, {"MACAddress": "fca47a111414", "MaximumCurrent": 217.8, "MaximumVoltage": 450.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 2.22, "VehicleLabel": "MG_ZS@SUV_49kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231211_204754.log"}, {"MACAddress": "a453ee031209", "MaximumCurrent": 250.0, "MaximumVoltage": 470.0, "MaximumPower": 100000.0, "BatteryCapacity": 50000.0, "BMSDataFrequency": 7.136, "VehicleLabel": "Opel_Mokka_46.3kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231212_000000.log"}, {"MACAddress": "ec65ccb22945", "MaximumCurrent": 500.0, "MaximumVoltage": 487.0, "MaximumPower": 250000.0, "BatteryCapacity": null, "BMSDataFrequency": 2.643, "VehicleLabel": "BMW_i4@eDrive40_80.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231212_131406.log"}, {"MACAddress": "f07f0c211a82", "MaximumCurrent": 400.0, "MaximumVoltage": 458.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.132, "VehicleLabel": "Audi_q8@e-tron_106kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231212_131406.log"}, {"MACAddress": "d00ea4022d46", "MaximumCurrent": 350.0, "MaximumVoltage": 459.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.141, "VehicleLabel": "Audi_e-tron_64.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231212_131406.log"}, {"MACAddress": "d00ea4022d46", "MaximumCurrent": 350.0, "MaximumVoltage": 459.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.136, "VehicleLabel": "Audi_e-tron_64.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231212_175216.log"}, {"MACAddress": "0018233521ca", "MaximumCurrent": 251.0, "MaximumVoltage": 408.0, "MaximumPower": 111700.0, "BatteryCapacity": 42000.0, "BMSDataFrequency": 5.946, "VehicleLabel": "Fiat_500e_37.3kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231212_175216.log"}, {"MACAddress": "007dfa080419", "MaximumCurrent": 500.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.14, "VehicleLabel": "Volkswagen_ID.3_58kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231212_175216.log"}, {"MACAddress": "0018239454e5", "MaximumCurrent": 251.0, "MaximumVoltage": 408.0, "MaximumPower": 111700.0, "BatteryCapacity": 42000.0, "BMSDataFrequency": 5.945, "VehicleLabel": "Fiat_500e_37.3kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231212_210928.log"}, {"MACAddress": "04e77e002861", "MaximumCurrent": 350.0, "MaximumVoltage": 825.6, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 10.798, "VehicleLabel": "KIA_EV6_74kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231212_000000.log"}, {"MACAddress": "a453ee016565", "MaximumCurrent": 250.0, "MaximumVoltage": 470.0, "MaximumPower": 100000.0, "BatteryCapacity": 50000.0, "BMSDataFrequency": 8.396, "VehicleLabel": "Citroen_Jumpy_68kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231212_082712.log"}, {"MACAddress": "007dfa082d1b", "MaximumCurrent": 500.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.142, "VehicleLabel": "Volkswagen_ID.3_58kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231212_101657.log"}, {"MACAddress": "f07f0c05d8a2", "MaximumCurrent": 350.0, "MaximumVoltage": 459.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.14, "VehicleLabel": "Audi_e-tron_64.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231212_121832.log"}, {"MACAddress": "ec65ccabe44f", "MaximumCurrent": 500.0, "MaximumVoltage": 488.0, "MaximumPower": 250000.0, "BatteryCapacity": null, "BMSDataFrequency": 2.644, "VehicleLabel": "BMW_i4@eDrive40_80.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231212_125904.log"}, {"MACAddress": "0018239454e5", "MaximumCurrent": 251.0, "MaximumVoltage": 408.0, "MaximumPower": 111700.0, "BatteryCapacity": 42000.0, "BMSDataFrequency": 5.948, "VehicleLabel": "Fiat_500e_37.3kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231212_150846.log"}, {"MACAddress": "f07f0c1e179d", "MaximumCurrent": 350.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.142, "VehicleLabel": "Audi_q8@e-tron_89kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231212_150846.log"}, {"MACAddress": "e00ee100261d", "MaximumCurrent": 200.0, "MaximumVoltage": 412.8, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.473, "VehicleLabel": "Hyundai_Ioniq_28kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231212_150846.log"}, {"MACAddress": "0030ab29c5be", "MaximumCurrent": 500.0, "MaximumVoltage": 453.0, "MaximumPower": 250000.0, "BatteryCapacity": null, "BMSDataFrequency": 5.716, "VehicleLabel": "BMW_iX@xDrive50_105.2kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231212_150846.log"}, {"MACAddress": "7cbc844ba2ee", "MaximumCurrent": 350.0, "MaximumVoltage": 412.0, "MaximumPower": 130000.0, "BatteryCapacity": null, "BMSDataFrequency": 5.268, "VehicleLabel": "Nissan_Ariya_87kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231214_135607.log"}, {"MACAddress": "0018230f75ac", "MaximumCurrent": 500.0, "MaximumVoltage": 422.0, "MaximumPower": 211000.0, "BatteryCapacity": null, "BMSDataFrequency": 4.581, "VehicleLabel": "Ford_Mustang@Mach-E_72.6kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231214_151229.log"}, {"MACAddress": "f07f0c1ced8b", "MaximumCurrent": 125.0, "MaximumVoltage": 411.6, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 13.672, "VehicleLabel": "MINI_Cooper@SE_28.9kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231214_163128.log"}, {"MACAddress": "0c86c700b864", "MaximumCurrent": 500.0, "MaximumVoltage": 488.0, "MaximumPower": 250000.0, "BatteryCapacity": null, "BMSDataFrequency": 2.646, "VehicleLabel": "BMW_i4@M50_80.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231214_175001.log"}, {"MACAddress": "007dfa09d2de", "MaximumCurrent": 500.0, "MaximumVoltage": 408.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.138, "VehicleLabel": "Audi_Q4@e-tron_76.6kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231214_175001.log"}, {"MACAddress": "0018239454e5", "MaximumCurrent": 251.0, "MaximumVoltage": 408.0, "MaximumPower": 111700.0, "BatteryCapacity": 42000.0, "BMSDataFrequency": 5.927, "VehicleLabel": "Fiat_500e_37.3kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231214_204933.log"}, {"MACAddress": "dc442710dee7", "MaximumCurrent": 799.0, "MaximumVoltage": 423.0, "MaximumPower": 326000.0, "BatteryCapacity": null, "BMSDataFrequency": 30.265, "VehicleLabel": "Tesla_Model@3_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231215_000000.log"}, {"MACAddress": "f07f0c05d8a2", "MaximumCurrent": 350.0, "MaximumVoltage": 459.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.127, "VehicleLabel": "Audi_e-tron_64.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231215_105921.log"}, {"MACAddress": "dc442711dae3", "MaximumCurrent": 799.0, "MaximumVoltage": 423.0, "MaximumPower": 326000.0, "BatteryCapacity": null, "BMSDataFrequency": 30.392, "VehicleLabel": "Tesla_Model@3_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231215_111800.log"}, {"MACAddress": "ec65ccd617ec", "MaximumCurrent": 500.0, "MaximumVoltage": 347.0, "MaximumPower": 250000.0, "BatteryCapacity": 100000.0, "BMSDataFrequency": 2.635, "VehicleLabel": "BMW_iX1@xDrive30_64.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231215_125139.log"}, {"MACAddress": "f07f0c16996b", "MaximumCurrent": 350.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.14, "VehicleLabel": "Audi_e-tron_86.5kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231215_125139.log"}, {"MACAddress": "00182334072b", "MaximumCurrent": 500.0, "MaximumVoltage": 422.0, "MaximumPower": 211000.0, "BatteryCapacity": null, "BMSDataFrequency": 4.637, "VehicleLabel": "Ford_Mustang@Mach-E_72.6kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231215_162731.log"}, {"MACAddress": "dc442719c7bd", "MaximumCurrent": 549.0, "MaximumVoltage": 423.0, "MaximumPower": 232000.0, "BatteryCapacity": null, "BMSDataFrequency": 30.388, "VehicleLabel": "Tesla_Model@3_49kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231215_171725.log"}, {"MACAddress": "007dfa07cd52", "MaximumCurrent": 500.0, "MaximumVoltage": 408.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.149, "VehicleLabel": "Volkswagen_ID.4_77kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231215_180340.log"}, {"MACAddress": "98ed5cd88067", "MaximumCurrent": 499.0, "MaximumVoltage": 430.0, "MaximumPower": 214000.0, "BatteryCapacity": null, "BMSDataFrequency": 26.612, "VehicleLabel": "Tesla_Model@3_57.5kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231215_184417.log"}, {"MACAddress": "481693e7a9bb", "MaximumCurrent": 375.0, "MaximumVoltage": 475.0, "MaximumPower": null, "BatteryCapacity": 75600.0, "BMSDataFrequency": 7.199, "VehicleLabel": "Polestar_2_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231215_194139.log"}, {"MACAddress": "48c58db2b276", "MaximumCurrent": 375.0, "MaximumVoltage": 475.0, "MaximumPower": null, "BatteryCapacity": 74940.0, "BMSDataFrequency": 7.198, "VehicleLabel": "Polestar_2_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231215_194139.log"}, {"MACAddress": "a453ee016565", "MaximumCurrent": 250.0, "MaximumVoltage": 470.0, "MaximumPower": 100000.0, "BatteryCapacity": 50000.0, "BMSDataFrequency": 7.137, "VehicleLabel": "Citroen_Jumpy_68kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231215_000000.log"}, {"MACAddress": "007dfa06ec71", "MaximumCurrent": 500.0, "MaximumVoltage": 413.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.132, "VehicleLabel": "Volkswagen_ID.4_52kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231215_094318.log"}, {"MACAddress": "007dfa0959db", "MaximumCurrent": 500.0, "MaximumVoltage": 408.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.139, "VehicleLabel": "Volkswagen_ID.4_77kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231215_114231.log"}, {"MACAddress": "280feb14892c", "MaximumCurrent": 300.0, "MaximumVoltage": 420.0, "MaximumPower": 126000.0, "BatteryCapacity": null, "BMSDataFrequency": 4.187, "VehicleLabel": "Mercedes-Benz_EQA@250_66.5kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231215_114231.log"}, {"MACAddress": "44422f010e99", "MaximumCurrent": 200.0, "MaximumVoltage": 421.4, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 9.83, "VehicleLabel": "KIA_Niro@EV_64kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231215_114231.log"}, {"MACAddress": "007dfa089637", "MaximumCurrent": 500.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.132, "VehicleLabel": "Skoda_Enyaq@60_58kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231215_151807.log"}, {"MACAddress": "04e77e00fdb1", "MaximumCurrent": 230.0, "MaximumVoltage": 427.2, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 10.742, "VehicleLabel": "KIA_Niro@EV_64.8kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231215_164235.log"}, {"MACAddress": "001823385c39", "MaximumCurrent": 300.0, "MaximumVoltage": 430.0, "MaximumPower": 129000.0, "BatteryCapacity": null, "BMSDataFrequency": 3.571, "VehicleLabel": "Mercedes-Benz_EQB@250_66.5kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231215_164235.log"}, {"MACAddress": "f07f0c1f15df", "MaximumCurrent": 400.0, "MaximumVoltage": 458.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.148, "VehicleLabel": "Audi_q8@e-tron_106kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231216_000000.log"}, {"MACAddress": "184cae1537d6", "MaximumCurrent": 350.0, "MaximumVoltage": 412.0, "MaximumPower": 130000.0, "BatteryCapacity": null, "BMSDataFrequency": 5.237, "VehicleLabel": "Renault_Megane@E-TECH_60kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231216_101831.log"}, {"MACAddress": "dc442711dae3", "MaximumCurrent": 799.0, "MaximumVoltage": 423.0, "MaximumPower": 326000.0, "BatteryCapacity": null, "BMSDataFrequency": 30.313, "VehicleLabel": "Tesla_Model@3_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231216_101831.log"}, {"MACAddress": "a453ee019a6d", "MaximumCurrent": 250.0, "MaximumVoltage": 470.0, "MaximumPower": 100000.0, "BatteryCapacity": 50000.0, "BMSDataFrequency": 7.14, "VehicleLabel": "Peugeot_e-208_46.3kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231216_124203.log"}, {"MACAddress": "dc442710dee7", "MaximumCurrent": 799.0, "MaximumVoltage": 423.0, "MaximumPower": 326000.0, "BatteryCapacity": null, "BMSDataFrequency": 30.144, "VehicleLabel": "Tesla_Model@3_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231216_124203.log"}, {"MACAddress": "007dfa08402a", "MaximumCurrent": 500.0, "MaximumVoltage": 408.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.141, "VehicleLabel": "Audi_Q4@e-tron_76.6kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231216_150926.log"}, {"MACAddress": "d00ea4022d46", "MaximumCurrent": 350.0, "MaximumVoltage": 459.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.133, "VehicleLabel": "Audi_e-tron_64.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231216_153513.log"}, {"MACAddress": "0018239454e5", "MaximumCurrent": 251.0, "MaximumVoltage": 408.0, "MaximumPower": 111700.0, "BatteryCapacity": 42000.0, "BMSDataFrequency": 5.93, "VehicleLabel": "Fiat_500e_37.3kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231216_000000.log"}, {"MACAddress": "007dfa0823f9", "MaximumCurrent": 500.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.137, "VehicleLabel": "Skoda_Enyaq@60_58kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231216_104706.log"}, {"MACAddress": "dc442711dae3", "MaximumCurrent": 799.0, "MaximumVoltage": 423.0, "MaximumPower": 326000.0, "BatteryCapacity": null, "BMSDataFrequency": 30.408, "VehicleLabel": "Tesla_Model@3_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231216_104706.log"}, {"MACAddress": "007dfa096f1b", "MaximumCurrent": 500.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.213, "VehicleLabel": "Skoda_Enyaq@60_58kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231216_135939.log"}, {"MACAddress": "04e77e00fdb1", "MaximumCurrent": 230.0, "MaximumVoltage": 427.2, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 10.843, "VehicleLabel": "KIA_Niro@EV_64.8kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231216_151103.log"}, {"MACAddress": "007dfa07b63f", "MaximumCurrent": 500.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.133, "VehicleLabel": "Skoda_Enyaq@60_58kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231216_165855.log"}, {"MACAddress": "000102030405", "MaximumCurrent": 250.0, "MaximumVoltage": 470.0, "MaximumPower": 100000.0, "BatteryCapacity": 50000.0, "BMSDataFrequency": 7.136, "VehicleLabel": "Peugeot_e-208_46.3kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231217_000000.log"}, {"MACAddress": "04e77e002861", "MaximumCurrent": 350.0, "MaximumVoltage": 825.6, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 10.714, "VehicleLabel": "KIA_EV6_74kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231217_113445.log"}, {"MACAddress": "007dfa083195", "MaximumCurrent": 500.0, "MaximumVoltage": 408.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.139, "VehicleLabel": "Audi_Q4@e-tron_76.6kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231217_113445.log"}, {"MACAddress": "a453ee059aef", "MaximumCurrent": 300.0, "MaximumVoltage": 500.0, "MaximumPower": 150000.0, "BatteryCapacity": 50000.0, "BMSDataFrequency": 6.666, "VehicleLabel": "Jeep_Avenger_50.8kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231217_113445.log"}, {"MACAddress": "98ed5cd88067", "MaximumCurrent": 499.0, "MaximumVoltage": 430.0, "MaximumPower": 214000.0, "BatteryCapacity": null, "BMSDataFrequency": 26.656, "VehicleLabel": "Tesla_Model@3_57.5kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231217_153402.log"}, {"MACAddress": "00182311afa6", "MaximumCurrent": 500.0, "MaximumVoltage": 414.0, "MaximumPower": 207000.0, "BatteryCapacity": null, "BMSDataFrequency": 4.76, "VehicleLabel": "Ford_Mustang@Mach-E_91kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231217_180809.log"}, {"MACAddress": "98ed5cc073d0", "MaximumCurrent": 499.0, "MaximumVoltage": 430.0, "MaximumPower": 214000.0, "BatteryCapacity": null, "BMSDataFrequency": 26.571, "VehicleLabel": "Tesla_Model@3_57.5kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231217_200125.log"}, {"MACAddress": "0018239454e5", "MaximumCurrent": 251.0, "MaximumVoltage": 408.0, "MaximumPower": 111700.0, "BatteryCapacity": 42000.0, "BMSDataFrequency": 5.947, "VehicleLabel": "Fiat_500e_37.3kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231217_000000.log"}, {"MACAddress": "fca47a1286f3", "MaximumCurrent": 275.0, "MaximumVoltage": 422.4, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 2.222, "VehicleLabel": "MG_5@EV_57.4kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231217_000000.log"}, {"MACAddress": "007dfa07f8e3", "MaximumCurrent": 500.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.211, "VehicleLabel": "Skoda_Enyaq@60_58kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231217_000000.log"}, {"MACAddress": "ec65cc6c1508", "MaximumCurrent": 500.0, "MaximumVoltage": 500.0, "MaximumPower": 250000.0, "BatteryCapacity": null, "BMSDataFrequency": 2.847, "VehicleLabel": "BMW_iX3_74kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231217_115529.log"}, {"MACAddress": "184cae00230f", "MaximumCurrent": 350.0, "MaximumVoltage": 412.0, "MaximumPower": 130000.0, "BatteryCapacity": null, "BMSDataFrequency": 5.252, "VehicleLabel": "Nissan_Ariya_63kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231217_115529.log"}, {"MACAddress": "dc4427124fd3", "MaximumCurrent": 799.0, "MaximumVoltage": 423.0, "MaximumPower": 326000.0, "BatteryCapacity": null, "BMSDataFrequency": 30.22, "VehicleLabel": "Tesla_Model@3_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231217_150746.log"}, {"MACAddress": "0c86c700b864", "MaximumCurrent": 500.0, "MaximumVoltage": 488.0, "MaximumPower": 250000.0, "BatteryCapacity": null, "BMSDataFrequency": 2.644, "VehicleLabel": "BMW_i4@M50_80.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231217_164412.log"}, {"MACAddress": "04e77e002861", "MaximumCurrent": 350.0, "MaximumVoltage": 825.6, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 10.856, "VehicleLabel": "KIA_EV6_74kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231217_164412.log"}, {"MACAddress": "98ed5c976ceb", "MaximumCurrent": 799.0, "MaximumVoltage": 423.0, "MaximumPower": 326000.0, "BatteryCapacity": null, "BMSDataFrequency": 26.517, "VehicleLabel": "Tesla_Model@Y_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231217_195114.log"}, {"MACAddress": "f07f0c113601", "MaximumCurrent": 350.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.138, "VehicleLabel": "Audi_e-tron_86.5kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231218_000000.log"}, {"MACAddress": "ec65cca69e8d", "MaximumCurrent": 500.0, "MaximumVoltage": 488.0, "MaximumPower": 250000.0, "BatteryCapacity": null, "BMSDataFrequency": 2.648, "VehicleLabel": "BMW_i4@M50_80.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231218_001346.log"}, {"MACAddress": "ec65ccabe44f", "MaximumCurrent": 500.0, "MaximumVoltage": 488.0, "MaximumPower": 250000.0, "BatteryCapacity": null, "BMSDataFrequency": 2.644, "VehicleLabel": "BMW_i4@eDrive40_80.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231218_001346.log"}, {"MACAddress": "e00ee100555b", "MaximumCurrent": 200.0, "MaximumVoltage": 421.4, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 9.836, "VehicleLabel": "Hyundai_Kona@electric_64kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231218_165626.log"}, {"MACAddress": "98ed5cd88067", "MaximumCurrent": 499.0, "MaximumVoltage": 430.0, "MaximumPower": 214000.0, "BatteryCapacity": null, "BMSDataFrequency": 26.626, "VehicleLabel": "Tesla_Model@3_57.5kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231218_165626.log"}, {"MACAddress": "fca47a1005d0", "MaximumCurrent": 234.3, "MaximumVoltage": 469.8, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 2.206, "VehicleLabel": "MG_ZS@SUV_42.5kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231218_165626.log"}, {"MACAddress": "e00ee1004db1", "MaximumCurrent": 200.0, "MaximumVoltage": 421.4, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 9.884, "VehicleLabel": "Hyundai_Kona@electric_64kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231219_000000.log"}, {"MACAddress": "0018239454e5", "MaximumCurrent": 251.0, "MaximumVoltage": 408.0, "MaximumPower": 111700.0, "BatteryCapacity": 42000.0, "BMSDataFrequency": 5.944, "VehicleLabel": "Fiat_500e_37.3kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231219_000000.log"}, {"MACAddress": "f07f0c1ced8b", "MaximumCurrent": 125.0, "MaximumVoltage": 411.6, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 13.649, "VehicleLabel": "MINI_Cooper@SE_28.9kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231219_000000.log"}, {"MACAddress": "98ed5cc073d0", "MaximumCurrent": 499.0, "MaximumVoltage": 430.0, "MaximumPower": 214000.0, "BatteryCapacity": null, "BMSDataFrequency": 26.553, "VehicleLabel": "Tesla_Model@3_57.5kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231219_180912.log"}, {"MACAddress": "007dfa08861f", "MaximumCurrent": 500.0, "MaximumVoltage": 413.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.138, "VehicleLabel": "Skoda_Enyaq@80_77kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231219_000000.log"}, {"MACAddress": "dc442712205f", "MaximumCurrent": 799.0, "MaximumVoltage": 423.0, "MaximumPower": 326000.0, "BatteryCapacity": null, "BMSDataFrequency": 30.395, "VehicleLabel": "Tesla_Model@3_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231219_122343.log"}, {"MACAddress": "d00ea4022d46", "MaximumCurrent": 350.0, "MaximumVoltage": 459.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.137, "VehicleLabel": "Audi_e-tron_64.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231219_160836.log"}, {"MACAddress": "007dfa077520", "MaximumCurrent": 350.0, "MaximumVoltage": 469.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.227, "VehicleLabel": "Skoda_Enyaq@60_58kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231219_173039.log"}, {"MACAddress": "007dfa02c641", "MaximumCurrent": 125.0, "MaximumVoltage": 369.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.367, "VehicleLabel": "Volkswagen_Golf_32kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231219_173039.log"}, {"MACAddress": "dc4427137b80", "MaximumCurrent": 549.0, "MaximumVoltage": 423.0, "MaximumPower": 232000.0, "BatteryCapacity": null, "BMSDataFrequency": 30.257, "VehicleLabel": "Tesla_Model@3_49kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231220_000000.log"}, {"MACAddress": "dc442711dae3", "MaximumCurrent": 799.0, "MaximumVoltage": 423.0, "MaximumPower": 326000.0, "BatteryCapacity": null, "BMSDataFrequency": 30.286, "VehicleLabel": "Tesla_Model@3_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231220_000000.log"}, {"MACAddress": "00b052000003", "MaximumCurrent": 250.0, "MaximumVoltage": 451.3, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 2.495, "VehicleLabel": "Opel_Corsa_46.3kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231220_085407.log"}, {"MACAddress": "44422f0461dd", "MaximumCurrent": 200.0, "MaximumVoltage": 421.4, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 9.843, "VehicleLabel": "KIA_Niro@EV_64kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231220_000000.log"}, {"MACAddress": "dc442711dae3", "MaximumCurrent": 799.0, "MaximumVoltage": 423.0, "MaximumPower": 326000.0, "BatteryCapacity": null, "BMSDataFrequency": 30.356, "VehicleLabel": "Tesla_Model@3_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231220_082937.log"}, {"MACAddress": "0018870185bc", "MaximumCurrent": 250.0, "MaximumVoltage": 480.0, "MaximumPower": 100000.0, "BatteryCapacity": 50000.0, "BMSDataFrequency": 10.557, "VehicleLabel": "Peugeot_e-208_46.3kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231220_122020.log"}, {"MACAddress": "0018239454e5", "MaximumCurrent": 251.0, "MaximumVoltage": 408.0, "MaximumPower": 111700.0, "BatteryCapacity": 42000.0, "BMSDataFrequency": 5.772, "VehicleLabel": "Fiat_500e_37.3kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231220_135143.log"}, {"MACAddress": "0c86c700b864", "MaximumCurrent": 500.0, "MaximumVoltage": 488.0, "MaximumPower": 250000.0, "BatteryCapacity": null, "BMSDataFrequency": 2.646, "VehicleLabel": "BMW_i4@M50_80.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231220_163224.log"}, {"MACAddress": "dc442711dae3", "MaximumCurrent": 799.0, "MaximumVoltage": 423.0, "MaximumPower": 326000.0, "BatteryCapacity": null, "BMSDataFrequency": 30.403, "VehicleLabel": "Tesla_Model@3_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231221_000001.log"}, {"MACAddress": "481693e7d9a9", "MaximumCurrent": 375.0, "MaximumVoltage": 475.0, "MaximumPower": null, "BatteryCapacity": 66840.0, "BMSDataFrequency": 7.181, "VehicleLabel": "Volvo_XC40_67kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231221_093808.log"}, {"MACAddress": "e00ee1014b6c", "MaximumCurrent": 200.0, "MaximumVoltage": 421.4, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 9.902, "VehicleLabel": "Hyundai_Kona@electric_64kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231221_093808.log"}, {"MACAddress": "98ed5cd88067", "MaximumCurrent": 499.0, "MaximumVoltage": 430.0, "MaximumPower": 214000.0, "BatteryCapacity": null, "BMSDataFrequency": 25.679, "VehicleLabel": "Tesla_Model@3_57.5kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231221_115612.log"}, {"MACAddress": "9012a1004e0d", "MaximumCurrent": 310.0, "MaximumVoltage": 774.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 10.781, "VehicleLabel": "Hyundai_Ioniq@5_70kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231221_131156.log"}, {"MACAddress": "ec65ccb55f89", "MaximumCurrent": 500.0, "MaximumVoltage": 488.0, "MaximumPower": 250000.0, "BatteryCapacity": null, "BMSDataFrequency": 2.645, "VehicleLabel": "BMW_i4@eDrive40_80.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231221_131156.log"}, {"MACAddress": "f07f0c05d8a2", "MaximumCurrent": 350.0, "MaximumVoltage": 459.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.14, "VehicleLabel": "Audi_e-tron_64.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231221_000000.log"}, {"MACAddress": "007dfa0898d0", "MaximumCurrent": 500.0, "MaximumVoltage": 408.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.134, "VehicleLabel": "Volkswagen_ID.BUZZ_77kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231221_090234.log"}, {"MACAddress": "007dfa085bd3", "MaximumCurrent": 500.0, "MaximumVoltage": 408.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.139, "VehicleLabel": "Volkswagen_ID.4_77kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231221_112337.log"}, {"MACAddress": "dc44271242f1", "MaximumCurrent": 799.0, "MaximumVoltage": 423.0, "MaximumPower": 326000.0, "BatteryCapacity": null, "BMSDataFrequency": 30.35, "VehicleLabel": "Tesla_Model@3_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231221_120513.log"}, {"MACAddress": "007dfa09044f", "MaximumCurrent": 350.0, "MaximumVoltage": 469.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.239, "VehicleLabel": "Volkswagen_ID.3_58kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231221_132232.log"}, {"MACAddress": "304950b1476d", "MaximumCurrent": 125.0, "MaximumVoltage": 310.0, "MaximumPower": 38750.0, "BatteryCapacity": null, "BMSDataFrequency": 6.662, "VehicleLabel": "Dacia_Spring_25kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231223_210557.log"}, {"MACAddress": "481693e46e27", "MaximumCurrent": 375.0, "MaximumVoltage": 475.0, "MaximumPower": null, "BatteryCapacity": 74240.0, "BMSDataFrequency": 7.189, "VehicleLabel": "Volvo_XC40_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231225_000000.log"}, {"MACAddress": "007dfa063b96", "MaximumCurrent": 350.0, "MaximumVoltage": 413.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.25, "VehicleLabel": "Volkswagen_ID.4_77kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231225_152309.log"}, {"MACAddress": "00b052000003", "MaximumCurrent": 250.0, "MaximumVoltage": 451.3, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 2.496, "VehicleLabel": "Opel_Vivaro_46.3kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231228_125421.log"}, {"MACAddress": "007dfa08c713", "MaximumCurrent": 350.0, "MaximumVoltage": 418.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.241, "VehicleLabel": "Volkswagen_ID.4_77kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231228_000000.log"}, {"MACAddress": "fca47a12b545", "MaximumCurrent": 267.3, "MaximumVoltage": 382.7, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 2.218, "VehicleLabel": "MG_4_50.8kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231229_104025.log"}, {"MACAddress": "44422f03cfd4", "MaximumCurrent": 200.0, "MaximumVoltage": 421.4, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 9.909, "VehicleLabel": "KIA_Soul@EV_64kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231230_170450.log"}, {"MACAddress": "304950b1476d", "MaximumCurrent": 125.0, "MaximumVoltage": 310.0, "MaximumPower": 38750.0, "BatteryCapacity": null, "BMSDataFrequency": 6.662, "VehicleLabel": "Dacia_Spring_25kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20231223_210557.log"}, {"MACAddress": "04656500d437", "MaximumCurrent": 200.0, "MaximumVoltage": 421.4, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 2.131, "VehicleLabel": "Hyundai_Kona@electric_64kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20240103_212657.log"}, {"MACAddress": "00182392f40e", "MaximumCurrent": 200.0, "MaximumVoltage": 460.0, "MaximumPower": 92000.0, "BatteryCapacity": null, "BMSDataFrequency": 12.141, "VehicleLabel": "Mercedes-Benz_C-Class_25kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20240106_170725.log"}, {"MACAddress": "fca47a1132f0", "MaximumCurrent": 220.0, "MaximumVoltage": 450.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 5.956, "VehicleLabel": "MG_5@EV_46kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20240106_192029.log"}, {"MACAddress": "dc442714465c", "MaximumCurrent": 399.0, "MaximumVoltage": 417.0, "MaximumPower": 254000.0, "BatteryCapacity": null, "BMSDataFrequency": 31.583, "VehicleLabel": "Tesla_Model@X_85.5kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20240106_162718.log"}, {"MACAddress": "f07f0c1ecede", "MaximumCurrent": 400.0, "MaximumVoltage": 849.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.144, "VehicleLabel": "Porsche_Taycan_83.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20240107_102400.log"}, {"MACAddress": "007dfa06dd0f", "MaximumCurrent": 350.0, "MaximumVoltage": 418.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 3.499, "VehicleLabel": "Volkswagen_ID.3_45kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20240107_133047.log"}, {"MACAddress": "a453ee00e079", "MaximumCurrent": 250.0, "MaximumVoltage": 470.0, "MaximumPower": 100000.0, "BatteryCapacity": 50000.0, "BMSDataFrequency": 2.253, "VehicleLabel": "Opel_Vivaro_68kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20240109_135715.log"}, {"MACAddress": "00182336815f", "MaximumCurrent": 500.0, "MaximumVoltage": 413.0, "MaximumPower": 206500.0, "BatteryCapacity": null, "BMSDataFrequency": 3.834, "VehicleLabel": "Ford_F150_131kWh", "Source": "log_DE1480F1GN1C00047W_CCU_41_20240109-105845.log"}, {"MACAddress": "fca47a1132f0", "MaximumCurrent": 220.0, "MaximumVoltage": 450.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 5.956, "VehicleLabel": "MG_5@EV_46kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20240106_192029.log"}, {"MACAddress": "0c86c7006101", "MaximumCurrent": 500.0, "MaximumVoltage": 488.0, "MaximumPower": 250000.0, "BatteryCapacity": null, "BMSDataFrequency": 2.645, "VehicleLabel": "BMW_i4@eDrive40_80.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20240120_101435.log"}, {"MACAddress": "00188700bfdd", "MaximumCurrent": 400.0, "MaximumVoltage": 849.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.4, "VehicleLabel": "Porsche_Taycan_83.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20240120_135246.log"}, {"MACAddress": "08f80dd029a2", "MaximumCurrent": 480.0, "MaximumVoltage": 480.0, "MaximumPower": 230000.0, "BatteryCapacity": null, "BMSDataFrequency": 5.87, "VehicleLabel": "NIO_ET5_73.5kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20240127_165645.log"}, {"MACAddress": "0018238b8ac9", "MaximumCurrent": 300.0, "MaximumVoltage": 430.0, "MaximumPower": 129000.0, "BatteryCapacity": null, "BMSDataFrequency": 3.569, "VehicleLabel": "Mercedes-Benz_eVito_90kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20240129_160130.log"}, {"MACAddress": "007dfa025893", "MaximumCurrent": 125.0, "MaximumVoltage": 369.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.317, "VehicleLabel": "Volkswagen_e-Crafter_35.8kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20240130_000001.log"}, {"MACAddress": "a453ee031296", "MaximumCurrent": 250.0, "MaximumVoltage": 470.0, "MaximumPower": 100000.0, "BatteryCapacity": 50000.0, "BMSDataFrequency": 7.139, "VehicleLabel": "Citroen_C4_46.3kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20240130_102607.log"}, {"MACAddress": "481693e95479", "MaximumCurrent": 500.0, "MaximumVoltage": 475.0, "MaximumPower": null, "BatteryCapacity": 81700.0, "BMSDataFrequency": 7.188, "VehicleLabel": "Polestar_2_79kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20240203_124448.log"}, {"MACAddress": "483133004cb1", "MaximumCurrent": 500.0, "MaximumVoltage": 408.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 8.326, "VehicleLabel": "Audi_Q4@e-tron_77kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20240204_000000.log"}, {"MACAddress": "046565011d05", "MaximumCurrent": 200.0, "MaximumVoltage": 421.4, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 9.887, "VehicleLabel": "KIA_Niro@EV_64kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20240204_154147.log"}, {"MACAddress": "e00ee100f892", "MaximumCurrent": 200.0, "MaximumVoltage": 378.4, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.86, "VehicleLabel": "Hyundai_Ioniq_38.3kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20240106_115936.log"}, {"MACAddress": "007dfa09ae67", "MaximumCurrent": 350.0, "MaximumVoltage": 418.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.245, "VehicleLabel": "Volkswagen_ID.4_52kWh", "Source": "log_DE0120B1GN9C00034J_CCU_20240204_113033.log"}, {"MACAddress": "98ed5cc4aae3", "MaximumCurrent": 399, "MaximumVoltage": 360, "MaximumPower": 254000, "BatteryCapacity": null, "BMSDataFrequency": 31.561, "VehicleLabel": "Tesla_Model@S_72.5kWh", "Source": "log_DE0120B1GN9C00034J_CCU1_20240221_000000.log"}, {"MACAddress": "001823c1b1d8", "MaximumCurrent": 500, "MaximumVoltage": 347, "MaximumPower": 250000, "BatteryCapacity": 100000, "BMSDataFrequency": 4.524, "VehicleLabel": "BMW_iX1@eDrive20_64.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU2_20240221_084405.log"}, {"MACAddress": "ec65cca116a6", "MaximumCurrent": 500, "MaximumVoltage": 399, "MaximumPower": 250000, "BatteryCapacity": 100000, "BMSDataFrequency": 2.628, "VehicleLabel": "BMW_iX@xDrive40_71kWh", "Source": "log_DE0120B1GN9C00034J_CCU2_20240222_000000.log"}, {"MACAddress": "007dfa01bf11", "MaximumCurrent": 120, "MaximumVoltage": 418, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.316, "VehicleLabel": "Volkswagen_e-up_16kWh", "Source": "log_DE0120B1GN9C00034J_CCU2_20240301_094230.log"}, {"MACAddress": "184cae2b15ac", "MaximumCurrent": 235, "MaximumVoltage": 412, "MaximumPower": 92250, "BatteryCapacity": null, "BMSDataFrequency": 5.189, "VehicleLabel": "Renault_Kangoo@E-Tech_45kWh", "Source": "log_DE0120B1GN9C00034J_CCU2_20240301_152841.log"}, {"MACAddress": "001887012954", "MaximumCurrent": 250.0, "MaximumVoltage": 480.0, "MaximumPower": 100000.0, "BatteryCapacity": 50000.0, "BMSDataFrequency": 10.531, "VehicleLabel": "Peugeot_e-2008_46.3kWh", "Source": "log_DE0120B1GN9C00034J_CCU1_20240309_111923.log"}, {"MACAddress": "ec65ccfd08b9", "MaximumCurrent": 500.0, "MaximumVoltage": 347.0, "MaximumPower": 250000.0, "BatteryCapacity": 100000.0, "BMSDataFrequency": 2.625, "VehicleLabel": "BMW_iX1@eDrive20_64.7kWh", "Source": "log_DE0120B1GN9C00034J_CCU1_20240309_144623.log"}, {"MACAddress": "54f8f0025628", "MaximumCurrent": 499.0, "MaximumVoltage": 430.0, "MaximumPower": 214000.0, "BatteryCapacity": null, "BMSDataFrequency": 28.508, "VehicleLabel": "Tesla_Model@3_57.5kWh", "Source": "log_DE0120B1GN9C00034J_CCU1_20240310_160402.log"}, {"MACAddress": "cc88263900e7", "MaximumCurrent": 150.0, "MaximumVoltage": 450.0, "MaximumPower": 60000.0, "BatteryCapacity": 18700, "BMSDataFrequency": 8.436, "VehicleLabel": "Chevrolet_Bolt@EV_66kWh", "Source": ""}, {"MACAddress": "f00000002637", "MaximumCurrent": 499.0, "MaximumVoltage": 430.0, "MaximumPower": 214000.0, "BatteryCapacity": null, "BMSDataFrequency": 27.705, "VehicleLabel": "Tesla_Model@3_75kWh", "Source": "log_DE0120B1GN9C00034J_CCU2_20240323_121200.log"}, {"MACAddress": "0ccc4786568d", "MaximumCurrent": 527.0, "MaximumVoltage": 743.4, "MaximumPower": 337000.0, "BatteryCapacity": 102140.0, "BMSDataFrequency": 3.034, "VehicleLabel": "Xpeng_G9_93.1kWh", "Source": "log_DE0120B1GN9C00034J_CCU2_20240324_154910.log"}, {"MACAddress": "48c58db2b276", "MaximumCurrent": 375.0, "MaximumVoltage": 475.0, "MaximumPower": null, "BatteryCapacity": 74840.0, "BMSDataFrequency": 7.2, "VehicleLabel": "BMW_i5@eDrive40_81.2kWh", "Source": "log_DE0120B1GN9C00034J_CCU2_20240325_170659.log"}, {"MACAddress": "e00ee1ffd069", "MaximumCurrent": 200.0, "MaximumVoltage": 412.8, "MaximumPower": 98000.0, "BatteryCapacity": 28000.0, "BMSDataFrequency": 9.883, "VehicleLabel": "Hyundai_Ioniq_28kWh", "Source": "log_DE0240B1GNCC00030L_CCU1_20240421_190515.log", "Number": "NX-886-T", "ChargeStartTime": "2024-04-21 17:55:33.912"}, {"MACAddress": "001887005989", "MaximumCurrent": 250.0, "MaximumVoltage": 480.0, "MaximumPower": 100000.0, "BatteryCapacity": 50000.0, "BMSDataFrequency": 11.858, "VehicleLabel": "Opel_Corsa_46.3kWh", "Source": "log_DE0240B1GNCC00030L_CCU1_20240422_000000.log", "Number": "J-049-LD", "ChargeStartTime": "2024-04-22 16:24:14.018"}, {"MACAddress": "0030ab29c5be", "MaximumCurrent": 500.0, "MaximumVoltage": 347.0, "MaximumPower": 250000.0, "BatteryCapacity": 100000.0, "BMSDataFrequency": 4.52, "VehicleLabel": "BMW_iX1@xDrive30_64.7kWh", "Source": "log_DE0240B1GNCC00030L_CCU1_20240423_000000.log", "Number": "X-014-JB", "ChargeStartTime": "2024-04-23 09:27:03.472"}, {"MACAddress": "48c58db263f6", "MaximumCurrent": 375.0, "MaximumVoltage": 475.0, "MaximumPower": null, "BatteryCapacity": 75500.0, "BMSDataFrequency": 7.16, "VehicleLabel": "Polestar_2_75kWh", "Source": "log_DE0240B1GNCC00030L_CCU1_20240423_000000.log", "Number": "K-126-DZ", "ChargeStartTime": "2024-04-23 15:01:30.101"}, {"MACAddress": "ec65cc7fff07", "MaximumCurrent": 500.0, "MaximumVoltage": 500.0, "MaximumPower": 250000.0, "BatteryCapacity": null, "BMSDataFrequency": 2.844, "VehicleLabel": "BMW_iX3_74kWh", "Source": "log_DE0240B1GNCC00030L_CCU1_20240424_163232.log", "Number": "N-711-TT", "ChargeStartTime": "2024-04-24 14:54:17.403"}, {"MACAddress": "e00ee1ffd06a", "MaximumCurrent": 200.0, "MaximumVoltage": 412.8, "MaximumPower": 98000.0, "BatteryCapacity": 28000.0, "BMSDataFrequency": 9.819, "VehicleLabel": "Hyundai_Ioniq_28kWh", "Source": "log_DE0240B1GNCC00030L_CCU1_20240425_000000.log", "Number": "NX-885-T", "ChargeStartTime": "2024-04-25 10:00:01.792"}, {"MACAddress": "08f80dd02476", "MaximumCurrent": 480.0, "MaximumVoltage": 480.0, "MaximumPower": 230000.0, "BatteryCapacity": null, "BMSDataFrequency": 5.868, "VehicleLabel": "NIO_ET5_73.5kWh", "Source": "log_DE0240B1GNCC00030L_CCU1_20240425_000000.log", "Number": "X-607-RL", "ChargeStartTime": "2024-04-25 15:32:40.513"}, {"MACAddress": "007dfa06334e", "MaximumCurrent": 500.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.14, "VehicleLabel": "Volkswagen_ID.3_58kWh", "Source": "log_DE0240B1GNCC00030L_CCU2_20240420_030303.log", "Number": "J-060-NP", "ChargeStartTime": "2024-04-20 15:43:53.419"}, {"MACAddress": "007dfa08960c", "MaximumCurrent": 500.0, "MaximumVoltage": 413.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.15, "VehicleLabel": "Skoda_Enyaq@80_77kWh", "Source": "log_DE0240B1GNCC00030L_CCU2_20240421_000000.log", "Number": "R-228-GH", "ChargeStartTime": "2024-04-21 11:01:46.869"}, {"MACAddress": "0030ab29c5be", "MaximumCurrent": 500.0, "MaximumVoltage": 347.0, "MaximumPower": 250000.0, "BatteryCapacity": 100000.0, "BMSDataFrequency": 4.529, "VehicleLabel": "BMW_iX1@eDrive20_64.7kWh", "Source": "log_DE0240B1GNCC00030L_CCU2_20240421_132345.log", "Number": "X-429-ZP", "ChargeStartTime": "2024-04-21 19:22:18.508"}, {"MACAddress": "007dfa075f01", "MaximumCurrent": 500.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.142, "VehicleLabel": "Volkswagen_ID.3_58kWh", "Source": "log_DE0240B1GNCC00030L_CCU2_20240422_000000.log", "Number": "J-774-ZV", "ChargeStartTime": "2024-04-22 13:05:57.453"}, {"MACAddress": "ec65ccd614d2", "MaximumCurrent": 500.0, "MaximumVoltage": 347.0, "MaximumPower": 250000.0, "BatteryCapacity": 100000.0, "BMSDataFrequency": 2.609, "VehicleLabel": "BMW_iX1@xDrive30_64.7kWh", "Source": "log_DE0240B1GNCC00030L_CCU2_20240423_000000.log", "Number": "S-918-PX", "ChargeStartTime": "2024-04-23 09:25:36.137"}, {"MACAddress": "44422f002e20", "MaximumCurrent": 200.0, "MaximumVoltage": 421.4, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 9.951, "VehicleLabel": "KIA_Niro@EV_64kWh", "Source": "log_DE0240B1GNCC00030L_CCU2_20240423_114235.log", "Number": "G-741-TV", "ChargeStartTime": "2024-04-23 14:33:28.894"}, {"MACAddress": "00b052000003", "MaximumCurrent": 250.0, "MaximumVoltage": 451.3, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 2.492, "VehicleLabel": "Peugeot_e-2008_46.3kWh", "Source": "log_DE0240B1GNCC00030L_CCU2_20240425_000000.log", "Number": "T-274-KX", "ChargeStartTime": "2024-04-25 13:25:01.475"}, {"MACAddress": "007dfa099c90", "MaximumCurrent": 500.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.14, "VehicleLabel": "Volkswagen_ID.3_58kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20240421_000000.log", "Number": "J-180-RH", "ChargeStartTime": "2024-04-21 11:38:46.006"}, {"MACAddress": "98ed5cc0b9c0", "MaximumCurrent": 499.0, "MaximumVoltage": 430.0, "MaximumPower": 214000.0, "BatteryCapacity": null, "BMSDataFrequency": 27.316, "VehicleLabel": "Tesla_Model@3_57.5kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20240422_132953.log", "Number": "R-684-VL", "ChargeStartTime": "2024-04-22 12:00:57.589"}, {"MACAddress": "dc44271393eb", "MaximumCurrent": 799.0, "MaximumVoltage": 423.0, "MaximumPower": 326000.0, "BatteryCapacity": null, "BMSDataFrequency": 29.965, "VehicleLabel": "Tesla_Model@3_75kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20240423_000000.log", "Number": "G-943-XH", "ChargeStartTime": "2024-04-23 09:40:40.510"}, {"MACAddress": "e00ee1ffd06b", "MaximumCurrent": 200.0, "MaximumVoltage": 412.8, "MaximumPower": 98000.0, "BatteryCapacity": 28000.0, "BMSDataFrequency": 9.915, "VehicleLabel": "Hyundai_Ioniq_28kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20240423_120356.log", "Number": "NX-884-T", "ChargeStartTime": "2024-04-23 10:16:38.257"}, {"MACAddress": "a453ee0310fe", "MaximumCurrent": 250.0, "MaximumVoltage": 470.0, "MaximumPower": 100000.0, "BatteryCapacity": 50000.0, "BMSDataFrequency": 7.128, "VehicleLabel": "Fiat_Ulysse_68kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20240423_120356.log", "Number": "R-671-LG", "ChargeStartTime": "2024-04-23 14:52:52.852"}, {"MACAddress": "dc44271393eb", "MaximumCurrent": 799.0, "MaximumVoltage": 423.0, "MaximumPower": 326000.0, "BatteryCapacity": null, "BMSDataFrequency": 29.944, "VehicleLabel": "Tesla_Model@3_75kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20240424_000000.log", "Number": "G-943-XH", "ChargeStartTime": "2024-04-24 13:21:04.990"}, {"MACAddress": "e00ee1ffd227", "MaximumCurrent": 200.0, "MaximumVoltage": 412.8, "MaximumPower": 98000.0, "BatteryCapacity": 28000.0, "BMSDataFrequency": 9.901, "VehicleLabel": "Hyundai_Ioniq_28kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20240424_153740.log", "Number": "ND-821-D", "ChargeStartTime": "2024-04-24 14:13:47.422"}, {"MACAddress": "f07f0c059346", "MaximumCurrent": 350.0, "MaximumVoltage": 459.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.139, "VehicleLabel": "Audi_e-tron_64.7kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20240425_000000.log", "Number": "G-135-HL", "ChargeStartTime": "2024-04-25 17:06:55.670"}, {"MACAddress": "98ed5cc0b9c0", "MaximumCurrent": 499.0, "MaximumVoltage": 430.0, "MaximumPower": 214000.0, "BatteryCapacity": null, "BMSDataFrequency": 26.952, "VehicleLabel": "Tesla_Model@3_57.5kWh", "Source": "log_DE0240B1GNCC00020K_CCU2_20240421_133543.log", "Number": "R-684-VL", "ChargeStartTime": "2024-04-21 11:54:57.533"}, {"MACAddress": "007dfa09d901", "MaximumCurrent": 350.0, "MaximumVoltage": 418.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.168, "VehicleLabel": "Volkswagen_ID.4_77kWh", "Source": "log_DE0240B1GNCC00020K_CCU2_20240421_202538.log", "Number": "K-089-LP", "ChargeStartTime": "2024-04-21 20:09:19.133"}, {"MACAddress": "00182338b129", "MaximumCurrent": 500.0, "MaximumVoltage": 460.0, "MaximumPower": 230000.0, "BatteryCapacity": null, "BMSDataFrequency": 3.571, "VehicleLabel": "Mercedes-Benz_EQS_107.8kWh", "Source": "log_DE0240B1GNCC00020K_CCU2_20240423_000000.log", "Number": "T-488-GJ", "ChargeStartTime": "2024-04-23 08:41:58.303"}, {"MACAddress": "e00ee1ffd2b0", "MaximumCurrent": 200.0, "MaximumVoltage": 412.8, "MaximumPower": 98000.0, "BatteryCapacity": 28000.0, "BMSDataFrequency": 9.917, "VehicleLabel": "Hyundai_Ioniq_28kWh", "Source": "log_DE0240B1GNCC00020K_CCU2_20240423_000000.log", "Number": "RD-131-J", "ChargeStartTime": "2024-04-23 10:55:57.513"}, {"MACAddress": "a453ee0310fe", "MaximumCurrent": 250.0, "MaximumVoltage": 470.0, "MaximumPower": 100000.0, "BatteryCapacity": 50000.0, "BMSDataFrequency": 7.14, "VehicleLabel": "Fiat_Ulysse_68kWh", "Source": "log_DE0240B1GNCC00020K_CCU2_20240423_125852.log", "Number": "R-671-LG", "ChargeStartTime": "2024-04-23 12:40:46.051"}, {"MACAddress": "f07f0c0b6c5c", "MaximumCurrent": 350.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.14, "VehicleLabel": "Audi_e-tron_64.7kWh", "Source": "log_DE0240B1GNCC00020K_CCU2_20240423_125852.log", "Number": "K-301-HB", "ChargeStartTime": "2024-04-23 13:08:12.230"}, {"MACAddress": "007dfa064c79", "MaximumCurrent": 500.0, "MaximumVoltage": 413.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.152, "VehicleLabel": "Skoda_Enyaq@80_77kWh", "Source": "log_DE0240B1GNCC00020K_CCU2_20240423_164813.log", "Number": "R-228-GH", "ChargeStartTime": "2024-04-23 15:35:27.932"}, {"MACAddress": "04e77e000f18", "MaximumCurrent": 310.0, "MaximumVoltage": 825.6, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 10.79, "VehicleLabel": "KIA_EV6_74kWh", "Source": "log_DE0240B1GNCC00020K_CCU2_20240423_164813.log", "Number": "N-899-GV", "ChargeStartTime": "2024-04-23 16:07:57.376"}, {"MACAddress": "ec65cc807345", "MaximumCurrent": 500.0, "MaximumVoltage": 500.0, "MaximumPower": 250000.0, "BatteryCapacity": null, "BMSDataFrequency": 2.835, "VehicleLabel": "BMW_iX3_74kWh", "Source": "log_DE0240B1GNCC00020K_CCU2_20240424_131451.log", "Number": "N-480-TS", "ChargeStartTime": "2024-04-24 17:46:11.094"}, {"MACAddress": "f07f0c12ddc3", "MaximumCurrent": 350.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.142, "VehicleLabel": "Audi_e-tron_86.5kWh", "Source": "log_DE0240B1GNCC00020K_CCU2_20240425_000000.log", "Number": "N-765-TB", "ChargeStartTime": "2024-04-25 06:10:12.750"}, {"MACAddress": "0018238b00f7", "MaximumCurrent": 500.0, "MaximumVoltage": 460.0, "MaximumPower": 230000.0, "BatteryCapacity": null, "BMSDataFrequency": 3.569, "VehicleLabel": "Mercedes-Benz_EQE_90.6kWh", "Source": "log_DE0240B1GNCC00020K_CCU2_20240425_084340.log", "Number": "R-965-NS", "ChargeStartTime": "2024-04-25 09:59:23.926"}, {"MACAddress": "e00ee10158f7", "MaximumCurrent": 200.0, "MaximumVoltage": 378.4, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 9.855, "VehicleLabel": "Hyundai_Ioniq_38.3kWh", "Source": "log_DE0240B1GNCC00020K_CCU2_20240425_185747.log", "Number": "J-011-XB", "ChargeStartTime": "2024-04-25 17:17:12.970"}, {"MACAddress": "e00ee1ffd06b", "MaximumCurrent": 200.0, "MaximumVoltage": 412.8, "MaximumPower": 98000.0, "BatteryCapacity": 28000.0, "BMSDataFrequency": 9.962, "VehicleLabel": "Hyundai_Ioniq_28kWh", "Source": "log_DE0240B1GNCC00030L_CCU1_20240415_000000.log", "Number": "NX-884-T", "ChargeStartTime": "2024-04-15 08:07:57.826"}, {"MACAddress": "dc442714bb74", "MaximumCurrent": 549.0, "MaximumVoltage": 423.0, "MaximumPower": 232000.0, "BatteryCapacity": null, "BMSDataFrequency": 29.898, "VehicleLabel": "Tesla_Model@3_49kWh", "Source": "log_DE0240B1GNCC00030L_CCU1_20240417_144321.log", "Number": "H-077-LX", "ChargeStartTime": "2024-04-17 15:53:53.332"}, {"MACAddress": "98ed5cb49997", "MaximumCurrent": 799.0, "MaximumVoltage": 423.0, "MaximumPower": 326000.0, "BatteryCapacity": null, "BMSDataFrequency": 26.978, "VehicleLabel": "Tesla_Model@Y_75kWh", "Source": "log_DE0240B1GNCC00030L_CCU1_20240418_000000.log", "Number": "X-433-<PERSON>", "ChargeStartTime": "2024-04-18 06:52:04.311"}, {"MACAddress": "00c05964d6cb", "MaximumCurrent": 400.0, "MaximumVoltage": 408.0, "MaximumPower": 150000.0, "BatteryCapacity": null, "BMSDataFrequency": 9.344, "VehicleLabel": "Toyota_Bz4x_71.4kWh", "Source": "log_DE0240B1GNCC00030L_CCU1_20240418_000000.log", "Number": "X-648-HB", "ChargeStartTime": "2024-04-18 14:49:02.982"}, {"MACAddress": "ec65cce645a4", "MaximumCurrent": 500.0, "MaximumVoltage": 416.0, "MaximumPower": 250000.0, "BatteryCapacity": null, "BMSDataFrequency": 2.637, "VehicleLabel": "BMW_iX3_74kWh", "Source": "log_DE0240B1GNCC00030L_CCU2_20240415_000001.log", "Number": "X-091-NF", "ChargeStartTime": "2024-04-15 15:04:17.289"}, {"MACAddress": "00182335e540", "MaximumCurrent": 500.0, "MaximumVoltage": 414.0, "MaximumPower": 207000.0, "BatteryCapacity": null, "BMSDataFrequency": 4.577, "VehicleLabel": "Ford_Mustang@Mach-E_91kWh", "Source": "log_DE0240B1GNCC00030L_CCU2_20240417_000000.log", "Number": "L-800-HT", "ChargeStartTime": "2024-04-17 07:03:44.356"}, {"MACAddress": "007dfa092631", "MaximumCurrent": 500.0, "MaximumVoltage": 408.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.141, "VehicleLabel": "Volkswagen_ID.BUZZ_77kWh", "Source": "log_DE0240B1GNCC00030L_CCU2_20240417_000000.log", "Number": "VZZ-89-V", "ChargeStartTime": "2024-04-17 09:52:18.313"}, {"MACAddress": "ec65cc8cd031", "MaximumCurrent": 500.0, "MaximumVoltage": 399.0, "MaximumPower": 250000.0, "BatteryCapacity": 100000.0, "BMSDataFrequency": 2.609, "VehicleLabel": "BMW_iX@xDrive40_71kWh", "Source": "log_DE0240B1GNCC00030L_CCU2_20240417_120403.log", "Number": "N-642-TT", "ChargeStartTime": "2024-04-17 16:54:45.662"}, {"MACAddress": "04e77e000f18", "MaximumCurrent": 310.0, "MaximumVoltage": 825.6, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 10.837, "VehicleLabel": "KIA_EV6_74kWh", "Source": "log_DE0240B1GNCC00030L_CCU2_20240418_144656.log", "Number": "N-899-GV", "ChargeStartTime": "2024-04-18 13:13:50.439"}, {"MACAddress": "ec65cc7fbfb6", "MaximumCurrent": 500.0, "MaximumVoltage": 500.0, "MaximumPower": 250000.0, "BatteryCapacity": null, "BMSDataFrequency": 2.842, "VehicleLabel": "BMW_iX3_74kWh", "Source": "log_DE0240B1GNCC00030L_CCU2_20240418_153351.log", "Number": "N-716-SL", "ChargeStartTime": "2024-04-18 16:43:27.207"}, {"MACAddress": "e00ee1ffd06c", "MaximumCurrent": 200.0, "MaximumVoltage": 412.8, "MaximumPower": 98000.0, "BatteryCapacity": 28000.0, "BMSDataFrequency": 9.921, "VehicleLabel": "Hyundai_Ioniq_28kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20240415_000000.log", "Number": "NX-883-T", "ChargeStartTime": "2024-04-15 10:54:13.374"}, {"MACAddress": "184cae0cb453", "MaximumCurrent": 350.0, "MaximumVoltage": 412.0, "MaximumPower": 130000.0, "BatteryCapacity": null, "BMSDataFrequency": 5.118, "VehicleLabel": "Renault_Megane@E-TECH_60kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20240415_000000.log", "Number": "R-179-PX", "ChargeStartTime": "2024-04-15 15:42:23.612"}, {"MACAddress": "98ed5cc0b9c0", "MaximumCurrent": 499.0, "MaximumVoltage": 430.0, "MaximumPower": 214000.0, "BatteryCapacity": null, "BMSDataFrequency": 27.223, "VehicleLabel": "Tesla_Model@3_57.5kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20240417_000000.log", "Number": "R-684-VL", "ChargeStartTime": "2024-04-17 06:19:03.549"}, {"MACAddress": "a453ee001c42", "MaximumCurrent": 250.0, "MaximumVoltage": 470.0, "MaximumPower": 100000.0, "BatteryCapacity": 50000.0, "BMSDataFrequency": 7.138, "VehicleLabel": "Peugeot_e-208_46.3kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20240418_000000.log", "Number": "K-728-HF", "ChargeStartTime": "2024-04-17 23:32:32.110"}, {"MACAddress": "e00ee1ffd069", "MaximumCurrent": 200.0, "MaximumVoltage": 412.8, "MaximumPower": 98000.0, "BatteryCapacity": 28000.0, "BMSDataFrequency": 9.757, "VehicleLabel": "Hyundai_Ioniq_28kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20240418_000000.log", "Number": "NX-886-T", "ChargeStartTime": "2024-04-18 10:02:59.276"}, {"MACAddress": "ec65ccd614d2", "MaximumCurrent": 500.0, "MaximumVoltage": 347.0, "MaximumPower": 250000.0, "BatteryCapacity": 100000.0, "BMSDataFrequency": 2.609, "VehicleLabel": "BMW_iX1@xDrive30_64.7kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20240418_122453.log", "Number": "S-918-PX", "ChargeStartTime": "2024-04-18 12:13:20.927"}, {"MACAddress": "007dfa07a4d2", "MaximumCurrent": 500.0, "MaximumVoltage": 413.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.138, "VehicleLabel": "Skoda_Enyaq@80_77kWh", "Source": "log_DE0240B1GNCC00020K_CCU2_20240415_000000.log", "Number": "X-262-RZ", "ChargeStartTime": "2024-04-15 08:38:21.766"}, {"MACAddress": "f07f0c06c96f", "MaximumCurrent": 350.0, "MaximumVoltage": 459.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.127, "VehicleLabel": "Audi_e-tron_64.7kWh", "Source": "log_DE0240B1GNCC00020K_CCU2_20240417_135635.log", "Number": "G-761-XP", "ChargeStartTime": "2024-04-17 18:01:38.664"}, {"MACAddress": "007dfa090db6", "MaximumCurrent": 500.0, "MaximumVoltage": 413.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 7.143, "VehicleLabel": "Volkswagen_ID.4_77kWh", "Source": "log_DE0240B1GNCC00020K_CCU2_20240418_140532.log", "Number": "S-558-ZH", "ChargeStartTime": "2024-04-18 12:33:00.228"}, {"MACAddress": "0016810ef122", "MaximumCurrent": 330.0, "MaximumVoltage": 750.0, "MaximumPower": 248000.0, "BatteryCapacity": null, "BMSDataFrequency": 0, "VehicleLabel": "Volvo_FH@ELECTRIC", "Source": "log_DE7240F1GR3C00004S_CCU_41_20240411-151904.log", "Number": "43-BXK-1", "ChargeStartTime": null}, {"MACAddress": "280feb39f2ee", "MaximumCurrent": 300.0, "MaximumVoltage": 420.0, "MaximumPower": 126000.0, "BatteryCapacity": null, "BMSDataFrequency": 4.187, "VehicleLabel": "Mercedes-Benz_eVito_90kWh", "Source": "log_DE0240B1GNCC00020K_CCU2_20240421_000000.log", "Number": "VPZ-75-B", "ChargeStartTime": "2024-04-21 10:56:03.945"}, {"MACAddress": "88504690af3c", "MaximumCurrent": 500.0, "MaximumVoltage": 475.0, "MaximumPower": null, "BatteryCapacity": 81540.0, "BMSDataFrequency": 7.177, "VehicleLabel": "Volvo_XC40_79kWh", "Source": "log_DE0240B1GNCC00030L_CCU1_20240421_000000.log", "Number": "X-605-ZK", "ChargeStartTime": "2024-04-21 09:27:40.029"}, {"MACAddress": "a453ee03ebdd", "MaximumCurrent": 250.0, "MaximumVoltage": 470.0, "MaximumPower": 100000.0, "BatteryCapacity": 50000.0, "BMSDataFrequency": 7.134, "VehicleLabel": "Renault_Master", "Source": "log_DE0240B1GNCC00020K_CCU2_20240424_000000.log", "Number": "VPZ-85-P", "ChargeStartTime": "2024-04-24 09:22:46.589"}, {"MACAddress": "48c58db63bd3", "MaximumCurrent": 375.0, "MaximumVoltage": 475.0, "MaximumPower": null, "BatteryCapacity": 56040.0, "VehicleLabel": "Polestar_2_61kWh", "Source": "log_DE0120B1GNAC00053L_CCU_20240614_100201.log"}, {"MACAddress": "54f8f001ae87", "MaximumCurrent": 799.0, "MaximumVoltage": 423.0, "MaximumPower": 326000.0, "BatteryCapacity": null, "VehicleLabel": "Tesla_Model@Y_75kWh", "Source": "log_DE0120B1GP3C00076K_CCU_20240523_000000.log"}, {"MACAddress": "18b6cc7008b0", "MaximumCurrent": 310.0, "MaximumVoltage": 825.6, "MaximumPower": null, "BatteryCapacity": null, "VehicleLabel": "GENESIS_GV70_74kWh", "Source": "log_DE0120B1GP3C00076K_CCU_20240526_110200.log"}, {"MACAddress": "00b052000003", "MaximumCurrent": 250.0, "MaximumVoltage": 451.3, "MaximumPower": null, "BatteryCapacity": null, "VehicleLabel": "Citroen_C4_46.3kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20240511_000000.log"}, {"MACAddress": "48c58da71157", "MaximumCurrent": 500.0, "MaximumVoltage": 475.0, "MaximumPower": null, "BatteryCapacity": 82400.0, "BMSDataFrequency": 7.159, "VehicleLabel": "Volvo_XC40_79kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20240511_000000.log", "Number": "X-733-BL", "ChargeStartTime": "2024-05-11 20:06:06.012"}, {"MACAddress": "cc8826dbd3b0", "MaximumCurrent": 300.0, "MaximumVoltage": 420.0, "MaximumPower": 126000.0, "BatteryCapacity": null, "BMSDataFrequency": 4.205, "VehicleLabel": "Mercedes-Benz_eVito_90kWh", "Source": "log_DE0240B1GNCC00020K_CCU2_20240514_030159.log", "Number": "VNK-60-D", "ChargeStartTime": "2024-05-14 10:40:32.391"}, {"MACAddress": "54f8f001922b", "MaximumCurrent": 549.0, "MaximumVoltage": 418.0, "MaximumPower": 230000.0, "BatteryCapacity": null, "BMSDataFrequency": 27.369, "VehicleLabel": "Tesla_Model@Y_57.5kWh", "Source": "log_DE0120B1GP3C00076K_CCU1_20240522_030151.log", "Number": "Z-185-BV", "ChargeStartTime": "2024-05-22 14:53:36.900"}, {"MACAddress": "aabb<PERSON><PERSON><PERSON><PERSON>", "MaximumCurrent": 150.0, "MaximumVoltage": 420.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 3.583, "VehicleLabel": "MAZDA_MX-30_30kWh", "Source": "log_DE0120B1GNAC00053L_CCU2_20240629_114215.log", "Number": "R-816-RR", "ChargeStartTime": "2024-06-29 12:48:14.752"}, {"MACAddress": "a453ee0515c5", "MaximumCurrent": 300.0, "MaximumVoltage": 500.0, "MaximumPower": 150000.0, "BatteryCapacity": 50000.0, "BMSDataFrequency": 6.386, "VehicleLabel": "DS_3_50.8kWh", "Source": "log_DE0120B1GNAC00053L_CCU2_20240630_000000.log", "Number": "T-463-<PERSON>", "ChargeStartTime": "2024-06-30 00:10:44.193"}, {"MACAddress": "00168108c806", "MaximumCurrent": 125.0, "MaximumVoltage": 455.0, "MaximumPower": 56250.0, "BatteryCapacity": 1000.0, "BMSDataFrequency": 6.666, "VehicleLabel": "Fiat_Ducato_71kWh", "Source": "log_DE0120B1GNAC00053L_CCU2_20240701_161556.log", "Number": "S-766-SV", "ChargeStartTime": "2024-07-01 17:13:32.205"}, {"MACAddress": "141fba1059da", "MaximumCurrent": 240.0, "MaximumVoltage": 649.1, "MaximumPower": null, "BatteryCapacity": null, "VehicleLabel": "BYD_e6_71.7kWh", "Source": "log_DE7240F1GR1C00001L_CCU_41_20240721-024809.log"}, {"MACAddress": "0ccc4785231e", "MaximumCurrent": 400.0, "MaximumVoltage": 626.0, "MaximumPower": null, "BatteryCapacity": null, "VehicleLabel": "BYD_Seal_82.5kWh", "Source": "log_DE0120B1GNAC00053L_CCU_20240614_113559.log"}, {"MACAddress": "885046902d2c", "MaximumCurrent": 500.0, "MaximumVoltage": 475.0, "MaximumPower": null, "BatteryCapacity": 68340.0, "BMSDataFrequency": 6.877, "VehicleLabel": "Volvo_C40@Recharge_66kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20240725_030225.log", "Number": "X213LP", "ChargeStartTime": "2024-07-25 09:11:21.297"}, {"MACAddress": "00b052000003", "MaximumCurrent": 250.0, "MaximumVoltage": 451.3, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 2.5, "VehicleLabel": "Opel_Mokka_46.3kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20240725_191010.log", "Number": "S785HK", "ChargeStartTime": "2024-07-25 17:13:49.931"}, {"MACAddress": "fca47a1ccea2", "MaximumCurrent": 421.8, "MaximumVoltage": 465.5, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 2.855, "VehicleLabel": "Volvo_EX30_64kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20240726_000000.log", "Number": "Z915RV", "ChargeStartTime": "2024-07-26 13:34:59.468"}, {"MACAddress": "cc8826dbdd30", "MaximumCurrent": 300.0, "MaximumVoltage": 420.0, "MaximumPower": 126000.0, "BatteryCapacity": null, "BMSDataFrequency": 4.061, "VehicleLabel": "Mercedes-Benz_EQA@250_66.5kWh", "Source": "log_DE0240B1GNCC00020K_CCU2_20240708_193406.log", "Number": "N100DN", "ChargeStartTime": "2024-07-08 19:24:45.093"}, {"MACAddress": "00b052000003", "MaximumCurrent": 250.0, "MaximumVoltage": 451.3, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 2.497, "VehicleLabel": "Peugeot_e-208_46.3kWh", "Source": "log_DE0240B1GNCC00020K_CCU2_20240727_000000.log", "Number": "P005SN", "ChargeStartTime": "2024-07-27 16:21:07.829"}, {"MACAddress": "e00ee1021f9c", "MaximumCurrent": 200.0, "MaximumVoltage": 387.0, "MaximumPower": null, "BatteryCapacity": null, "VehicleLabel": "Hyundai_Kona@electric_39.2kWh", "Source": "log_DE0240B1GNCC00020K_CCU2_20240823_000000.log", "Number": "S842GD"}, {"MACAddress": "a453ee077ac6", "MaximumCurrent": 500.0, "MaximumVoltage": 480.0, "MaximumPower": 250000.0, "BatteryCapacity": 50000.0, "VehicleLabel": "Peugeot_3008_73kWh", "Source": "log_DE0240B1GNCC00020K_CCU_20240826_000000.log", "Number": "GFP89R"}, {"MACAddress": "0ccc47865e43", "MaximumCurrent": 400.0, "MaximumVoltage": 495.1, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 15.846, "VehicleLabel": "BYD_Dolphin_60.4kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20240925_000000.log", "Number": "Z-891-BZ"}, {"MACAddress": "0c659a0469cf", "MaximumCurrent": 500.0, "MaximumVoltage": 399.0, "MaximumPower": 250000.0, "BatteryCapacity": null, "BMSDataFrequency": 2.616, "VehicleLabel": "BMW_iX@xDrive40_71kWh", "Source": "log_DE0240B1GNCC00020K_CCU2_20241015_000000.log", "Number": "N-248-SF", "ChargeStartTime": "2024-10-15 12:18:38.402"}, {"MACAddress": "001887ffffff", "MaximumCurrent": 457.0, "MaximumVoltage": 459.0, "MaximumPower": 200000.0, "BatteryCapacity": null, "VehicleLabel": "Togg_T10X_88.5kWh", "Source": "log_DE0120B1GP2C00146G_CCU_20241102_130644.log"}, {"MACAddress": "001887042db5", "MaximumCurrent": 250.0, "MaximumVoltage": 480.0, "MaximumPower": 100000.0, "BatteryCapacity": 50000.0, "VehicleLabel": "Citroen_C4_46.3kWh", "Source": "log_DE0120B1GP2C00126E_CCU_20241028_124958.log"}, {"MACAddress": "ec65ccd335d2", "MaximumCurrent": 500.0, "MaximumVoltage": 462.0, "MaximumPower": 250000.0, "BatteryCapacity": null, "VehicleLabel": "BMW_i7@xDrive60_101.7kWh", "Source": "log_DE0040B1GP3C00059M_CCU_20241030_120746.log"}, {"MACAddress": "141fba10fc48", "MaximumCurrent": 400.0, "MaximumVoltage": 700.8, "MaximumPower": null, "BatteryCapacity": null, "VehicleLabel": "Zhongtong_N12@Bus_350kWh", "Source": "log_DE0120B1GP2C00097M_CCU_20241107_100849.log"}, {"MACAddress": "141fba10ecf5", "MaximumCurrent": 250.0, "MaximumVoltage": 754.8, "MaximumPower": null, "BatteryCapacity": null, "VehicleLabel": "BYD_B12A03@Bus_433.45kWh", "Source": "log_DE0120B1GP3C00009F_CCU_20241108_100843.log"}, {"MACAddress": "141fba1369d4", "MaximumCurrent": 400.0, "MaximumVoltage": 675.0, "MaximumPower": null, "BatteryCapacity": null, "VehicleLabel": "SANY_Semi-Trailer_350kWh", "Source": "log_DE0240B1GNAC00021J_CCU_20241105_000000.log"}, {"MACAddress": "48c58db11050", "MaximumCurrent": 100.0, "MaximumVoltage": 471.0, "MaximumPower": 50000.0, "BatteryCapacity": null, "BMSDataFrequency": null, "VehicleLabel": "Land-Rover_Discovery_12.17kWh", "Source": "log_DE7480F1GR4C000053_CCU2_41_20241106-125930.log"}, {"MACAddress": "f07f0c27d7b1", "MaximumCurrent": 400.0, "MaximumVoltage": 464.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 0, "VehicleLabel": "Audi_q8@e-tron_89kWh", "Source": "log_DE0240B1GNCC00020K_CCU2_20241108_000000.log", "Number": "Z-270-PJ"}, {"MACAddress": "c0d941f02640", "MaximumCurrent": 586.2, "MaximumVoltage": 478.5, "MaximumPower": null, "BatteryCapacity": 100000.0, "BMSDataFrequency": 0, "VehicleLabel": "Polestar_4_94kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20241106_154617.log", "Number": "GLZ-02-Z"}, {"MACAddress": "0c659a0bd7f9", "MaximumCurrent": 500.0, "MaximumVoltage": 453.0, "MaximumPower": 250000.0, "BatteryCapacity": null, "BMSDataFrequency": 0, "VehicleLabel": "BMW_iX@xDrive50_105.2kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20241108_000000.log", "Number": "GRB-18-N"}, {"MACAddress": "0ccc4785d6ca", "MaximumCurrent": 400.0, "MaximumVoltage": 495.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 0, "VehicleLabel": "BYD_ATTO@3_60.5kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20241113_163011.log", "Number": "GDN-85-Z"}, {"MACAddress": "001a3770e886", "MaximumCurrent": 250.0, "MaximumVoltage": 471.0, "MaximumPower": 110000.0, "BatteryCapacity": null, "VehicleLabel": "Jaguar_I-Pace_84.7kWh", "Source": "log_DE0240B1GNCC00020K_CCU2_20241105_000000.log", "Number": "H-273-BD"}, {"MACAddress": "0030ab29c5be", "MaximumCurrent": 500.0, "MaximumVoltage": 488.0, "MaximumPower": 250000.0, "BatteryCapacity": 100000.0, "VehicleLabel": "BMW_i5@eDrive40_81.2kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20241109_121620.log", "Number": "M-CG-3149E"}, {"MACAddress": "141fba1059da", "MaximumCurrent": 156.0, "MaximumVoltage": 649.1, "MaximumPower": null, "BatteryCapacity": null, "VehicleLabel": "BYD_e6_71.7kWh", "Source": ""}, {"MACAddress": "9012a172bd15", "MaximumCurrent": 350.0, "MaximumVoltage": 825.6, "MaximumPower": null, "BatteryCapacity": null, "VehicleLabel": "Hyundai_Ioniq@5_74kWh", "Source": "log_DL0240B1GP5C00026S_CCU1_20241203_095203.log"}, {"MACAddress": "481693e5ee40", "MaximumCurrent": 375.0, "MaximumVoltage": 475.0, "MaximumPower": null, "BatteryCapacity": 75840.0, "BMSDataFrequency": 0, "VehicleLabel": "Volvo_XC40_75kWh", "Source": "log_DE0240B1GP7C00015K_CCU1_20241129_000000.log", "Number": ""}, {"MACAddress": "70b3d500f705", "MaximumCurrent": 201.0, "MaximumVoltage": 408.0, "MaximumPower": 82000.0, "BatteryCapacity": 71900.0, "BMSDataFrequency": 0, "VehicleLabel": "<PERSON><PERSON>_Deliver@9_72kwh", "Source": "log_DE0240B1GP7C00002F_CCU1_20241129_122354.log", "Number": ""}, {"MACAddress": "0ccc4789ba35", "MaximumCurrent": 400.0, "MaximumVoltage": 672.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 0, "VehicleLabel": "BYD_SEAL@U_87kWh", "Source": "log_DE4040B1GR1C00018K_CCU1_20241129_000000.log", "Number": ""}, {"MACAddress": "40ff400048e3", "MaximumCurrent": 400.0, "MaximumVoltage": 463.6, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 0, "VehicleLabel": "<PERSON><PERSON><PERSON><PERSON>@EVX_72.0kWh", "Source": "log_DE0240B1GP7C00002F_CCU2_20241202_104426.log", "Number": ""}, {"MACAddress": "a453ee0409c9", "MaximumCurrent": 250.0, "MaximumVoltage": 470.0, "MaximumPower": 100000.0, "BatteryCapacity": 50000.0, "BMSDataFrequency": 0, "VehicleLabel": "Peugeot_e-Expert_46.3kWh", "Source": "log_DE0240B1GP7C00015K_CCU2_20241206_115007.log", "Number": ""}, {"MACAddress": "fca47a13b04b", "MaximumCurrent": 200.0, "MaximumVoltage": 404.8, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 0, "VehicleLabel": "MAXUS_T90EV_88.5kWh", "Source": "log_DE0120B1GPAC00011F_CCU1_20241128_000000.log", "Number": ""}, {"MACAddress": "141fba10cfd9", "MaximumCurrent": 200.0, "MaximumVoltage": 666.0, "MaximumPower": null, "BatteryCapacity": null, "VehicleLabel": "BYD_ETM6_126kWh", "Source": "log_DE0120B1GNAC00053L_CCU1_20240614_120515.log"}, {"MACAddress": "e00ee1053c44", "MaximumCurrent": 200.0, "MaximumVoltage": 387.0, "MaximumPower": null, "BatteryCapacity": null, "VehicleLabel": "Hyundai_Kona@electric_39.2kWh", "Source": "log_DE0120B1GNAC00053L_CCU1_20240614_143337.log"}, {"MACAddress": "00c0595ea785", "MaximumCurrent": 302.0, "MaximumVoltage": 408.0, "MaximumPower": 120000.0, "BatteryCapacity": null, "VehicleLabel": "Subaru_Solterra_71.4kWh", "Source": "log_DE7240F1GR4C00001P_CCU2_20241105_030103.log", "Number": ""}, {"MACAddress": "ec65ccd7ea8e", "MaximumCurrent": 500.0, "MaximumVoltage": 417.0, "MaximumPower": 250000.0, "BatteryCapacity": null, "BMSDataFrequency": 0, "VehicleLabel": "BMW_iX3_74kWh", "Source": "log_DE0240B1GNCC00020K_CCU2_20241208_000000.log", "Number": "X-530-JS"}, {"MACAddress": "0c659a174da4", "MaximumCurrent": 500.0, "MaximumVoltage": 347.0, "MaximumPower": 250000.0, "BatteryCapacity": null, "BMSDataFrequency": 0, "VehicleLabel": "BMW_iX1@eDrive20_64.7kWh", "Source": "log_DE0240B1GNCC00020K_CCU2_20241207_120446.log", "Number": "GPT-04-Z"}, {"MACAddress": "0000f07f0c00", "MaximumCurrent": 125.0, "MaximumVoltage": 400.2, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 16.315, "VehicleLabel": "BMW_i3@60Ah_18.8kWh", "Source": "log_DE0240B1GNCC00020K_CCU2_20241027_000000.log", "Number": "2-AXU-780"}, {"MACAddress": "48c58dba07b8", "MaximumCurrent": 375.0, "MaximumVoltage": 475.0, "MaximumPower": null, "BatteryCapacity": 66200.0, "BMSDataFrequency": 0, "VehicleLabel": "Polestar_2_67kWh", "Source": "log_DE0120B1GP3C00076K_CCU1_20240508_115445.log"}, {"MACAddress": "54f8f00ca3c2", "MaximumCurrent": 399.0, "MaximumVoltage": 360.0, "MaximumPower": 254000.0, "BatteryCapacity": null, "BMSDataFrequency": 0, "VehicleLabel": "Tesla_Model@S_72.5kWh", "Source": "log_DE1120B1GN1C000307_CCU2_20241216_154147.log", "Number": "TP-541-L"}, {"MACAddress": "885046916f4c", "MaximumCurrent": 500.0, "MaximumVoltage": 475.0, "MaximumPower": null, "BatteryCapacity": 70740.0, "BMSDataFrequency": 0, "VehicleLabel": "Volvo_EX40_79.0kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20241220_000000.log", "Number": "GFP-22-L"}, {"MACAddress": "c0d941f02640", "MaximumCurrent": 586.2, "MaximumVoltage": 478.5, "MaximumPower": null, "BatteryCapacity": 100000.0, "VehicleLabel": "Polestar_4_94kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20241212_020206.log"}, {"MACAddress": "00b052000003", "MaximumCurrent": 250.0, "MaximumVoltage": 453.7, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 0, "VehicleLabel": "Peugeot_e-208_46.3kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20241219_020234.log", "Number": "P-325-NS"}, {"MACAddress": "481693e7bc49", "MaximumCurrent": 375.0, "MaximumVoltage": 475.0, "MaximumPower": null, "BatteryCapacity": 73640.0, "BMSDataFrequency": 0, "VehicleLabel": "Polestar_2_75kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20250131_074056.log", "Number": "T-762-NG"}, {"MACAddress": "441db167f22a", "MaximumCurrent": 500.0, "MaximumVoltage": 780.0, "MaximumPower": null, "BatteryCapacity": null, "BMSDataFrequency": 0, "VehicleLabel": "Porsche_Macan_95kWh", "Source": "log_DE0240B1GNCC00020K_CCU1_20250127_020219.log", "Number": "GXX-57-P"}]