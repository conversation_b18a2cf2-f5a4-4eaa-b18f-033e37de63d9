apiVersion: apps/v1
kind: Deployment
metadata:
  name: ems
  namespace: energy
  labels:
    app: ems
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ems
  template:
    metadata:
      labels:
        app: ems
    spec:
      containers:
      - name: ems
        image: 762233743316.dkr.ecr.us-west-2.amazonaws.com/ems-cpu:{{COMMIT_ID_SHORTER}}
        ports:
        - containerPort: 8000
        env:
        - name: PYTHONPATH
          value: "/app"
        - name: ENVIRONMENT
          value: "dev"
        - name: CONFIG_ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: ems-secrets
              key: config-encryption-key
        volumeMounts:
        - name: logs-volume
          mountPath: /app/logs
        resources:
          requests:
            memory: "4Gi"
            cpu: "2"
          limits:
            memory: "4Gi"
            cpu: "2"
      volumes:
      - name: logs-volume
        hostPath:
          path: /var/logs/ems
          type: DirectoryOrCreate
---
apiVersion: v1
kind: Service
metadata:
  name: ems-service
  namespace: energy
spec:
  selector:
    app: ems
  ports:
  - port: 8000
    targetPort: 8000
  type: ClusterIP
---
apiVersion: v1
kind: Secret
metadata:
  name: ems-secrets
  namespace: energy
type: Opaque
data:
  config-encryption-key: "dFRaMjU2S3A0UlRPMndPdVpFb2NPeGQzVXVYMGNiRVhFZUMyYkpYaF8wRT0="
---
apiVersion: v1
kind: Service
metadata:
  name: ems-alb-backup
  namespace: energy
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: http
    service.beta.kubernetes.io/aws-load-balancer-nlb-target-type: ip
    service.beta.kubernetes.io/aws-load-balancer-scheme: internet-facing
    service.beta.kubernetes.io/aws-load-balancer-type: external
spec:
  selector:
    app: ems
  ports:
  - name: http
    protocol: TCP
    port: 80
    targetPort: 8000
  type: LoadBalancer
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ems-ingress
  namespace: energy
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: ems.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ems-service
            port:
              number: 8000
