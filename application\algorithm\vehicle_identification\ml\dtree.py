#############################################################################
# Copyright (C) 2023 AUTEL Netherlands B.V. - All Rights Reserved
# Unauthorized copying of this file, via any medium is strictly prohibited
#
# Proprietary and confidential
#############################################################################
import sklearn.tree as tree
from .base import Classifier


class DecisionTreeClassifier(Classifier):
    def _algorithm(self):
        return tree.DecisionTreeClassifier()
