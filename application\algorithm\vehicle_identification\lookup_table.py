
capatable = {
    "Audi_Q4@e-tron_52kWh,Volkswagen_ID.3_45kWh,Volkswagen_ID.4_52kWh,Volkswagen_ID.4_77kWh":
        {
            (38, 65): "Audi_Q4@e-tron_52kWh,Volkswagen_ID.3_45kWh,Volkswagen_ID.4_52kWh",
            (66, 84): "Volkswagen_ID.4_77kWh"
        },
    "Skoda_Enyaq@80_77kWh,Volkswagen_ID.4_52kWh,Volkswagen_ID.4_77kWh":
        {
            (46, 65): "Volkswagen_ID.4_52kWh",
            (66, 90): "Skoda_Enyaq@80_77kWh,Volkswagen_ID.4_77kWh"
        },
    "Mercedes-Benz_EQE_90.6kWh,Mercedes-Benz_EQS_107.8kWh":
        {
            (82, 98): "Mercedes-Benz_EQE_90.6kWh",
            (100, 115): "Mercedes-Benz_EQS_107.8kWh"
        },
    "Mercedes-Benz_EQB@250_66.5kWh,Mercedes-Benz_eVito_90kWh":
        {
            (60, 81): "Mercedes-Benz_EQB@250_66.5kWh",
            (83, 97): "Mercedes-Benz_eVito_90kWh"
        },
    "Mercedes-Benz_EQA@250_66.5kWh,Mercedes-Benz_eVito_90kWh":
    {
        (60, 81): "Mercedes-Benz_EQA@250_66.5kWh",
        (83, 97): "Mercedes-Benz_eVito_90kWh"
    },
    "Citroen_C4_46.3kWh,Citroen_Jumpy_68kWh,Citroen_e-Berlingo_46.3kWh,Fiat_Ulysse_68kWh,Opel_Corsa_46.3kWh,Opel_Mokka_46.3kWh,Opel_Vivaro_68kWh,Peugeot_e-2008_46.3kWh,Peugeot_e-208_46.3kWh,Peugeot_e-Expert_46.3kWh,Renault_Master":
        {
            (40, 51): "Citroen_C4_46.3kWh,Citroen_e-Berlingo_46.3kWh,Opel_Corsa_46.3kWh,Opel_Mokka_46.3kWh,Peugeot_e-2008_46.3kWh,Peugeot_e-208_46.3kWh,Peugeot_e-Expert_46.3kWh,Renault_Master",
            (53, 75): "Citroen_Jumpy_68kWh,Opel_Vivaro_68kWh,Renault_Master"
        },
    "Audi_e-tron_64.7kWh,Audi_e-tron_86.5kWh,Audi_q8@e-tron_89kWh":
        {
            (50, 75): "Audi_e-tron_64.7kWh",
            (76, 100): "Audi_e-tron_86.5kWh,Audi_q8@e-tron_89kWh"
        },
    "Audi_e-tron_64.7kWh,Audi_e-tron_86.5kWh":
        {
            (50, 75): "Audi_e-tron_64.7kWh",
            (76, 100): "Audi_e-tron_86.5kWh"
        }
}

paratable = {
    "MG_5@EV_46kWh,MG_ZS@SUV_49kWh":
        {
            "MaximumCurrent": {"220.0": "MG_5@EV_46kWh", "217.8": "MG_ZS@SUV_49kWh"}
        },
    "Audi_e-tron_64.7kWh,Audi_e-tron_86.5kWh,Audi_q8@e-tron_89kWh,Hyundai_Kona@electric_65.4kWh":
        {
            "MaximumVoltage": {"459.0": "Audi_e-tron_64.7kWh"}
        },
    "Hyundai_Ioniq_28kWh,Hyundai_Kona@electric_64kWh":
        {
            "MaximumVoltage": {"412.8": "Hyundai_Ioniq_28kWh", "421.4": "Hyundai_Kona@electric_64kWh"}
        },
    "BMW_i3@120Ah_37.9kWh,BMW_i3@60Ah_18.8kWh,MINI_Cooper@SE_28.9kWh":
        {
            "MaximumVoltage": {"415.1": "BMW_i3@120Ah_37.9@kWh", "400.2": "BMW_i3@60Ah_18.8kWh", "411.6": "MINI_Cooper@SE_28.9kWh"}
        },
    "BMW_i4@M50_80.7kWh,BMW_i4@eDrive40_80.7kWh,BMW_iX3_74kWh":
        {
            "MaximumVoltage": {"500.0": "BMW_iX3_74kWh"}
        },
    "Skoda_Citigo_32.3kWh,Volkswagen_Golf_32kWh,Volkswagen_e-Crafter_35.8kWh":
        {
            "MaximumVoltage": {"357.0": "Skoda_Citigo_32.3kWh"}
        }
}

Mac_match = {
    "ac965b": "Lucid_xxx_xxx kWh",
    "9c36f8": "Hyundai_xxx_xxx kWh",
    "54f8f0": "Tesla_xxx_xxx kWh",
    "dc4427": "Tesla_xxx_xxx kWh",
    "98ed5c": "Tesla_xxx_xxx kWh"
}
def capatable2VehicleLabel(VehicleLabel, capa):
    # capa = capa/1000
    VehicleLabel_new = VehicleLabel
    if VehicleLabel in capatable:
        for capas, label in capatable.get(VehicleLabel).items():
            if capas[0] <= capa <= capas[1]:
                VehicleLabel_new = label
    return VehicleLabel_new

def paratable2VehicleLabel(VehicleLabel, MaximumCurrent, MaximumVoltage):
    VehicleLabel_new = None
    if VehicleLabel in paratable:
        if 'MaximumCurrent' in paratable.get(VehicleLabel):
            if str(MaximumCurrent) in paratable.get(VehicleLabel).get('MaximumCurrent'):
                VehicleLabel_new = paratable.get(VehicleLabel).get('MaximumCurrent').get(str(MaximumCurrent))
        elif 'MaximumVoltage' in paratable.get(VehicleLabel):
            if str(MaximumVoltage) in paratable.get(VehicleLabel).get('MaximumVoltage'):
                VehicleLabel_new = paratable.get(VehicleLabel).get('MaximumVoltage').get(str(MaximumVoltage))
    return VehicleLabel_new

def mac2VehicleLabel(mac):
    mac_tmp = mac[:6].lower()
    if mac_tmp in Mac_match:
        return Mac_match[mac_tmp]
    else:
        return 'Unknown'