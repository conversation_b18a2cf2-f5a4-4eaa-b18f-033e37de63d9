import time
import threading
from datetime import datetime, timedelta
import pytz

from application.algorithm_schedule.dynamic_price_fetch import dynamic_price_fetch
from application.utils.logger import setup_logger
from application.utils.event_bus import EventType, event_bus

logger = setup_logger("dynamic_price_scheduler", direction="dynamic_price_scheduler")


class DynamicPriceScheduler:
    def __init__(self):
        """Initialize Dynamic Price Scheduler"""
        try:
            self.site_no = 'ALL'
            self.first_run_complete = False
            self._stop_event = threading.Event()
            self.cet = pytz.timezone('CET')
            self.first_run = True
            self.running = False
            self.last_dynamic_price_biz_seq = None
            
            logger.info("Dynamic Price Scheduler initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Dynamic Price Scheduler: {str(e)}")
            raise

    def start_scheduler(self):
        """
        启动动态电价调度器
        每天在CET时间的12:30执行动态电价获取
        """
        logger.info("启动全局动态电价获取调度器")
        self.running = True
        
        cet = pytz.timezone('CET')

        while self.running:
            if not self.first_run_complete:
                # 首次运行时立即执行
                logger.info("首次运行立即获取电价")
                self._perform_dynamic_price_fetch()
                self.first_run_complete = True

            now = datetime.now(tz=cet)
            logger.info(f'now:{now}')

            # 计算下一个CET时间的12:30
            next_exec_time = (now + timedelta(days=0)).replace(hour=13, minute=00, second=0, microsecond=0)
            if now >= next_exec_time:
                # 如果今天的时间已经过了12:30，就设定为明天的12:30
                next_exec_time += timedelta(days=1)

            sleep_duration = (next_exec_time - now).total_seconds()
            logger.info(f"距离下次执行动态电价获取剩余: {sleep_duration/3600}h.")

            # 休眠直到下一个12:30 PM CET
            if self._stop_event.wait(timeout=sleep_duration):
                break

            self._perform_dynamic_price_fetch()

    def _perform_dynamic_price_fetch(self):
        try:
            cet = pytz.timezone('CET')
            now = datetime.now(tz=cet)
            today = now.date()
            noon = now.replace(hour=12, minute=0, second=0, microsecond=0)
            if now >= noon:
                # 如果时间大于等于12:00，获取当天和明天的电价
                tomorrow = today + timedelta(days=1)
                if not self.first_run_complete:
                    dynamic_price_fetch(self.site_no, today)
                    price = dynamic_price_fetch(self.site_no, tomorrow)
                else:
                    price = dynamic_price_fetch(self.site_no, tomorrow)
            else:
                # 获取当天的电价
                price = dynamic_price_fetch(self.site_no, today)
                logger.info(f'price:{price}')

            # 添加价格数据有效性检查
            if price and price.get("biz_seq"):
                if self.last_dynamic_price_biz_seq is None or price.get("biz_seq") != self.last_dynamic_price_biz_seq:

                    logger.info(f"电价数据变化，触发储能调度")
                    event_bus.publish(
                        EventType.DYNAMIC_PRICE_CHANGE
                    )
                self.last_dynamic_price_biz_seq = price.get("biz_seq")
            else:
                logger.warning(f"获取电价数据失败或数据无效，跳过储能调度触发")
        except Exception as e:
            logger.error(f"perform fetch dynamic price failed: {e}")

    def _calculate_next_execution_time(self):
        """计算下一次执行时间的睡眠秒数"""
        now = datetime.now(tz=self.cet)
        next_exec_time = (now + timedelta(days=0)).replace(hour=12, minute=30, second=0, microsecond=0)
        
        # 如果今天的时间已经过了12:30，就设定为明天的12:30
        if now >= next_exec_time:
            next_exec_time += timedelta(days=1)
        
        return (next_exec_time - now).total_seconds()

    def stop_scheduler(self):
        """停止调度器"""
        logger.info("正在停止动态电价调度器...")
        self.running = False
