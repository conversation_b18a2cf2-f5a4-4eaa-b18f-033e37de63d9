import time
import threading
from datetime import datetime, timedelta
import pytz
from typing import List, Dict, Any, Optional

from application.algorithm_schedule.dynamic_price_fetch import dynamic_price_fetch, get_dynamic_electricity_price, format_electricity_price_data, formate_electricity_price_data_to_db, get_current_time, get_cet_offset
from application.db_operate.db_operate import DBOperate
from application.utils.electricity_price import NordPoolAPI
from application.utils.idempotency import generate_idempotency_key
from application.utils.logger import setup_logger
from application.utils.event_bus import EventType, event_bus

logger = setup_logger("dynamic_price_scheduler", direction="dynamic_price_scheduler")


class DynamicPriceRetryConfig:
    """动态电价重试配置"""
    # 支持的国家列表
    SUPPORTED_COUNTRIES = ['AT', 'BE', 'DE', 'FR', 'NL', 'PL']

    # 重试配置
    MAX_RETRIES_PER_COUNTRY = 5  # 每个国家最大重试次数
    RETRY_INTERVALS = [2, 5, 10, 15, 30]  # 重试间隔（分钟）
    MAX_TOTAL_RETRY_TIME = 120  # 最大总重试时间（分钟）

    # 数据发布时间配置
    PRICE_PUBLISH_TIME = 12  # 电价通常在12:10左右发布
    EARLY_FETCH_START_TIME = 12  # 从12:00开始尝试获取
    LATE_FETCH_END_TIME = 14  # 最晚到14:00结束获取

    # 日志配置
    DETAILED_LOGGING = True


class DynamicPriceScheduler:
    def __init__(self):
        """Initialize Dynamic Price Scheduler"""
        try:
            self.site_no = 'ALL'
            self.first_run_complete = False
            self._stop_event = threading.Event()
            self.cet = pytz.timezone('CET')
            self.first_run = True
            self.running = False
            self.last_dynamic_price_biz_seq = None

            # 重试相关状态
            self.countries_retry_count = {country: 0 for country in DynamicPriceRetryConfig.SUPPORTED_COUNTRIES}
            self.countries_success_status = {country: False for country in DynamicPriceRetryConfig.SUPPORTED_COUNTRIES}
            self.last_fetch_date = None

            logger.info("Dynamic Price Scheduler initialized successfully")
            logger.info(f"支持的国家: {DynamicPriceRetryConfig.SUPPORTED_COUNTRIES}")
        except Exception as e:
            logger.error(f"Failed to initialize Dynamic Price Scheduler: {str(e)}")
            raise

    def start_scheduler(self):
        """
        启动动态电价调度器
        每天在CET时间的12:10开始执行动态电价获取，确保第一时间获取到电价数据
        """
        logger.info("启动全局动态电价获取调度器")
        logger.info("调度策略: 每天12:10开始获取次日电价，支持重试机制")
        self.running = True

        cet = pytz.timezone('CET')

        while self.running:
            if not self.first_run_complete:
                # 首次运行时立即执行
                logger.info("首次运行立即获取电价")
                self._perform_dynamic_price_fetch_with_retry()
                self.first_run_complete = True

            now = datetime.now(tz=cet)
            logger.info(f'当前时间: {now}')

            # 计算下一个CET时间的12:10（电价发布时间）
            next_exec_time = (now + timedelta(days=0)).replace(hour=12, minute=10, second=0, microsecond=0)
            if now >= next_exec_time:
                # 如果今天的时间已经过了12:10，就设定为明天的12:10
                next_exec_time += timedelta(days=1)

            sleep_duration = (next_exec_time - now).total_seconds()
            logger.info(f"距离下次执行动态电价获取剩余: {sleep_duration/3600:.2f}小时 (下次执行时间: {next_exec_time})")

            # 休眠直到下一个12:10 CET
            if self._stop_event.wait(timeout=sleep_duration):
                break

            # 重置每日状态
            self._reset_daily_status()
            self._perform_dynamic_price_fetch_with_retry()

    def _reset_daily_status(self):
        """重置每日状态"""
        self.countries_retry_count = {country: 0 for country in DynamicPriceRetryConfig.SUPPORTED_COUNTRIES}
        self.countries_success_status = {country: False for country in DynamicPriceRetryConfig.SUPPORTED_COUNTRIES}
        logger.info("已重置每日电价获取状态")

    def _perform_dynamic_price_fetch_with_retry(self):
        """
        执行带重试机制的动态电价获取
        针对每个国家单独进行重试，确保获取到所有国家的电价数据
        """
        try:
            cet = pytz.timezone('CET')
            now = datetime.now(tz=cet)
            today = now.date()

            # 确定要获取的日期
            target_date = today + timedelta(days=1)  # 获取明天的电价

            if not self.first_run_complete:
                # 首次运行时也获取今天的电价
                logger.info("首次运行，同时获取今天和明天的电价")
                self._fetch_all_countries_with_retry(today)

            # 获取明天的电价（主要目标）
            logger.info(f"开始获取 {target_date} 的电价数据")
            self.last_fetch_date = target_date

            success_countries, failed_countries = self._fetch_all_countries_with_retry(target_date)

            # 检查获取结果
            if success_countries:
                logger.info(f"成功获取到 {len(success_countries)} 个国家的电价: {success_countries}")

                # 构建并发送电价数据
                self._build_and_send_price_data(target_date, success_countries)

                # 触发储能调度
                logger.info("电价数据获取成功，触发储能调度")
                event_bus.publish(EventType.DYNAMIC_PRICE_CHANGE)

            if failed_countries:
                logger.warning(f"以下国家电价获取失败: {failed_countries}")
                # 启动后台重试任务
                self._start_background_retry(target_date, failed_countries)

        except Exception as e:
            logger.error(f"动态电价获取过程发生异常: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

    def _fetch_all_countries_with_retry(self, date) -> tuple[List[str], List[str]]:
        """
        为所有国家获取电价数据，支持重试机制

        Returns:
            tuple: (成功的国家列表, 失败的国家列表)
        """
        success_countries = []
        failed_countries = []

        for country in DynamicPriceRetryConfig.SUPPORTED_COUNTRIES:
            logger.info(f"开始获取国家 {country} 的电价数据 (日期: {date})")

            success = self._fetch_single_country_with_retry(country, date)

            if success:
                success_countries.append(country)
                self.countries_success_status[country] = True
                logger.info(f"✅ 国家 {country} 电价获取成功")
            else:
                failed_countries.append(country)
                logger.warning(f"❌ 国家 {country} 电价获取失败")

        return success_countries, failed_countries

    def _fetch_single_country_with_retry(self, country: str, date) -> bool:
        """
        为单个国家获取电价数据，支持重试机制

        Args:
            country: 国家代码
            date: 目标日期

        Returns:
            bool: 是否成功获取
        """
        max_retries = DynamicPriceRetryConfig.MAX_RETRIES_PER_COUNTRY
        retry_intervals = DynamicPriceRetryConfig.RETRY_INTERVALS

        for attempt in range(max_retries + 1):  # +1 包含初始尝试
            try:
                if DynamicPriceRetryConfig.DETAILED_LOGGING:
                    logger.info(f"国家 {country} 第 {attempt + 1} 次尝试获取电价 (日期: {date})")

                # 调用API获取单个国家的电价数据
                price_data = get_dynamic_electricity_price(areas=country, date=date)

                if self._is_valid_country_price_data(price_data, country):
                    # 保存到数据库
                    self._save_country_price_data(country, price_data, date)

                    if DynamicPriceRetryConfig.DETAILED_LOGGING:
                        logger.info(f"国家 {country} 电价数据获取成功 (第 {attempt + 1} 次尝试)")

                    return True
                else:
                    if DynamicPriceRetryConfig.DETAILED_LOGGING:
                        logger.warning(f"国家 {country} 电价数据无效或为空 (第 {attempt + 1} 次尝试)")

                    # 如果不是最后一次尝试，等待后重试
                    if attempt < max_retries:
                        wait_minutes = retry_intervals[min(attempt, len(retry_intervals) - 1)]
                        logger.info(f"国家 {country} 等待 {wait_minutes} 分钟后进行第 {attempt + 2} 次重试...")
                        time.sleep(wait_minutes * 60)  # 转换为秒
                        self.countries_retry_count[country] += 1
                    else:
                        logger.error(f"国家 {country} 电价获取最终失败，已尝试 {attempt + 1} 次")

            except Exception as e:
                logger.error(f"国家 {country} 电价获取异常 (第 {attempt + 1} 次尝试): {str(e)}")

                # 如果不是最后一次尝试，等待后重试
                if attempt < max_retries:
                    wait_minutes = retry_intervals[min(attempt, len(retry_intervals) - 1)]
                    logger.info(f"国家 {country} 等待 {wait_minutes} 分钟后进行第 {attempt + 2} 次重试...")
                    time.sleep(wait_minutes * 60)
                    self.countries_retry_count[country] += 1
                else:
                    logger.error(f"国家 {country} 电价获取最终失败，已尝试 {attempt + 1} 次，最后异常: {str(e)}")

        return False

    def _is_valid_country_price_data(self, price_data: Any, country: str) -> bool:
        """
        验证单个国家的电价数据是否有效

        Args:
            price_data: 电价数据
            country: 国家代码

        Returns:
            bool: 数据是否有效
        """
        if not price_data:
            if DynamicPriceRetryConfig.DETAILED_LOGGING:
                logger.warning(f"国家 {country} 电价数据为空")
            return False

        # 检查是否有prices数组
        prices = price_data.get('prices', [])
        if not prices:
            if DynamicPriceRetryConfig.DETAILED_LOGGING:
                logger.warning(f"国家 {country} 缺少prices数组")
            return False

        # 检查是否有24小时的数据
        if len(prices) != 24:
            if DynamicPriceRetryConfig.DETAILED_LOGGING:
                logger.warning(f"国家 {country} 电价数据不完整，期望24条记录，实际 {len(prices)} 条")
            return False

        # 检查价格数据的基本字段
        for i, price in enumerate(prices):
            if not all(key in price for key in ['price', 'deliveryStart', 'deliveryEnd']):
                if DynamicPriceRetryConfig.DETAILED_LOGGING:
                    logger.warning(f"国家 {country} 第 {i+1} 条价格记录缺少必要字段")
                return False

        if DynamicPriceRetryConfig.DETAILED_LOGGING:
            logger.debug(f"国家 {country} 电价数据验证通过，包含 {len(prices)} 条有效记录")

        return True

    def _save_country_price_data(self, country: str, price_data: Dict[str, Any], date):
        """
        保存单个国家的电价数据到数据库

        Args:
            country: 国家代码
            price_data: 电价数据
            date: 日期
        """
        try:
            db_op = DBOperate()
            db_price_data = formate_electricity_price_data_to_db(price_data)
            db_op.save_site_electricity_price(region=country, datas=db_price_data, date=date)

            if DynamicPriceRetryConfig.DETAILED_LOGGING:
                logger.info(f"国家 {country} 电价数据已保存到数据库")

        except Exception as e:
            logger.error(f"保存国家 {country} 电价数据到数据库失败: {str(e)}")
            raise

    def _start_background_retry(self, date, failed_countries: List[str]):
        """
        启动后台重试任务，为失败的国家继续尝试获取电价

        Args:
            date: 目标日期
            failed_countries: 失败的国家列表
        """
        def background_retry():
            logger.info(f"启动后台重试任务，目标国家: {failed_countries}")

            retry_success_countries = []
            max_background_time = DynamicPriceRetryConfig.MAX_TOTAL_RETRY_TIME  # 最大后台重试时间
            start_time = time.time()

            while failed_countries and (time.time() - start_time) < (max_background_time * 60):
                countries_to_remove = []

                for country in failed_countries:
                    if self.countries_retry_count[country] >= DynamicPriceRetryConfig.MAX_RETRIES_PER_COUNTRY:
                        logger.warning(f"国家 {country} 已达到最大重试次数，停止重试")
                        countries_to_remove.append(country)
                        continue

                    logger.info(f"后台重试获取国家 {country} 的电价数据")
                    success = self._fetch_single_country_with_retry(country, date)

                    if success:
                        retry_success_countries.append(country)
                        countries_to_remove.append(country)
                        logger.info(f"✅ 后台重试成功: 国家 {country}")

                # 移除已成功或已达到最大重试次数的国家
                for country in countries_to_remove:
                    failed_countries.remove(country)

                # 如果还有失败的国家，等待一段时间后继续
                if failed_countries:
                    wait_time = 10  # 后台重试间隔10分钟
                    logger.info(f"后台重试等待 {wait_time} 分钟后继续，剩余国家: {failed_countries}")
                    time.sleep(wait_time * 60)

            if retry_success_countries:
                logger.info(f"后台重试成功获取到以下国家的电价: {retry_success_countries}")
                # 重新构建并发送电价数据
                self._build_and_send_price_data(date, retry_success_countries, is_retry=True)

            if failed_countries:
                logger.error(f"后台重试最终失败的国家: {failed_countries}")

        # 启动后台线程
        retry_thread = threading.Thread(target=background_retry, daemon=True)
        retry_thread.start()

    def _build_and_send_price_data(self, date, success_countries: List[str], is_retry: bool = False):
        """
        构建并发送电价数据

        Args:
            date: 日期
            success_countries: 成功获取电价的国家列表
            is_retry: 是否为重试获取的数据
        """
        try:
            electricity_prices = []
            today = datetime.now(tz=pytz.timezone('CET')).date()

            # 从数据库读取已保存的电价数据并格式化
            db_op = DBOperate()

            for country in success_countries:
                try:
                    # 这里需要从数据库读取数据，然后格式化为前端需要的格式
                    # 由于我们已经保存了数据，这里构建标准格式
                    electricity_price = {
                        "region": country,
                        "grid_name": "Nord Pool",
                        "time_zone": f"UTC+{get_cet_offset()}",
                        "refresh_time": f"D-0 {get_current_time()}" if date == today else f"D-1 {get_current_time()}",
                        "freq": "H-1",
                        "can_use": True,
                        "price_data": []  # 这里应该从数据库读取具体的价格数据
                    }
                    electricity_prices.append(electricity_price)

                except Exception as e:
                    logger.error(f"构建国家 {country} 电价数据失败: {str(e)}")
                    continue

            if electricity_prices:
                # 构建价格段数据
                price_segments = {
                    "biz_seq": generate_idempotency_key({"electricity_prices": electricity_prices, "date": str(date), "is_retry": is_retry}),
                    "electricity_price": electricity_prices
                }

                # 发送电价数据
                from application.algorithm_schedule.dynamic_price_fetch import send_electricity_price_with_retry
                success = send_electricity_price_with_retry(price_segments)

                if success:
                    logger.info(f"{'重试' if is_retry else ''}电价数据发送成功，包含 {len(success_countries)} 个国家")

                    # 更新业务序列号
                    if not is_retry:  # 只有非重试的数据才更新主序列号
                        self.last_dynamic_price_biz_seq = price_segments["biz_seq"]
                else:
                    logger.error(f"{'重试' if is_retry else ''}电价数据发送失败")

        except Exception as e:
            logger.error(f"构建和发送电价数据失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

    def get_retry_status(self) -> Dict[str, Any]:
        """
        获取重试状态信息

        Returns:
            dict: 包含各国重试状态的字典
        """
        return {
            "countries_retry_count": self.countries_retry_count.copy(),
            "countries_success_status": self.countries_success_status.copy(),
            "last_fetch_date": str(self.last_fetch_date) if self.last_fetch_date else None,
            "supported_countries": DynamicPriceRetryConfig.SUPPORTED_COUNTRIES,
            "config": {
                "max_retries_per_country": DynamicPriceRetryConfig.MAX_RETRIES_PER_COUNTRY,
                "retry_intervals": DynamicPriceRetryConfig.RETRY_INTERVALS,
                "max_total_retry_time": DynamicPriceRetryConfig.MAX_TOTAL_RETRY_TIME
            }
        }

    def _calculate_next_execution_time(self):
        """计算下一次执行时间的睡眠秒数"""
        now = datetime.now(tz=self.cet)
        next_exec_time = (now + timedelta(days=0)).replace(hour=12, minute=10, second=0, microsecond=0)

        # 如果今天的时间已经过了12:10，就设定为明天的12:10
        if now >= next_exec_time:
            next_exec_time += timedelta(days=1)

        return (next_exec_time - now).total_seconds()

    def stop_scheduler(self):
        """停止调度器"""
        logger.info("正在停止动态电价调度器...")
        self.running = False
