schema: 1.0.0

artifacts:
  ems:
    kind: service
    type: python
    workdir: application
    auth_required: true
    resources:
      replicas: 1
      cpu: 2
      memory: 4Gi
      gpu: 0
      storage: 10Gi
    dependencies:
        - name: mysql
          alias: ems-mysql
          type: service
          resources:
            storage: "100Gi"
            cpu: "2"
            memory: "2Gi"
    params:
      - name: KAFKA_CONSUMER
        value: ip:port group-id topics
        type: env
