class ChargePileStatus:
    """充电桩状态枚举"""
    VEHICLE_NOT_CONNECTED = '0'  # 车辆未连接
    VEHICLE_CONNECTED = '1'  # 车辆已连接
    PARAMETER_HANDSHAKE = '2'  # 参数握手阶段
    INSULATION_DETECTION = '3'  # 绝缘检测阶段
    PARAMETER_IDENTIFICATION = '4'  # 参数辨识阶段
    PARAMETER_CONFIGURATION = '5'  # 参数配置阶段
    PRE_CHARGING = '6'  # 预充电阶段
    FORMAL_CHARGING = '7'  # 正式充电阶段
    CHARGING_PAUSED = '8'  # 充电暂停
    CHARGING_STOPPING = '9'  # 充电停止中
    CHARGING_STOP_COMPLETED = '10'  # 充电停止完成
    CHARGING_COMPLETED = '11'  # 充电完成

    CHARGING_STATES = {VEHICLE_CONNECTED, FORMAL_CHARGING}  # 自定义状态方便使用


class OptimizationTaskStatus:
    """优化任务状态枚举"""
    ALLOCATION = 1  # 分配
    SENT = 2  # 下发


class ChargerControlPowerStatus:
    """充电枪控制功率状态枚举"""
    NOT_SENT = 1  # 未下发
    SENT = 2  # 已下发


class TriggerEvent:
    """触发事件枚举"""
    NORMAL = 0  # 正常
    IS_STARTUP = 1  # 启动
    IS_2MIN = 2  # 2分钟
    IS_CONSTRAINT = 3  # 约束
    CONFIG_CHANGED = 4  # 配置改变


class EVPredictionStatus:
    NOT_PREDICTED = 0  # 没有进行预测
    WAITING_TWICE_PREDICT = 1  # 完成了开机预测
    TWICE_PREDICTED = 2  # 完成了二次预测
    NO_FORCE_PREDICTED = 3  # 该状态表示不再跳过周期时间检查强制触发功率和soc预测


# 强制进行功率分配和下发的事件
FORCE_EVENT = (TriggerEvent.IS_STARTUP, TriggerEvent.IS_2MIN, TriggerEvent.CONFIG_CHANGED)
