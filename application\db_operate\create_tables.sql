CREATE DATABASE IF NOT EXISTS `ems` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `ems`;

CREATE TABLE `Site` (
    `site_no` VARCHAR(50) NOT NULL,
    `demand_limit` INT NULL COMMENT '月最优demand_limit值',
    `es_total_energy` INT NULL COMMENT '储能电池容量，单位kwh',
    `region` VARCHAR(255) NULL COMMENT '场站所在国家/地区',
    `lat_and_lng` VARCHAR(100) NULL COMMENT '场站的经纬度信息，用于获取天气信息',
    `site_grid_limit` INT NULL COMMENT '场站电网功率限制，单位kw',
    `grid_reverse` TINYINT NULL COMMENT '电网能否逆流 1 可以 0 不可以',
    `grid_name` VARCHAR(100) NULL COMMENT '电力公司名称',
    `pv_can_control` INT NULL COMMENT '光伏是否可调 1可调 0不可调',
    `is_ai_active` TINYINT NOT NULL COMMENT '是否开启AI能力  1 start 0 stop',
    `purchase_price_type` INT NULL COMMENT '购买电价类型 1：固定电价,2：分时电价,3：动态电价',
    `load_limit` INT NULL COMMENT '场站总线负载限制功率，单位kW',
    `pv_max_power` DECIMAL(10,2) NULL COMMENT '光伏最大发电功率，单位kW',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`site_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `Pile` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `pile_sn` VARCHAR(50) NOT NULL,
    `gun_num` INT NOT NULL,
    `pile_type` VARCHAR(20) NOT NULL,
    `rated_power` DECIMAL(10,2) NOT NULL COMMENT '运维发送的功率',
    `site_no` VARCHAR(50) NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_pile_sn` (`pile_sn`),
    KEY `idx_site_no` (`site_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `Charger` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `charger_sn` VARCHAR(50) NOT NULL,
    `gun_no` INT NOT NULL,
    `bom_index` INT NOT NULL,
    `pile_sn` VARCHAR(50) NOT NULL,
    `max_curr` FLOAT NOT NULL DEFAULT 0 COMMENT '枪线最大电流',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_charger_sn` (`charger_sn`),
    KEY `idx_pile_sn` (`pile_sn`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `Module` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `pile_sn` VARCHAR(50) NOT NULL,
    `type` VARCHAR(20) NOT NULL DEFAULT 'unknown' COMMENT '模块类型',
    `module_no` INT NOT NULL,
    `unit_power` DECIMAL(10,2) NOT NULL,
    `unit_num` INT NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_pile_sn` (`pile_sn`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `ChargerSession` (
    `local_id` VARCHAR(50) NOT NULL COMMENT '桩本地订单ID',
    `site_no` VARCHAR(50) NOT NULL COMMENT '场站ID',
    `charger_sn` VARCHAR(50) NOT NULL,
    `mac_addr` VARCHAR(50) NOT NULL,
    `start_time` DATETIME NOT NULL,
    `end_time` DATETIME,
    `status` VARCHAR(10) NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`local_id`),
    KEY `idx_charger_sn` (`charger_sn`),
    KEY `idx_site_no` (`site_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `EVModel` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `local_id` VARCHAR(50) NOT NULL COMMENT '桩本地订单ID',
    `charger_sn` VARCHAR(50) NOT NULL,
    `mac_addr` VARCHAR(50) NOT NULL,
    `capacity` DECIMAL(10,2) NOT NULL,
    `max_power` DECIMAL(10,2) NOT NULL,
    `max_current` DECIMAL(10,2) NOT NULL,
    `max_voltage` DECIMAL(10,2) NOT NULL,
    `recognized_vehicle` VARCHAR(100) COMMENT '车型识别结果',
    `predict_status` INT DEFAULT 0 COMMENT '预测状态，见代码',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_local_id` (`local_id`),
    KEY `idx_charger_sn` (`charger_sn`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `ChargingRecord` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `local_id` VARCHAR(50) NOT NULL COMMENT '桩本地订单ID',
    `charger_sn` VARCHAR(50) NOT NULL,
    `soc` DECIMAL(5,2) NOT NULL,
    `curr_output` FLOAT NOT NULL COMMENT '实际电流',
    `vol_output` FLOAT NOT NULL COMMENT '实际电压',
    `curr_demand` FLOAT NOT NULL COMMENT '需求电流',
    `max_power` FLOAT NOT NULL COMMENT '最大功率',
    `consumed_energy` FLOAT NOT NULL COMMENT '累计能耗',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `report_at` DATETIME NOT NULL COMMENT '上报时间',
    PRIMARY KEY (`id`),
    KEY `idx_local_id` (`local_id`),
    KEY `idx_charger_sn` (`charger_sn`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `PileTask` (
    `id` VARCHAR(50) NOT NULL COMMENT '桩功率分配策略记录id',
    `site_no` VARCHAR(50) NOT NULL,
    `generate_time` DATETIME NOT NULL COMMENT '调度计划生成的时刻',
    `pile_power_list` VARCHAR(2000) NOT NULL COMMENT '桩的功率分配详细情况',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_site_no` (`site_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `EnergyStorageTask` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '储能调度记录id',
    `site_no` VARCHAR(50) NOT NULL,
    `es_sn` VARCHAR(50) NULL COMMENT '储能电池编号',
    `scheduling_time` INT NOT NULL COMMENT '调度时间',
    `es_scheduling_strategy` JSON NULL COMMENT '储能调度策略',
    `scheduling_time_interval` INT NOT NULL COMMENT '调度时间间隔',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_site_no` (`site_no`),
    KEY `idx_es_sn` (`es_sn`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `PVTask` (
    `id` VARCHAR(50) NOT NULL COMMENT '光伏控制记录id',
    `site_no` VARCHAR(50) NOT NULL,
    `generate_time` DATETIME NOT NULL COMMENT '调度计划生成的时刻',
    `generate_time_power` DECIMAL(10,2) NOT NULL COMMENT '计划生成时间点功率',
    `time_list` VARCHAR(2000) NOT NULL,
    `power_list` VARCHAR(2000) NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_site_no` (`site_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `PVCurvePrediction` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `site_no` VARCHAR(50) NOT NULL,
    `curve_start_time` DATETIME NOT NULL COMMENT '曲线开始时刻',
    `curve_end_time` DATETIME NOT NULL COMMENT '曲线结束时刻',
    `time_list` VARCHAR(2000) NOT NULL,
    `power_list` VARCHAR(2000) NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_site_no` (`site_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `ChargerNonSuppressPowerPrediction` (
    `local_id` VARCHAR(50) NOT NULL COMMENT '桩本地订单ID',
    `curve_start_time` DATETIME NOT NULL COMMENT '曲线开始时刻',
    `curve_end_time` DATETIME NOT NULL COMMENT '曲线结束时刻',
    `time_list` VARCHAR(2000) NOT NULL,
    `power_list` VARCHAR(2000) NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`local_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `SiteDemandPrediction` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `site_no` VARCHAR(50) NOT NULL,
    `curve_start_time` DATETIME NOT NULL COMMENT '曲线开始时刻',
    `curve_end_time` DATETIME NOT NULL COMMENT '曲线结束时刻',
    `time_list` VARCHAR(2000) NOT NULL,
    `power_list` VARCHAR(2000) NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_site_no` (`site_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `SiteLongTermLoadPrediction` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `site_no` VARCHAR(50) NOT NULL,
    `curve_start_time` DATETIME NOT NULL COMMENT '曲线开始时刻',
    `curve_end_time` DATETIME NOT NULL COMMENT '曲线结束时刻',
    `time_list` VARCHAR(2000) NOT NULL,
    `power_list` VARCHAR(2000) NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_site_no` (`site_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `HybridLoadPrediction` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `site_no` VARCHAR(50) NOT NULL,
    `curve_start_time` DATETIME NOT NULL COMMENT '曲线开始时刻',
    `curve_end_time` DATETIME NOT NULL COMMENT '曲线结束时刻',
    `time_list` VARCHAR(2000) NOT NULL,
    `power_list` VARCHAR(2000) NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_site_no` (`site_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `PVAndMeterRealtimeData` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `site_no` VARCHAR(50) NOT NULL,
    `ts` BIGINT NOT NULL COMMENT '采样点时间毫秒时间戳',
    `pv_power` INT NOT NULL COMMENT '当前光伏发电功率，单位 kW',
    `pv_status` VARCHAR(20) NOT NULL COMMENT '光伏状态：discharge/non-discharge',
    `meter_value` INT NOT NULL COMMENT '市电电表输入功率',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_site_no` (`site_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `PileRealtimeData` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `pile_sn` VARCHAR(50) NOT NULL COMMENT '桩号',
    `connector` INT NOT NULL COMMENT '枪号',
    `site_no` VARCHAR(50) NOT NULL COMMENT '场站编号',
    `charger_status` VARCHAR(20) NOT NULL COMMENT '枪状态',
    `charger_power` INT NOT NULL COMMENT '枪实际功率kw',
    `ocpp_limit` INT NOT NULL COMMENT '枪的功率限制（上限）kw',
    `sample_time` DATETIME NOT NULL COMMENT '采样时间点',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_site_no` (`site_no`),
    KEY `idx_pile_sn` (`pile_sn`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `ESRealtimeData` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `site_no` VARCHAR(50) NOT NULL,
    `ts` BIGINT NOT NULL COMMENT '采样点时间毫秒时间戳',
    `es_sn` VARCHAR(50) NOT NULL COMMENT '储能设备序列号',
    `es_soc` INT NOT NULL COMMENT '储能电池状态（SOC）',
    `es_power` INT NOT NULL COMMENT '储能电池功率，单位 kW',
    `rated_cap` DECIMAL(10,2) NOT NULL COMMENT '额定容量 单位kwh',
    `real_cap` DECIMAL(10,2) NOT NULL COMMENT '实时容量 单位kwh',
    `es_max_soc` INT NOT NULL COMMENT '最大SOC',
    `es_min_soc` INT NOT NULL COMMENT '最小SOC',
    `status` VARCHAR(20) NOT NULL COMMENT '状态：discharge/charging/standby',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_site_no` (`site_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `ChargerRealtimeData` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `site_no` VARCHAR(50) NOT NULL,
    `ts` BIGINT NOT NULL COMMENT '采样点时间毫秒时间戳',
    `pile_sn` VARCHAR(50) NOT NULL COMMENT '充电桩序列号',
    `connector` INT NOT NULL COMMENT '充电枪接口编号',
    `status` VARCHAR(20) NOT NULL COMMENT '充电枪状态',
    `power` INT NOT NULL COMMENT '充电枪功率',
    `ocpp_limit` INT NOT NULL COMMENT '最大功率',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_site_no` (`site_no`),
    KEY `idx_pile_sn` (`pile_sn`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `SiteSellPriceData` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `site_no` VARCHAR(50) NOT NULL,
    `version` VARCHAR(50) NOT NULL COMMENT '电价版本号，格式：YYYYMMDD_HHMMSS',
    `start_time` DATETIME NOT NULL COMMENT '电价生效开始时间',
    `end_time` DATETIME NOT NULL COMMENT '电价生效结束时间',
    `price` DECIMAL(10,4) NOT NULL COMMENT '电价',
    `unit` VARCHAR(20) NOT NULL COMMENT '电价单位',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_site_no` (`site_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `SiteDynamicPriceData` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `site_no` VARCHAR(50) NULL,
    `region` VARCHAR(50) NOT NULL,
    `date` VARCHAR(50) NOT NULL COMMENT '电价日期，格式：YYYYMMDD',
    `start_time` DATETIME NOT NULL COMMENT '电价生效开始时间',
    `end_time` DATETIME NOT NULL COMMENT '电价生效结束时间',
    `price` DECIMAL(10,4) NOT NULL COMMENT '电价',
    `unit` VARCHAR(20) NOT NULL COMMENT '电价单位',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_site_no` (`site_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `SiteDemandData` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `site_no` VARCHAR(50) NOT NULL,
    `version` VARCHAR(50) NOT NULL COMMENT '电价版本号，格式：YYYYMMDD_HHMMSS',
    `start_time` DATETIME NOT NULL COMMENT '生效开始时间',
    `end_time` DATETIME NOT NULL COMMENT '生效结束时间',
    `price` DECIMAL(10,4) NOT NULL COMMENT '电价',
    `unit` VARCHAR(20) NOT NULL COMMENT '电价单位',
    `total_demand_target` INT NOT NULL COMMENT '总需求目标',
    `target_demand_warning_ratio` DECIMAL(5,2) NOT NULL COMMENT '目标需求警告比例',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_site_no` (`site_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `SiteFixedAndTimeOfUsePriceData` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `site_no` VARCHAR(50) NOT NULL,
    `belong_year` INT NOT NULL COMMENT '所属年份',
    `belong_month` INT NOT NULL COMMENT '所属月份',
    `start_time` VARCHAR(5) NOT NULL COMMENT '时段划分开始时间，HH:mm格式',
    `end_time` VARCHAR(5) NOT NULL COMMENT '时段划分结束时间，HH:mm格式',
    `time_type` INT NOT NULL COMMENT '时段类型 1:尖, 2:峰, 3:平, 4:谷',
    `price` DECIMAL(10,4) NOT NULL COMMENT '买电单价',
    `unit` VARCHAR(20) NOT NULL COMMENT '电价单位 dollar:美元, rmb:人民币',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_site_no` (`site_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
