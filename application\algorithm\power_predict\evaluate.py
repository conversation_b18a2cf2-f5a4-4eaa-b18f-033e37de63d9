import matplotlib.pyplot as plt
import numpy as np
from scipy.integrate import trapz
import os
from calculate_power_curve import calcPowerCurve
import argparse
from plotSoc import getPowerSocCurve
from datetime import datetime
import json
import re
import pandas as pd


def draw_curves(jsonname, actual_curve, fit_curve):
    soc1, power1 = zip(*actual_curve)
    soc2, power2 = zip(*fit_curve)
    plt.figure(figsize=(8, 5))
    plt.plot(soc1, power1, label='actual', marker='o', linestyle='-')
    plt.plot(soc2, power2, label='predict', marker='x', linestyle='--')
    plt.xlabel('soc')
    plt.ylabel('power')
    plt.title('power-soc curve')
    plt.legend()
    plt.grid(True)
    png_name = jsonname[:-4] + "png"
    plt.savefig(os.path.join("res", png_name))
    plt.close()


def get_start_up_voltage_power(json_data):
    fmt = " %Y-%m-%d %H:%M:%S.%f"
    voltage_at_startup = 0
    startup_power = 0
    max_target_cur = 0
    descending_flag = False  # indicate it will be descending from the point at 60th second
    for i, item in enumerate(json_data):
        if isinstance(item, dict):
            for k, v in item.items():
                # print(f"-{k}:{v}")
                if k == "MaximumCurrent":
                    maxim_current = v
                if k == "MaximumVoltage":
                    maxim_voltage = v
        elif isinstance(item, list):
            cnt = 0
            for list_item in item:
                # pdb.set_trace()
                parts = list_item.split(',')
                time_str = parts[7]
                if '.' not in time_str:
                    time_str += ".000"
                cnt = cnt + 1
                if cnt == 1:
                    continue  # it is the title
                elif cnt == 2:
                    initial_time = datetime.strptime(time_str, fmt)
                    continue
                target_cur = float(parts[0])
                actual_cur = float(parts[1])
                out_vol = float(parts[3])
                actual_power = actual_cur * out_vol
                time = datetime.strptime(time_str, fmt)
                deta = (time - initial_time).total_seconds()
                if target_cur > max_target_cur:
                    max_target_cur = target_cur
                if deta >= 59 and deta <= 61:
                    startup_power = actual_power
                    voltage_at_startup = out_vol
                    if target_cur < max_target_cur:
                        descending_flag = True

    if voltage_at_startup == 0:
        print(f"voltage_at_startup is invalid")
    return voltage_at_startup, startup_power, descending_flag


def load_data(row, vehicle):
    jsonname = row['jsonname']
    sSOC = row['sSOC']
    gunlimit = row['gunlimit1']
    ratedpower = row['ratedpower']
    print(f"jsonname is {jsonname}")
    # now to load json as power-soc curve
    try:
        with open(os.path.join(f'{vehicle}', jsonname)) as file:
            jsondata = json.load(file)
    except FileNotFoundError:
        print(f"{jsonname} not exist")
        return dict()  # return a empty dict

    # to calculate the plimit,use the voltage at 60th second to calculate the maxpower
    startup_voltage, startup_power, descending_flag = get_start_up_voltage_power(jsondata)
    if startup_voltage == 0:
        print(f"{jsonname} startup_voltage invalid")
        return dict()
        # maxVoltage = jsondata[0]["MaximumVoltage"] #jsondata[0] is a dictionary
    pmax_by_curr = startup_voltage * gunlimit / 1000  # unit is kw
    startup_power = startup_power / 1000  # now unit is kw
    plimit = min(pmax_by_curr, ratedpower)
    # pdb.set_trace()
    soc_list, power_list = getPowerSocCurve(jsondata)

    json_info = {
        "sSOC": sSOC,
        "startup_power": startup_power,
        "descending_flag": descending_flag,
        "soc_list": soc_list,
        "power_list": power_list,
        "plimit": plimit,
        "jsonname": jsonname
    }
    return json_info


def evaluate(fit_curve, json_dict):
    error = 0
    actual_curve = list(zip(json_dict["soc_list"], json_dict["power_list"]))

    draw_curves(json_dict["jsonname"], actual_curve, fit_curve)
    soc_list = json_dict["soc_list"]
    power_list = json_dict["power_list"]
    actual_curve = list(zip(soc_list, power_list))
    error += curve_diff(fit_curve, actual_curve)
    print(f"error is {error}")
    pass


def fit(sSOC, startup_power, descending_flag, plimit, power_table, coeff_a, coeff_b1, coeff_b2, coeff_c):
    with open(power_table, "r") as file:
        f = json.load(file)
    power_series = f['powertable']
    dict_power = {index: value for index, value in enumerate(power_series)}
    error = 0
    control_points, fit_curve = calcPowerCurve(sSOC, startup_power, descending_flag, plimit, coeff_a, coeff_b1,
                                               coeff_b2, coeff_c, dict_power)
    # fit_curve is a list,whose element is tuple(soc,power)

    return fit_curve


def curve_diff(curve1, curve2):
    dict1 = {p[0]: p[1] for p in curve1}
    dict2 = {p[0]: p[1] for p in curve2}

    # print(f"dict1 keys is {dict1.keys()}")
    # print(f"dict2 keys is {dict2.keys()}")
    common_x = set(dict1.keys()).intersection(dict2.keys())
    common_x = sorted(common_x)  # very important
    if not common_x:
        return 0.0

    common_x_array = np.array(list(common_x))  # convert to np array
    y1 = np.array([dict1[x] for x in common_x])
    y2 = np.array([dict2[x] for x in common_x])
    # print(f"common_x is {common_x},y1 is {y1},y2 is {y2}")
    diff = np.abs(y1 - y2)
    # pdb.set_trace()
    integral_diff = trapz(diff, common_x_array)
    print(f"integral_diff is {integral_diff}")
    return integral_diff


def detect_descending(tarcur_list):    #  判断曲线在1min时是否出现了下降趋势
    # print(tarcur_list)
    max_tarcur = max(tarcur_list)
    if tarcur_list[-1] - max_tarcur >= 0:
        return False
    else:
        return True


def find_power_limit(start_voltage, rated_power, gun_limit):
    pmax_by_curr = start_voltage * gun_limit / 1000  # unit is kw
    plimit = min(pmax_by_curr, rated_power)

    return plimit


def convert_to_time_power(vehicle_label, soc_power):
    matches = re.findall(r'_([\d.]+)kWh', vehicle_label)
    numbers = [float(m) for m in matches]
    battery = np.mean(numbers)
    time_power = []
    time = 0
    for value in soc_power:
        time += battery * 0.001 * 3600 / value[1]
        time_power.append((time, value[1]))

    return time_power


def load_json_for_test(jsonpath):
    with open(jsonpath) as file:
        json_data = json.load(file)

        fmt = " %Y-%m-%d %H:%M:%S.%f"
        voltage_at_startup = 0
        startup_power = 0
        max_target_cur = 0
        descending_flag = False  # indicate it will be descending from the point at 60th second
        tarcur_list = []
        for i, item in enumerate(json_data):
            if isinstance(item, dict):
                for k, v in item.items():
                    # print(f"-{k}:{v}")
                    if k == "MaximumCurrent":
                        maxim_current = v
                    if k == "MaximumVoltage":
                        maxim_voltage = v
            elif isinstance(item, list):
                cnt = 0
                for list_item in item:
                    # pdb.set_trace()
                    parts = list_item.split(',')
                    time_str = parts[7]
                    if '.' not in time_str:
                        time_str += ".000"
                    cnt = cnt + 1
                    if cnt == 1:
                        continue  # it is the title
                    elif cnt == 2:
                        initial_time = datetime.strptime(time_str, fmt)
                        continue
                    target_cur = float(parts[0])
                    actual_cur = float(parts[1])
                    out_vol = float(parts[3])
                    actual_power = actual_cur * out_vol
                    time = datetime.strptime(time_str, fmt)
                    deta = (time - initial_time).total_seconds()
                    if deta <= 61:
                        tarcur_list.append(target_cur)
                    if deta >= 59 and deta <= 61:
                        startup_power = actual_power
                        voltage_at_startup = out_vol

        if voltage_at_startup == 0:
            print(f"voltage_at_startup is invalid")
        return voltage_at_startup, startup_power/1000, tarcur_list


def predict(vehicle_label, sSOC, power_1min, voltage_1min, current_list_1min, rated_power, gun_limit):
    '''
    :param vehicle_label: 车型识别结果
    :param sSOC: 起始soc
    :param power_1min: 充电1min时刻（调用算法时刻）功率，单位kW
    :param voltage_1min: 充电1min时刻（调用算法时刻）输出电压，单位V
    :param current_list_1min: 从充电开始到1min内目标电流list，单位A
    :param rated_power: 桩额定功率，单位kW
    :param gunlimit: 枪限制电流，单位A
    :return:
    '''
    with open("model/vehicle_model_match.json") as file:
        match_dict = json.loads(file.read())

    if vehicle_label in match_dict:
        model_name = match_dict[vehicle_label]
        power_table_path = os.path.join("model", f"{model_name}_power_table.json")
        model_path = os.path.join("model", f"{model_name}.json")
    else:
        print("Do not support this vehicle")
        return None

    with open(model_path, "r") as f:
        json_para = json.load(f)
        coeff_a = json_para["coeff_a"]
        coeff_b1 = json_para["coeff_b1"]
        coeff_b2 = json_para["coeff_b2"]
        coeff_c = json_para["coeff_c"]
    descending_flag = detect_descending(current_list_1min)
    plimit = find_power_limit(voltage_1min, rated_power, gun_limit)
    fitted_curve = fit(sSOC, power_1min, descending_flag, plimit, power_table_path, coeff_a, coeff_b1, coeff_b2,
                           coeff_c)
    time_power = convert_to_time_power(vehicle_label, fitted_curve)
    return time_power


if __name__ == "__main__":
    # vehicle_label = "Audi_Q4@e-tron_76.6kWh,Cupra_Born_77kWh,Skoda_Enyaq@80_77kWh,Volkswagen_ID.4_77kWh,Volkswagen_ID.5_77kWh,Volkswagen_ID.BUZZ_77kWh"
    # sSOC = 46
    # # power_1min = 100
    # # voltage_1min = 360
    # # current_list_1min = []
    # voltage_1min, power_1min, current_list_1min = load_json_for_test(os.path.join("volkswagen_test", "Audi_Q4@e-tron_76.6kWh,Cupra_Born_77kWh,Skoda_Enyaq@80_77kWh,Volkswagen_ID.4_77kWh,Volkswagen_ID.5_77kWh,Volkswagen_ID.BUZZ_77kWh_007dfa06b174_553.json"))
    # charger_rated_power = 120
    # gunlimit = 200
    # predict(vehicle_label, sSOC, power_1min, voltage_1min, current_list_1min, charger_rated_power, gunlimit)

    parser = argparse.ArgumentParser(description='Power predict')
    parser.add_argument('--vehicle', required=True, help='vehicle for training, same with the folder name')
    parser.add_argument('--model', required=True, help='model for power prediction')
    parser.add_argument('--file_list', required=True, help='csv file for predicting')
    parser.add_argument('--power_table', required=True, help='power table of the training vehicle')
    args = parser.parse_args()

    with open(args.model, "r") as f:
        json_para = json.load(f)
        coeff_a = json_para["coeff_a"]
        coeff_b1 = json_para["coeff_b1"]
        coeff_b2 = json_para["coeff_b2"]
        coeff_c = json_para["coeff_c"]

    df = pd.read_csv(args.file_list)
    for i in range(len(df)):
        row = df.iloc[i]
        json_dict = load_data(row, args.vehicle)
        if json_dict:
            sSOC = json_dict["sSOC"]
            plimit = json_dict["plimit"]
            startup_power = json_dict["startup_power"]
            descending_flag = json_dict["descending_flag"]
            jsonname = json_dict["jsonname"]

            print(f"jsonname is {jsonname}")
            # print("descending_flag", descending_flag)
            # voltage_1min, power_1min, current_list_1min = load_json_for_test(os.path.join("tesla", jsonname))
            # print(detect_descending(current_list_1min))
            # descending_flag_2 = detect_descending(current_list_1min)
            fitted_curve = fit(sSOC, startup_power, descending_flag, plimit, args.power_table, coeff_a, coeff_b1, coeff_b2, coeff_c)
            # fitted_curve_2 = fit(sSOC, startup_power, descending_flag_2, plimit, args.power_table, coeff_a, coeff_b1,
            #                    coeff_b2, coeff_c)
            #print("fitted_curve", fitted_curve)
            # evaluate(fitted_curve, json_dict)
            # evaluate(fitted_curve_2, json_dict)

            # time_power = convert_to_time_power("Volkswagen_ID.BUZZ_77kWh", fitted_curve)
            # #print("time_power", time_power)

