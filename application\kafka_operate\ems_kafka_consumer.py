import json
import yaml
from confluent_kafka import Consumer, KafkaError

from application.kafka_operate.ems_kafka_message_handler import EMSMessageHandler
from application.utils.logger import setup_logger

logger = setup_logger("ems_kafka_consumer", direction="kafka")


class EMSKafkaConsumer:
    def __init__(self):
        """Initialize Kafka Manager"""
        try:
            # Read configuration file
            with open('application/settings/config.yaml', 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # Get kafka configuration
            kafka_config = config['artifact_instances']['ems_kafka_consumer']
            
            # Configure consumer
            consumer_config = {
                'bootstrap.servers': kafka_config['bootstrap_servers'],
                'group.id': kafka_config['group_id'],
                'enable.auto.commit': False,
                'session.timeout.ms': 6000,
                'max.partition.fetch.bytes': 1000000000
            }
            
            # Initialize consumer
            self.kafka_consumer = Consumer(consumer_config)
            # Subscribe to topics
            self.kafka_consumer.subscribe(kafka_config['topics'])
            
            self.messageHandler = EMSMessageHandler()

            logger.info("Kafka Consumer initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Kafka Consumer: {str(e)}")
            raise

    def start_consumer(self):
        """Start Kafka consumer to listen for real-time data"""
        max_retries = 3
        retry_count = 0
        while retry_count < max_retries:
            try:
                while True:
                    try:
                        msg = self.kafka_consumer.poll(1.0)
                        if msg is None:
                            continue
                        if msg.error():
                            # 检查是否是分区结束错误
                            if msg.error().code() == -191:  # PARTITION_EOF 错误码
                                continue
                            logger.error(f"Kafka error: {msg.error()}")
                            continue
                            
                        data = json.loads(msg.value().decode('utf-8'))
                        self.messageHandler.process_message(data)

                        self.kafka_consumer.commit(msg)
                    except Exception as e:
                        logger.error(f"Error processing real-time data: {str(e)}")
                        retry_count += 1
                        continue
            except Exception as e:
                logger.error(f"Failed to start Kafka consumer: {str(e)}")
                raise

    def stop_consumer(self):
        """Stop the consumer"""
        try:
            if hasattr(self, 'kafka_consumer'):
                self.kafka_consumer.close()
            logger.info("Kafka consumer stopped")
        except Exception as e:
            logger.error(f"Error stopping Kafka consumer: {str(e)}")

