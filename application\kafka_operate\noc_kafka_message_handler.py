from datetime import datetime

from application.algorithm_schedule.noc_algorithm_schedule import NocAlgorithmSchedule
from application.algorithm_schedule.status_enumeration import ChargePileStatus, EVPredictionStatus
from application.db_operate.db_operate import DBOperate
from application.utils.logger import setup_logger

logger = setup_logger("noc_kafka_consumer", direction="kafka")


class NOCMessageHandler:
    def __init__(self):
        self.algorithm_schedule = NocAlgorithmSchedule()
        self.db_operate = DBOperate()

    def process_message(self, message_data):
        """
        Main message processing function, routes to corresponding handler based on bomCode and action
        """
        try:
            bom_code = message_data.get('bomCode')
            action = message_data.get('action')
            logger.info(f"Received message, bomCode: {bom_code}, action: {action}")

            # Message processing routing table - using (bomCode, action) tuple as key
            message_handlers = {
                ('B0001', 'UpMessage'): self.handle_charging_data,  # 适配DC fast桩 充电数据消息
                ('B0805', None): self.handle_charging_data,  # 适配N+1桩 充电数据消息
                ('B0002', 'UpMessage'): self.handle_pile_status,  # 适配DC fast桩 充电数据消息
                ('B0609', None): self.handle_pile_status,  # 适配N+1桩 枪接口状态消息
                (None, 'ccuStartCompletionFrameEvent'): self.handle_ccu_start_completion,
                # Can continue adding other combinations...
            }

            handler = message_handlers.get((bom_code, action))
            if handler:
                handler(message_data)
            else:
                logger.warning(f"No corresponding message handler found - bomCode: {bom_code}, action: {action}")

        except Exception as e:
            logger.error(f"Error processing message: {str(e)}", exc_info=True)

    @staticmethod
    def convert_to_record_data(message_data):
        """Convert B0001 message to record_data format"""
        try:
            payload = message_data.get('payload', {})
            current_time = datetime.now()
            # May involve unit conversion
            record_data = {
                'curr_output': payload.get('evChargingCurrent', 0) / 10,  # 10A -> A
                'vol_output': payload.get('evChargingVoltage', 0) / 10,  # 10V -> V
                'curr_demand': payload.get('evDemandCurrent', 0) / 10,  # 10A -> A
                'vol_demand': payload.get('evDemandVoltage', 0) / 10,  # 10V -> V
                'soc': int(payload.get('soc', 0)),  # %
                'consumed_energy': (payload.get('accumulatedEnergy', 0) / 10) or  # 10kWh -> kWh
                payload.get('currentChargingEnergySentByTCU', 0),
                'max_power': (payload.get('evseMaxPower') or payload.get('evseMaximumPower', 0)) / 10,  # 10kWh -> kWh
                'report_at': datetime.fromtimestamp(message_data.get('time', 0) / 1000),  # Convert millisecond timestamp to datetime
                'created_at': current_time
            }

            # Log record
            logger.info(f"Converted record data:")
            logger.info(f"Output current: {record_data['curr_output']:.2f} A")
            logger.info(f"Output voltage: {record_data['vol_output']:.2f} V")
            logger.info(f"Demand current: {record_data['curr_demand']:.2f} A")
            logger.info(f"Demand voltage: {record_data['vol_demand']:.2f} V")
            logger.info(f"SOC: {record_data['soc']}%")
            logger.info(f"Consumed energy: {record_data['consumed_energy']:.2f} kWh")

            return record_data

        except Exception as e:
            logger.error(f"Error converting data: {str(e)}", exc_info=True)
            return None

    def handle_charging_data(self, message_data):
        """Handle B0001 charging data message"""
        try:
            pile_sn = message_data.get('sn')
            bom_index = message_data.get('bomIndex')
            msg_id = message_data.get('msgId')
            charger_sn = f"{pile_sn}_{bom_index}"
            logger.info(f"Processing charging data - SN: {pile_sn}")

            # Convert to record_data format
            record_data = self.convert_to_record_data(message_data)

            charger_session = self.db_operate.query_charger_session(charger_sn, ChargePileStatus.CHARGING_STATES)
            if not charger_session:
                logger.warning(f"Charger: {charger_sn} has no active charging session, skipping power and SOC prediction")
                return
            else:
                record_data['local_id'] = charger_session.local_id
                record_data["charger_sn"] = charger_sn
                record_data["mac_addr"] = charger_session.mac_addr
                record_data["pile_sn"] = pile_sn
                record_data["msg_id"] = msg_id
                record_data["bom_index"] = bom_index

            if record_data:
                logger.info("Data conversion successful")
                # Save charging data
                if not self.db_operate.create_charging_record(record_data):
                    logger.error(f"Failed to create charging record for charger_sn: {charger_sn} and mac_addr: " 
                                 f"{charger_session.mac_addr}")
                    return

                local_id = record_data.get('local_id')

                # Get prediction status
                ev_model = self.db_operate.get_ev_model_by_local_id(local_id)
                if ev_model is None:
                    logger.error(f"Failed to get prediction status for local_id: {local_id}")
                    return

                if ev_model.predict_status == EVPredictionStatus.WAITING_TWICE_PREDICT:
                    # Need to perform vehicle model secondary recognition and forced prediction
                    logger.info(f"Performing twice prediction for local_id: {local_id}")
                    # Get all necessary fields from ev_model
                    record_data.update({
                        "capacity": ev_model.capacity,
                        "max_voltage": ev_model.max_voltage,
                        "max_current": ev_model.max_current,
                        "max_power": ev_model.max_power
                    })
                    recognize_vehicle_success = self.algorithm_schedule.recognize_vehicle(record_data)

                    # If vehicle recognition is complete, update status to no force prediction needed
                    if recognize_vehicle_success:
                        self.db_operate.update_ev_model_prediction_status_by_local_id(
                            local_id, EVPredictionStatus.NO_FORCE_PREDICTED)
                        logger.info(f"Updated prediction status to NO_FORCE_PREDICTED for local_id: {local_id}")

                else:
                    # Perform normal prediction
                    logger.info(f"Performing normal prediction for local_id: {local_id}")

        except Exception as e:
            logger.error(f"Error processing charging data: {str(e)}", exc_info=True)
            return None

    def handle_pile_status(self, message_data):
        """Handle charging pile status data (bomCode: B0002)"""
        if (not message_data.get('bomIndex') or not message_data.get('payload') or
                message_data.get('payload').get('chargePileStatus') not in (ChargePileStatus.VEHICLE_CONNECTED,
                                                                            ChargePileStatus.FORMAL_CHARGING,
                                                                            ChargePileStatus.CHARGING_COMPLETED)):
            logger.info(f"B0002 or B0609 message data incomplete or non-processing status - bomIndex: "
                        f"{message_data.get('bomIndex')}, payload: {message_data.get('payload')}, skipping processing")
            return

        charger_sn = f"{message_data.get('sn')}_{message_data.get('bomIndex')}"
        payload = message_data.get('payload', {})

        # Handle charging pile status specific logic...
        self.algorithm_schedule.handle_charger_event(charger_sn, payload.get('chargePileStatus'))

    def handle_ccu_start_completion(self, message_data):
        """Handle CCU startup completion event, extract only key fields"""
        try:
            pile_sn = message_data.get('sn')
            payload = message_data.get('payload', {})
            msg_id = message_data.get('msgId', '')
            bom_index = message_data.get('bomIndex', '')
            mac_addr = payload.get('evccMAC').replace(':', '')
            # Extract only key fields
            record_data = {
                'pile_sn': pile_sn,
                'msg_id': msg_id,
                'bom_index': bom_index,
                'local_id': payload.get('localId'),
                'mac_addr': mac_addr,
                'max_current': float(payload.get('evMaxChargingCurrent', 0)),
                'max_voltage': float(payload.get('evMaxChargingVoltage', 0)),
                'max_power': float(payload.get('evMaxChargingPower', 0)) if payload.get('evMaxChargingPower', 0) else 0,
                'capacity': float(payload.get('evPowerBatteryNominalEnergy', 0)),
                'full_soc': payload.get('fullSOC'),
                'charging_connection_logo': payload.get('chargingConnectionLogo')  # New field
            }

            # Log key information
            logger.info(f"Processing CCU startup completion event")
            logger.info(f"Order number: {record_data['local_id']}")
            logger.info(f"Maximum charging current: {record_data['max_current']} A")
            logger.info(f"Maximum charging voltage: {record_data['max_voltage']} V")
            logger.info(f"Maximum charging power: {record_data['max_power']} kW")
            logger.info(f"Rated capacity: {record_data['capacity']} kWh")
            logger.info(f"Target SOC: {record_data['full_soc']}%")
            logger.info(f"Gun SN: {record_data['charging_connection_logo']}")

            charger_sn = f'{pile_sn}_{record_data.get('charging_connection_logo')}'
            record_data["charger_sn"] = charger_sn
            record_data["mac_addr"] = record_data['mac_addr']

            local_id = record_data.get('local_id')
            success = self.db_operate.create_charger_session_if_not_exist(
                local_id,
                mac_addr,
                charger_sn,
                ChargePileStatus.VEHICLE_CONNECTED,
                datetime.fromtimestamp(message_data.get('time', 0) / 1000),  # Convert millisecond timestamp to datetime
            )
            if not success:
                logger.warning(f"Charger: {charger_sn} failed to get or create charging session")
                return

            self.algorithm_schedule.recognize_vehicle(record_data, True)

        except Exception as e:
            logger.error(f"Error processing CCU startup completion event: {str(e)}", exc_info=True)
            return None
