import json
from datetime import datetime, timedelta
from typing import List, Tuple, Optional
import pytz

from application.db_operate.db_operate import DBOperate
from application.db_operate.models import SiteDemandPredictionDB, SiteLongTermLoadPredictionDB, HybridLoadPredictionDB
from application.utils.logger import setup_logger

logger = setup_logger("fused_load_forecast", direction="algorithm_schedule")

# 配置选项：是否使用时区信息
USE_TIMEZONE = True  # 设为False可以简化时间格式

# 配置选项：时间对齐容差（秒）
TIME_ALIGNMENT_TOLERANCE = 450  # 7.5分钟，超过此时间差异的点不进行融合


def fused_load_forecast(site_no, data):
    """
    长短期融合负载预测算法调度入口
    :param site_no: 场站编号
    :param data: 相关输入数据
    :return: 预测结果 (96个点的功率数据列表)
    
    融合策略：
    1. 从SiteDemandPrediction表获取短期预测数据
    2. 从SiteLongTermLoadPrediction表获取长期预测数据
    3. 将数据转换为15分钟间隔的96个点
    4. 对同一时间点取两者的最大值
    5. 将结果存入HybridLoadPrediction表
    """
    
    logger.info(f"[fused_load_forecast] 开始处理场站 {site_no} 的融合负载预测")
    
    try:
        db_operate = DBOperate()
        
        # 1. 获取短期预测数据
        short_term_data = _get_short_term_prediction(db_operate, site_no)
        logger.info(f"短期预测数据: {len(short_term_data) if short_term_data else 0} 个点")
        
        # 2. 获取长期预测数据
        long_term_data = _get_long_term_prediction(db_operate, site_no)
        logger.info(f"长期预测数据: {len(long_term_data) if long_term_data else 0} 个点")
        
        # 3. 数据融合
        fused_data = _fuse_predictions(short_term_data, long_term_data)
        logger.info(f"融合后数据: {len(fused_data)} 个点")
        
        # 4. 保存融合结果
        success = _save_fused_result(db_operate, site_no, fused_data)
        
        if success:
            logger.info(f"[fused_load_forecast] 场站 {site_no} 融合负载预测完成")
            return fused_data
        else:
            logger.error(f"[fused_load_forecast] 场站 {site_no} 融合结果保存失败")
            return None
            
    except Exception as e:
        logger.error(f"[fused_load_forecast] 场站 {site_no} 融合负载预测异常: {e}")
        return None


def _get_short_term_prediction(db_operate: DBOperate, site_no: str) -> Optional[List[Tuple[datetime, float]]]:
    """
    获取短期预测数据
    :param db_operate: 数据库操作对象
    :param site_no: 场站编号
    :return: [(时间, 功率)] 列表，如果没有数据返回None
    """
    try:
        with db_operate.get_db() as db:
            # 查询最新的短期预测记录
            record = db.query(SiteDemandPredictionDB).filter(
                SiteDemandPredictionDB.site_no == site_no
            ).order_by(SiteDemandPredictionDB.created_at.desc()).first()
            
            if record is None:
                logger.warning(f"场站 {site_no} 没有找到短期预测数据")
                return None
            
            # 检查预测时间是否符合要求（至少1-2小时，超过15分钟）
            start_time = record.curve_start_time # 2025-06-25 08:06:00
            end_time = record.curve_end_time # 2025-06-25 11:04:12
            duration = end_time - start_time # 2小时58分12秒
            if duration.total_seconds() < 15 * 60:  # 15分钟 = 900秒
                logger.warning(f"场站 {site_no} 短期预测时间段太短: {duration}")
                return None
            
            # 解析时间和功率数据，使用带起始时间的解析函数   datetime.fromisoformat: 将字符串转换为datetime对象
            time_list = _parse_time_list_with_start_time(str(record.time_list), datetime.fromisoformat(str(start_time)))
            power_list = json.loads(str(record.power_list))
            
            # 将datetime对象转换为字符串以兼容原有函数
            time_list_str = [dt.strftime('%Y-%m-%d %H:%M:%S') for dt in time_list] # datetime对象 → 字符串
            
            # 转换为15分钟间隔的数据
            return _convert_to_15min_intervals(time_list_str, power_list)
            
    except Exception as e:
        logger.error(f"获取场站 {site_no} 短期预测数据失败: {e}")
        return None


def _get_long_term_prediction(db_operate: DBOperate, site_no: str) -> Optional[List[Tuple[datetime, float]]]:
    """
    获取长期预测数据
    :param db_operate: 数据库操作对象
    :param site_no: 场站编号
    :return: [(时间, 功率)] 列表，如果没有数据返回None
    """
    try:
        with db_operate.get_db() as db:
            # 查询最新的长期预测记录
            record = db.query(SiteLongTermLoadPredictionDB).filter(
                SiteLongTermLoadPredictionDB.site_no == site_no
            ).order_by(SiteLongTermLoadPredictionDB.created_at.desc()).first()
            
            if record is None:
                logger.warning(f"场站 {site_no} 没有找到长期预测数据")
                return None
            
            # 解析时间和功率数据，使用带起始时间的解析函数
            start_time = record.curve_start_time
            # 确保start_time是datetime对象 - 强制转换为datetime
            start_time_dt = datetime.fromisoformat(str(start_time))
            time_list = _parse_time_list_with_start_time(str(record.time_list), start_time_dt)
            power_list = json.loads(str(record.power_list))
            
            # 将datetime对象转换为字符串以兼容原有函数
            time_list_str = [dt.strftime('%Y-%m-%d %H:%M:%S') for dt in time_list]
            
            # 长期预测应该是96个点，如果不够就补0
            return _ensure_96_points(time_list_str, power_list)
            
    except Exception as e:
        logger.error(f"获取场站 {site_no} 长期预测数据失败: {e}")
        return None


def _convert_to_15min_intervals(time_list: List[str], power_list: List[float]) -> List[Tuple[datetime, float]]:
    """
    将短期预测数据转换为15分钟间隔，使用简化的归属规则
    归属规则：
    - 分钟数 < 7.5：归到当前15分钟间隔（如8:06归到8:00-8:15）
    - 分钟数 >= 7.5：归到下一个15分钟间隔（如8:11归到8:15-8:30）
    
    :param time_list: 时间字符串列表
    :param power_list: 功率值列表
    :return: [(时间, 平均功率)] 列表，每个元素代表一个15分钟间隔
    """
    result = []
    
    # 将时间字符串转换为datetime对象
    time_power_pairs = []
    for i, time_str in enumerate(time_list):
        if i < len(power_list):
            dt = datetime.fromisoformat(time_str.replace('Z', '+00:00'))
            time_power_pairs.append((dt, power_list[i]))
    
    if not time_power_pairs:
        return result
    
    # 使用简化规则将数据归属到15分钟间隔
    interval_groups = {}  # {interval_start: [power_values]}
    
    for dt, power in time_power_pairs:
        # 获取分钟和秒
        minute = dt.minute
        second = dt.second
        
        # 计算在当前小时内的精确分钟数（包含秒的小数部分）
        exact_minute = minute + second / 60.0
        
        # 计算在当前15分钟周期内的分钟数
        minute_in_quarter = exact_minute % 15
        
        # 应用简化归属规则：< 7.5 分钟归前面，>= 7.5 分钟归后面
        if minute_in_quarter < 7.5:
            # 归到当前15分钟间隔
            interval_minute = (minute // 15) * 15
            interval_dt = dt.replace(minute=interval_minute, second=0, microsecond=0)
        else:
            # 归到下一个15分钟间隔
            next_interval_minute = ((minute // 15) + 1) * 15
            if next_interval_minute >= 60:
                # 跨小时处理
                interval_dt = dt.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
            else:
                interval_dt = dt.replace(minute=next_interval_minute, second=0, microsecond=0)
        
        # 分组数据
        if interval_dt not in interval_groups: # 新的15分钟间隔
            interval_groups[interval_dt] = []
        interval_groups[interval_dt].append(power) # 将功率值添加到对应的15分钟间隔中
        
        logger.debug(f"数据点 {dt.strftime('%H:%M:%S')} (精确分钟:{exact_minute:.1f}, "
                    f"quarter内分钟:{minute_in_quarter:.1f}) -> 间隔 {interval_dt.strftime('%H:%M')}")
    
    # 计算每个间隔的平均功率并排序
    for interval_start in sorted(interval_groups.keys()): # 遍历所有15分钟间隔
        powers = interval_groups[interval_start] # 获取该间隔的功率值列表
        avg_power = round(sum(powers) / len(powers)) # 计算平均功率
        result.append((interval_start, avg_power)) # 将平均功率添加到结果列表中
        logger.debug(f"间隔 {interval_start.strftime('%H:%M')}: {len(powers)}个数据点, 平均功率={avg_power}kW")
    
    logger.info(f"短期预测转换完成: {len(time_power_pairs)}个原始点 -> {len(result)}个15分钟间隔")
    
    return result


def _ensure_96_points(time_list: List[str], power_list: List[float]) -> List[Tuple[datetime, float]]:
    """
    确保长期预测数据有96个点，不够的补0
    :param time_list: 时间字符串列表
    :param power_list: 功率值列表
    :return: [(时间, 功率)] 列表，确保96个点
    """
    result = []
    
    # 解析现有数据
    for i, time_str in enumerate(time_list):
        if i < len(power_list):
            dt = datetime.fromisoformat(time_str.replace('Z', '+00:00'))
            result.append((dt, power_list[i]))
    
    # 如果数据不足96个点，用0补齐
    if len(result) < 96:
        if result:
            # 从最后一个时间点开始，每15分钟添加一个0值点
            last_time = result[-1][0]
            for i in range(len(result), 96):
                next_time = last_time + timedelta(minutes=15 * (i - len(result) + 1))
                result.append((next_time, 0.0))
        else:
            # 如果没有任何数据，从当前时间开始生成96个0值点
            start_time = datetime.now(pytz.UTC).replace(minute=0, second=0, microsecond=0)
            for i in range(96):
                time_point = start_time + timedelta(minutes=15 * i)
                result.append((time_point, 0.0))
    
    return result[:96]  # 确保只返回96个点


def _fuse_predictions(short_term_data: Optional[List[Tuple[datetime, float]]], 
                     long_term_data: Optional[List[Tuple[datetime, float]]]) -> List[Tuple[datetime, float]]:
    """
    融合短期和长期预测数据，支持时间对齐
    融合策略：
    1. 找到短期和长期预测的时间重叠区域
    2. 在重叠区域内对同一时间点取两者的最大值
    3. 在非重叠区域使用单一数据源
    4. 确保最终输出96个点
    :param short_term_data: 短期预测数据
    :param long_term_data: 长期预测数据
    :return: 融合后的数据，96个点
    """
    
    # 如果只有一个数据源，直接使用该数据源
    if short_term_data is None and long_term_data is None:
        # 两个都没有，生成96个0值点
        start_time = datetime.now(pytz.UTC).replace(minute=0, second=0, microsecond=0)
        return [(start_time + timedelta(minutes=15 * i), 0.0) for i in range(96)]
    
    if short_term_data is None:# 短期数据为空，使用长期数据, 如果长期数据为空，生成96个0值点
        return long_term_data[:96] if long_term_data else _extend_to_96_points([])
    
    if long_term_data is None:# 长期数据为空，使用短期数据
        # 短期数据需要扩展到96个点
        return _extend_to_96_points(short_term_data)
    
    # 两个数据源都有，进行时间对齐的融合
    logger.info("开始时间对齐融合...")
    
    # 获取短期和长期预测的时间范围
    short_start = short_term_data[0][0]
    short_end = short_term_data[-1][0]
    long_start = long_term_data[0][0]
    long_end = long_term_data[-1][0]
    
    logger.info(f"短期预测时间范围: {short_start} - {short_end}")
    logger.info(f"长期预测时间范围: {long_start} - {long_end}")
    
    # 创建时间到功率的映射
    short_term_dict = {dt: power for dt, power in short_term_data}
    long_term_dict = {dt: power for dt, power in long_term_data}
    
    # 确定融合结果的时间范围：以长期预测为基准（确保96个点）
    # 但需要考虑短期预测的有效范围
    base_times = [dt for dt, _ in long_term_data]
    
    result = []
    overlap_count = 0
    long_only_count = 0
    
    for dt in base_times:
        short_power = short_term_dict.get(dt, None)
        long_power = long_term_dict.get(dt, 0.0)
        
        if short_power is not None:
            # 在重叠区域，使用融合策略
            fused_power = max(short_power, long_power)
            overlap_count += 1
            logger.debug(f"时间点 {dt}: 短期={short_power}, 长期={long_power}, 融合={fused_power}")
        else:
            # 不在短期预测范围内，仅使用长期预测
            fused_power = long_power
            long_only_count += 1
            logger.debug(f"时间点 {dt}: 仅长期={long_power}")
        
        result.append((dt, fused_power))
    
    # 检查是否有短期预测点不在长期预测的时间点上
    # 理论上如果同一天短期预测点与长期预测点是对齐的，但若没有对齐则使用高级时间对齐算法。
    unmatched_short_points = []
    for short_dt, short_power in short_term_data:
        if short_dt not in long_term_dict:
            unmatched_short_points.append((short_dt, short_power))
    
    if unmatched_short_points:
        logger.warning(f"发现 {len(unmatched_short_points)} 个短期预测点不在长期预测时间点上")
        logger.info("启用高级时间对齐算法...")
        
        # 使用高级时间对齐算法重新处理
        aligned_result = _align_time_series(short_term_data, long_term_data)
        final_result = aligned_result[:96]
        logger.info(f"高级对齐完成，最终结果: {len(final_result)} 个点")
        return final_result
    
    logger.info(f"简单融合统计: 重叠点={overlap_count}, 仅长期点={long_only_count}")
    
    # 确保96个点
    final_result = result[:96]
    logger.info(f"简单融合完成，最终结果: {len(final_result)} 个点")
    
    return final_result


def _align_time_series(short_term_data: List[Tuple[datetime, float]], 
                      long_term_data: List[Tuple[datetime, float]]) -> List[Tuple[datetime, float]]:
    """
    时间序列对齐函数：处理短期预测时间点不在长期预测15分钟网格上的情况
    
    策略：
    1. 以长期预测的15分钟网格为基准
    2. 对于每个长期预测时间点，找到最近的短期预测点进行匹配
    3. 如果短期预测点距离超过7.5分钟，则认为无匹配
    
    :param short_term_data: 短期预测数据
    :param long_term_data: 长期预测数据（15分钟间隔）
    :return: 对齐后的融合数据
    """
    
    logger.info("执行高级时间对齐...")
    
    # 创建短期预测的时间索引（用于快速查找最近时间点）
    short_times = [dt for dt, _ in short_term_data]
    short_powers = {dt: power for dt, power in short_term_data}
    
    result = []
    alignment_stats = {
        'exact_match': 0,
        'near_match': 0,
        'no_match': 0,
        'total_points': len(long_term_data)
    }
    
    for long_dt, long_power in long_term_data:
        # 查找最近的短期预测时间点
        closest_short_dt = None
        min_time_diff = float('inf')
        
        for short_dt in short_times:
            time_diff = abs((short_dt - long_dt).total_seconds())
            if time_diff < min_time_diff: # 找到最接近长期预测15分钟的短期预测点(每分钟一个点)，并计算时间差
                min_time_diff = time_diff
                closest_short_dt = short_dt
        
        if closest_short_dt is not None:
            if min_time_diff == 0: # 没有时间差，完全匹配
                # 完全匹配
                short_power = short_powers[closest_short_dt]
                fused_power = max(short_power, long_power)
                alignment_stats['exact_match'] += 1
                logger.debug(f"完全匹配 {long_dt}: 短期={short_power}, 长期={long_power}, 融合={fused_power}")
                
            elif min_time_diff <= TIME_ALIGNMENT_TOLERANCE: # <= 7.5分钟，认为是有效匹配
                # 近似匹配
                short_power = short_powers[closest_short_dt]
                fused_power = max(short_power, long_power)
                alignment_stats['near_match'] += 1
                logger.debug(f"近似匹配 {long_dt} -> {closest_short_dt} (差异{min_time_diff:.0f}秒): "
                           f"短期={short_power}, 长期={long_power}, 融合={fused_power}")
                
            else: # > 7.5分钟，不匹配，直接使用长期预测值
                fused_power = long_power
                alignment_stats['no_match'] += 1
                logger.debug(f"无匹配 {long_dt} (最近点差异{min_time_diff:.0f}秒): 仅长期={long_power}")
        
        else: # 没有短期数据，直接使用长期预测值
            fused_power = long_power
            alignment_stats['no_match'] += 1
        
        result.append((long_dt, fused_power))
    
    # 输出对齐统计信息
    logger.info(f"时间对齐统计:")
    logger.info(f"  完全匹配: {alignment_stats['exact_match']} 点")
    logger.info(f"  近似匹配: {alignment_stats['near_match']} 点 (±{TIME_ALIGNMENT_TOLERANCE/60:.1f}分钟内)")
    logger.info(f"  无匹配点: {alignment_stats['no_match']} 点")
    logger.info(f"  总计: {alignment_stats['total_points']} 点")
    
    return result


def _extend_to_96_points(data: List[Tuple[datetime, float]]) -> List[Tuple[datetime, float]]:
    """
    将数据扩展到96个点
    :param data: 原始数据
    :return: 扩展后的数据
    """
    if len(data) >= 96:
        return data[:96]
    
    result = list(data)
    if result:
        # 从最后一个时间点开始扩展
        last_time = result[-1][0]
        for i in range(len(result), 96):
            next_time = last_time + timedelta(minutes=15 * (i - len(result) + 1))
            result.append((next_time, 0.0))
    else:
        # 没有数据，生成96个0值点
        start_time = datetime.now(pytz.UTC).replace(minute=0, second=0, microsecond=0)
        for i in range(96):
            time_point = start_time + timedelta(minutes=15 * i)
            result.append((time_point, 0.0))
    
    return result


def _save_fused_result(db_operate: DBOperate, site_no: str, 
                      fused_data: List[Tuple[datetime, float]]) -> bool:
    """
    保存融合结果到数据库
    :param db_operate: 数据库操作对象
    :param site_no: 场站编号
    :param fused_data: 融合后的数据
    :return: 是否保存成功
    """
    try:
        if not fused_data:
            logger.warning(f"场站 {site_no} 没有融合数据需要保存")
            return False
        
        # 使用差分存储方式减少time_list字段占用
        start_time = fused_data[0][0]
        power_list = [power for _, power in fused_data]
        
        # 检查是否为等间隔时间序列
        if len(fused_data) > 1:
            interval = (fused_data[1][0] - fused_data[0][0]).total_seconds() / 60  # 分钟
            is_regular_interval = True
            
            # 验证所有间隔是否相等
            for i in range(2, len(fused_data)):
                current_interval = (fused_data[i][0] - fused_data[i-1][0]).total_seconds() / 60
                if abs(current_interval - interval) > 0.1:  # 允许0.1分钟误差
                    is_regular_interval = False
                    break
            
            if is_regular_interval:
                # 使用紧凑的差分存储格式
                start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
                time_list_compact = {
                    "start_time": start_time_str,
                    "interval_minutes": int(interval),
                    "count": len(fused_data)
                }
                time_list_json = json.dumps(time_list_compact)
                logger.info(f"使用等间隔差分存储，JSON长度从约{len(fused_data)*27}字符减少到{len(time_list_json)}字符")
            else:
                # 使用分钟偏移数组存储
                minute_offsets = [0]  # 第一个点偏移为0
                for i in range(1, len(fused_data)):
                    offset_minutes = int((fused_data[i][0] - start_time).total_seconds() / 60)
                    minute_offsets.append(offset_minutes)
                
                start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
                time_list_compact = {
                    "start_time": start_time_str,
                    "minute_offsets": minute_offsets
                }
                time_list_json = json.dumps(time_list_compact)
                logger.info(f"使用偏移数组差分存储，JSON长度从约{len(fused_data)*27}字符减少到{len(time_list_json)}字符")
        else:
            # 只有一个点，直接存储
            start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
            time_list_json = json.dumps([start_time_str])
        
        # 计算曲线开始和结束时间
        curve_start_time = fused_data[0][0]
        curve_end_time = fused_data[-1][0]
        
        with db_operate.get_db() as db:
            # 删除旧记录（如果存在）
            db.query(HybridLoadPredictionDB).filter(
                HybridLoadPredictionDB.site_no == site_no
            ).delete()
            
            # 创建新记录
            hybrid_record = HybridLoadPredictionDB(
                site_no=site_no,
                curve_start_time=curve_start_time,
                curve_end_time=curve_end_time,
                time_list=time_list_json,  # 使用差分存储格式
                power_list=json.dumps(power_list)
            )
            
            db.add(hybrid_record)
            db.commit()
            
            logger.info(f"场站 {site_no} 融合负载预测结果保存成功，数据点数: {len(fused_data)}")
            logger.info(f"time_list JSON长度: {len(time_list_json)}, power_list JSON长度: {len(json.dumps(power_list))}")
            return True
            
    except Exception as e:
        logger.error(f"场站 {site_no} 融合结果保存失败: {e}")
        return False


def _parse_time_list_json(time_list_json: str) -> List[datetime]:
    """
    解析时间列表JSON，支持差分存储格式和传统格式
    :param time_list_json: 时间列表的JSON字符串
    :return: datetime对象列表
    """
    try:
        time_data = json.loads(time_list_json)
        
        # 判断是否为差分存储格式
        if isinstance(time_data, dict):
            start_time_str = time_data.get("start_time")
            if not start_time_str:
                raise ValueError("差分存储格式缺少start_time字段")
            
            # 根据配置决定是否处理时区
            if USE_TIMEZONE: # "2024-01-01T12:30:45Z" → "2024-01-01T12:30:45+00:00"，解析为datetime对象
                start_time = datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
            else:
                # 移除时区信息，使用本地时间
                clean_time_str = start_time_str.replace('Z', '').replace('+00:00', '')
                start_time = datetime.fromisoformat(clean_time_str)
            
            # 等间隔格式（支持分钟和秒间隔） {"start_time": "2024-01-01T12:30:45Z", "interval_minutes": 15, "count": 96}
            if "interval_minutes" in time_data and "count" in time_data:
                interval_minutes = time_data["interval_minutes"]
                count = time_data["count"]
                
                result = []
                for i in range(count):
                    time_point = start_time + timedelta(minutes=interval_minutes * i)
                    result.append(time_point)
                return result
            
            # 等间隔格式（支持秒间隔） {"start_time": "2024-01-01T12:30:45Z", "interval_seconds": 900, "count": 96}
            elif "interval_seconds" in time_data and "count" in time_data:
                interval_seconds = time_data["interval_seconds"]
                count = time_data["count"]
                
                result = []
                for i in range(count):
                    time_point = start_time + timedelta(seconds=interval_seconds * i)
                    result.append(time_point)
                return result
            
            # 偏移数组格式（支持分钟偏移） {"start_time": "2024-01-01T12:30:45Z", "minute_offsets": [0, 15, 30, 45]}
            elif "minute_offsets" in time_data:
                minute_offsets = time_data["minute_offsets"]
                
                result = []
                for offset in minute_offsets:
                    time_point = start_time + timedelta(minutes=offset)
                    result.append(time_point)
                return result
            
            # 偏移数组格式（支持秒偏移） {"start_time": "2024-01-01T12:30:45Z", "second_offsets": [0, 900, 1800, 2700]}
            elif "second_offsets" in time_data:
                second_offsets = time_data["second_offsets"]
                
                result = []
                for offset in second_offsets:
                    time_point = start_time + timedelta(seconds=offset)
                    result.append(time_point)
                return result
            
            # 其他格式
            else:
                raise ValueError("未知的差分存储格式")
        
        # 传统格式：时间字符串数组 curve_start_time: 2025-06-25 08:00:00, time_list: [0, 900, 1800, 2700]
        elif isinstance(time_data, list):
            # 检查是否为简单的秒偏移量数组（所有元素都是数字）
            if time_data and all(isinstance(x, (int, float)) for x in time_data):
                # 这是秒偏移量数组，需要结合 curve_start_time 来计算实际时间
                # 由于这里没有 curve_start_time，我们需要从调用上下文获取
                logger.warning("检测到简单秒偏移量数组格式，需要结合curve_start_time来解析")
                # 返回一个特殊标记，让调用者知道需要额外处理
                return [datetime.min] * len(time_data)  # 占位符，实际时间由调用者计算
            
            # 时间字符串数组
            result = []
            for time_str in time_data:
                if USE_TIMEZONE:
                    dt = datetime.fromisoformat(str(time_str).replace('Z', '+00:00'))
                else:
                    # 移除时区信息
                    clean_time_str = str(time_str).replace('Z', '').replace('+00:00', '')
                    dt = datetime.fromisoformat(clean_time_str)
                result.append(dt)
            return result
        
        else:
            raise ValueError("无法识别的时间列表格式")
            
    except Exception as e:
        logger.error(f"解析时间列表JSON失败: {e}")
        return []

def _parse_time_list_with_start_time(time_list: str, start_time: datetime) -> List[datetime]:
    """
    解析时间列表JSON，支持多种格式
    :param time_list_json: 时间列表的JSON字符串
    :param start_time: 起始时间，用于计算秒偏移量的实际时间
    :return: datetime对象列表
    """
    try:
        time_data = json.loads(time_list)
        
        # 新的差分存储格式：{"start_time": "2025-07-14 17:34:37", "interval_minutes": 15, "count": 96}
        if isinstance(time_data, dict) and 'start_time' in time_data and 'interval_minutes' in time_data and 'count' in time_data:
            result = []
            stored_start_time = datetime.strptime(time_data['start_time'], "%Y-%m-%d %H:%M:%S")
            interval_minutes = time_data['interval_minutes']
            count = time_data['count']
            
            for i in range(count):
                time_point = stored_start_time + timedelta(minutes=interval_minutes * i)
                result.append(time_point)
            return result
        
        # 如果是简单的秒偏移量数组，比如长期预测的time_list : [0, 900, 1800, 2700]
        if isinstance(time_data, list) and time_data and all(isinstance(x, (int, float)) for x in time_data):
            result = []
            for offset_seconds in time_data:
                time_point = start_time + timedelta(seconds=offset_seconds)
                result.append(time_point)
            return result
        # 否则使用标准解析方法，比如短期预测的time_list : {"start_time": "2024-01-01T12:30:45Z", "second_offsets": [0, 900, 1800, 2700]}
        return _parse_time_list_json(time_list)
        
    except Exception as e:
        logger.error(f"解析时间列表JSON失败: {e}")
        return []

# 测试代码已移动到 tests/test_fused_load_forecast.py
# 运行测试：python -m pytest tests/test_fused_load_forecast.py -v
