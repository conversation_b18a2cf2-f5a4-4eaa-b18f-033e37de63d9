#!/usr/bin/env python3
"""
简单的 storage_dispatch 调用测试
基于用户选中的代码: storage_dispatch(site_no='SITE001', data=None)
"""

import sys
import os
import logging
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def setup_file_logging():
    """设置日志输出到 log.txt 文件"""
    # 清空或创建 log.txt 文件
    with open('log.txt', 'w', encoding='utf-8') as f:
        f.write(f"Storage Dispatch Test Log - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("=" * 80 + "\n\n")
    
    # 配置日志
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('log.txt', mode='a', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)  # 同时输出到控制台
        ]
    )
    
    return logging.getLogger(__name__)

def main():
    """主函数 - 调用 storage_dispatch"""
    logger = setup_file_logging()
    
    logger.info("开始测试 storage_dispatch 方法")
    logger.info(f"Python版本: {sys.version}")
    logger.info(f"工作目录: {os.getcwd()}")
    logger.info("-" * 50)
    
    try:
        # 导入 storage_dispatch 方法
        logger.info("正在导入 storage_dispatch...")
        from application.algorithm_schedule.storage_dispatch import storage_dispatch
        logger.info("导入成功")
        
        # 执行用户选中的代码
        logger.info("开始执行: storage_dispatch(site_no='SITE001', data=None)")
        start_time = datetime.now()
        
        # 调用方法
        result = storage_dispatch(site_no='SITE001', data=None)
        
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()
        
        # 记录结果
        logger.info(f"执行完成，耗时: {execution_time:.3f} 秒")
        logger.info("-" * 50)
        
        if result is not None:
            logger.info("✅ 调用成功!")
            logger.info(f"返回结果类型: {type(result)}")
            
            if isinstance(result, dict):
                logger.info("返回结果概要:")
                for key, value in result.items():
                    if isinstance(value, list):
                        logger.info(f"  {key}: 列表，包含 {len(value)} 个元素")
                        if len(value) > 0:
                            logger.info(f"    首个元素: {value[0]}")
                            if len(value) > 1:
                                logger.info(f"    末个元素: {value[-1]}")
                    else:
                        logger.info(f"  {key}: {value}")
            else:
                logger.info(f"返回结果: {result}")
        else:
            logger.warning("❌ 调用失败，返回 None")
            logger.warning("可能的原因:")
            logger.warning("  - 场站 'SITE001' 不存在")
            logger.warning("  - 缺少必要的数据（储能实时数据、场站数据等）")
            logger.warning("  - 数据库连接问题")
        
        logger.info("=" * 50)
        logger.info("测试完成，详细日志已保存到 log.txt")
        
        return result
        
    except ImportError as e:
        logger.error(f"❌ 导入失败: {e}")
        logger.error("请确保:")
        logger.error("  1. 项目路径正确")
        logger.error("  2. 所有依赖模块已安装")
        logger.error("  3. 数据库配置正确")
        return None
        
    except Exception as e:
        logger.error(f"❌ 执行过程中发生错误: {e}")
        logger.error(f"错误类型: {type(e).__name__}")
        
        # 记录详细错误信息
        import traceback
        logger.error("详细错误堆栈:")
        for line in traceback.format_exc().split('\n'):
            if line.strip():
                logger.error(f"  {line}")
        
        return None

if __name__ == '__main__':
    print("开始执行 storage_dispatch 测试...")
    print("日志将同时输出到控制台和 log.txt 文件")
    print("-" * 50)
    
    result = main()
    
    print("-" * 50)
    if result is not None:
        print("✅ 测试成功完成")
    else:
        print("❌ 测试失败")
    
    print("请查看 log.txt 文件获取完整日志")
