import json
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

def load_json_data(filename):
    dataset = np.empty((0,6), dtype=np.unicode_)
    with open(filename, 'r') as f:
        for entry in json.load(f):
            mac_address = entry["MACAddress"][:6].upper()
            max_voltage = entry["MaximumVoltage"]
            max_current = entry["MaximumCurrent"]
            max_power = entry["MaximumPower"] if entry["MaximumPower"] is not None else 0
            battery_capacity = entry["BatteryCapacity"] if entry["BatteryCapacity"] is not None else 0
            vehicle_label = entry.get("VehicleLabel", None)
            if not vehicle_label:
                print("warning", entry, "has an empty label\n")
                continue
            item = np.array([vehicle_label, mac_address, max_current, max_voltage, max_power, battery_capacity])
            if np.any(np.all(item == dataset, axis = 1)):
                # print("warning", entry, "is duplicate\n")
                continue
            dataset = np.concatenate((dataset, np.expand_dims(item, axis=0)), axis = 0)
    
    print(f"info dataset shape: {dataset.shape}")
    x, y = dataset[:, 1:], dataset[:, 0] 
    return x, y

def load_and_analyze_data(dataset_path, save_dir, config, flag):
    with open(dataset_path, 'r') as f:
        data = json.load(f)
    df_ori = pd.DataFrame(data)

    pop_cols = ["Number", "ChargeStartTime"] 
    if flag != "split":
        pop_cols += ["Source", "BMSDataFrequency"]
    for col in df_ori.columns:
        if col in pop_cols: 
            df_ori.pop(col)
        if col == "MACAddress":
            df_ori["MACAddress"]=df_ori["MACAddress"].apply(lambda x: x[:6].upper())
        if flag != "split"  and col in ["MaximumPower", "BatteryCapacity"]: 
            df_ori.fillna({col: 0}, inplace=True) 

    df_nodup = df_ori.drop_duplicates()
    x = df_nodup.drop("VehicleLabel", axis=1)
    y = df_nodup["VehicleLabel"]

    train_cols = ["MACAddress", "MaximumCurrent", "MaximumVoltage", "MaximumPower", "BatteryCapacity"]
    if flag != "split":
        assert x.columns.tolist() == train_cols, print(f"please check feature: {x.columns.tolist()}")
    
    # 车型分析, 目标车型在训练集 测试集中的分布
    analyze_vehicle_distribution(df_nodup, save_dir, config, flag) 
    print(f"info: {flag} dataset drop duplicate: {df_ori.shape} --> {df_nodup.shape}")
    print(df_nodup.head())
    return x.to_numpy(), y.to_numpy()


def analyze_vehicle_distribution(df, save_dir, config, flag):
    vehicles_stat = get_target_vehicals(config, df, flag)
    vehical_counts=df["VehicleLabel"].value_counts().sort_values(ascending=True)
    indices=[df[df["VehicleLabel"] == label].index.tolist() for label in vehical_counts.index]
    vehicles_stat["ALL"] = [list(vehical_counts.index), list(vehical_counts.values), indices]

    if flag == "test":
        with open(os.path.join(save_dir, f"train_{config['vehical_analysis_file']}.json"), 'r', encoding="utf-8") as f:
            vehicles_stat_train = json.load(f)
        # 查看测试集有但是训练集没有的目标标签
        for label in vehicles_stat["EU+US"][0]:
            if label not in vehicles_stat_train["EU+US"][0]:
                print(f"warning: {label} not in train dataset, please check test dataset")
                exit()
        # 查看训练集有但是测试集没有的标签
        for label in vehicles_stat_train["ALL"][0]:
            if label not in vehicles_stat["ALL"][0]:
                vehicles_stat["ALL"][0].insert(0, label)
                vehicles_stat["ALL"][1].insert(0, 0)
        
    plot_vehicle_distribution(config, vehicles_stat, save_dir, flag)


def get_target_vehicals(config, df, flag):
    vehical_counts=df["VehicleLabel"].value_counts().sort_values(ascending=True)
    vehical_dict = {k: v for k, v in zip(vehical_counts.index, vehical_counts.values)}
    # print(f"vehical counts: {vehical_counts}")
    vehicles_stat = dict()
    for region_list, name in zip([config["EU"], config["US"]], ["EU", "US"]):
        labels, counts, indices = list(), list(), list()
        for company_type_capacity_dict in region_list:
           for company, type_capacity_list in company_type_capacity_dict.items():
               for type_capacity_dict in type_capacity_list:
                    for type, capacity_list in type_capacity_dict.items():
                        for cap in capacity_list:
                            label = f"{company}_{type}_{cap}" # 目标车型
                            labels.append(label)
                            if label not in vehical_dict.keys():
                                print(f"warning: {label} not in {flag} dataset")
                                counts.append(0)
                                indices.append(None)
                            else:
                                counts.append(vehical_dict[label])
                                indices.append(df[df["VehicleLabel"] == label].index.tolist())
                                
        sorted_labels, sorted_counts, sorted_indices = zip(*sorted(zip(labels, counts, indices), key=lambda x: x[1]))
        vehicles_stat[name] = [list(sorted_labels), list(sorted_counts), list(sorted_indices)]

    vehicles_stat = merge_target_vehicals(vehicles_stat)
    
    return vehicles_stat

def merge_target_vehicals(vehicles_stat):
    counts, indices = [], []
    labels = set(vehicles_stat["EU"][0] + vehicles_stat["US"][0])
    for label in labels:
        if label in vehicles_stat["EU"][0]:
            idx = vehicles_stat["EU"][0].index(label) 
            counts.append(vehicles_stat["EU"][1][idx])
            indices.append(vehicles_stat["EU"][2][idx])
        else:
            idx = vehicles_stat["US"][0].index(label)
            counts.append(vehicles_stat["US"][1][idx])
            indices.append(vehicles_stat["US"][2][idx])
    sorted_labels, sorted_counts, sorted_indices = zip(*sorted(zip(labels, counts, indices), key=lambda x: x[1]))
    vehicles_stat["EU+US"] = [list(sorted_labels), list(sorted_counts), list(sorted_indices)]

    return vehicles_stat


def plot_vehicle_distribution(config, vehicles_stat, save_dir, flag):
    plt.figure(figsize=(40,50))
    titles = ["ALL", "EU+US", "EU", "US"]
    for i, title in enumerate(titles):
        plt.subplot(2,2,i+1)
        plt.barh(vehicles_stat[title][0], vehicles_stat[title][1])
        plt.xticks(range(0, int(max(vehicles_stat[title][1])+1), 1))
        plt.grid(axis='both', linestyle=':')
        plt.title(f"{titles[i]} vehicals", fontsize=20)
        plt.ylabel("vehical type/{}".format(len(set(vehicles_stat[title][0]))), fontsize=20)
        plt.xlabel("vehical number/{}".format(sum(vehicles_stat[title][1])), fontsize=20)

    plt.savefig(os.path.join(save_dir, f"{flag}_{config['vehical_analysis_file']}.png"))
    save_path = os.path.join(save_dir, f"{flag}_{config['vehical_analysis_file']}.json")
    with open(save_path, 'w', encoding="utf-8") as f:
        json.dump(vehicles_stat, f, indent=2, ensure_ascii=False, cls=NpEncoder)
    print(f"info: vehicle distribution saved in {save_path}\n")


def analyze_label_distribution(config, save_dir, mapping_single, mapping_multi):
    with open(os.path.join(save_dir, f"train_{config['vehical_analysis_file']}.json"), 'r', encoding="utf-8") as f:
        vehical_mapping = json.load(f)

    label_mapping = {**mapping_multi, **mapping_single}
    save_label_clusters_mapping(config, vehical_mapping, label_mapping, save_dir)
    
    save_path = os.path.join(save_dir, f"{config['label_analysis_file']}.txt")
    num = 1
    with open(save_path, 'w', encoding="utf-8") as f:
        string = ""
        for target_label in vehical_mapping["EU+US"][0]:
            for train_label in mapping_multi.keys():
                if target_label in train_label.split(','):
                    string += f"{num}, {target_label} -> {train_label}\n"
                    for feat in mapping_multi[train_label]:
                        string += f"{list(feat)[:5]}\n"
                    num += 1
        f.write(string)
    # print(f"info: label distribution saved in {save_path}\n")


def save_label_clusters_mapping(config, vehical_mapping, label_mapping, save_dir):
    label_clusters_mapping = {}
    for region, info in vehical_mapping.items():
        label_clusters_mapping[region] = [[], []]
        for original_label in info[0]:
            for train_label in label_mapping.keys():
                if original_label in train_label and train_label not in label_clusters_mapping[region][0]:
                    label_clusters_mapping[region][0].append(train_label)
                    label_clusters_mapping[region][1].append(label_mapping[train_label])
    
    save_path = os.path.join(save_dir, f"{config['label_analysis_file']}.json")
    with open(save_path, 'w', encoding="utf-8") as f:
        json.dump(label_clusters_mapping, f, indent=2, ensure_ascii=False, cls=NpEncoder)
    print(f"info: training target label saved in {save_path}\n")


# 逻辑取反函数
def logical_not(value):
    return 1 if value == 0 else 0


def train_test_split(dataset_path, save_dir, config, flag):
    load_and_analyze_data(dataset_path, save_dir, config, flag)

    with open(os.path.join(save_dir, f"split_{config['vehical_analysis_file']}.json"), 'r', encoding="utf-8") as f:
        vehical_analysis = json.load(f)
    
    with open(dataset_path, 'r', encoding="utf-8") as f:
        dataset = np.array(json.load(f))

    train_dataset, test_dataset = [], []
    for label, count, indices in zip(vehical_analysis["ALL"][0], vehical_analysis["ALL"][1], vehical_analysis["ALL"][2]):
        np.random.seed(42)
        np.random.shuffle(indices)
        split_index = 1 if count == 1 else int(0.8 * count)
        train_dataset.extend(dataset[indices[:split_index]])
        if count == 1: continue
        test_dataset.extend(dataset[indices[split_index:]])
    
    print(f"info: dataset split into training and testing sets.")

    return train_dataset, test_dataset


def test_dataset_split(predictions, ground_truth, label_file):
    with open(label_file, 'r', encoding="utf-8") as f:
        label_clusters_mapping = json.load(f)

    pred_EU, pred_US, pred_EU_US, pred_ALL = [], [], [], []
    gt_EU, gt_US, gt_EU_US, gt_ALL = [], [], [], []
    for pred, gt in zip(predictions, ground_truth):
        if gt in label_clusters_mapping["EU"][0]:
            pred_EU.append(pred)
            gt_EU.append(gt)
        if gt in label_clusters_mapping["US"][0]:
            pred_US.append(pred)
            gt_US.append(gt)
        if gt in label_clusters_mapping["EU+US"][0]:
            pred_EU_US.append(pred)
            gt_EU_US.append(gt)
        if gt in label_clusters_mapping["ALL"][0]:
            pred_ALL.append(pred)
            gt_ALL.append(gt)

    return (pred_ALL, pred_EU_US, pred_EU, pred_US), (gt_ALL, gt_EU_US, gt_EU, gt_US)


def caculate_fpr_and_fnr(cm):
    num_classes = cm.shape[0]
    fpr, fnr = [], []
    for i in range(num_classes):
        TP = cm[i, i]
        FN = np.sum(cm[i, :]) - TP
        FP = np.sum(cm[:, i]) - TP
        TN = np.sum(cm) - TP - FN - FP

        fpr.append(FP / float(FP + TN) if (FP + TN) >0 else 0)
        fnr.append(FN / float(TP + FN) if (TP + FN) >0 else 0)

    return np.mean(fpr), np.mean(fnr)


def predict_error_analysis(label_file, unknow_list, error_list, save_path):
    with open(label_file, 'r', encoding="utf-8") as f:
        label_clusters_mapping = json.load(f)

    string = ""
    sep_txts = ["unknow", "error"]
    all_mapping = label_clusters_mapping["ALL"]
    target_labels = [label for labels in label_clusters_mapping["EU+US"][0] for label in labels.split(',')] 
    with open(save_path, 'w', encoding="utf-8") as f:
        for sep_txt,  check_list in zip(sep_txts, [unknow_list, error_list]):
            string += f"-----------------target {sep_txt}(test/train/predict)-----------------\n"
            num = 1
            for i in range(len(check_list[0])): 
                feat, test_label, train_label, pred_label = check_list[0][i], check_list[1][i], check_list[2][i], check_list[3][i]
                # if test_label not in target_labels: continue
                string += f"{num}, {test_label} -> {train_label} <- {pred_label}\n"
                string += ('\n').join([('\t').join(list(map(lambda x: str(x), feat[:5])))])
                string += '\n'
                for labels in [train_label, pred_label]:
                    string += "+++++++++++++++++++++++++++++++++++\n"
                    for label in labels.split(';'):
                        idx = all_mapping[0].index(label)
                        string += ("\n").join([('\t').join(list(map(lambda x: str(x), feat[:5]))) for feat in all_mapping[1][idx]])
                        string += '\n'
                num += 1
        f.write(string)
    print(f"info: error analysis file saved in {save_path}\n")


class NpEncoder(json.JSONEncoder):
    # transform the numpy data type to python type for json data output
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            return float(obj)
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(obj,set):
            return list(obj)
        return json.JSONEncoder.default(self, obj)