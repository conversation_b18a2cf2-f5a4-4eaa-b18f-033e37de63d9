#!/usr/bin/env python3
"""
动态电价获取功能的快速测试
专注于核心功能和关键异常场景
"""

import sys
import os
import logging
import time
from datetime import datetime
from unittest.mock import Mock, patch

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('quick_test.log', mode='w', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def test_scheduler_basic_functionality():
    """测试调度器基本功能"""
    logger = logging.getLogger(__name__)
    logger.info("测试调度器基本功能")
    
    try:
        from application.algorithm_schedule.dynamic_price_scheduler import DynamicPriceScheduler
        
        # 测试初始化
        scheduler = DynamicPriceScheduler()
        assert scheduler.site_no == 'ALL'
        assert scheduler.running == False
        
        # 测试时间计算
        next_exec_seconds = scheduler._calculate_next_execution_time()
        assert 0 <= next_exec_seconds <= 24 * 3600
        
        # 测试停止机制（如果存在stop方法）
        if hasattr(scheduler, 'stop'):
            scheduler.stop()
            assert scheduler.running == False
        else:
            # 如果没有stop方法，直接设置running为False
            scheduler.running = False
            assert scheduler.running == False
        
        logger.info("✅ 调度器基本功能测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 调度器基本功能测试失败: {e}")
        return False

def test_normal_price_fetch():
    """测试正常电价获取"""
    logger = logging.getLogger(__name__)
    logger.info("测试正常电价获取")
    
    try:
        from application.algorithm_schedule.dynamic_price_fetch import dynamic_price_fetch
        
        # 模拟正常的API响应
        mock_price_data = {
            'prices': [
                {
                    'price': 100 + i,
                    'deliveryStart': f'2024-01-01T{i:02d}:00:00Z',
                    'deliveryEnd': f'2024-01-01T{(i+1) % 24:02d}:00:00Z' if i < 23 else '2024-01-02T00:00:00Z'
                } for i in range(24)
            ],
            'marketMainCurrency': 'EUR'
        }
        
        mock_db = Mock()
        mock_db.save_site_electricity_price = Mock()
        
        with patch('application.algorithm_schedule.dynamic_price_fetch.get_dynamic_electricity_price', return_value=mock_price_data), \
             patch('application.algorithm_schedule.dynamic_price_fetch.DBOperate', return_value=mock_db), \
             patch('application.algorithm_schedule.dynamic_price_fetch.send_electricity_price_with_retry', return_value=True):
            
            result = dynamic_price_fetch('ALL', datetime.now().date())
            
            # 验证结果
            assert result is not None
            assert 'biz_seq' in result
            assert 'electricity_price' in result
            assert len(result['electricity_price']) > 0
            
            logger.info("✅ 正常电价获取测试通过")
            return True
            
    except Exception as e:
        logger.error(f"❌ 正常电价获取测试失败: {e}")
        return False

def test_api_error_handling():
    """测试API错误处理"""
    logger = logging.getLogger(__name__)
    logger.info("测试API错误处理")
    
    try:
        from application.algorithm_schedule.dynamic_price_fetch import get_region_prices
        
        # 测试API超时
        def mock_timeout_api(*args, **kwargs):
            import requests
            raise requests.exceptions.Timeout("API请求超时")
        
        mock_db = Mock()
        
        with patch('application.algorithm_schedule.dynamic_price_fetch.get_dynamic_electricity_price', side_effect=mock_timeout_api), \
             patch('application.algorithm_schedule.dynamic_price_fetch.DBOperate', return_value=mock_db), \
             patch('application.algorithm_schedule.dynamic_price_fetch.time.sleep'):
            
            result = get_region_prices(['DE'], datetime.now().date())
            
            # 验证错误处理
            assert isinstance(result, list)
            assert len(result) == 0
            
            logger.info("✅ API错误处理测试通过")
            return True
            
    except Exception as e:
        logger.error(f"❌ API错误处理测试失败: {e}")
        return False

def test_invalid_data_handling():
    """测试无效数据处理"""
    logger = logging.getLogger(__name__)
    logger.info("测试无效数据处理")
    
    try:
        from application.algorithm_schedule.dynamic_price_fetch import get_region_prices
        
        # 测试各种无效数据
        invalid_responses = [
            None,  # 空响应
            {},    # 空字典
            {'prices': []},  # 空价格数组
            {'prices': [{'price': 100}] * 12},  # 数据不完整
        ]
        
        mock_db = Mock()
        
        for invalid_response in invalid_responses:
            with patch('application.algorithm_schedule.dynamic_price_fetch.get_dynamic_electricity_price', return_value=invalid_response), \
                 patch('application.algorithm_schedule.dynamic_price_fetch.DBOperate', return_value=mock_db), \
                 patch('application.algorithm_schedule.dynamic_price_fetch.time.sleep'):
                
                result = get_region_prices(['DE'], datetime.now().date())
                
                # 所有无效响应都应该返回空列表
                assert isinstance(result, list)
                assert len(result) == 0
        
        logger.info("✅ 无效数据处理测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 无效数据处理测试失败: {e}")
        return False

def test_retry_mechanism():
    """测试重试机制"""
    logger = logging.getLogger(__name__)
    logger.info("测试重试机制")
    
    try:
        from application.algorithm_schedule.dynamic_price_fetch import get_region_prices
        
        call_count = {}
        
        def mock_retry_api(areas, date, currency="EUR"):
            if areas not in call_count:
                call_count[areas] = 0
            call_count[areas] += 1
            
            # DE第一次就成功
            if areas == 'DE':
                return {
                    'prices': [{'price': 100 + i, 'deliveryStart': f'2024-01-01T{i:02d}:00:00Z', 'deliveryEnd': f'2024-01-01T{(i+1) % 24:02d}:00:00Z' if i < 23 else '2024-01-02T00:00:00Z'} for i in range(24)],
                    'marketMainCurrency': 'EUR'
                }
            # FR第二次成功
            elif areas == 'FR' and call_count[areas] >= 2:
                return {
                    'prices': [{'price': 200 + i, 'deliveryStart': f'2024-01-01T{i:02d}:00:00Z', 'deliveryEnd': f'2024-01-01T{(i+1) % 24:02d}:00:00Z' if i < 23 else '2024-01-02T00:00:00Z'} for i in range(24)],
                    'marketMainCurrency': 'EUR'
                }
            else:
                return None
        
        mock_db = Mock()
        
        with patch('application.algorithm_schedule.dynamic_price_fetch.get_dynamic_electricity_price', side_effect=mock_retry_api), \
             patch('application.algorithm_schedule.dynamic_price_fetch.DBOperate', return_value=mock_db), \
             patch('application.algorithm_schedule.dynamic_price_fetch.time.sleep'):
            
            result = get_region_prices(['DE', 'FR'], datetime.now().date())
            
            # 等待后台重试完成
            time.sleep(0.5)
            
            # 验证重试机制
            assert call_count['DE'] == 1  # DE第一次就成功
            assert call_count.get('FR', 0) >= 1  # FR至少被调用一次
            
            # 验证第一轮返回的结果
            assert isinstance(result, list)
            assert len(result) >= 1  # 至少DE成功
            
            logger.info("✅ 重试机制测试通过")
            return True
            
    except Exception as e:
        logger.error(f"❌ 重试机制测试失败: {e}")
        return False

def test_async_processing():
    """测试异步处理"""
    logger = logging.getLogger(__name__)
    logger.info("测试异步处理")
    
    try:
        from application.algorithm_schedule.dynamic_price_fetch import get_region_prices
        
        # 模拟部分成功的API
        def mock_partial_api(areas, date, currency="EUR"):
            if areas == 'DE':
                return {
                    'prices': [{'price': 100 + i, 'deliveryStart': f'2024-01-01T{i:02d}:00:00Z', 'deliveryEnd': f'2024-01-01T{(i+1) % 24:02d}:00:00Z' if i < 23 else '2024-01-02T00:00:00Z'} for i in range(24)],
                    'marketMainCurrency': 'EUR'
                }
            else:
                return None
        
        mock_db = Mock()
        
        with patch('application.algorithm_schedule.dynamic_price_fetch.get_dynamic_electricity_price', side_effect=mock_partial_api), \
             patch('application.algorithm_schedule.dynamic_price_fetch.DBOperate', return_value=mock_db), \
             patch('application.algorithm_schedule.dynamic_price_fetch.time.sleep'):
            
            start_time = time.time()
            result = get_region_prices(['DE', 'FR', 'NL'], datetime.now().date())
            end_time = time.time()
            
            # 验证异步处理（主流程快速返回）
            execution_time = end_time - start_time
            assert execution_time < 2.0, f"主流程执行时间过长: {execution_time:.2f}秒"
            
            # 验证立即返回成功的数据
            assert isinstance(result, list)
            assert len(result) >= 1  # 至少DE成功
            
            logger.info("✅ 异步处理测试通过")
            return True
            
    except Exception as e:
        logger.error(f"❌ 异步处理测试失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("=" * 60)
    logger.info("开始运行动态电价获取功能的快速测试")
    logger.info("=" * 60)
    
    tests = [
        ("调度器基本功能", test_scheduler_basic_functionality),
        ("正常电价获取", test_normal_price_fetch),
        ("API错误处理", test_api_error_handling),
        ("无效数据处理", test_invalid_data_handling),
        ("重试机制", test_retry_mechanism),
        ("异步处理", test_async_processing),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                logger.info(f"✅ {test_name} 测试通过")
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
    
    logger.info(f"\n{'='*60}")
    logger.info("快速测试结果")
    logger.info(f"{'='*60}")
    logger.info(f"总测试数: {total}")
    logger.info(f"通过测试: {passed}")
    logger.info(f"失败测试: {total - passed}")
    logger.info(f"成功率: {(passed/total)*100:.1f}%")
    
    if passed == total:
        logger.info("\n🎉 所有快速测试都通过了！")
        logger.info("核心功能验证成功")
        logger.info("建议运行完整测试套件进行更全面的验证")
        return True
    else:
        logger.error(f"\n❌ {total - passed} 个测试失败")
        logger.error("建议检查失败的测试并修复相关问题")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
