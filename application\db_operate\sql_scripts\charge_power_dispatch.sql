-- 测试数据插入SQL语句
-- 基于 test_case03.json 的数据样例
-- 用于测试充电桩功率分配算法

-- 设置场站编号变量 (用户可以根据需要修改此值)
SET @site_no = 'SITE001';

-- 0. 清理现有测试数据 (按照外键依赖关系顺序)
-- 注意：TRUNCATE 会清空表数据并重置自增ID，请谨慎使用

-- 先清理有外键依赖的表
TRUNCATE TABLE `ChargerRealtimeData`;
TRUNCATE TABLE `ESRealtimeData`;
TRUNCATE TABLE `PVAndMeterRealtimeData`;
TRUNCATE TABLE `ChargerNonSuppressPowerPrediction`;
TRUNCATE TABLE `ChargingRecord`;
TRUNCATE TABLE `EVModel`;
TRUNCATE TABLE `ChargerSession`;
TRUNCATE TABLE `Module`;
TRUNCATE TABLE `Charger`;
TRUNCATE TABLE `Pile`;

-- 最后清理主表
TRUNCATE TABLE `Site`;

-- 1. 插入场站信息
INSERT INTO `Site` (
    `site_no`, 
    `demand_limit`, 
    `es_total_energy`, 
    `region`, 
    `lat_and_lng`, 
    `site_grid_limit`, 
    `grid_reverse`, 
    `grid_name`, 
    `pv_can_control`, 
    `is_ai_active`
) VALUES (
    @site_no,            -- site_no (使用变量)
    800,                -- demand_limit (来自test_case03.json)
    1000,               -- es_total_energy (储能电池容量，单位kwh)
    'China',            -- region
    '39.9042,116.4074', -- lat_and_lng (北京坐标示例)
    1200,               -- site_grid_limit (来自test_case03.json)
    1,                  -- grid_reverse (电网能否逆流 1可以 0不可以)
    'State Grid',       -- grid_name
    1,                  -- pv_can_control (光伏是否可调 1可调 0不可调)
    1                   -- is_ai_active (是否开启AI能力 1 start 0 stop)
);

-- 2. 插入充电桩信息
INSERT INTO `Pile` (`pile_sn`, `gun_num`, `pile_type`, `rated_power`, `site_no`) VALUES
('DE0120B1GN7C00009J', 2, 'DC_FAST', 120.00, @site_no),
('DL7480B2GR6C00017B', 4, 'N1', 480.00, @site_no),
('DE1120B1GN1CFBC005', 7, 'N1', 480.00, @site_no);

-- 3. 插入充电枪信息
INSERT INTO `Charger` (`charger_sn`, `gun_no`, `bom_index`, `pile_sn`, `max_curr`) VALUES
-- DE0120B1GN7C00009J 的充电枪
('DE0120B1GN7C00009J_1', 1, 1, 'DE0120B1GN7C00009J', 300.0),
('DE0120B1GN7C00009J_2', 2, 2, 'DE0120B1GN7C00009J', 200.0),

-- DL7480B2GR6C00017B 的充电枪
('DL7480B2GR6C00017B_1', 1, 1, 'DL7480B2GR6C00017B', 350.0),
('DL7480B2GR6C00017B_2', 2, 2, 'DL7480B2GR6C00017B', 350.0),
('DL7480B2GR6C00017B_3', 3, 3, 'DL7480B2GR6C00017B', 350.0),
('DL7480B2GR6C00017B_4', 4, 4, 'DL7480B2GR6C00017B', 350.0),

-- DE1120B1GN1CFBC005 的充电枪
('DE1120B1GN1CFBC005_1', 1, 1, 'DE1120B1GN1CFBC005', 350.0),
('DE1120B1GN1CFBC005_2', 2, 2, 'DE1120B1GN1CFBC005', 350.0),
('DE1120B1GN1CFBC005_3', 3, 3, 'DE1120B1GN1CFBC005', 350.0),
('DE1120B1GN1CFBC005_4', 4, 4, 'DE1120B1GN1CFBC005', 350.0),
('DE1120B1GN1CFBC005_5', 5, 5, 'DE1120B1GN1CFBC005', 350.0),
('DE1120B1GN1CFBC005_6', 6, 6, 'DE1120B1GN1CFBC005', 350.0),
('DE1120B1GN1CFBC005_7', 7, 7, 'DE1120B1GN1CFBC005', 350.0);

-- 4. 插入模块信息
INSERT INTO `Module` (`pile_sn`, `type`, `module_no`, `unit_power`, `unit_num`) VALUES
-- DE0120B1GN7C00009J 的模块
('DE0120B1GN7C00009J', 'DC_FAST', 1, 20.0, 3),
('DE0120B1GN7C00009J', 'DC_FAST', 2, 20.0, 3),

-- DL7480B2GR6C00017B 的模块
('DL7480B2GR6C00017B', 'N1', 1, 40.0, 1),
('DL7480B2GR6C00017B', 'N1', 2, 40.0, 2),
('DL7480B2GR6C00017B', 'N1', 3, 40.0, 1),
('DL7480B2GR6C00017B', 'N1', 4, 40.0, 2),
('DL7480B2GR6C00017B', 'N1', 5, 40.0, 1),
('DL7480B2GR6C00017B', 'N1', 6, 40.0, 2),
('DL7480B2GR6C00017B', 'N1', 7, 40.0, 1),
('DL7480B2GR6C00017B', 'N1', 8, 40.0, 2),

-- DE1120B1GN1CFBC005 的模块
('DE1120B1GN1CFBC005', 'N1', 1, 40.0, 1),
('DE1120B1GN1CFBC005', 'N1', 2, 40.0, 2),
('DE1120B1GN1CFBC005', 'N1', 3, 40.0, 1),
('DE1120B1GN1CFBC005', 'N1', 4, 40.0, 2),
('DE1120B1GN1CFBC005', 'N1', 5, 40.0, 1),
('DE1120B1GN1CFBC005', 'N1', 6, 40.0, 2),
('DE1120B1GN1CFBC005', 'N1', 7, 40.0, 1),
('DE1120B1GN1CFBC005', 'N1', 8, 40.0, 2);

-- 5. 插入充电会话信息 (正在充电的会话)
INSERT INTO `ChargerSession` (`local_id`, `site_no`, `charger_sn`, `mac_addr`, `start_time`, `status`) VALUES
-- DE0120B1GN7C00009J 的充电会话
('session_DE0120B1GN7C00009J_1', @site_no, 'DE0120B1GN7C00009J_1', 'AA:BB:CC:DD:EE:01', NOW(), '1'),  -- VEHICLE_CONNECTED
('session_DE0120B1GN7C00009J_2', @site_no, 'DE0120B1GN7C00009J_2', 'AA:BB:CC:DD:EE:02', NOW(), '7'),  -- FORMAL_CHARGING

-- DL7480B2GR6C00017B 的充电会话
('session_DL7480B2GR6C00017B_1', @site_no, 'DL7480B2GR6C00017B_1', 'AA:BB:CC:DD:EE:03', NOW(), '1'),  -- VEHICLE_CONNECTED
('session_DL7480B2GR6C00017B_2', @site_no, 'DL7480B2GR6C00017B_2', 'AA:BB:CC:DD:EE:04', NOW(), '7'),  -- FORMAL_CHARGING
('session_DL7480B2GR6C00017B_3', @site_no, 'DL7480B2GR6C00017B_3', 'AA:BB:CC:DD:EE:05', NOW(), '1'),  -- VEHICLE_CONNECTED
('session_DL7480B2GR6C00017B_4', @site_no, 'DL7480B2GR6C00017B_4', 'AA:BB:CC:DD:EE:06', NOW(), '7'),  -- FORMAL_CHARGING

-- DE1120B1GN1CFBC005 的充电会话
('session_DE1120B1GN1CFBC005_1', @site_no, 'DE1120B1GN1CFBC005_1', 'AA:BB:CC:DD:EE:07', NOW(), '1'),  -- VEHICLE_CONNECTED
('session_DE1120B1GN1CFBC005_2', @site_no, 'DE1120B1GN1CFBC005_2', 'AA:BB:CC:DD:EE:08', NOW(), '7'),  -- FORMAL_CHARGING
('session_DE1120B1GN1CFBC005_3', @site_no, 'DE1120B1GN1CFBC005_3', 'AA:BB:CC:DD:EE:09', NOW(), '1'),  -- VEHICLE_CONNECTED
('session_DE1120B1GN1CFBC005_4', @site_no, 'DE1120B1GN1CFBC005_4', 'AA:BB:CC:DD:EE:10', NOW(), '7'),  -- FORMAL_CHARGING
('session_DE1120B1GN1CFBC005_5', @site_no, 'DE1120B1GN1CFBC005_5', 'AA:BB:CC:DD:EE:11', NOW(), '0'),  -- VEHICLE_NOT_CONNECTED (未连接)
('session_DE1120B1GN1CFBC005_6', @site_no, 'DE1120B1GN1CFBC005_6', 'AA:BB:CC:DD:EE:12', NOW(), '1'),  -- VEHICLE_CONNECTED
('session_DE1120B1GN1CFBC005_7', @site_no, 'DE1120B1GN1CFBC005_7', 'AA:BB:CC:DD:EE:13', NOW(), '7');  -- FORMAL_CHARGING

-- 6. 插入电动汽车模型信息
INSERT INTO `EVModel` (`local_id`, `charger_sn`, `mac_addr`, `capacity`, `max_power`, `max_current`, `max_voltage`, `recognized_vehicle`, `predict_status`) VALUES
-- DE0120B1GN7C00009J 的车型信息
('session_DE0120B1GN7C00009J_1', 'DE0120B1GN7C00009J_1', 'AA:BB:CC:DD:EE:01', 75.0, 120.0, 300.0, 400.0, 'Tesla Model 3', 2),
('session_DE0120B1GN7C00009J_2', 'DE0120B1GN7C00009J_2', 'AA:BB:CC:DD:EE:02', 100.0, 150.0, 375.0, 400.0, 'BYD Han', 2),

-- DL7480B2GR6C00017B 的车型信息
('session_DL7480B2GR6C00017B_1', 'DL7480B2GR6C00017B_1', 'AA:BB:CC:DD:EE:03', 80.0, 140.0, 350.0, 400.0, 'NIO ES6', 2),
('session_DL7480B2GR6C00017B_2', 'DL7480B2GR6C00017B_2', 'AA:BB:CC:DD:EE:04', 90.0, 180.0, 450.0, 400.0, 'XPeng P7', 2),
('session_DL7480B2GR6C00017B_3', 'DL7480B2GR6C00017B_3', 'AA:BB:CC:DD:EE:05', 60.0, 90.0, 225.0, 400.0, 'GAC Aion S', 2),
('session_DL7480B2GR6C00017B_4', 'DL7480B2GR6C00017B_4', 'AA:BB:CC:DD:EE:06', 70.0, 70.0, 175.0, 400.0, 'Geely Geometry A', 2),

-- DE1120B1GN1CFBC005 的车型信息
('session_DE1120B1GN1CFBC005_1', 'DE1120B1GN1CFBC005_1', 'AA:BB:CC:DD:EE:07', 80.0, 140.0, 350.0, 400.0, 'NIO ES6', 2),
('session_DE1120B1GN1CFBC005_2', 'DE1120B1GN1CFBC005_2', 'AA:BB:CC:DD:EE:08', 90.0, 180.0, 450.0, 400.0, 'XPeng P7', 2),
('session_DE1120B1GN1CFBC005_3', 'DE1120B1GN1CFBC005_3', 'AA:BB:CC:DD:EE:09', 60.0, 90.0, 225.0, 400.0, 'GAC Aion S', 2),
('session_DE1120B1GN1CFBC005_4', 'DE1120B1GN1CFBC005_4', 'AA:BB:CC:DD:EE:10', 70.0, 70.0, 175.0, 400.0, 'Geely Geometry A', 2),
('session_DE1120B1GN1CFBC005_5', 'DE1120B1GN1CFBC005_5', 'AA:BB:CC:DD:EE:11', 0.0, 0.0, 0.0, 400.0, 'Unknown', 0),  -- 未连接
('session_DE1120B1GN1CFBC005_6', 'DE1120B1GN1CFBC005_6', 'AA:BB:CC:DD:EE:12', 25.0, 25.0, 62.5, 400.0, 'Mini EV', 2),
('session_DE1120B1GN1CFBC005_7', 'DE1120B1GN1CFBC005_7', 'AA:BB:CC:DD:EE:13', 45.0, 45.0, 112.5, 400.0, 'Small EV', 2);

-- 7. 插入充电车辆的非压制功率预测
INSERT INTO `ChargerNonSuppressPowerPrediction` (`local_id`, `curve_start_time`, `curve_end_time`, `time_list`, `power_list`) VALUES
-- DE0120B1GN7C00009J 的功率预测
('session_DE0120B1GN7C00009J_1', NOW(), DATE_ADD(NOW(), INTERVAL 15 MINUTE), '[0, 60, 120, 180, 240, 300, 360, 420, 480, 540, 600, 660, 720, 780, 840]', '[55.0, 55.5, 56.0, 56.5, 57.0, 57.5, 58.0, 58.5, 59.0, 59.5, 60.0, 60.5, 61.0, 61.5, 62.0]'),
('session_DE0120B1GN7C00009J_2', NOW(), DATE_ADD(NOW(), INTERVAL 15 MINUTE), '[0, 60, 120, 180, 240, 300, 360, 420, 480, 540, 600, 660, 720, 780, 840]', '[135.0, 135.5, 136.0, 136.5, 137.0, 137.5, 138.0, 138.5, 139.0, 139.5, 140.0, 140.5, 141.0, 141.5, 142.0]'),

-- DL7480B2GR6C00017B 的功率预测
('session_DL7480B2GR6C00017B_1', NOW(), DATE_ADD(NOW(), INTERVAL 15 MINUTE), '[0, 60, 120, 180, 240, 300, 360, 420, 480, 540, 600, 660, 720, 780, 840]', '[140.0, 140.5, 141.0, 141.5, 142.0, 142.5, 143.0, 143.5, 144.0, 144.5, 145.0, 145.5, 146.0, 146.5, 147.0]'),
('session_DL7480B2GR6C00017B_2', NOW(), DATE_ADD(NOW(), INTERVAL 15 MINUTE), '[0, 60, 120, 180, 240, 300, 360, 420, 480, 540, 600, 660, 720, 780, 840]', '[180.0, 180.5, 181.0, 181.5, 182.0, 182.5, 183.0, 183.5, 184.0, 184.5, 185.0, 185.5, 186.0, 186.5, 187.0]'),
('session_DL7480B2GR6C00017B_3', NOW(), DATE_ADD(NOW(), INTERVAL 15 MINUTE), '[0, 60, 120, 180, 240, 300, 360, 420, 480, 540, 600, 660, 720, 780, 840]', '[90.0, 90.5, 91.0, 91.5, 92.0, 92.5, 93.0, 93.5, 94.0, 94.5, 95.0, 95.5, 96.0, 96.5, 97.0]'),
('session_DL7480B2GR6C00017B_4', NOW(), DATE_ADD(NOW(), INTERVAL 15 MINUTE), '[0, 60, 120, 180, 240, 300, 360, 420, 480, 540, 600, 660, 720, 780, 840]', '[70.0, 70.5, 71.0, 71.5, 72.0, 72.5, 73.0, 73.5, 74.0, 74.5, 75.0, 75.5, 76.0, 76.5, 77.0]'),

-- DE1120B1GN1CFBC005 的功率预测
('session_DE1120B1GN1CFBC005_1', NOW(), DATE_ADD(NOW(), INTERVAL 15 MINUTE), '[0, 60, 120, 180, 240, 300, 360, 420, 480, 540, 600, 660, 720, 780, 840]', '[140.0, 140.5, 141.0, 141.5, 142.0, 142.5, 143.0, 143.5, 144.0, 144.5, 145.0, 145.5, 146.0, 146.5, 147.0]'),
('session_DE1120B1GN1CFBC005_2', NOW(), DATE_ADD(NOW(), INTERVAL 15 MINUTE), '[0, 60, 120, 180, 240, 300, 360, 420, 480, 540, 600, 660, 720, 780, 840]', '[180.0, 180.5, 181.0, 181.5, 182.0, 182.5, 183.0, 183.5, 184.0, 184.5, 185.0, 185.5, 186.0, 186.5, 187.0]'),
('session_DE1120B1GN1CFBC005_3', NOW(), DATE_ADD(NOW(), INTERVAL 15 MINUTE), '[0, 60, 120, 180, 240, 300, 360, 420, 480, 540, 600, 660, 720, 780, 840]', '[90.0, 90.5, 91.0, 91.5, 92.0, 92.5, 93.0, 93.5, 94.0, 94.5, 95.0, 95.5, 96.0, 96.5, 97.0]'),
('session_DE1120B1GN1CFBC005_4', NOW(), DATE_ADD(NOW(), INTERVAL 15 MINUTE), '[0, 60, 120, 180, 240, 300, 360, 420, 480, 540, 600, 660, 720, 780, 840]', '[70.0, 70.5, 71.0, 71.5, 72.0, 72.5, 73.0, 73.5, 74.0, 74.5, 75.0, 75.5, 76.0, 76.5, 77.0]'),
('session_DE1120B1GN1CFBC005_5', NOW(), DATE_ADD(NOW(), INTERVAL 15 MINUTE), '[0, 60, 120, 180, 240, 300, 360, 420, 480, 540, 600, 660, 720, 780, 840]', '[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]'),
('session_DE1120B1GN1CFBC005_6', NOW(), DATE_ADD(NOW(), INTERVAL 15 MINUTE), '[0, 60, 120, 180, 240, 300, 360, 420, 480, 540, 600, 660, 720, 780, 840]', '[25.0, 25.5, 26.0, 26.5, 27.0, 27.5, 28.0, 28.5, 29.0, 29.5, 30.0, 30.5, 31.0, 31.5, 32.0]'),
('session_DE1120B1GN1CFBC005_7', NOW(), DATE_ADD(NOW(), INTERVAL 15 MINUTE), '[0, 60, 120, 180, 240, 300, 360, 420, 480, 540, 600, 660, 720, 780, 840]', '[45.0, 45.5, 46.0, 46.5, 47.0, 47.5, 48.0, 48.5, 49.0, 49.5, 50.0, 50.5, 51.0, 51.5, 52.0]');

-- 8. 插入光伏和电表实时数据 (PVAndMeterRealtimeData表)
-- 插入多个时间点的数据，用于测试时间范围查询功能
INSERT INTO `PVAndMeterRealtimeData` (`site_no`, `ts`, `pv_power`, `pv_status`, `meter_value`) VALUES
-- 当前时间点的数据 (与test_case03.json一致)
(@site_no, UNIX_TIMESTAMP(NOW()) * 1000, 20, 'discharge', 500),

-- 3秒前的数据
(@site_no, (UNIX_TIMESTAMP(NOW()) - 3) * 1000, 18, 'discharge', 480),

-- 2秒前的数据
(@site_no, (UNIX_TIMESTAMP(NOW()) - 2) * 1000, 19, 'discharge', 490),

-- 1秒前的数据
(@site_no, (UNIX_TIMESTAMP(NOW()) - 1) * 1000, 21, 'discharge', 510),

-- 1秒后的数据
(@site_no, (UNIX_TIMESTAMP(NOW()) + 1) * 1000, 22, 'discharge', 520),

-- 2秒后的数据
(@site_no, (UNIX_TIMESTAMP(NOW()) + 2) * 1000, 23, 'discharge', 530),

-- 3秒后的数据
(@site_no, (UNIX_TIMESTAMP(NOW()) + 3) * 1000, 24, 'discharge', 540),

-- 不同光伏状态的数据
(@site_no, (UNIX_TIMESTAMP(NOW()) + 5) * 1000, 0, 'non-discharge', 600),

-- 不同场站的数据 (用于测试多场站场景)
('654321', UNIX_TIMESTAMP(NOW()) * 1000, 15, 'discharge', 400),
('654321', (UNIX_TIMESTAMP(NOW()) - 1) * 1000, 14, 'discharge', 390),
('654321', (UNIX_TIMESTAMP(NOW()) + 1) * 1000, 16, 'discharge', 410);

-- 9. 插入储能实时数据
INSERT INTO `ESRealtimeData` (`site_no`, `ts`, `es_sn`, `es_soc`, `es_power`, `rated_cap`, `real_cap`, `es_max_soc`, `es_min_soc`, `status`) VALUES
-- 当前时间点的数据 (与test_case03.json一致)
(@site_no, UNIX_TIMESTAMP(NOW()) * 1000, 'ES001', 60, 300, 1000.00, 600.00, 90, 10, 'discharge'),

-- 3秒前的数据
(@site_no, (UNIX_TIMESTAMP(NOW()) - 3) * 1000, 'ES001', 59, 295, 1000.00, 590.00, 90, 10, 'discharge'),

-- 2秒前的数据
(@site_no, (UNIX_TIMESTAMP(NOW()) - 2) * 1000, 'ES001', 59, 298, 1000.00, 595.00, 90, 10, 'discharge'),

-- 1秒前的数据
(@site_no, (UNIX_TIMESTAMP(NOW()) - 1) * 1000, 'ES001', 60, 301, 1000.00, 598.00, 90, 10, 'discharge'),

-- 1秒后的数据
(@site_no, (UNIX_TIMESTAMP(NOW()) + 1) * 1000, 'ES001', 60, 302, 1000.00, 602.00, 90, 10, 'discharge'),

-- 2秒后的数据
(@site_no, (UNIX_TIMESTAMP(NOW()) + 2) * 1000, 'ES001', 61, 305, 1000.00, 605.00, 90, 10, 'discharge'),

-- 3秒后的数据
(@site_no, (UNIX_TIMESTAMP(NOW()) + 3) * 1000, 'ES001', 61, 308, 1000.00, 608.00, 90, 10, 'discharge'),

-- 不同状态的数据
(@site_no, (UNIX_TIMESTAMP(NOW()) + 5) * 1000, 'ES001', 80, 0, 1000.00, 800.00, 90, 10, 'standby'),

-- 不同场站的数据
('654321', UNIX_TIMESTAMP(NOW()) * 1000, 'ES002', 50, 200, 800.00, 400.00, 90, 10, 'charging'),
('654321', (UNIX_TIMESTAMP(NOW()) - 1) * 1000, 'ES002', 49, 195, 800.00, 395.00, 90, 10, 'charging'),
('654321', (UNIX_TIMESTAMP(NOW()) + 1) * 1000, 'ES002', 51, 205, 800.00, 405.00, 90, 10, 'charging');

-- 10. 插入充电桩实时数据 (可选，用于更完整的测试)
INSERT INTO `ChargerRealtimeData` (`site_no`, `ts`, `pile_sn`, `connector`, `status`, `power`, `ocpp_limit`) VALUES
-- DE0120B1GN7C00009J 的实时数据
(@site_no, UNIX_TIMESTAMP(NOW()) * 1000, 'DE0120B1GN7C00009J', 1, 'charging', 55, 120),
(@site_no, UNIX_TIMESTAMP(NOW()) * 1000, 'DE0120B1GN7C00009J', 2, 'charging', 135, 120),

-- DL7480B2GR6C00017B 的实时数据
(@site_no, UNIX_TIMESTAMP(NOW()) * 1000, 'DL7480B2GR6C00017B', 1, 'charging', 140, 480),
(@site_no, UNIX_TIMESTAMP(NOW()) * 1000, 'DL7480B2GR6C00017B', 2, 'charging', 180, 480),
(@site_no, UNIX_TIMESTAMP(NOW()) * 1000, 'DL7480B2GR6C00017B', 3, 'charging', 90, 480),
(@site_no, UNIX_TIMESTAMP(NOW()) * 1000, 'DL7480B2GR6C00017B', 4, 'charging', 70, 480),

-- DE1120B1GN1CFBC005 的实时数据
(@site_no, UNIX_TIMESTAMP(NOW()) * 1000, 'DE1120B1GN1CFBC005', 1, 'charging', 140, 480),
(@site_no, UNIX_TIMESTAMP(NOW()) * 1000, 'DE1120B1GN1CFBC005', 2, 'charging', 180, 480),
(@site_no, UNIX_TIMESTAMP(NOW()) * 1000, 'DE1120B1GN1CFBC005', 3, 'charging', 90, 480),
(@site_no, UNIX_TIMESTAMP(NOW()) * 1000, 'DE1120B1GN1CFBC005', 4, 'charging', 70, 480),
(@site_no, UNIX_TIMESTAMP(NOW()) * 1000, 'DE1120B1GN1CFBC005', 5, 'available', 0, 480),
(@site_no, UNIX_TIMESTAMP(NOW()) * 1000, 'DE1120B1GN1CFBC005', 6, 'charging', 25, 480),
(@site_no, UNIX_TIMESTAMP(NOW()) * 1000, 'DE1120B1GN1CFBC005', 7, 'charging', 45, 480);

-- 注意：
-- 1. 时间戳使用 UNIX_TIMESTAMP(NOW()) * 1000 生成毫秒级时间戳
-- 2. 充电会话状态：'1'=VEHICLE_CONNECTED, '7'=FORMAL_CHARGING, '0'=VEHICLE_NOT_CONNECTED
-- 3. 所有正在充电的充电枪都有对应的功率预测数据
-- 4. 数据与 test_case03.json 中的 demand_info 保持一致
-- 5. 增加了多个时间点的数据，便于测试时间范围查询功能
-- 7. 增加了不同场站的数据，便于测试多场站场景
-- 8. 可以根据实际需要调整时间戳和其他参数
-- 9. TRUNCATE 操作会清空表数据并重置自增ID，请谨慎使用 
-- 10. 使用变量 @site_no 使脚本更加灵活，用户只需修改脚本开头的变量值即可
