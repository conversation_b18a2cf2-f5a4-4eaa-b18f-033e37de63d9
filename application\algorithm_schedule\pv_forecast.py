import json
from datetime import datetime, timedelta
import traceback
from typing import Dict, List, Optional, Any
import numpy as np
import pandas as pd
from application.algorithm.pv_prediction.prediction_model import predict_from_input
from application.db_operate.db_operate import DBOperate
from application.db_operate.models import PVCurvePredictionDB
from application.utils.get_input_data import (
    get_site_basic_info, get_system_metadata, get_weather_forecast,
    get_solar_radiation_data, get_site_realtime_data, get_pv_mock_realtime_data,
    extract_weather_data, create_pv_data_row, get_pv_default_input_data, get_time_field,
    ENABLE_REALTIME_DATA, save_prediction_to_db
)
from application.utils.logger import setup_logger
INPUT_DATA_FILE = 'pv_input_data.json'
logger = setup_logger("pv_forecast", direction="algorithm_schedule")


def get_pv_input_data(site_no: str, current_time: datetime, data_frames: int) -> Dict:
    """
    统一获取光伏预测所需的所有输入数据，采用 README 中定义的标准格式
    
    Args:
        site_no: 场站编号
        current_time: 当前时间（预测起始点）
        data_frames: 数据帧数
    
    Returns:
        Dict: 完整的标准格式input_data，符合 pv_prediction README 格式
    """
    try:
        logger.info(f"开始获取场站{site_no}的PV预测标准格式输入数据")
        
        # 获取场站基本信息（包含坐标）
        site_info = get_site_basic_info(site_no)
        latitude, longitude = site_info['latitude'], site_info['longitude']
        
        # 获取系统元数据
        system_meta = get_system_metadata(site_no)
        
        # 获取天气相关数据（未来24小时）
        start_time = current_time
        end_time = current_time + timedelta(hours=24)
        start_date = start_time.strftime('%Y-%m-%d')
        end_date = end_time.strftime('%Y-%m-%d')
        
        # 并行获取天气数据源
        basic_weather = get_weather_forecast(latitude, longitude, start_date, end_date)
        solar_data = get_solar_radiation_data(latitude, longitude, start_date, end_date)

        # 一次性获取实时数据（获取光伏发电功率）
        if ENABLE_REALTIME_DATA:
            sample_realtime_data = get_site_realtime_data(site_no, current_time, data_frames)
        else:
            sample_realtime_data = get_pv_mock_realtime_data(data_frames)
        
        logger.info(f"实时数据获取完成，ENABLE_REALTIME_DATA={ENABLE_REALTIME_DATA}")

        # 构建符合 README 标准格式的 input_data 结构
        input_data = {
            "site_name": site_info['station_name'],
            "site_id": site_no,
            "current_time": current_time.strftime("%Y-%m-%d %H:%M:%S"),
            "system_metadata": {
                "max_capacity": system_meta['max_capacity'],
                "azimuth": system_meta['azimuth'],
                "tilt": system_meta['tilt'],
                "latitude": latitude,
                "longitude": longitude,
                "elevation": system_meta['elevation'],
                "fields": [
                    "Timestamp", "GHI", "DNI", "DHI", "air_temperature", "cloud_cover",
                    "wind_speed", "relative_humidity", "hour", "minute",
                    "dayofyear", "generated_power"
                ],
                "values": []
            }
        }
        
        # 构建过去24小时的96个数据点
        for i in range(data_frames):
            # 计算历史时间点（从24小时前到现在）
            timestamp = current_time - timedelta(minutes=15 * (data_frames - 1 - i))
            
            # 获取当前时间点的天气数据
            weather_idx = i % len(basic_weather) if basic_weather else 0
            solar_idx = i % len(solar_data) if solar_data else 0
            
            # 提取天气数据
            weather_data = extract_weather_data(basic_weather, solar_data, weather_idx, solar_idx)
            
            # 获取光伏发电功率（generated_power）
            realtime_idx = min(i, len(sample_realtime_data['grid_power']) - 1)
            generated_power = sample_realtime_data['grid_power'][realtime_idx]
            
            # 创建数据行
            row = create_pv_data_row(timestamp, weather_data, generated_power)
            input_data["system_metadata"]["values"].append(row)
        
        logger.info(f"场站{site_no}PV预测标准格式输入数据构建完成 - 共{len(input_data['system_metadata']['values'])}行数据")
        return input_data
        
    except Exception as e:
        logger.error(f"获取场站{site_no}PV预测标准格式输入数据失败: {e}")
        traceback.print_exc()
        # 返回默认数据
        logger.warning(f"使用默认数据构建PV预测标准格式input_data")
        return get_pv_default_input_data(site_no, current_time, data_frames)


def pv_forecast(site_no: str, data: Dict) -> Optional[Dict]:
    """
    光伏预测算法调度入口
    
    Args:
        site_no: 场站编号
        data: 输入数据，包含以下字段：
            - pv_history_list: 过去24小时光伏发电功率数据（可选）
            - weather_forecast_list: 过去24小时天气数据（可选）
            
    Returns:
        Dict: 预测结果，包含以下字段：
            - predicted_time: 预测时间戳
            - pv_predicted_list: 未来24小时光伏发电功率预测数据
    """
    try:
        logger.info(f"开始执行光伏预测算法 - 场站: {site_no}")
        
        # 定义当前时间（预测起始点）
        current_time = get_time_field(datetime.now())
        data_frames = 97  # 光伏预测需要的数据点
        # 获取完整的input_data
        input_data = get_pv_input_data(site_no, current_time, data_frames)
        # 测试专用
        # if INPUT_DATA_FILE:
        #     if False: # 两不兼容
        #         json.dump(input_data, open(INPUT_DATA_FILE, 'w'), indent=4) # 保存输入数据到文件
        #     else:
        #         with open(INPUT_DATA_FILE, 'r') as f: # 使用本地输入数据
        #             input_data = json.load(f)
        # 执行光伏功率预测
        import pandas as pd
        try:
            predicted_power = predict_from_input(input_data, pd.to_datetime(input_data['current_time']))
            if predicted_power is None or len(predicted_power) == 0:
                logger.warning(f"预测结果为空，将返回零值")
                predicted_power = np.array([0.0] * 96)
            predicted_power_list = predicted_power.tolist()
            max_power = max(predicted_power_list)
            logger.info(f"光伏预测完成 - 场站: {site_no}, 预测峰值功率: {max_power:.2f}kW")
        except Exception as model_error:
            logger.error(f"预测模型调用失败，将返回零值: {model_error}")
            traceback.print_exc()
            predicted_power_list = [0.0] * 96

        save_prediction_to_db(site_no, predicted_power_list, PVCurvePredictionDB)  
        result = {
            'predicted_time': int(current_time.timestamp()),
            'pv_predicted_list': predicted_power_list
        }
        return result
        
    except Exception as e:
        logger.error(f"光伏预测算法执行失败: {e}")
        traceback.print_exc()
        return None


if __name__ == "__main__":
    ret = pv_forecast("SITE_002", {})
    print(f'len(ret):{len(ret["pv_predicted_list"])}; ret: {ret}')
