#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""天气缓存功能测试脚本"""
import time
from application.utils.weather_cache import weather_cache_manager, get_weather_data_cached


def test_weather_cache():
    """测试天气缓存功能"""
    print("=== 天气缓存功能测试 ===")
    
    # 测试参数
    test_date = "2025-06-23"
    test_lat = 22.52
    test_lng = 113.92
    
    print(f"测试参数: 日期={test_date}, 坐标=({test_lat}, {test_lng})")
    
    # 第一次调用 - 应该调用API
    print("\n1. 第一次调用（应该调用API）:")
    start_time = time.time()
    weather_data1 = weather_cache_manager.get_cached_weather_data(test_date, test_lat, test_lng)
    end_time = time.time()
    print(f"   耗时: {end_time - start_time:.2f}秒")
    print(f"   数据获取成功: {'是' if weather_data1 else '否'}")
    
    # 第二次调用 - 应该从缓存获取
    print("\n2. 第二次调用（应该从缓存获取）:")
    start_time = time.time()
    weather_data2 = weather_cache_manager.get_cached_weather_data(test_date, test_lat, test_lng)
    end_time = time.time()
    print(f"   耗时: {end_time - start_time:.2f}秒")
    print(f"   数据获取成功: {'是' if weather_data2 else '否'}")
    
    # 验证数据一致性
    print(f"   数据一致性: {'是' if weather_data1 == weather_data2 else '否'}")
    
    # 测试装饰器版本的缓存函数
    print("\n3. 测试装饰器版本缓存函数:")
    start_time = time.time()
    weather_data3 = get_weather_data_cached(test_date, test_lat, test_lng)
    end_time = time.time()
    print(f"   耗时: {end_time - start_time:.2f}秒")
    
    # 显示缓存信息
    print("\n4. 缓存信息:")
    cache_info = weather_cache_manager.get_cache_info()
    for key, value in cache_info.items():
        print(f"   {key}: {value}")
    
    # 测试不同坐标的缓存
    print("\n5. 测试不同坐标的缓存:")
    test_lat2, test_lng2 = 31.2304, 121.4737  # 上海坐标
    weather_data4 = weather_cache_manager.get_cached_weather_data(test_date, test_lat2, test_lng2)
    print(f"   深圳坐标数据获取成功: {'是' if weather_data4 else '否'}")
    
    # 再次显示缓存信息
    cache_info2 = weather_cache_manager.get_cache_info()
    print(f"   缓存条目数: {cache_info2['cache_size']}")


def test_cache_performance():
    """测试缓存性能"""
    print("\n=== 缓存性能测试 ===")
    
    test_date = "2025-06-23"
    test_lat = 39.9042
    test_lng = 116.4074
    
    # 测试多次调用同一数据的性能
    print("测试多次调用同一数据的性能:")
    
    # 预热缓存
    weather_cache_manager.get_cached_weather_data(test_date, test_lat, test_lng)
    
    # 测试缓存命中性能
    start_time = time.time()
    for i in range(100):
        weather_cache_manager.get_cached_weather_data(test_date, test_lat, test_lng)
    end_time = time.time()
    
    avg_time = (end_time - start_time) / 100
    print(f"   100次缓存命中调用平均耗时: {avg_time*1000:.2f}毫秒")


if __name__ == "__main__":
    try:
        test_weather_cache()
        test_cache_performance()
        print("\n=== 测试完成 ===")
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
