import json
import traceback
from application.db_operate.db_operate import DBOperate
from application.algorithm.energy_storage_optimization.energy_storage_optimization import \
    energy_storage_optimization_runner
from application.utils.logger import setup_logger

logger = setup_logger("storage_dispatch", direction="algorithm_schedule")


def storage_dispatch(site_no, data, trigger=None):
    """
    储能调度算法调度入口
    :param site_no: 场站编号
    :param data: 相关输入数据
    :param trigger: 触发源（如有）
    :return: 调度结果
    """
    logger.info(f'场站 {site_no} 储能调度开始 (触发器: {trigger})')
    db_op = DBOperate()
    es_realtime = db_op.get_es_realtime_data(site_no=site_no)
    if not es_realtime:
        logger.error(f'场站 {site_no} 无储能实时数据')
        return
    site = db_op.get_site(site_no=site_no)
    if not site:
        logger.error(f'场站 {site_no} 不存在')
        return
    pile = db_op.get_pile(site_no=site_no)
    if not pile:
        logger.error(f'场站 {site_no} 无充电桩数据')
        return
    es_soc = es_realtime.es_soc
    min_soc = es_realtime.es_min_soc
    max_soc = es_realtime.es_max_soc

    pv_predicted_list = db_op.get_pv_predicted_list(site_no=site_no)

    hybrid_load = db_op.get_hybrid_load_predicted(site_no=site_no)
    if not hybrid_load:
        logger.error(f'场站 {site_no} 获取融合负载预测失败')
        return
    hybrid_load_predicted_list = json.loads(hybrid_load.power_list)
    start_time = hybrid_load.curve_start_time.strftime("%Y-%m-%d %H:%M:%S")
    logger.info(f'场站 {site_no} 融合负载预测: {hybrid_load_predicted_list}')

    date = hybrid_load.curve_start_time.strftime("%Y-%m-%d")
    logger.info(f'场站 {site_no} 预测日期: {date}')
    price_list = db_op.get_price_by_site_no_and_date(site_no=site_no, date=date)
    if len(price_list) != 24:
        logger.info(f'场站 {site_no} 电价列表: {price_list}')
        logger.error(f'场站 {site_no} 电价列表必须包含24个数据点')
        return
    logger.info(f'场站 {site_no} 电价列表: {price_list}')

    demand_data = db_op.get_site_demand_data(site_no=site_no)
    logger.info(f'场站 {site_no} 需量数据: {demand_data}')

    electricity_sale_price_list = db_op.get_future_24h_sell_price_from_db(site_no)

    input_data = {
        'start_time': start_time,
        'pv_predicted_list': pv_predicted_list,
        'hybrid_load_predicted_list': hybrid_load_predicted_list,
        'electricity_purchase_price_list': price_list,
        'es_soc': es_soc,
        'es_total_energy': site.es_total_energy,
        'es_rated_power': float(pile.rated_power),
        'site_grid_limit': site.site_grid_limit,
        'demand_data': demand_data,
        'min_soc': min_soc,
        'max_soc': max_soc,
        'electricity_sale_price_list': electricity_sale_price_list

    }
    logger.info(f'场站 {site_no} 储能调度输入数据: {input_data}')
    result = {}
    try:
        result = energy_storage_optimization_runner(input_data)
        logger.info(f'场站 {site_no} 储能调度结果: {result}')
        db_op.save_energy_storage_task(site_no=site_no, result=result)
    except Exception as e:
        logger.error(f'场站 {site_no} 储能优化算法调用失败: {str(e)}')
        logger.error(traceback.format_exc())
    return result


if __name__ == '__main__':
    storage_dispatch(site_no='SITE001', data=None)
