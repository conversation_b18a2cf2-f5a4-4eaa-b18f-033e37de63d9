from typing import Dict, Callable, List
from threading import Lock


class EventBus:
    """事件总线，用于处理场站级别的事件"""
    _handlers: Dict[str, List[Callable]]
    _instance = None
    _lock = Lock()

    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(EventBus, cls).__new__(cls)
                cls._instance._handlers = {}
            return cls._instance

    def subscribe(self, event_type: str, handler: Callable) -> None:
        """
        订阅事件
        
        Args:
            event_type: 事件类型
            handler: 事件处理函数
        """
        with self._lock:
            if event_type not in self._handlers:
                self._handlers[event_type] = []
            if handler not in self._handlers[event_type]:
                self._handlers[event_type].append(handler)

    def unsubscribe(self, event_type: str, handler: Callable) -> None:
        """
        取消订阅事件
        
        Args:
            event_type: 事件类型
            handler: 事件处理函数
        """
        with self._lock:
            if event_type in self._handlers:
                if handler in self._handlers[event_type]:
                    self._handlers[event_type].remove(handler)

    def publish(self, event_type: str, **kwargs) -> None:
        """
        发布事件
        
        Args:
            event_type: 事件类型
            **kwargs: 事件参数
        """
        with self._lock:
            if event_type in self._handlers:
                for handler in self._handlers[event_type]:
                    try:
                        handler(**kwargs)
                    except Exception as e:
                        from application.utils.logger import setup_logger
                        logger = setup_logger("event_bus")
                        logger.error(f"Error handling event {event_type}: {str(e)}")


# 全局事件总线实例
event_bus = EventBus()


# 定义事件类型
class EventType:
    """事件类型常量"""
    CHARGER_PLUGGED = "charger_plugged"  # 充电枪插枪事件
    SITE_LOAD_FORECAST_NEEDED = "site_load_forecast_needed"  # 场站负载预测需求事件
    DYNAMIC_PRICE_CHANGE = 'dynamic_price_change' # 动态电价变化事件
